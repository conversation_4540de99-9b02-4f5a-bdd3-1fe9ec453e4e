import { AzureProvider } from '@yaradigitallabs/sh-user-management-browser';
import { Env } from '@yaradigitallabs/sh-user-management-common';
import React from 'react';

export const AzureProviderWithRedirect = ({ children }: { children: React.ReactNode }) => {
  const env = (process.env.AZURE_ENV ?? 'dev') as Env;
  const clientId = process.env.AZURE_CLIENT_ID;
  const redirectUri = window.location.origin;

  if (!clientId && !redirectUri) return null;

  return (
    <AzureProvider
      env={env}
      clientId={clientId}
      region={'eu'}
      callbackUrl={redirectUri}
      cacheLocation='localstorage'
    >
      {children}
    </AzureProvider>
  );
};
