import React, { useState, createContext } from 'react';
interface NavbarState {
  navbarTranslationKey: string;
  setNavbarTranslationKey: React.Dispatch<React.SetStateAction<string>>;
  activeRoutePath: string;
  setActiveRoutePath: React.Dispatch<React.SetStateAction<string>>;
  isSubPageNavbar: boolean;
  setIsSubPageNavbar: React.Dispatch<React.SetStateAction<boolean>>;
}

const initialState = {
  navbarTranslationKey: '',
  setNavbarTranslationKey: () => undefined,
  activeRoutePath: '',
  setActiveRoutePath: () => undefined,
  isSubPageNavbar: false,
  setIsSubPageNavbar: () => undefined,
};

export const NavbarContext = createContext<NavbarState>(initialState);

interface NavbarProviderProps {
  children?: JSX.Element;
}

export const NavbarProvider = (props: NavbarProviderProps) => {
  const [navbarTranslationKey, setNavbarTranslationKey] = useState<string>('');
  const [isSubPageNavbar, setIsSubPageNavbar] = useState<boolean>(false);
  const [activeRoutePath, setActiveRoutePath] = useState<string>('');

  return (
    <NavbarContext.Provider
      value={{
        navbarTranslationKey,
        setNavbarTranslationKey,
        activeRoutePath,
        setActiveRoutePath,
        isSubPageNavbar,
        setIsSubPageNavbar,
      }}
    >
      {props.children}
    </NavbarContext.Provider>
  );
};

export const useNavbar = () => React.useContext(NavbarContext);
