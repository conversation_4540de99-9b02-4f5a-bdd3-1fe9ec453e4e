import React, { useState, createContext } from 'react';
import { SNACKBAR_INITIAL_STATE } from '../../common/constants/global';
import { SnackBarType } from '@common/types';

export type SnackbarStateType = {
  displaySnackbar: SnackBarType;
  setDisplaySnackbar: React.Dispatch<React.SetStateAction<SnackBarType>>;
};

const initialState = {
  displaySnackbar: {},
  setDisplaySnackbar: () => undefined,
};

export const SnackbarContext = createContext<SnackbarStateType>(initialState);

type SnackbarProviderProps = {
  children?: JSX.Element;
};

const SnackbarProvider = (props: SnackbarProviderProps) => {
  const [displaySnackbar, setDisplaySnackbar] = useState(SNACKBAR_INITIAL_STATE);

  return (
    <SnackbarContext.Provider value={{ displaySnackbar, setDisplaySnackbar }}>
      {props.children}
    </SnackbarContext.Provider>
  );
};

export default SnackbarProvider;

export const useSnackbar = () => React.useContext(SnackbarContext);
