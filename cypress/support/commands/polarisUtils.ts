/* eslint-disable cypress/no-unnecessary-waiting */
/// <reference types="cypress" />
import './index';
import configFeatData from '../../fixtures/stage/ConfigureFeatures/featureDataMap.json';
import { homePage } from '../../pages/homepage/home.page';
import { cnpLandingPage } from '../../pages/cnp/landing/landing.page';
import { cnpConfigPage } from '../../pages/cnp/config/cnpConfig.page';
import { normalizeFeatureName } from '../utilFns/reusableFns';

Cypress.Commands.add('configureFeature', (feature, metaData) => {
  feature = { ...feature, ...metaData };
  cy.wait('@countries');
  cy.wait(500);
  homePage.configureFeature.getCountry().click();
  cy.getDropDownValue(feature.country).click({ force: true });
  cy.wait('@regions');
  cy.wait(500);
  homePage.configureFeature.getRegion().click();
  cy.getDropDownValue(feature.region).click({ force: true });
  cy.wait('@cropRegions');
  cy.wait(500);
  homePage.configureFeature.getFeature().click();
  cy.getDropDownValue(feature.featureName).click({ force: true });
  cy.wait('@cropSubClasses');
  cy.wait(500);
  homePage.configureFeature.getCrop().click();
  cy.getDropDownValue(feature.crop).click({ force: true });
  //Goto homepage
  homePage.configureFeature.getGObutton().click();
});

Cypress.Commands.add(
  'EditCropSettings',
  (growthScale: string, yieldUnit: string, demandUnit: string): void => {
    cy.intercept({
      method: 'POST',
      url: 'https://polaris-mock-service.stage.agtech.yara.com/unit-countries/filter',
    }).as('getUnitCountries');
    cy.intercept({
      method: 'GET',
      url: 'https://polaris-mock-service.stage.agtech.yara.com/units',
    }).as('getUnits');
    cy.intercept({
      method: 'GET',
      url: 'https://polaris-mock-service.stage.agtech.yara.com/growth-scales',
    }).as('getGrowthScales');
    cnpLandingPage.cropSettings.getEditBtn().click();
    // cy.wait(['@getGrowthScales', '@getUnits', '@getUnitCountries']).then(
    //   () => {
    cnpLandingPage.cropSettings.getGrowthScaleDropdown().click();
    cy.getDropDownValue(growthScale).click();
    cnpLandingPage.cropSettings.getYieldUnitDropdown().click();
    cy.getDropDownValue(yieldUnit).click();
    cnpLandingPage.cropSettings.getRecommendationUnitDropdown().click();
    cy.getDropDownValue(demandUnit).click();
    //   },
    // );
  },
);

Cypress.Commands.add('navigateToSoilNutritionPlanPage', () => {
  cnpLandingPage.soilCNPCard.getConfigureBtn().should('be.visible').click();

  //check main page title
  homePage.header
    .getPageTitle()
    .should('be.visible')
    .should('contain.text', 'Soil-based Nutrition Plan');
  cy.wait(1000);
  //check subpage title
  cnpConfigPage
    .getPlanTitle()
    .should('be.visible')
    .should(
      'contain.text',
      'You are configuring CNP for ' +
        configFeatData.cnp.crop +
        ' in ' +
        configFeatData.cnp.region +
        ', ' +
        configFeatData.cnp.country,
    );

  //verify the calculation parameters table is visible
  cnpConfigPage.paramsTab.calcParams.getCalcParamsTable().should('be.visible');
});

Cypress.Commands.add('waitForFeature', (feature) => {
  const featName = normalizeFeatureName(feature.featureName);
  switch (featName) {
    case 'cnp':
    case 'cmmm':
    case 'fertigation':
      cy.wait('@countries');
      cy.wait('@regions');
      cy.wait('@featureTags');
      cy.wait('@cropRegions');
      cy.wait('@cropSubClasses');
      cy.wait('@cropDescriptions');
      // cy.wait([
      //   '@countries',
      //   '@regions',
      //   '@featureTags',
      //   '@cropRegions',
      //   '@cropSubClasses',
      //   '@cropDescriptions',
      // ]);
      break;
  }
});
