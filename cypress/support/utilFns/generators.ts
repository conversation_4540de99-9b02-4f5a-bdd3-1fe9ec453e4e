export function randomString(length: number) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

export function randomSpecialChar(length: number) {
  let result = '';
  const characters = `!@#$%^&*()_+=-/.,?><"";:[{}]|\\`;
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

export function randomNumber(length: number) {
  let result = '';
  const characters = `0123456789`;
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

export function generateRandomDecimal(min = 10, max = 999, decimalPlaces = 4): number {
  const range = max - min;
  const multiplier = Math.pow(10, decimalPlaces);
  const randomInt = Math.floor(Math.random() * (range * multiplier)) + 1;
  const randomDecimal = randomInt / multiplier;
  const result = min + randomDecimal;
  return truncateDecimals(result, decimalPlaces);
}

export function getRandomDayOfYear(): number {
  const day = Math.floor(Math.random() * 365) + 1;
  return day;
}

export const truncateDecimals = (num: number, decimalPlaces: number): number => {
  return Number(num.toString().slice(0, num.toString().indexOf('.') + (decimalPlaces + 1)));
};
