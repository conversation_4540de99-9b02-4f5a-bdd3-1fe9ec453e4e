import { IReplaceInput } from '../types';

export class RegisterAliases {
  private static urls: Record<string, object> = {
    getCountries: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-location/api/v1/countries/filter',
      method: 'POST',
    },
    getRegions: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-location/api/v1/regions/filter',
      method: 'POST',
    },
    getAllCropSubClasses: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-crop/api/v1/crop-sub-classes/*',
      method: 'POST',
    },
    getAllCropDescription: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-crop/api/v1/crop-descriptions/*',
      method: 'POST',
    },
    getUnits: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-unit/api/v1/units/filter',
      method: 'POST',
    },
    getUnit: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-unit/api/v1/units/*',
      method: 'GET',
    },
    getAnalysisMethods: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-nutrient-planning/api/v1/analysis-methods/filter',
      method: 'POST',
    },
    getPartnerTags: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-shared/api/v1/partner-tags/filter',
      method: 'POST',
    },
    getMMMValidation: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-master-mind-map/api/v1/mmm-validations/filter',
      method: 'POST',
    },
    getPlanValidation: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-nutrient-planning/api/v1/plan-validations/filter',
      method: 'POST',
    },
    getDemandCalculationModules: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-master-mind-map/api/v1/fertigation/demand-calculation/demand-calculation-modules/*',
      method: 'PUT',
    },
    updateDemandCalculationModulesCMMM: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-master-mind-map/api/v1/cereals/demand-calculation/demand-calculation-modules/**',
      method: 'PUT',
    },
    getCMMMSoilAnalysisConfigs: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-master-mind-map/api/v1/cereals/soil-analysis/configurations/filter',
      method: 'POST',
    },
    updateCMMMNutrientsClassification: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-master-mind-map/api/v1/cereals/soil-analysis/nutrient-classifications/*',
      method: 'PUT',
    },
    updateFertigationSplittingConfiguration: {
      url: 'https://api.stage.api-gw.yaradigitallabs.io/agronomic-master-mind-map/api/v1/fertigation/splitting/configurations/*/splits/*',
      method: 'PUT',
    },
  };

  static configFeature = () => {
    cy.intercept(this.urls.getCountries).as('getCountries');
    cy.intercept(this.urls.getRegions).as('getRegions');
    cy.intercept(this.urls.getAllCropSubClasses).as('getCropSubClasses');
    cy.intercept(this.urls.getAllCropDescription).as('getCropDescription');
  };

  static customEndpoints = (endpointNames: string[]) => {
    endpointNames.forEach((endpointName) => {
      if (!this.urls[endpointName])
        throw new Error(`${endpointName} does not match with the urls in RegisterAliases class`);
      cy.intercept(this.urls[endpointName]).as(endpointName);
    });
  };
}

export const toastMessage = (): Cypress.Chainable => {
  return cy.get('[data-cy="snackbar"]', { timeout: 15000 }).should('exist');
};

export const getAllDropdownValues = (): Cypress.Chainable => {
  return cy.get("div[role='option']").find('label');
};

export const getDropDownValueByIdx = (idx: number): Cypress.Chainable => {
  return cy.get("div[role='option']").find('label').eq(idx);
};

export const formatValue = (value: string | number): string => {
  const parts = value.toString().split('.');
  return parts[1] ? `${parts[0]}.${parts[1].slice(0, 3)}` : value.toString();
};

export const normalizeFeatureName = (featureName: string) => {
  const featName = featureName.toLowerCase();

  if (featName.includes('fertigation')) return 'fertigation';
  if (featName.includes('cereals') || featName === 'cmmm') return 'cmmm';
  if (featName.includes('cnp') || featName.includes('crop') || featName.includes('nutrition'))
    return 'cnp';

  throw new Error(`No matching feature found for ${featureName}`);
};

export const checkNumberUpdateOnBlur = (input: IReplaceInput) => {
  /*
  @param input: IReplaceInput
  @param input.locator: Cypress.Chainable
  @param input.alias: string 
  */
  if (input.alias) RegisterAliases.customEndpoints([input.alias]);
  input.locator.type('{selectall}{backspace}').wait(200).type(`${input.inpVal}`, { delay: 100 });
  cy.get('body').click({ force: true });
  if (input.alias) cy.wait(`@${input.alias}`);
  input.locator.should('have.value', input.expVal);
};
