/* eslint-disable cypress/no-unnecessary-waiting */
import configFeatData from '../../fixtures/stage/ConfigureFeatures/featureDataMap.json';
import { fertigationLandingPage } from '../../pages/fertigation/landing/fertigationLanding.page';
import { fertigationConfigPage } from '../../pages/fertigation/config/fertigationConfig.page';
import { RegisterAliases, toastMessage } from '../../support/utilFns/reusableFns';
import { mockEndpoints } from '../../support/utilFns/mockEngine';
import { env } from '../../support/e2e';
import { getRandomDayOfYear } from '../../support/utilFns/generators';
import { checkNumberUpdateOnBlur } from '../../support/utilFns/reusableFns';
import { randomNumber } from '../../support/utilFns/generators';

describe('Fertigation - Splitting Schedule Growth Phase table and start date', () => {
  before(() => {
    mockEndpoints(configFeatData.fertigationTestNotValidated);
    cy.loginViaSSO(env.userEmail, env.userPassword);
    cy.configureFeature(configFeatData.fertigationTestNotValidated);
    fertigationLandingPage.fertigationValidation.getConfigureBtn().click();
    fertigationConfigPage
      .getPlanTitle()
      .should(
        'have.text',
        `Configure Fertigation Plan for ${configFeatData.fertigationTestNotValidated.crop} in Test Automation Region, Spain`,
      );
  });

  beforeEach(() => {
    cy.reload();
    fertigationConfigPage.getTab('Splitting schedule').click();
  });

  it('[YAPF-T8420] Verify the default state of the Splitting page', () => {
    fertigationConfigPage.splittingScheduleTab
      .getTitle()
      .should('have.text', 'Split fertiliser nutrient application across the growing season');
    fertigationConfigPage.splittingScheduleTab.getCalendarInput().should('be.visible');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseNo()
      .should('have.text', 'Growth phase no.');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseNoValue()
      .should('have.text', '1');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseName()
      .should('have.text', 'Growth phase name');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseNameInput()
      .should('be.visible');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseDuration()
      .should('have.text', 'Duration (days)');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseDurationDays()
      .should('be.visible');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseDurationInput()
      .should('be.visible');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseImage()
      .should('have.text', 'Growth phase image');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseImageButton()
      .should('be.visible');
    fertigationConfigPage.splittingScheduleTab.getScheduleTable().should('be.visible');
    cy.scrollTo('bottom');
    fertigationConfigPage.splittingScheduleTab.getScheduleShowMoreButton().should('be.visible');
  });

  it('[YAPF-T8423] Verify Total Duration exceeding 365 days', () => {
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseDurationInput()
      .clear()
      .type('470')
      .blur()
      .should('have.value', '470');
    cy.scrollTo('top');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseDurationDaysError()
      .should('have.attr', 'class')
      .and('contain', 'validation-error');
  });
  it('[YAPF-T8422] Verify edit functionality for Duration (days)', () => {
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseDurationDays()
      .should('be.visible');
    RegisterAliases.customEndpoints(['updateFertigationSplittingConfiguration']);
    const randomValue: number = getRandomDayOfYear();
    checkNumberUpdateOnBlur({
      locator: fertigationConfigPage.splittingScheduleTab.getScheduleGrowthPhaseDurationInput(),
      inpVal: randomValue,
      expVal: randomValue,
      alias: 'updateFertigationSplittingConfiguration',
    });
    // Verify if decimal value is not allowed
    checkNumberUpdateOnBlur({
      locator: fertigationConfigPage.splittingScheduleTab.getScheduleGrowthPhaseDurationInput(),
      inpVal: 11.4,
      expVal: 114,
      alias: 'updateFertigationSplittingConfiguration',
    });
    // Verify if negative value is not allowed
    cy.wait(1000);
    checkNumberUpdateOnBlur({
      locator: fertigationConfigPage.splittingScheduleTab.getScheduleGrowthPhaseDurationInput(),
      inpVal: -48,
      expVal: 48,
      alias: 'updateFertigationSplittingConfiguration',
    });
    cy.wait(1000);
    // Verify if string value is not allowed
    checkNumberUpdateOnBlur({
      locator: fertigationConfigPage.splittingScheduleTab.getScheduleGrowthPhaseDurationInput(),
      inpVal: 'abc',
      expVal: 0,
      alias: 'updateFertigationSplittingConfiguration',
    });
  });

  it('[YAPF-T8421] Verify the Start Date functionality', () => {
    fertigationConfigPage.splittingScheduleTab.getCalendarInput().click();
    fertigationConfigPage.splittingScheduleTab.getCalendarContent().should('be.visible');
    const day = Number(randomNumber(1)) + 1;
    cy.wait(1000);
    fertigationConfigPage.splittingScheduleTab.getCalendarDay().contains(day).click();
    fertigationConfigPage.splittingScheduleTab
      .getCalendarInput()
      .invoke('val')
      .should('contain', day);
    toastMessage().should('have.text', 'Changes saved');
  });

  it('[YAPF-T8467] Verify edit functionality for Growth phase name', () => {
    fertigationConfigPage.splittingScheduleTab
      .getScheduleGrowthPhaseNameInput()
      .should('be.visible')
      .clear()
      .type('Test Growth phase 1')
      .blur()
      .should('have.value', 'Test Growth phase 1');
  });

  it('[YAPF-T8424] Verify Show More button functionality', () => {
    cy.scrollTo('bottom');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleShowMoreButton()
      .should('be.visible')
      .click();
    fertigationConfigPage.splittingScheduleTab
      .getScheduleTable()
      .find('th:contains("Cu")')
      .should('have.text', 'Cu');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleTable()
      .find('th:contains("Fe")')
      .should('have.text', 'Fe');
    cy.scrollTo('bottom');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleShowMoreButton()
      .should('be.visible')
      .click();
    fertigationConfigPage.splittingScheduleTab
      .getScheduleTable()
      .find('th:contains("Cu")')
      .should('not.exist');
    fertigationConfigPage.splittingScheduleTab
      .getScheduleTable()
      .find('th:contains("Fe")')
      .should('not.exist');
  });

  it('[YAPF-T8425] Verify Horizontal Scrolling of Growth Phase Columns', () => {
    cy.scrollTo('bottom');
    fertigationConfigPage.splittingScheduleTab.getScheduleTable().then(($table) => {
      const initialScrollLeft = $table[0].scrollLeft;

      // Try to scroll right by 100px
      cy.wrap($table).scrollTo(100, 0);

      // Check if scroll position actually changed
      cy.wrap($table).then(($t) => {
        const newScrollLeft = $t[0].scrollLeft;
        expect(newScrollLeft).to.be.greaterThan(initialScrollLeft);
      });
    });
  });
});
