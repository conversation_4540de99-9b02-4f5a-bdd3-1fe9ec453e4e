/* eslint-disable cypress/no-unnecessary-waiting */
import configFeatData from '../../fixtures/stage/ConfigureFeatures/featureDataMap.json';
import { fertigationLandingPage } from '../../pages/fertigation/landing/fertigationLanding.page';
import { fertigationConfigPage } from '../../pages/fertigation/config/fertigationConfig.page';
import { homePage } from '../../pages/homepage/home.page';
import { formatValue, RegisterAliases } from '../../support/utilFns/reusableFns';
import { generateRandomDecimal } from '../../support/utilFns/generators';
import { mockEndpoints } from '../../support/utilFns/mockEngine';
import { env } from '../../support/e2e';

describe('Fertigation Demand Calculation', () => {
  before(() => {
    mockEndpoints(configFeatData.fertigationTestNotValidated);
    cy.loginViaSSO(env.userEmail, env.userPassword);
    cy.configureFeature(configFeatData.fertigationTestNotValidated);
    fertigationLandingPage.fertigationValidation.getConfigureBtn().click();
    fertigationConfigPage
      .getPlanTitle()
      .should(
        'have.text',
        `Configure Fertigation Plan for ${configFeatData.fertigationTestNotValidated.crop} in Test Automation Region, Spain`,
      );
  });

  beforeEach(() => {
    cy.reload();
    fertigationConfigPage.fertigationDemandCalculationsTab.getDemandCaluclationsTab().click();
  });

  it('[YAPF-T8354] Verify the correct display of the Demand Calculations page', () => {
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getEquationTitle()
      .should('have.text', 'Main equation');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getEquationHelperText()
      .should(
        'have.text',
        'The algorithm uses this equation to calculate fertiliser nutrient demand. Some inputs are optional.',
      );
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getEquationContent()
      .invoke('text')
      .then((text) => text.replace(/\s+/g, ' ').trim())
      .should(
        'eq',
        'Fertiliser nutrient demand = ((Crop nutrient demand / Nutrient use efficiency) - Irrigation water nutrient) x Soil correction factor',
      );
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTitle()
      .should('have.text', 'Nutrient');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('N')
      .should('exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('P')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('K')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Ca')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Mg')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('S')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('B')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Cu')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Fe')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Mn')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Zn')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Mo')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientFormDescription()
      .should('exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientFormDescription()
      .should('have.attr', 'data-disabled');
  });

  it('[YAPF-T8355] Verify the behavior when selecting a nutrient', () => {
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientFormHelperText()
      .should('have.text', 'Calculations are done for this nutrient form.');
    fertigationConfigPage.fertigationDemandCalculationsTab.getNutrientTile('Mg').click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientFormElement()
      .should('have.text', 'MgO');
  });
  it('[YAPF-T8354] Verify the Nutrients component in the Demand Calculations page', () => {
    fertigationConfigPage.fertigationDemandCalculationsTab.getNutrientTitle().should('be.visible');
    const nutrientSymbol = ['N', 'P', 'K', 'Ca', 'Mg', 'S', 'B', 'Cu', 'Fe', 'Mn', 'Zn', 'Mo'];
    const nutrientNames = [
      'Nitrogen',
      'Phosphorus',
      'Potassium',
      'Calcium',
      'Magnesium',
      'Sulphur',
      'Boron',
      'Copper',
      'Iron',
      'Manganese',
      'Zinc',
      'Molybdenum',
    ];

    fertigationConfigPage.fertigationDemandCalculationsTab.getAllNutrients().each(($el, index) => {
      cy.wrap($el).find('span').eq(0).should('have.text', nutrientSymbol[index]);
      cy.wrap($el).find('span').eq(1).should('have.text', nutrientNames[index]);
    });

    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Molybdenum')
      .trigger('mouseover');
    cy.contains('Molybdenum');
  });

  it('[YAPF-T8356] Verify the behavior when refreshing the Demand calculations page', () => {
    fertigationConfigPage.fertigationDemandCalculationsTab.getNutrientTile('Cu').click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Cu')
      .should('exist');
    cy.reload();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Cu')
      .should('exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('P')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('K')
      .should('not.exist');
  });

  it('[YAPF-T8357] Verify nutrient selection persistence within Fertigation Plan', () => {
    fertigationConfigPage.fertigationDemandCalculationsTab.getNutrientTile('Cu').click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Cu')
      .should('exist');
    fertigationConfigPage.fertigationSoilTab.getSoilAnalysisTab().click();
    fertigationConfigPage.fertigationDemandCalculationsTab.getDemandCaluclationsTab().click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('Cu')
      .should('exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('P')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('K')
      .should('not.exist');
  });

  it('[YAPF-T8358] Verify reset of selections when navigating outside Fertigation Plan', () => {
    cy.wait(1000);
    homePage.getWelcomeScreenHomePage().should('be.visible').click();
    homePage.getWelcomeScreenMessage().should('be.visible').should('exist');
    mockEndpoints(configFeatData.fertigationTestNotValidated);
    cy.reload();
    cy.configureFeature(configFeatData.fertigationTestNotValidated);
    fertigationLandingPage.fertigationValidation.getConfigureBtn().click();
    fertigationConfigPage.fertigationDemandCalculationsTab.getDemandCaluclationsTab().click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTitle()
      .should('have.text', 'Nutrient');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('N')
      .should('exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('P')
      .should('not.exist');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTileSelected('K')
      .should('not.exist');
  });

  it('[YAPF-T8523] Verify Default State and Chevron Functionality of Soil Correction Factor Sub-Module', () => {
    cy.wait(1000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('be.visible');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('have.attr', 'data-state', 'closed');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorCheckBox()
      .should('have.attr', 'data-state', 'unchecked');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('have.attr', 'data-state', 'closed');
  });

  it('[YAPF-T8524] [YAPF-T8525] Verify Enabling or disabling Fertiliser Calculation Option', () => {
    cy.wait(2000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('be.visible');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorCheckBox()
      .click();
    RegisterAliases.customEndpoints(['getDemandCalculationModules']);
    cy.wait('@getDemandCalculationModules').then((interception) => {
      expect(interception.response?.body?.isEnabled).to.be.true;
    });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorCheckBox()
      .should('have.attr', 'data-state', 'checked');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorCheckBox()
      .click();
    cy.wait('@getDemandCalculationModules').then((interception) => {
      expect(interception.response?.body?.isEnabled).to.be.false;
    });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorCheckBox()
      .should('have.attr', 'data-state', 'unchecked');
  });

  it('[YAPF-T8526] Verify Input of Multiplier Values', () => {
    cy.wait(2000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorComponent()
      .should('be.visible');
    fertigationConfigPage.fertigationDemandCalculationsTab.expandSoilCorrectionFactorComponentState();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorParameterTable()
      .should('be.visible');
    const parameterLevels = ['VERY_LOW', 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH'];
    parameterLevels.forEach((level) => {
      const randomValue = generateRandomDecimal(0, 4);
      cy.log(`Testing ${level} with random value: ${randomValue}`);
      cy.wrap(null).then(async () => {
        fertigationConfigPage.fertigationDemandCalculationsTab
          .getSoilCorrectionFactorParameterLevelInput(level)
          .should('be.visible')
          .clear()
          .wait(500)
          .type(randomValue.toString())
          .blur()
          .invoke('val')
          .then((actualValue) => {
            // Convert both values to numbers for comparison
            // Get expected value by trimming to 3 decimal places
            // Format input value to match expected decimal places

            const expectedValue = formatValue(randomValue);
            // Verify the values match
            expect(actualValue.toString()).to.equal(expectedValue.toString());

            // Verify decimal places
            const decimalPart = actualValue.toString().split('.')[1];
            if (decimalPart) {
              expect(
                decimalPart.length,
                `Value ${actualValue} should have at most 3 decimal places`,
              ).to.be.at.most(3);
            }
            cy.log(
              `✓ Validated ${level}: ${actualValue} (${
                decimalPart ? decimalPart.length : 0
              } decimal places)`,
            );
          });
      });
      cy.wait(2000); // Add a wait to ensure synchronization between inputs
    });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getSoilCorrectionFactorParameterLevelInput('MEDIUM')
      .clear()
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('0');
      });
  });

  it('[YAPF-T8592] Verify Default State and Collapse/Expand Section - Crop Nutrient Demand', () => {
    cy.wait(1000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandComponent()
      .should('be.visible')
      .and('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandHeader()
      .should('have.text', 'Crop nutrient demand');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandEquationsTitle()
      .should('have.text', 'Equations');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandEquationsContent()
      .invoke('text')
      .invoke('replace', /\s+/g, ' ')
      .should(
        'eq',
        'Crop nutrient demand = Vegetative demand + Reproductive demand, where Reproductive demand = Target yield x Nutrient removal',
      );
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandEquationsComment()
      .should('have.text', 'Note: Target yield is provided by the farming-solution user.');
    fertigationConfigPage.fertigationDemandCalculationsTab.getCropNutrientDemandComponent().click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandComponent()
      .should('have.attr', 'data-state', 'closed');
    fertigationConfigPage.fertigationDemandCalculationsTab.getCropNutrientDemandComponent().click();
  });

  it('[YAPF-T8593] Verify Vegetative Demand Input', () => {
    cy.wait(1000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandComponent()
      .should('be.visible')
      .and('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandUptakeText()
      .should('have.text', 'Vegetative demand (kg/ha)');
    const randomValue = generateRandomDecimal(0, 4);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandUptakeInput()
      .clear()
      .type(randomValue.toString())
      .blur()
      .invoke('val')
      .then((actualValue) => {
        // Convert both values to numbers for comparison
        // Get expected value by trimming to 3 decimal places
        const parts = randomValue.toString().split('.');
        const expectedValue = parts[1] ? `${parts[0]}.${parts[1].slice(0, 3)}` : randomValue;

        // Verify the values match
        expect(actualValue.toString()).to.equal(expectedValue.toString());

        // Verify decimal places
        const decimalPart = actualValue.toString().split('.')[1];
        if (decimalPart) {
          expect(
            decimalPart.length,
            `Value ${actualValue} should have at most 3 decimal places`,
          ).to.be.at.most(3);
        }
      });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandUptakeInput()
      .clear()
      .type('ab*cd')
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('0');
      });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandUptakeInput()
      .clear()
      .type('-50')
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('50');
      });
  });

  it('[YAPF-T8594] Verify Nutrient Removal Input Functionality', () => {
    cy.wait(1000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandComponent()
      .should('be.visible')
      .and('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalText()
      .should('have.text', 'Nutrient removal (kg/t)');
    const randomValue = generateRandomDecimal(0, 4);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalInput()
      .clear()
      .type(randomValue.toString())
      .blur()
      .invoke('val')
      .then((actualValue) => {
        // Convert both values to numbers for comparison
        // Get expected value by trimming to 3 decimal places
        const parts = randomValue.toString().split('.');
        const expectedValue = parts[1] ? `${parts[0]}.${parts[1].slice(0, 3)}` : randomValue;

        // Verify the values match
        expect(actualValue.toString()).to.equal(expectedValue.toString());

        // Verify decimal places
        const decimalPart = actualValue.toString().split('.')[1];
        if (decimalPart) {
          expect(
            decimalPart.length,
            `Value ${actualValue} should have at most 3 decimal places`,
          ).to.be.at.most(3);
        }
      });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalInput()
      .clear()
      .wait(500)
      .type('ab*cd')
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('0');
      });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalInput()
      .clear()
      .wait(500)
      .type('-50')
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('50');
      });
  });

  it('[YAPF-T8595] Verify Error Handling When Removing Input Values', () => {
    cy.wait(1000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Sulphur')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandComponent()
      .should('be.visible')
      .and('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandUptakeInput()
      .clear()
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('0');
      });
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalInput()
      .clear()
      .invoke('val')
      .then((defaultValue) => {
        expect(defaultValue.toString()).to.equal('0');
      });
  });
  it('[YAPF-T8984] Verify decimal place limit for nutrient removal value field', () => {
    cy.wait(1000);
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getNutrientTile('Boron')
      .should('be.visible')
      .click();
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandComponent()
      .should('be.visible')
      .and('have.attr', 'data-state', 'open');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalText()
      .should('have.text', 'Nutrient removal (kg/t)');
    fertigationConfigPage.fertigationDemandCalculationsTab
      .getCropNutrientDemandRemovalInput()
      .clear()
      .type('3.6989879')
      .blur()
      .invoke('val')
      .then((actualValue) => {
        // Convert both values to numbers for comparison
        // Get expected value by trimming to 3 decimal places
        // Verify the values match
        expect(actualValue.toString()).to.equal('3.69898');

        // Verify decimal places
        const decimalPart = actualValue.toString().split('.')[1];
        if (decimalPart) {
          expect(
            decimalPart.length,
            `Value ${actualValue} should have at most 5 decimal places`,
          ).to.be.at.most(5);
        }
      });
  });
});
