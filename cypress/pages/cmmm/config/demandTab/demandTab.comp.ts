import { nutrientPCropDemand } from './cropDemand.comp';
import { useEfficiency } from './useEfficiency.comp';
import { soilOrganicMatter } from './soilOrganicMatter.comp';
import { soilMineralN } from './soilMineralN.comp';
import { soilCorrection } from './soilCorrection.comp';

class DemandTab {
  nutrient_cropDemand;
  nutrient_useEfficiency;
  nutrient_soilOrganicMatter;
  nutrient_soilMineralN;
  nutrient_soilCorrection;

  constructor() {
    this.nutrient_cropDemand = nutrientPCropDemand;
    this.nutrient_useEfficiency = useEfficiency;
    this.nutrient_soilOrganicMatter = soilOrganicMatter;
    this.nutrient_soilMineralN = soilMineralN;
    this.nutrient_soilCorrection = soilCorrection;
  }

  getRoot = () => cy.get('[data-cy="plan-configuration-tab-content-demandCalculations"]');

  getTitle(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="nutrient-list-title"]');
  }

  getNutrientTile(nutrientName: string): Cypress.Chainable {
    return this.getRoot().find('[data-cy="nutrients-static-list"]').contains(nutrientName);
  }

  getAllNutrients(): Cypress.Chainable {
    return this.getRoot()
      .find('[data-cy="nutrients-static-list"]')
      .find('div[data-cy^="nutrient-tile"]');
  }

  getNutrientTileName(elementalName: string): Cypress.Chainable {
    return this.getRoot().find('[data-cy="nutrients-static-list"]').contains(elementalName);
  }

  getNutrientTileSelected(nutrientName: string): Cypress.Chainable {
    return this.getRoot()
      .find('[data-cy="nutrients-static-list"]')
      .contains(nutrientName)
      .parent('[class*="-selected-true"]'); // Check if the parent element has the class indicating it's selected
  }

  getNutrientForm(): Cypress.Chainable {
    return this.getRoot().find('button[aria-label="Select nutrient form"]');
  }

  getNutrientFormLabel(): Cypress.Chainable {
    return this.getRoot().find('label').eq(1);
  }

  getDemandCalaculationMainEquationHeader(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="cereals-demand-calculations-main-equation"]').find('h2');
  }

  getDemandCalaculationMainEquationContent(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="cereals-demand-calculations-main-equation-content"]');
  }

  getCropNutrientDemandCollapsible(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="crop-nutrient-demand-collapsible"]');
  }

  getDemandCalculationCropNutrientDemandCollapsible(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="crop-nutrient-demand-collapsible"]').find('button');
  }

  getUptakeField(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="cereals-crop-nutrient-demand-uptakeValue-input"]');
  }

  getRemovalField(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="cereals-crop-nutrient-demand-removalValue-input"]');
  }

  getToolTip(): Cypress.Chainable {
    return this.getRoot().find('h2 svg');
  }
}

export const demandTab = new DemandTab();
