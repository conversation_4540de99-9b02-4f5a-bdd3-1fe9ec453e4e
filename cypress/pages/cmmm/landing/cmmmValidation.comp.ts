class CmmmValidation {
  getRoot = () => cy.get('[data-cy="mmm-configuration-card"]');
  getConfigureBtn = () => cy.get('[data-cy="configuration-card-test-harness-button"]');
  getSoilBasedNutritionCard = () => cy.get('[data-cy="soil-nutrition-card"]');
  getTestHarnessBtn = () => cy.get('[data-cy="soil-nutrition-card-test-button"]');
  getValidateToggle = () => cy.get('[data-cy="soil-validation-switch-btn"]');

  getCMMMValidationText(): Cypress.Chainable {
    return cy.get('[data-cy="mmm-configuration-card"] p');
  }

  //custom fns
  checkCMMMValidationNotValidated = () => {
    cy.intercept({
      method: 'POST',
      url: 'https://agronomic-master-mind-map.stage.emea.yaradigitallabs.io/api/v1/mmm-validations/filter',
    }).as('getMMMValidation');
    cy.wait('@getMMMValidation', { timeout: 20000 }).then((interception) => {
      const validationStatus = interception.response?.body?.entities?.[0].validationStatus;
      cy.log(validationStatus);
      if (validationStatus == 'NOT_SET') {
        this.getCMMMValidationText().eq(0).should('contain.text', 'Not validated yet');
      }
    });
  };

  enableOrDisableToggle = () => {
    cy.intercept({
      method: 'POST',
      url: 'https://agronomic-master-mind-map.stage.emea.yaradigitallabs.io/api/v1/mmm-validations/filter',
    }).as('getMMMValidation');
    cy.wait('@getMMMValidation', { timeout: 20000 }).then((interception) => {
      const validationStatus = interception.response?.body?.entities?.[0].validationStatus;
      cy.log(validationStatus);
      if (validationStatus == 'INVALIDATED') {
        this.getCMMMValidationText().eq(0).should('contain.text', 'Invalidated');
        this.enableValidateToggle();
        this.getCMMMValidationText().eq(0).should('contain.text', 'Validated by');
      }
      if (validationStatus == 'VALIDATED') {
        this.getCMMMValidationText().eq(0).should('contain.text', 'Validated by');
        this.disableValidateToggle();
        this.getCMMMValidationText().eq(0).should('contain.text', 'Invalidated');
      }
    });
  };

  enableValidateToggle = () => {
    this.getValidateToggle()
      .invoke('attr', 'data-state')
      .then((status) => {
        if (status === 'unchecked') {
          this.getValidateToggle().click();
        }
      });
  };
  disableValidateToggle = () => {
    this.getValidateToggle()
      .invoke('attr', 'data-state')
      .then((status) => {
        cy.log('toggle status', status);
        if (status === 'checked') {
          this.getValidateToggle().click();
          this.getInvalidateConfirmBtn().click();
        }
      });
  };

  //validation popup
  getValidationPopupRoot = () => cy.get('[data-cy="validation-popup-content"]');
  getInvalidateConfirmBtn = () =>
    this.getValidationPopupRoot().find('[data-cy="validation-popup-confirm"]');
}
export const cmmmValidation = new CmmmValidation();
