import { cropSettings } from './cropSettings.comp';
import { fertigationValidation } from './fertigationValidation.comp';

class FertigationLanding {
  fertigationValidation;
  cropSettings;

  constructor() {
    this.fertigationValidation = fertigationValidation;
    this.cropSettings = cropSettings;
  }

  getDetailedTitle = () => cy.get('[data-cy="mmm-details-title"]');

  getMainTitle = () => cy.get('[data-cy="widget-mainTitle"]');
}

export const fertigationLandingPage = new FertigationLanding();
