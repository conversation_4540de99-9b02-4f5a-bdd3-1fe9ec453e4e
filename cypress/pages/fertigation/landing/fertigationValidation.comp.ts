class FertigationValidation {
  getRoot = () => cy.get('[data-cy="mmm-configuration-card"]');

  getFertigationPlanTitle = () => this.getRoot().find('h1').eq(0);

  getFertigationPlanInformation = () => this.getRoot().find('h1').eq(1);

  getConfigureBtn = () => cy.get('[data-cy="configuration-card-configure-button"]');
  getSoilBasedNutritionCard = () => cy.get('[data-cy="soil-nutrition-card"]');
  getTestHarnessBtn = () => cy.get('[data-cy="configuration-card-test-harness-button"]');
  getValidateToggle = () => cy.get('[data-cy="soil-validation-switch-btn"]');

  getValidationPopUpHeaderText = () => this.getValidationPopupRoot().find('h2');

  getValidationPopUpText = () => this.getValidationPopupRoot().find('p');

  getFertigationValidationText(): Cypress.Chainable {
    return cy.get('[data-cy="mmm-configuration-card"] p').eq(0);
  }

  modifyToggleStatus = (validationStatus: string) => {
    if (validationStatus == 'INVALIDATED') {
      this.getFertigationValidationText().eq(0).should('contain.text', 'Invalidated');
      this.enableValidateToggle();
      this.getFertigationValidationText().eq(0).should('contain.text', 'Validated by');
    }
  };

  enableValidateToggle = () => {
    this.getValidateToggle()
      .invoke('attr', 'data-state')
      .then((status) => {
        if (status === 'unchecked') {
          this.getValidateToggle().click();
        }
      });
  };
  disableValidateToggle = () => {
    this.getValidateToggle()
      .invoke('attr', 'data-state')
      .then((status) => {
        cy.log('toggle status', status);
        if (status === 'checked') {
          this.getValidateToggle().click();
          this.getValidationPopupRoot().should('be.visible');
          this.getValidationPopUpHeaderText().should('have.text', 'Invalidate this plan?');
          this.getValidationPopUpText().should(
            'have.text',
            'Once invalidated, this configuration can no longer be used by farming solutions.',
          );
          this.getInvalidateConfirmBtn().should('be.visible');
          this.getInvalidateCancelBtn().should('be.visible');
          this.getInvalidateConfirmBtn().click();
        }
      });
  };

  //validation popup
  getValidationPopupRoot = () => cy.get('[data-cy="validation-popup-content"]');
  getInvalidateConfirmBtn = () =>
    this.getValidationPopupRoot().find('[data-cy="validation-popup-confirm"]');
  getInvalidateCancelBtn = () =>
    this.getValidationPopupRoot().find('[data-cy="validation-popup-cancel"]');
}
export const fertigationValidation = new FertigationValidation();
