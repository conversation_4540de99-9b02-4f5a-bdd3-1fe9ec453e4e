class FertigationSplittingScheduleTab {
  getComponent(): Cypress.Chainable {
    return cy.get('[data-cy="plan-configuration-tab-content-splittingSchedule"]');
  }

  getTitle(): Cypress.Chainable {
    return this.getComponent().find('h1');
  }

  getCalendarInput(): Cypress.Chainable {
    return this.getComponent().find('[data-testid="calendar-trigger-input-start"]');
  }

  getCalendarContent(): Cypress.Chainable {
    return cy.get('[data-testid="calendar-content"]');
  }

  getCalendarDay(): Cypress.Chainable {
    return cy.get('[data-testid="calendar-day"]');
  }

  getCalendarInputText(): Cypress.Chainable {
    return this.getComponent().find('[data-testid="calendar-trigger-input-start"]').siblings('p');
  }

  getScheduleTable(): Cypress.Chainable {
    return cy.get('[data-cy="fertigation-splitting-schedule-table"]');
  }

  getScheduleTableRows(): Cypress.Chainable {
    return cy.get('[data-cy="fertigation-splitting-schedule-table"] tr');
  }

  getScheduleGrowthPhaseNo(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(0).find('th').eq(0);
  }

  getScheduleGrowthPhaseNoValue(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(0).find('td').eq(0).find('div');
  }

  getScheduleGrowthPhaseName(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(1).find('th').eq(0);
  }

  getScheduleGrowthPhaseNameInput(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(1).find('input').eq(0);
  }

  getScheduleGrowthPhaseDuration(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(2).find('th').eq(0);
  }

  getScheduleGrowthPhaseDurationDays(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(2).find('th').eq(1);
  }

  getScheduleGrowthPhaseDurationDaysError(): Cypress.Chainable {
    return cy.get(':nth-child(3) > th.validation-error');
  }

  getScheduleGrowthPhaseDurationInput(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(2).find('input').eq(0);
  }

  getScheduleGrowthPhaseImage(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(3).find('th').eq(0);
  }

  getScheduleGrowthPhaseImageButton(): Cypress.Chainable {
    return this.getScheduleTable().find('[data-cy="fertigation-splitting-add-image-button"]');
  }

  getScheduleShowMoreButton(): Cypress.Chainable {
    return this.getScheduleTable().find('[data-cy="fertigation-splitting-expand-button"]');
  }

  getScheduleNutrient(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(4).find('th').eq(0);
  }

  getScheduleNutrientTotal(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(4).find('th').eq(1);
  }

  getScheduleNutrientSplitPercentage(): Cypress.Chainable {
    return this.getScheduleTableRows().eq(4).find('td');
  }

  enterNutrientSplitPercentage(nutrient: string, splitPercentage: number): void {
    this.getScheduleTable()
      .contains('tr', nutrient)
      .within(() => {
        cy.get('input').should('be.visible');
        cy.get('input').clear();
        cy.get('input').type(splitPercentage.toString());
        cy.get('input').should('have.value', splitPercentage.toString());
      });
  }
}
export const splittingSchedule = new FertigationSplittingScheduleTab();
