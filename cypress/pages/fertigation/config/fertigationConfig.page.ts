import { fertigationSoilTab } from './fertigationSoilTab/fertigationSoilTab.comp';
import { fertigationDemandCalculationsTab } from './fertigationDemandCalculations/fertigationDemandCalculationsTab.comp';
import { fertigationLeafTab } from './fertigationLeafTab/fertigationLeafTab.comp';
import { splittingSchedule } from './fertigationSplitTab/fertigationSplittingScheduleTab.comp';

class FertigationConfig {
  fertigationSoilTab;
  fertigationDemandCalculationsTab;
  fertigationLeafTab;
  splittingScheduleTab;
  constructor() {
    this.fertigationSoilTab = fertigationSoilTab;
    this.fertigationDemandCalculationsTab = fertigationDemandCalculationsTab;
    this.fertigationLeafTab = fertigationLeafTab;
    this.splittingScheduleTab = splittingSchedule;
  }
  private getRoot(): Cypress.Chainable {
    return cy.get('[data-cy="plan-configuration-content"]');
  }

  getPlanTitle(): Cypress.Chainable {
    return this.getRoot().find('[data-cy="plan-configuration-title"]');
  }

  getTab(tabName: string): Cypress.Chainable {
    return this.getRoot().find(`[data-cy^=plan-configuration-tab-button]`).contains(tabName);
  }

  getAllTab(): Cypress.Chainable {
    return this.getRoot().find(`[data-cy^=plan-configuration-tab-button]`);
  }
}

export const fertigationConfigPage = new FertigationConfig();
