import { getTranslationKeyFromPath } from './getCropTranslationKey';

describe('String utils', () => {
  test('getTranslationKeyFromPath fails, null returned', () => {
    const invalidData = ['', undefined, null, 0, -0, 0n, false, NaN, {}, []];
    invalidData.forEach((nonStr) => {
      expect(getTranslationKeyFromPath(nonStr)).toEqual(null);
    });
  });

  test('getTranslationKeyFromPath success, with spaces', () => {
    const strWithSpace = 'test Test test';
    const expectedResult = 'testTestTest';

    expect(getTranslationKeyFromPath(strWithSpace)).toEqual(expectedResult);
  });

  test('getTranslationKeyFromPath success, with spaces and dashes', () => {
    const strWithSpace = 'test-Test test';
    const expectedResult = 'testTestTest';

    expect(getTranslationKeyFromPath(strWithSpace)).toEqual(expectedResult);
  });

  test('getTranslationKeyFromPath success, without spaces', () => {
    const strWithSpace = 'TesttesttestTest';
    const expectedResult = 'testtesttesttest';

    expect(getTranslationKeyFromPath(strWithSpace)).toEqual(expectedResult);
  });
});
