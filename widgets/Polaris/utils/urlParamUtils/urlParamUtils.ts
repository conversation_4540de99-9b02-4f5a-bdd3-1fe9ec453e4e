import { FEATURE_IDS, FEATURE_NAME_PARAM, URLParamKey } from '@common/constants';
import { capitalizeFirstLetter } from '../stringUtils';
import {
  CMMM_PLAN_CONFIGURATION_TABS,
  CNP_PLAN_CONFIGURATION_TABS,
  FERTIGATION_PLAN_CONFIGURATION_TABS,
} from '@widgets/Polaris/src/screens/CropFeatures/shared/constants';

/**
 * Helper method to retrieve IDs from URL params by their keys/prefixes
 * @param pathname string
 * @param paramKey string
 * @returns string | undefined
 */
export const getValueByKeyFromUrlParams = (pathname: string, paramKey: URLParamKey) => {
  const splitPathname = pathname.split('/');
  const indexOfParamKey = splitPathname.indexOf(paramKey);

  if (indexOfParamKey === -1 || indexOfParamKey === splitPathname.length - 1) return;

  const param = splitPathname[indexOfParamKey + 1];

  if (param.startsWith(':')) return;

  return param;
};

export const featureInParamsToFeatureId = (featureNameParam: string | undefined) => {
  if (!featureNameParam) return;
  const isFeatureNameKey = (key: string) => key === 'CMMM' || key === 'CNP' || key === 'FP';
  const featureName = Object.keys(FEATURE_NAME_PARAM).find(
    (key) => isFeatureNameKey(key) && FEATURE_NAME_PARAM[key] === featureNameParam,
  );
  if (!featureName) return;

  if (isFeatureNameKey(featureName)) {
    const featureId = FEATURE_IDS[featureName];
    return featureId;
  }
};

export const configTabInParamsToConfigTabName = (
  configTabParam: string | undefined,
): string | undefined => {
  if (!configTabParam) return;

  const tabName = capitalizeFirstLetter(configTabParam.split('-').join(' '));
  const configTabs = {
    ...CNP_PLAN_CONFIGURATION_TABS,
    ...CMMM_PLAN_CONFIGURATION_TABS,
    ...FERTIGATION_PLAN_CONFIGURATION_TABS,
  };
  const isConfigTab = Object.values(configTabs).includes(tabName);

  return isConfigTab ? tabName : undefined;
};
