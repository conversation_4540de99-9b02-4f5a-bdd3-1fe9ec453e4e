import { FEATURE_NAME_PARAM, URLParamKey } from '@common/constants';
import {
  getValueByKeyFromUrlParams,
  featureInParamsToFeatureId,
  configTabInParamsToConfigTabName,
} from '.';
import {
  CMMM_PLAN_CONFIGURATION_TABS,
  CNP_PLAN_CONFIGURATION_TABS,
  FERTIGATION_PLAN_CONFIGURATION_TABS,
} from '@widgets/Polaris/src/screens/CropFeatures/shared/constants';

describe('getValueByKeyFromUrlParams', () => {
  it('returns feature from an URL with path containing "crop-features"', () => {
    expect(
      getValueByKeyFromUrlParams(
        '/crop-features/cereals-mmm/crop-region/83255854-4b04-4af4-9922-c233d7b94f30',
        URLParamKey.CROP_FEATURES,
      ),
    ).toBe('cereals-mmm');
  });

  it('returns undefined when a path does not contain a desired param key', () => {
    expect(
      getValueByKeyFromUrlParams('/some-path/cereals-mmm', URLParamKey.CROP_FEATURES),
    ).toBeUndefined();
    expect(getValueByKeyFromUrlParams('/', URLParamKey.CROP_FEATURES)).toBeUndefined();
  });

  it('returns undefined if searched param is a placeholder (e.g. ":cropRegionId")', () => {
    expect(
      getValueByKeyFromUrlParams('/crop-features/:cropFeature', URLParamKey.CROP_FEATURES),
    ).toBeUndefined();
  });
});

describe('featureInParamsToFeatureId', () => {
  it('returns a valid feature ID based on a feature name in URL', () => {
    expect(featureInParamsToFeatureId(FEATURE_NAME_PARAM.CMMM)).toBe(
      '5aacf96f-dce2-4a7a-a36c-0753f016d083',
    );
    expect(featureInParamsToFeatureId('cereals-mmm')).toBe('5aacf96f-dce2-4a7a-a36c-0753f016d083');
    expect(featureInParamsToFeatureId('fertigation-plan')).toBe(
      'b49b8561-4f04-43f6-8c7c-b22f33b2de73',
    );
    expect(featureInParamsToFeatureId('crop-nutrition-plan')).toBe(
      '92b5d200-0ccb-4d6a-a6f4-3651b935521c',
    );
  });

  it('returns undefined when provided with an invalid feature name from URL', () => {
    expect(featureInParamsToFeatureId('zerealien')).toBeUndefined();
    expect(featureInParamsToFeatureId('fp-cnp')).toBeUndefined();
  });
});

describe('configTabInParamsToConfigTabName', () => {
  it('returns a valid configuration tab name if provided a corresponding URL param', () => {
    expect(configTabInParamsToConfigTabName('soil-analysis')).toBe(
      FERTIGATION_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS,
    );
    expect(configTabInParamsToConfigTabName('demand-calculations')).toBe(
      FERTIGATION_PLAN_CONFIGURATION_TABS.DEMAND_CALC,
    );
    expect(configTabInParamsToConfigTabName('organic-fertilisers')).toBe(
      CMMM_PLAN_CONFIGURATION_TABS.ORGANIC_FERTILISERS,
    );
    expect(configTabInParamsToConfigTabName('product-recommendations')).toBe(
      FERTIGATION_PLAN_CONFIGURATION_TABS.PRODUCT_REC,
    );
    expect(configTabInParamsToConfigTabName('leaf-analysis')).toBe(
      FERTIGATION_PLAN_CONFIGURATION_TABS.LEAF_ANALYSIS,
    );
    expect(configTabInParamsToConfigTabName('water-analysis')).toBe(
      FERTIGATION_PLAN_CONFIGURATION_TABS.WATER_ANALYSIS,
    );
    expect(configTabInParamsToConfigTabName('splitting-schedule')).toBe(
      FERTIGATION_PLAN_CONFIGURATION_TABS.SPLITTING_SCHEDULE,
    );
    expect(configTabInParamsToConfigTabName('parameters')).toBe(
      CNP_PLAN_CONFIGURATION_TABS.PARAMETERS,
    );
    expect(configTabInParamsToConfigTabName('n-splitting-schedule')).toBe(
      CMMM_PLAN_CONFIGURATION_TABS.N_SPLITTING_SCHEDULE,
    );
  });

  it('returns undefined if provided an invalid URL param', () => {
    expect(configTabInParamsToConfigTabName('n-analysis')).toBe(undefined);
    expect(configTabInParamsToConfigTabName('n-schedule')).toBe(undefined);
    expect(configTabInParamsToConfigTabName('')).toBe(undefined);
    expect(configTabInParamsToConfigTabName(undefined)).toBe(undefined);
    expect(configTabInParamsToConfigTabName('master-mind-map')).toBe(undefined);
  });
});
