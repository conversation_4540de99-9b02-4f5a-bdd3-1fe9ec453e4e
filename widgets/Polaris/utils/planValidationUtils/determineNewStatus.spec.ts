import { determineNewStatus } from './determineNewStatus';
import { ValidationStatus } from '@common/types';

describe('determineNewStatus', () => {
  it('returns INVALIDATED for VALIDATED status', () => {
    expect(determineNewStatus(ValidationStatus.VALIDATED)).toEqual(ValidationStatus.INVALIDATED);
  });

  it('returns VALIDATED for INVALIDATED status', () => {
    expect(determineNewStatus(ValidationStatus.INVALIDATED)).toEqual(ValidationStatus.VALIDATED);
  });

  it('returns NOT_SET for NOT_SET status', () => {
    expect(determineNewStatus(ValidationStatus.NOT_SET)).toEqual(ValidationStatus.NOT_SET);
  });
});
