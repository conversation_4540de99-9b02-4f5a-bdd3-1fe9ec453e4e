import { TFunction } from 'i18next';
import { PlanValidation, ValidationStatus, MMMValidation } from '@common/types';

const isMMMValidation = (
  validation: PlanValidation | MMMValidation,
): validation is MMMValidation => {
  return 'configurationType' in validation && validation.configurationType !== undefined;
};

export const getUpdateDetails = (
  planValidation: PlanValidation | MMMValidation | undefined,
  validationStatus: ValidationStatus | undefined,
  formatDateString: (dateString: string | Date | undefined) => string | null,
  t: TFunction,
): string => {
  if (!planValidation) {
    return '--';
  }
  if (validationStatus === ValidationStatus.NOT_SET) {
    return t('polaris.sharedText.notValidatedYet');
  }

  const modifiedDate = planValidation.modified ? formatDateString(planValidation.modified) : null;

  if (modifiedDate) {
    const statusText = !isMMMValidation(planValidation)
      ? 'lastUpdateValidationStatus'
      : validationStatus === ValidationStatus.VALIDATED
      ? 'validatedStatus'
      : validationStatus === ValidationStatus.FAILED
      ? 'failedStatus'
      : 'invalidatedStatus';
    return t(`polaris.sharedText.${statusText}`, {
      modifiedBy: planValidation.modifiedBy,
      modifiedDate,
    });
  }

  return '--';
};
