import { getUpdateDetails } from './getUpdateDetails';
import { TFunction } from 'i18next';
import { PlanValidation, ValidationStatus } from '@common/types';

describe('getUpdateDetails', () => {
  const mockFormatDateString = jest.fn();
  const mockT: TFunction = jest.fn(
    (key: string, params: { modifiedBy?: string; modifiedDate?: string }) => {
      if (key === 'polaris.sharedText.notValidatedYet') {
        return 'Not set yet';
      }
      if (key === 'polaris.sharedText.lastUpdateValidationStatus') {
        return `Last update by ${params.modifiedBy} on ${params.modifiedDate}`;
      }
      if (key === 'polaris.sharedText.validatedStatus') {
        return `Validated by ${params.modifiedBy} on ${params.modifiedDate}`;
      }
      if (key === 'polaris.sharedText.invalidatedStatus') {
        return `Invalidated by ${params.modifiedBy} on ${params.modifiedDate}`;
      }
      return '';
    },
  ) as unknown as TFunction;

  const basePlanValidation: PlanValidation = {
    id: 'example-id',
    countryId: 'country-id',
    cropRegionId: 'region-id',
    localName: null,
    soilAnalysisStatus: ValidationStatus.NOT_SET,
    leafAnalysisStatus: ValidationStatus.NOT_SET,
    created: '2024-02-01T12:17:18.738Z',
    modified: '2024-02-29T12:05:32.875Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  };

  beforeEach(() => {
    mockFormatDateString.mockReset();
    // mockT.mockReset();
  });

  it('returns "--" if planValidation is undefined', () => {
    expect(
      getUpdateDetails(undefined, ValidationStatus.VALIDATED, mockFormatDateString, mockT),
    ).toEqual('--');
  });

  it('returns "Not set yet" if validationStatus is NOT_SET', () => {
    expect(
      getUpdateDetails({} as PlanValidation, ValidationStatus.NOT_SET, mockFormatDateString, mockT),
    ).toEqual('Not set yet');
  });

  it('returns the last update message if validationStatus is not NOT_SET and modifiedDate is provided', () => {
    const planValidation: PlanValidation = {
      id: '1',
      countryId: 'US',
      cropRegionId: 'North',
      localName: 'Sample',
      soilAnalysisStatus: ValidationStatus.VALIDATED,
      leafAnalysisStatus: ValidationStatus.VALIDATED,
      created: new Date(),
      modified: new Date(),
      modifiedBy: 'John Doe',
      deleted: false,
    };

    mockFormatDateString.mockReturnValue('2024-09-03');

    expect(
      getUpdateDetails(planValidation, ValidationStatus.VALIDATED, mockFormatDateString, mockT),
    ).toEqual('Last update by John Doe on 2024-09-03');
  });

  it('returns "--" if validationStatus is not NOT_SET but modifiedDate is not provided', () => {
    const planValidation: PlanValidation = {
      id: '1',
      countryId: 'US',
      cropRegionId: 'North',
      localName: 'Sample',
      soilAnalysisStatus: ValidationStatus.VALIDATED,
      leafAnalysisStatus: ValidationStatus.VALIDATED,
      created: new Date(),
      modified: '',
      modifiedBy: 'John Doe',
      deleted: false,
    };

    mockFormatDateString.mockReturnValue(null);

    expect(
      getUpdateDetails(planValidation, ValidationStatus.VALIDATED, mockFormatDateString, mockT),
    ).toEqual('--');
  });

  it('returns formatted date string when soilAnalysisStatus is VALIDATED', () => {
    const validatedPlanValidation = {
      ...basePlanValidation,
      soilAnalysisStatus: ValidationStatus.VALIDATED,
    };
    mockFormatDateString.mockReturnValue('29 Feb 2024');

    expect(
      getUpdateDetails(
        validatedPlanValidation,
        ValidationStatus.VALIDATED,
        mockFormatDateString,
        mockT,
      ),
    ).toEqual('Last <NAME_EMAIL> on 29 Feb 2024');
  });

  it('returns formatted date string when configurationType is VALIDATED', () => {
    const validatedPlanValidation = {
      ...basePlanValidation,
      configurationType: ValidationStatus.VALIDATED,
    };
    mockFormatDateString.mockReturnValue('29 Feb 2024');

    expect(
      getUpdateDetails(
        validatedPlanValidation,
        ValidationStatus.VALIDATED,
        mockFormatDateString,
        mockT,
      ),
    ).toEqual('<NAME_EMAIL> on 29 Feb 2024');
  });

  it('returns formatted date string when configurationType is INVALIDATED', () => {
    const validatedPlanValidation = {
      ...basePlanValidation,
      configurationType: ValidationStatus.INVALIDATED,
    };
    mockFormatDateString.mockReturnValue('29 Feb 2024');

    expect(
      getUpdateDetails(
        validatedPlanValidation,
        ValidationStatus.INVALIDATED,
        mockFormatDateString,
        mockT,
      ),
    ).toEqual('<NAME_EMAIL> on 29 Feb 2024');
  });

  it('returns "--" if modified date is invalid and ValidationStatus is set', () => {
    const invalidDatePlanValidation = {
      ...basePlanValidation,
      modified: 'invalid-date',
    };
    mockFormatDateString.mockReturnValue(null);

    expect(
      getUpdateDetails(
        invalidDatePlanValidation,
        ValidationStatus.INVALIDATED,
        mockFormatDateString,
        mockT,
      ),
    ).toEqual('--');
  });
});
