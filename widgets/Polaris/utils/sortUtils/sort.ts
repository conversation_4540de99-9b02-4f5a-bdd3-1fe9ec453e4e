import { cloneDeep } from 'lodash';

type ObjectMatchingProps<T> = {
  [K in keyof T]: string;
};

export function sortArrObjectsAlphabetically<T>(
  arr: ObjectMatchingProps<T>[],
  objectKey: keyof T,
): ObjectMatchingProps<T>[] {
  if (
    !arr ||
    !Array.isArray(arr) ||
    (arr.length && !Object.keys(arr[0]).length && objectKey && typeof objectKey !== 'string')
  ) {
    return [];
  }

  const deepClonedArr = cloneDeep(arr);
  return deepClonedArr.sort((a: ObjectMatchingProps<T>, b: ObjectMatchingProps<T>) =>
    a[objectKey].toLowerCase().localeCompare(b[objectKey].toLowerCase()),
  );
}
