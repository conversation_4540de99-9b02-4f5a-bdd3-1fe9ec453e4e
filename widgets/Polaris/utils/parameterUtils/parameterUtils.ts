import { MAX_LIMIT_NUMBER } from '@widgets/Polaris/src/screens/CropFeatures/shared/constants';
import {
  AnalysisActions,
  UpdateAnalysisActions,
} from '@widgets/Polaris/src/screens/CropFeatures/shared/NutritionParamsWithValuesTable/NutritionParamsWithValuesTable.type';
import { UpdateType } from '@widgets/Polaris/src/screens/CropFeatures/shared/NutritionParamsWithValuesTable/NutritionParamsWithValueTable.constants';

interface DateChangeData {
  startDate: Date | undefined;
  endDate?: Date | undefined;
}

// Sanitizes edited input value based on its original data type, removing leading zeros for numerical inputs.
export const sanitizeParameterValue = (
  defaultValueType: 'number' | 'string' | 'array' | string,
  editedValue: string,
): number | string | string[] => {
  if (defaultValueType === 'number') {
    return Number(editedValue.replace(/^0+/, '') || '0');
  } else if (defaultValueType === 'string') {
    return editedValue.replace(/^0+/, '') || '0';
  }

  return editedValue;
};

/**
 * Processes a date change event and triggers the appropriate action based on the presence of startDate.
 * @param data - Object containing startDate and optionally endDate.
 * @param onDateChange - Callback function to handle the date change.
 */
export function handleDateChange(
  data: DateChangeData,
  onDateChange: (date: Date | undefined) => void,
): void {
  if (data.startDate) {
    onDateChange(data.startDate);
  } else {
    onDateChange(undefined);
  }
}

export const validateParameterNumber = (value = ''): boolean => {
  return value === '' || !isNaN(Number(value));
};

export const truncateToThreeDecimals = (value: string) => {
  const decimalIndex = value.indexOf('.');
  if (decimalIndex === -1) return value;
  return value.substring(0, decimalIndex + 4);
};

export const formatValidTreeDecimalsNumber = (value: string) => {
  value = value.replace(/-/g, '');
  const decimalIndex = value.indexOf('.');
  if (decimalIndex > -1) value = value.substring(0, decimalIndex + 4);
  if (value.startsWith('0') && !value.startsWith('0.')) {
    return parseInt(value, 10).toString();
  }
  return value;
};

export const formatValidNegativeNumber = (value: string) => {
  const containsMinusNotAtStart = value.includes('-') && !value.startsWith('-');

  let newValue = value;
  if (containsMinusNotAtStart) {
    newValue = `-${newValue.replace('-', '')}`;
  }
  return newValue;
};

export const formatValidDecimalsNumber = (value: string, numOfDecimals: number) => {
  value = value.replace(/-/g, '');
  if (value.startsWith('0') && !value.startsWith('0.')) {
    return parseInt(value, 10).toString();
  }
  const decimalIndex = value.indexOf('.');
  if (decimalIndex === -1) return value;
  const truncateUntilIndex = numOfDecimals + 1;
  if (decimalIndex > -1) value = value.substring(0, decimalIndex + truncateUntilIndex);

  return value;
};

export const formatNegativeOrPositiveDecimalsNumber = (
  value: string,
  numOfDecimals: number,
): string => {
  const isNegative = value.includes('-');
  if (isNegative) {
    value = `-${value.replaceAll('-', '')}`;
  }

  if (value.startsWith('0') && !value.startsWith('0.') && value.length > 1) {
    value = parseInt(value, 10).toString();
  }
  const decimalIndex = value.indexOf('.');
  if (decimalIndex !== -1) {
    const truncateUntilIndex = decimalIndex + numOfDecimals + 1;
    value = value.substring(0, truncateUntilIndex);
  }

  return isNegative ? formatValidNegativeNumber(value) : value;
};

export const truncateToLimitedNumberOfDecimals = (value: string, numOfDecimals: number) => {
  const decimalIndex = value.indexOf('.');
  if (decimalIndex === -1) return value;
  const truncateUntilIndex = numOfDecimals + 1;
  return value.substring(0, decimalIndex + truncateUntilIndex);
};

export const createNumberDecimalChangeHandler =
  (dispatch: (action: AnalysisActions) => void) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (key: string, value: string, nutrient: any) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;

    value = formatValidTreeDecimalsNumber(value);

    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;

    nutrient[key] = value;

    dispatch({
      type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION,
      payload: nutrient,
    });
    dispatch({
      type: UpdateAnalysisActions.SET_UPDATE_TYPE,
      payload: UpdateType.OTHER,
    });
  };
