## [1.212.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.212.2...@mxp/polaris-v1.212.3) (2025-06-27)


### fix

* apply max value validation for nutrients only in splitting inputs ([2109a58](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2109a58477e0cb5694122d7ad8539d99b8a50672))
* change the function to fix redundancies ([8be6cc8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8be6cc83cec19e81d2c9b138d9af5db71733a21a))

## [1.212.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.212.1...@mxp/polaris-v1.212.2) (2025-06-26)


### fix

* add missing ac ([28842e4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/28842e423ef36de0c5070908599ddc4b3188a0e6))

## [1.212.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.212.0...@mxp/polaris-v1.212.1) (2025-06-18)


### fix

* bring back soil correction factor fix, fix split percent decimals ([9fd3c4e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9fd3c4ed7bbc3522ef883b11421b45a023041414))

# [1.212.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.211.0...@mxp/polaris-v1.212.0) (2025-06-18)


### chore

* remove console log leftovers ([48a16d1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/48a16d118e61c32c8504f5c6723cd0faba8b63b4))
* remove redundant checks, merge master ([e6ede34](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e6ede348e741e7ec5f177e641cdd7d44fb87cc05))


### feat

* refactor additional parameters popup and related helpers, types ([0c74b5f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0c74b5f3461ef4dd1c7583a4c9a826925e48c434))
* refactor admin reducers, related components and utils etc ([454b6fa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/454b6fa135ad0c6e93ec4d436c55b4f50d299a77))
* refactor crop features and related utils, types etc ([6fcdc73](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6fcdc73378f241080b9d722d4627365337e5c9c5))
* refactor demand calculations and related helpers, types ([1b87fab](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1b87fab6babf5d8720c20b628816e3f331610899))
* refactor general components and related helpers, types ([6e9609d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6e9609d486b455ed7f3ddd783e3810d0af7912bc))
* refactor product recommendations and related helpers, types ([d378d25](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d378d256c4aa2bbfdb585d4ac5d6400e3bbec29d))
* refactor type assertions in general mocks, providers, contexts ([3c1ca77](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3c1ca77ae24b07ab2ed586d13f26ebcbe43c8963))
* refator hooks ([ec57230](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ec57230ba50d0ad608e9b3e1e38e2e852fa4b0c5))
* use env var as is in azure provider ([0d881b0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0d881b04d9530b91c043a7afac669d50722d8f2a))
* YAPF-17136 FE | MMM | FE | Tech Debt | Configuration Page | Overlapping functionality for CNP, Fertigation and CMMM ([#524](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/524)) ([480f3ce](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/480f3ce2c99885309b9c5f579d7339c30086bc7d))


### fix

* remove another if statement ([dcb100c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/dcb100c98f8812a125472430b92f4dd157d25cb5))
* remove unnecessary if check ([fa97f06](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fa97f06c6df82740056d46926c96a59baafaf137))

# [1.211.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.210.5...@mxp/polaris-v1.211.0) (2025-06-18)


### chore

* remove mistake in soil correction factor collapsible ([83fb2e8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/83fb2e81e4f3d8120bd5edfc1f34d9553e5e6390))
* remove typo in soil correction factor collapsible ([41cd1d1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/41cd1d1d804aaa5048d5046e9aae5d98471af6dc))
* revert mistake ([de7cbb0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/de7cbb060c30a287a028d63081df8bcfdb4d7a02))


### feat

* fix total percent calculation ([5eb59af](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5eb59af20c339a3faed9dd1b17c128d0e038d0bd))
* make inputs in splitting schedule allow for up to three decimal places ([694ffcb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/694ffcb0e28e7998093d5f56fa37fe87674870b5))

## [1.210.5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.210.4...@mxp/polaris-v1.210.5) (2025-06-16)


### fix

* remove number conversion for multiplierValue, add type ([ba37fcb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ba37fcba418e05b8f4d7779e9b119df87ad604ab))

## [1.210.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.210.3...@mxp/polaris-v1.210.4) (2025-06-09)


### fix

* **YAPF-17138:** nutrition params with values table ([#525](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/525)) ([5325bb2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5325bb2854159bd69544eaf53d3192984cc6c907))

## [1.210.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.210.2...@mxp/polaris-v1.210.3) (2025-06-04)


### refactor

* **YAPF-17138:** demand calculation module ([#522](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/522)) ([d8d0526](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d8d0526956a10be278036a62358c137efe60bbab))

## [1.210.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.210.1...@mxp/polaris-v1.210.2) (2025-05-30)


### fix

* **YAPF-17099:** display growth scale in CMMM ([#521](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/521)) ([cb938e9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cb938e9322880f20c55815187faacb895bb0866d))


### refactor

* **yapf -16782:** Naming and folder structure part 2 ([#518](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/518)) ([9b567b9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9b567b9ae31805c1f709603264c9d2c035a5dbea)), closes [#516](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/516) [#519](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/519)

## [1.210.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.210.0...@mxp/polaris-v1.210.1) (2025-05-27)

### fix

- **YAPF-16363:** multiple fetch calls ([#519](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/519)) ([49effa7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/49effa7a635c34c0d5a88a0513f9cacbd54e4039))

# [1.210.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.209.3...@mxp/polaris-v1.210.0) (2025-05-21)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-polaris 1.0.1 [skip ci] ([4353f23](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4353f23a3c2920cce2175205ac8d8609a3666544)), closes [#516](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/516)

### feat

- change population density to have value and unit id, add validation and conversion ([4d6b9d9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4d6b9d90c6eaa59001d33c1cc867b86ed27db654))
- rebase master, change const name ([abe4e71](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/abe4e71aa4c79dc270d0ef131ee3ae729a5fb65f))

## [1.209.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.209.2...@mxp/polaris-v1.209.3) (2025-05-20)

### refactor

- **yapf-16782:** naming and folder structure ([#514](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/514)) ([f262dc9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f262dc9cb2152c4c3d84974cf008fe9ae17cffe7)), closes [#516](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/516)

## [1.209.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.209.1...@mxp/polaris-v1.209.2) (2025-05-19)

### fix

- **YAPF-16363:** add back deleted hook ([#515](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/515)) ([98bcb7d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/98bcb7d48fdd468afe86abbee8a9731a2645a4b7))

## [1.209.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.209.0...@mxp/polaris-v1.209.1) (2025-05-13)

### fix

- **YAPF-16450:** validation description ([#513](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/513)) ([bd3411f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bd3411f77b54de10b53813ad4e63c341bbea0772))

# [1.209.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.208.1...@mxp/polaris-v1.209.0) (2025-05-12)

### feat

- YAPF-15129: update on parameter level update limits ([#512](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/512)) ([9582a40](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9582a4028a54685820d73235609c3f7a4d368af5))

## [1.208.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.208.0...@mxp/polaris-v1.208.1) (2025-05-12)

### fix

- reset mmm validation when nav to the Home page ([#511](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/511)) ([2ba17f2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2ba17f299ab787a90d9633f4b4b1087414ab296a))

# [1.208.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.207.1...@mxp/polaris-v1.208.0) (2025-05-09)

### feat

- update param add dialog upper limit accessability ([#510](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/510)) ([6fe2736](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6fe273623471d33484e2fb6cfdbfe318fb4b804d))

## [1.207.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.207.0...@mxp/polaris-v1.207.1) (2025-05-08)

### refactor

- **YAPF-16363:** use filter instead of multiple calls hook ([#506](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/506)) ([14c0ab6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/14c0ab6edd416fe0b0ba42c104a3d133cee82576))

# [1.207.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.206.0...@mxp/polaris-v1.207.0) (2025-05-08)

### feat

- **YAPF-16450:** enable validate switch function ([#501](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/501)) ([801a9a8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/801a9a8c16125e41654acf5faf40461e2ca9107e))

# [1.206.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.205.0...@mxp/polaris-v1.206.0) (2025-05-07)

### feat

- YAPF-15129: post qa bug fixing ([#509](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/509)) ([17a5954](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/17a595465e27f1640d2e93ba6060f81887452c08))

# [1.205.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.204.1...@mxp/polaris-v1.205.0) (2025-04-30)

### feat

- **YAPF-15129:** parameter level inputs improvement ([#505](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/505)) ([77967f1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/77967f171e8e299ba81ffdef96a4d96b35f7483c))

## [1.204.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.204.0...@mxp/polaris-v1.204.1) (2025-04-28)

### refactor

- **YAPF-16913:** use separator instead of styles ([#507](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/507)) ([6b76b97](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6b76b979df84808664372732c7be981d8b588f38))

# [1.204.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.203.1...@mxp/polaris-v1.204.0) (2025-04-25)

### feat

- add unit settings state update in product recommendation ([#504](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/504)) ([a3a21be](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a3a21be13f431037876e8a06b483cc8e0779cda4))

## [1.203.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.203.0...@mxp/polaris-v1.203.1) (2025-04-24)

### refactor

- delete unused var import in test ([#503](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/503)) ([0939e8c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0939e8c34abaf2313e38e705d751f2875728d76f))

# [1.203.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.202.1...@mxp/polaris-v1.203.0) (2025-04-23)

### feat

- adjusting state update in cereal ([#502](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/502)) ([7ca6d22](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7ca6d22a7b9a7e4d32ddccf7ec6679493f17ce4a))

## [1.202.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.202.0...@mxp/polaris-v1.202.1) (2025-04-23)

### fix

- send correct nutrient form ids when creating new splits ([#500](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/500)) ([0672d02](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0672d0261bd33d4fb6b8d3ac020b979271ab5b2d))
- YAPF-15858 ui improvements change and add icon ([#499](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/499)) ([3cd1f13](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3cd1f13044d4a02d5c29ac96e9120d0463aaf521))

# [1.202.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.201.0...@mxp/polaris-v1.202.0) (2025-04-17)

### feat

- YAPF-16358: units api calls and usage optimisation ([#493](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/493)) ([4ff0386](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4ff0386a0a5ad80f5a4cb8a7d3388228dd6356cd))

# [1.201.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.200.0...@mxp/polaris-v1.201.0) (2025-04-17)

### feat

- YAPF-15797: caption tooltip in fertigation ([#497](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/497)) ([c3a45cf](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c3a45cf3e6d92760b4e9749785feaa6f3673862c))

# [1.200.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.199.0...@mxp/polaris-v1.200.0) (2025-04-17)

### feat

- add display text value for lowerThan max value ([6ef2ff5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6ef2ff5b283d4250800f58ebeed7c2823df968c7))
- add mock handlers, change and add to required tests ([ae4e166](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ae4e166ccc7557100c17382699fc2b31944170f3))
- add validation helper funcs and use them display table, add parameter level value change logic ([414118c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/414118cb2e40fa3eb504a658354c6dbe118bc2de))
- change type, add helper functions, change parameter level cell onChange ([407a6b5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/407a6b58e154dd1441c8cb26dfda385fbe860e0a))
- change types, add logic to disable input in add popup and set value automatically ([7fb1ecf](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7fb1ecfff136d0ed8df87c66640c175e8bdb2285))
- update snapshots ([0155408](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0155408aa159eedb8b74883952782568885cb869))
- update validation check ([6547632](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6547632646630a1fab364fa7b7e7d26b1c2d7e06))

# [1.199.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.198.0...@mxp/polaris-v1.199.0) (2025-04-16)

### feat

- **YAPF-15826:** make nutrient form updatable ([#487](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/487)) ([9d9a351](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9d9a3514c96714a456cbbdce716a6e703c8cdf9e))

# [1.198.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.197.0...@mxp/polaris-v1.198.0) (2025-04-10)

### feat

- validation status bug fixes ([#496](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/496)) ([7199bbc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7199bbc253d177f6bd79580eb86f8a78c8d66268))

### fix

- default value in crop settings dialog' ([#494](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/494)) ([e574a0a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e574a0a334bbe9053148d8e47fae511a9daa184e))

# [1.197.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.196.1...@mxp/polaris-v1.197.0) (2025-04-09)

### feat

- YAPF-15797: add empty and error state in fertigation ([#490](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/490)) ([477d8e8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/477d8e8ecd8db354caccf50b9146d97c760ee65d))

## [1.196.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.196.0...@mxp/polaris-v1.196.1) (2025-04-07)

### fix

- YAPF-13306 FE | CMMM | Nitrogen Splitting | validation rules ([#492](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/492)) ([531bc59](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/531bc5900de2057bd273eec5ccb3020b714e29d1))

# [1.196.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.195.1...@mxp/polaris-v1.196.0) (2025-04-07)

### feat

- YAPF-16473-read-partners-tag-with-user ms ([#489](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/489)) ([5a2d5fa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5a2d5faa484427e2f06babcaab926c89c808cb88))

## [1.195.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.195.0...@mxp/polaris-v1.195.1) (2025-04-04)

### fix

- YAPF-13306 FE | CMMM | Nitrogen Splitting | validation rules ([#488](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/488)) ([7dd4101](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7dd41014f6f4360715876c1fef64fbbbacf77165))

# [1.195.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.194.1...@mxp/polaris-v1.195.0) (2025-04-04)

### feat

- YAPF-16466 CreateFeature userms integration ([#484](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/484)) ([b9e1796](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b9e17966a3b3121815c503cedeef5eb1ed052afc))

## [1.194.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.194.0...@mxp/polaris-v1.194.1) (2025-04-03)

### fix

- delete request, styles ([#485](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/485)) ([eb07008](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/eb0700879d86479f8298342066c5cebb73a5bf98))

# [1.194.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.193.0...@mxp/polaris-v1.194.0) (2025-04-02)

### feat

- YAPF-16467 ReadFeature userms integration ([#480](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/480)) ([e558406](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e5584069b5f9cf95308085743a93fdcb0fec3a16))

# [1.193.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.192.2...@mxp/polaris-v1.193.0) (2025-04-01)

### feat

- add crop split type ([fa80ece](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fa80ece63a6e31398eaa97aaf9fcdf3c0b7a46f7))
- add fetch crop splits hook ([5c69138](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5c69138295679f4cce42ac9759885d990ee9add6))

### fix

- use new can modify response object ([39e66a2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/39e66a2c3ac4203d3dce2b1aacf22a3519a8c36b))

## [1.192.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.192.1...@mxp/polaris-v1.192.2) (2025-04-01)

### fix

- translations ([#483](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/483)) ([cdc060c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cdc060cd80084e3d326a464958e25762d4f2ad2c))

## [1.192.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.192.0...@mxp/polaris-v1.192.1) (2025-03-28)

### fix

- input fields ([#482](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/482)) ([280c4d1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/280c4d1d11df3823d07179eb617f2c1a373652c2))

# [1.192.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.191.0...@mxp/polaris-v1.192.0) (2025-03-28)

### feat

- YAPF-14352-edit-partner ([#477](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/477)) ([4e9a6ec](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4e9a6ec525bb120a37c0666be63e70aaa1804270))

# [1.191.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.190.0...@mxp/polaris-v1.191.0) (2025-03-27)

### feat

- YAPF-13306 FE | CMMM | Nitrogen Splitting | validation rules ([#478](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/478)) ([064c79d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/064c79d3e2c8bbfea2e464dac7f009d5d193ddf0))

# [1.190.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.189.1...@mxp/polaris-v1.190.0) (2025-03-27)

### feat

- **yapf-13780:** add products ([#474](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/474)) ([f975bf1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f975bf1c9634bad3a32e1ef656c708d173bf7f86))

## [1.189.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.189.0...@mxp/polaris-v1.189.1) (2025-03-26)

### fix

- remove unnecessary cnp validations call, change checks to mmm validations ([93b637b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/93b637bf70ea865382b4da8ed6a42cbf3459c8f7))

# [1.189.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.188.2...@mxp/polaris-v1.189.0) (2025-03-26)

### feat

- input validations ([#479](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/479)) ([22eb78d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/22eb78dd14e0c5c74ae36d8257e37797548a3f2e))

## [1.188.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.188.1...@mxp/polaris-v1.188.2) (2025-03-25)

### fix

- YAPF-14925: Fertigation Plan: settings management adjustments ([#473](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/473)) ([7f82672](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7f8267278d32d8536b270e2c61e06e49c5d44689))

## [1.188.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.188.0...@mxp/polaris-v1.188.1) (2025-03-25)

### fix

- **YAPF-16587:** display nutrient forms by full tag ([#476](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/476)) ([0cd43ee](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0cd43ee63e3df1e2d219c647c979e71d0585d3fe))

# [1.188.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.187.1...@mxp/polaris-v1.188.0) (2025-03-21)

### feat

- YAPF-16470 update fetcher hook for UserMS integration ([#471](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/471)) ([41fd41b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/41fd41b722410ade5e6a9a5b7cc5db58165f9a97))

## [1.187.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.187.0...@mxp/polaris-v1.187.1) (2025-03-21)

### fix

- add empty cells for splits with less than 4 items ([acccaf4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/acccaf426b06b74caa1c2cca434bbd27546a790c))

# [1.187.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.186.0...@mxp/polaris-v1.187.0) (2025-03-20)

### feat

- **YAPF-11737:** cmmm product recommendations ([#467](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/467)) ([9fce0e2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9fce0e2004dd89ab93555321bd3fc6cf0a0fc440))

# [1.186.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.185.0...@mxp/polaris-v1.186.0) (2025-03-20)

### feat

- YAPF-15961: add failed validation dialog on fertigation plan switch ([#469](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/469)) ([7febdd7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7febdd74248023f0741ed92532d2c95e6e142c44))

# [1.185.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.184.0...@mxp/polaris-v1.185.0) (2025-03-20)

### feat

- adjust growth scale stages, add tests, changes to types ([e736a3b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e736a3b91c43a3d33b03451846acf4f2566483fa))
- renaming of vegetative demand unit in fertigation ([#470](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/470)) ([7197a02](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7197a02fabe20099e3c20282d0f471842e9516f1))

### fix

- fix input to reset to zero when deleting last number, fix bug with validations, fix naming ([37ee5df](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/37ee5df6f7c734730a8478d948515a5c33f85511))
- namings, remove hardcoded stage numbers arr, use from data configuration ([ab7dc95](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ab7dc952714c10568753c698159084c1efd096b6))

# [1.184.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.183.0...@mxp/polaris-v1.184.0) (2025-03-20)

### feat

- YAPF-15801: re-enable unit selection in fertigation analysis ([#465](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/465)) ([0307da5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0307da531b3a041d44266c019d276d183f522263))

# [1.183.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.182.0...@mxp/polaris-v1.183.0) (2025-03-20)

### feat

- YAPF-14349 Delete Feature ([#466](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/466)) ([c4e4d0c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c4e4d0c915f487170933953438ba963e3d98952d))

# [1.182.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.181.0...@mxp/polaris-v1.182.0) (2025-03-17)

### feat

- YAPF-14925: update dynamic units display in fertigation ([#448](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/448)) ([3281401](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/32814019841bdb94f9c95293ff9840d619c8b300))
- YAPF-16085-sorting-fix ([#464](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/464)) ([b655155](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b6551553d6a81b225c385edb1c4d7f1164bf7091))

# [1.181.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.180.0...@mxp/polaris-v1.181.0) (2025-03-17)

### feat

- YAPF-15569 feature tag search and sort improvements ([#462](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/462)) ([182bcab](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/182bcab6a2cf3016ea295de1a966356bee15846c))

# [1.180.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.179.0...@mxp/polaris-v1.180.0) (2025-03-14)

### chore

- remove console logs ([faafbab](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/faafbaba8dd9dc26e911c17f3dd8e7861890de4d))

### feat

- add and change tests ([820b0c6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/820b0c66081fec5370ddde970376fe1a227a6f9c))
- add hook call, changes to body, reducer, utils ([e859e95](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e859e95691fc3b8fae6a86c91f1475fa94f06197))
- change and add types, hooks ([ebf03e1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ebf03e164c9bae75cf243ad04163751bbdfd33a1))

### fix

- fix incorrect field validations ([94f31f4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/94f31f411e26d3ec8e6676d12084693de646e8c7))

# [1.179.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.178.0...@mxp/polaris-v1.179.0) (2025-03-14)

### feat

- YAPF-14347-add-feature-tags-form ([#457](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/457)) ([38fa88b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/38fa88b4819ce7a5623f96f5f4ecb8191ed48fd9))

# [1.178.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.177.2...@mxp/polaris-v1.178.0) (2025-03-13)

### feat

- update fertigation unit tags usage ([#461](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/461)) ([f95503b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f95503b3f20924e2ceef9985ceb202834481b624))

## [1.177.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.177.1...@mxp/polaris-v1.177.2) (2025-03-13)

### fix

- YAPF-16323 [CMMM][Demand Calculation] Incorrect nutrient form displayed for S ([#460](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/460)) ([7f43713](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7f437136dd11246f37bcbd2440dca495d68fa769))

## [1.177.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.177.0...@mxp/polaris-v1.177.1) (2025-03-12)

### fix

- fix snapshot ([63ed19a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/63ed19a713a4898ac7479c9530e196cb734c4d50))
- fix state so text and values are populated on initial load ([2c344d5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2c344d57522ab8fb38541851120b73f5c32a6b9e))

# [1.177.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.176.1...@mxp/polaris-v1.177.0) (2025-03-10)

### feat

- **YAPF-16078:** update rate defining nutrients ([#455](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/455)) ([5b5d29a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5b5d29a6210e7e99ce526d7dad881b87a943f65c))

## [1.176.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.176.0...@mxp/polaris-v1.176.1) (2025-03-10)

### fix

- unit ids and validation ([#458](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/458)) ([8b38f1e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8b38f1edf6f796833eefa202940eea70e347d87f))

# [1.176.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.175.1...@mxp/polaris-v1.176.0) (2025-03-10)

### feat

- YAPF-14348 Edit Feature Tag ([#456](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/456)) ([0da9a06](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0da9a06c06af7c40b7c203d101c2dabbba0d5f54))

## [1.175.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.175.0...@mxp/polaris-v1.175.1) (2025-03-10)

### fix

- **YAPF-11724:** styles, descriptions and residual units ([#454](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/454)) ([ec85bdd](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ec85bdd34578e9322428d61dc0ca0e80a163987d))

# [1.175.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.174.0...@mxp/polaris-v1.175.0) (2025-03-06)

### feat

- **YAPF-15500:** add filter by feature tag ([#452](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/452)) ([3dbd56d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3dbd56d947f2b07b286f255eab32115ae7b2667f))

# [1.174.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.173.0...@mxp/polaris-v1.174.0) (2025-03-06)

### feat

- add tests, mocks, handlers, update snapshots ([9e42b07](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9e42b0796faf992fbc9d683bf0d7a035327805c4))
- add types, tranlsations and spreader method hook and types ([81bfb75](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/81bfb75aaa64fee324a48b716d93aba0cf76f745))
- create component, styles, reducer, utils ([a152707](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a15270730570382880d019d759386529f7bf9de5))
- fix namings in useSpreaderMethods ([919b16c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/919b16c5bc88a44e42b464a8711371f44125a7e5))

### fix

- run yarn install, update snapshots ([ad2b84d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ad2b84dce0aef280f7d444746398b4d9c2a6e8d9))

# [1.173.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.172.0...@mxp/polaris-v1.173.0) (2025-03-05)

### feat

- YAPF-15824: expand fertigation settings ([#445](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/445)) ([84556a1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/84556a12b392ee2d2fc3c2fdb3108fc76f6022cd))

# [1.172.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.171.0...@mxp/polaris-v1.172.0) (2025-03-05)

### feat

- YAPF-15930: adjust fertigation splitting start date ([#451](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/451)) ([6f44bb8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6f44bb8982f6de361ba4814f44554f2ab6640a50))

### fix

- YAPF-14346 feature partner page css alignments ([#453](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/453)) ([fb4547a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fb4547ad66af8ac6647dcc8dd77a12c1eac37c30))

# [1.171.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.170.0...@mxp/polaris-v1.171.0) (2025-03-05)

### feat

- **YAPF-11724:** add pre crop module ([#447](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/447)) ([bf06312](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bf06312b8f79d1b15679e4bf7b42422d116788eb))

# [1.170.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.169.0...@mxp/polaris-v1.170.0) (2025-02-28)

### feat

- YAPF-15640: integrate fertigation crop settings handling ([#439](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/439)) ([cbae0d5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cbae0d5218ba790798dc436a8f1164e2ecff3161))

# [1.169.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.168.0...@mxp/polaris-v1.169.0) (2025-02-28)

### feat

- YAPF-14353 Delete partner tag ([#443](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/443)) ([a120541](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a1205410a20b79e575d2b90792c408dcf93ed63e))

# [1.168.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.167.0...@mxp/polaris-v1.168.0) (2025-02-28)

### feat

- search-fix ([#446](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/446)) ([e3958a4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e3958a416cfaa9ff3a77fdef0180d1cf667df5af))

# [1.167.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.166.2...@mxp/polaris-v1.167.0) (2025-02-27)

### chore

- remove new line ([ba5e526](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ba5e52613ad3bc9aa59d3ce6011ff6121d869bc1))

### feat

- YAPF-15570-search-sort-and-pagination ([#441](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/441)) ([4fba7de](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4fba7de9801700d240f60e91ff52da322a454e7f))

### fix

- fix negative input not able to delete last digit and correctly reset to 0 ([1828ed4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1828ed4cf55dd7154934840ab6328dd142502959))

## [1.166.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.166.1...@mxp/polaris-v1.166.2) (2025-02-24)

### chore

- change names in update func ([8d5e96b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8d5e96bef364cc77ac44380bc97bd789d4be2f33))

### fix

- fix input race condition problem on quick update of both, fix negative number backspace delete ([6cf6850](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6cf685088aacc5dd4e210c77ade530b13743e181))

## [1.166.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.166.0...@mxp/polaris-v1.166.1) (2025-02-21)

### fix

- change styling ([af179eb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/af179eb214624b9877b3ebb2e6602bc2d2483d83))
- fix number validation for p availability correction, update snapshot ([94e2065](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/94e20658217af68c73e258438994799c47fdcaed))
- fix styling, fix being able to input minus between numbers inside input ([5993b52](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5993b5267c1865aa2e6272c4e6587951099ef4eb))

# [1.166.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.165.0...@mxp/polaris-v1.166.0) (2025-02-21)

### feat

- **yapf-15930:** update fertigation splitting start date ([#436](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/436)) ([328f951](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/328f9516521b2312321c7f8ba41e3078882b18a3))

# [1.165.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.164.0...@mxp/polaris-v1.165.0) (2025-02-20)

### chore

- change format negative number related util methods, change according to comment ([0094b66](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0094b66cd4824d726a291661d9246e9c1e813e18))

### feat

- add p availability correction component, utils, types, tests ([7c9dd05](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7c9dd05d16c862b975812d84b36e57886de4659e))
- change constants, change organic fertiliser types, fix related type problems ([acb923d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/acb923d6d20d4e603b1e52745a66bf975827022e))

### fix

- fix bugs with negative number validation, fix font weigth for headers, merge master ([a44f8b7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a44f8b750b19a2c54c170b679b1261826bb12c72))
- fix util functions, change enums to pascal case, fix according to comments ([a58fdd4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a58fdd405ff67c705595acb467920fc955c0ff6e))
- remove unnecessary switch function to get typed objects, fix code in organic fertiliser modules ([4111172](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/411117277977a3aaaebdd09f14ebb73d38285601))

# [1.164.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.163.0...@mxp/polaris-v1.164.0) (2025-02-19)

### feat

- **YAPF-14351:** Add new partner tag ([#434](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/434)) ([3fbca1c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3fbca1c17daa07d676ddf22c149068f02f4438e5))

# [1.163.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.162.2...@mxp/polaris-v1.163.0) (2025-02-19)

### feat

- **YAPF-14350:** partners-list-screen ([#432](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/432)) ([d317181](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d317181ef82e1672e06e0b4bbc097ed1c6da93e1))

## [1.162.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.162.1...@mxp/polaris-v1.162.2) (2025-02-14)

### fix

- status issue ([#433](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/433)) ([8c34b74](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8c34b74dd7ee11251ce4d615cd5f14d5928d2ba9))

## [1.162.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.162.0...@mxp/polaris-v1.162.1) (2025-02-13)

### refactor

- **YAPF-15840:** refactoring fertigation water analysis ([#424](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/424)) ([4884841](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/48848415858ca7f2e580d545e9617bffa2d363d6))

# [1.162.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.161.0...@mxp/polaris-v1.162.0) (2025-02-13)

### feat

- **YAPF-14346:** features list screen ([#430](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/430)) ([da60f66](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/da60f66046c6c1a4034666cfeda699e9e8571bc0))

# [1.161.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.160.2...@mxp/polaris-v1.161.0) (2025-02-12)

### feat

- **YAPF-11771:** CMMM soil correction factor ([#425](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/425)) ([457b142](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/457b1423f5120f28814a44b45197c274eb1a5a68))

## [1.160.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.160.1...@mxp/polaris-v1.160.2) (2025-02-11)

### fix

- add constant, use it to fix units showing correctly on labels ([2c9c7ac](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2c9c7ac1b1f524a40691497842bd59d98282e9ea))
- add test, add comment to existing test, make marketregulations state safer ([8293ead](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8293eadf12f84e2c89878418ce3c7138b4c05c89))

## [1.160.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.160.0...@mxp/polaris-v1.160.1) (2025-02-11)

### fix

- fix component rendering, show empty state when data has not been loaded yet ([a5c328f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a5c328fd291429e7d77df4a2047a9b7572cc93d7))
- fix tests, remove console logs ([a3d8619](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a3d8619a0f34db6f50b65111d40fe445b2eff12b))
- update hooks, update mock handlers, update tests and snapshot ([1c9fe0a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1c9fe0ad7c25f2379838294a650b785d91136867))

# [1.160.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.159.0...@mxp/polaris-v1.160.0) (2025-02-11)

### feat

- **YAPF-15326:** use APIs via Mule gateway ([#422](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/422)) ([775a251](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/775a25153ded079ed91fbf22da5f259805ae0894))

# [1.159.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.158.0...@mxp/polaris-v1.159.0) (2025-02-10)

### feat

- add market regulations component, utils, translations ([7cb5c2f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7cb5c2f338eacdd4c50617b40fb29d5ed203428b))
- add more tests ([19eca9e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/19eca9e3be48eec73fcb9beb37c72d3ffe09d5ba))
- add more tests ([4380e9a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4380e9aef9da8831b44d1d07da80c06700f4d0fe))
- add some more tests and change existing ones ([9a6b0da](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9a6b0da42a70d7978da0a8644c2629ac14b2f761))
- add tests ([46eb154](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/46eb154865dea668c4e5d78d73a01eacc591c5a9))
- add tests ([efea194](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/efea194e324a8bde1703c312339ed43d59675789))
- add tests, fix typo in collapsile comp, ([2d8a96b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2d8a96b5366278b198800d845eb15170955bb6f0))
- add to tests, change products available table to have some cells stretch when page is wider ([a995cee](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a995cee1e7e7d8fcb556e28eb9fed04a3496da96))
- remove comment from soil types test ([28b51fd](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/28b51fd0a97e13307207b86cc75de69fdd3dff4b))
- remove test ([56dd55a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/56dd55a71a73f55dfdf05fdbdc1722e15ccf19d3))
- update mock handlers, update market regulations tests ([3101d1f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3101d1f346df837388b30cdcab5e079f06171e37))

# [1.158.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.157.0...@mxp/polaris-v1.158.0) (2025-02-05)

### feat

- add curly braces ([24d5c09](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/24d5c094122453557a2ce1ff95b1cadc0d181d7e))
- add hooks, changes to handlers and hooks,collapsible header comp ([b8954b0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b8954b01139f10435b5390b5ea3186b73fcada12))
- add soil types component, table body, utils etc ([3da5cdf](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3da5cdf65a761cd5f96d815ee556f637e25c1c8e))
- add types, constants, mock data ([1e85efe](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1e85efed414dc0cc4b2919095773bf9c91761bee))
- change namings in hook, get configurations in organic fertiliser component ([9a0f771](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9a0f7717cedc4447f92e8e1521e879232a220104))
- change tests ([ff07d4d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ff07d4d41569e2500365fb2cf804b3500268464b))
- change types, remove unnecessary util func, change enum namings ([dd87441](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/dd87441693871db9175dc6b83d3a85a2ae3d7ee2))
- fixes from desk check, reorder list, no refresh on udpate on page 2,3, stylings ([4221897](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4221897fe144d149380dc272ab2a7a154db828db))
- minor change to soil types component, add tests ([c356003](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c356003b35aca76fda214bc897eed8daf33ebbad))

# [1.157.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.156.2...@mxp/polaris-v1.157.0) (2025-02-04)

### feat

- **YAPF-12953:** cmmm som and soil mineral modules ([#421](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/421)) ([1449a95](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1449a9582e8ab27acba278f7e035649ac60beb3c))

## [1.156.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.156.1...@mxp/polaris-v1.156.2) (2025-02-03)

### test

- **YAPF-12659:** Tests for Demand Calculation and its Modules ([#419](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/419)) ([d5c2d5c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d5c2d5c78ed526d7045701e88cd210a5eeca40dd))

## [1.156.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.156.0...@mxp/polaris-v1.156.1) (2025-02-03)

### fix

- **yapf-14532:** fertigation - removing unnecessary calls on soil analysis method delete ([#420](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/420)) ([bc5d3fb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bc5d3fb77383f41da981a8560ba12146d605b28f))

# [1.156.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.155.1...@mxp/polaris-v1.156.0) (2025-01-28)

### feat

- **YAPF-13395:** configuration, soil analysis, organic fertiliser page UI issues ([#406](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/406)) ([4bcac58](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4bcac58430ad9369af07c0d161f41839a28f3b41))

## [1.155.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.155.0...@mxp/polaris-v1.155.1) (2025-01-24)

### fix

- add reference to sorted nutrients ([#414](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/414)) ([29e8802](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/29e8802d162dd96dcd457b1e43f408e29f5df6c5))
- selected stage in the add image dialog ([#413](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/413)) ([da32644](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/da32644621ed926c08524abe5ab89e2daa958fb1))

# [1.155.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.154.0...@mxp/polaris-v1.155.0) (2025-01-22)

### feat

- **YAPF-11769:** cmmm nutrient use efficiency ([#410](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/410)) ([0652682](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/065268280c327e2c2ec828d592f4af5f06d4e8cc))

# [1.154.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.153.2...@mxp/polaris-v1.154.0) (2025-01-22)

### feat

- **YAPF-13836:** growth phase images ([#409](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/409)) ([4850c98](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4850c980bfec4645b9c99a8038cd591d976e4bf1))

## [1.153.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.153.1...@mxp/polaris-v1.153.2) (2025-01-21)

### fix

- fertigation: adjusting product chip name ([#412](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/412)) ([5774b69](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5774b699ba518a76127d44cd97af117bb70ebc83))

## [1.153.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.153.0...@mxp/polaris-v1.153.1) (2025-01-21)

### fix

- fertigation: switching used product name property in product recommendation ([#411](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/411)) ([1d715a9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1d715a95560c3f81f5ec78fc59e9e8623df5450c))

# [1.153.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.152.0...@mxp/polaris-v1.153.0) (2025-01-20)

### feat

- **YAPF-11723:** cmmm crop demand ([#408](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/408)) ([244cc17](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/244cc17001c5534e68f65488cdfa612e4d40cf22))

# [1.152.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.151.0...@mxp/polaris-v1.152.0) (2025-01-20)

### feat

- YAPF-13557 FE | CMMM | Organic Fertiliser | Products Table titles update ([#403](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/403)) ([907a0ae](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/907a0aee662ea6e05657659da1b84ab13751f2ea))

# [1.151.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.150.0...@mxp/polaris-v1.151.0) (2025-01-17)

### feat

- prevent up and down arrow changing value on input in fp edit settings ([7d90fd0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7d90fd08a8878e32e49dea6d14604cba59aeb18e))

# [1.150.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.149.0...@mxp/polaris-v1.150.0) (2025-01-17)

### feat

- fix styles in fertigation plan home page as pointed out in docs ([890440f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/890440f8b386d7bf8e2d193982ca1ef98966e9c2))

# [1.149.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.148.0...@mxp/polaris-v1.149.0) (2025-01-17)

### feat

- add on close handler to reset default yield for cmmm and fertigation ([38463ef](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/38463ef1e9126598e134ff6b1a6a6142db7b5f91))

# [1.148.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.147.1...@mxp/polaris-v1.148.0) (2025-01-16)

### feat

- **YAPF-13736:** preserving state with URLs ([#369](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/369)) ([b20ada5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b20ada52dce76fe4a6815f5366bf3d2281a877dd))

## [1.147.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.147.0...@mxp/polaris-v1.147.1) (2025-01-13)

### fix

- remove megalab code from polaris ([#398](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/398)) ([b450ff4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b450ff4cb0323259c0b430c5c425ce11183acbeb))

# [1.147.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.146.0...@mxp/polaris-v1.147.0) (2025-01-09)

### feat

- YAPF-15253 FE | CMMM | Soil Analysis | Add analysis method | Analysis Unit ([#402](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/402)) ([976685d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/976685d9024c9a6d0a45a7e6a777a0f18bf6f35e))

# [1.146.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.145.4...@mxp/polaris-v1.146.0) (2025-01-09)

### feat

- YAPF-15262 FE | CMMM | Soil Analysis | Soil Analysis Unit ([#401](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/401)) ([05959e2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/05959e2154a2f37414f7fb1546f4558b50aa4a30))

## [1.145.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.145.3...@mxp/polaris-v1.145.4) (2025-01-08)

### fix

- **YAPF-12671:** nutrients order in the combobox input and styles ([#400](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/400)) ([8390d7e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8390d7ee04df3cb42b46b37eefeb0dce1d517d08))

## [1.145.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.145.2...@mxp/polaris-v1.145.3) (2025-01-07)

### fix

- **yapf-14504:** adjusting faulty behavior in fertigation default target yield update ([#399](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/399)) ([cb886f7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cb886f7ee59242d3408d2924f981919e0b9be3af))

## [1.145.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.145.1...@mxp/polaris-v1.145.2) (2024-12-23)

### fix

- **yapf-000:** fixing state update in soil correction ([#397](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/397)) ([41ef5fc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/41ef5fc321d032b0961f459a8c49cc6ef2ffc285))

## [1.145.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.145.0...@mxp/polaris-v1.145.1) (2024-12-20)

### fix

- **YAPF-12671:** small style fixes ([#396](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/396)) ([8800863](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8800863fe0b1cbe89a8f17485747a385f7be0250))

# [1.145.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.144.3...@mxp/polaris-v1.145.0) (2024-12-19)

### feat

- **YAPF-15224:** extends nutrient removal decimal places in fertigation configuration ([#395](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/395)) ([3d3b285](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3d3b2856366ca77c6d9b459ddfa9ed832218677c))

## [1.144.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.144.2...@mxp/polaris-v1.144.3) (2024-12-17)

### fix

- **YAPF-13897:** application condition fixes ([#393](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/393)) ([82fbcc3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/82fbcc39bed7664bb506a1e19fe31016d9693512))

## [1.144.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.144.1...@mxp/polaris-v1.144.2) (2024-12-16)

### fix

- YAPF-000 Disable unit selection in fertigation soil analysis ([#394](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/394)) ([0c2bd28](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0c2bd28884638dadae4f452892a1ca7f5c37259a))

## [1.144.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.144.0...@mxp/polaris-v1.144.1) (2024-12-13)

### fix

- **YAPF-12671:** input validations ([#391](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/391)) ([62e4961](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/62e4961f8f8f69332d19c18443a5a95209474897))

# [1.144.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.143.0...@mxp/polaris-v1.144.0) (2024-12-13)

### feat

- updating override behaviour of crop settings in fertigation ([#392](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/392)) ([6d200d9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6d200d9349cc60b752beefb151c2f16d268fb26a))

# [1.143.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.142.2...@mxp/polaris-v1.143.0) (2024-12-12)

### feat

- **YAPF-12671:** Dropdown list selection and value input validation ([#388](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/388)) ([3cc406f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3cc406f3885c29f30df2ab192707808221e9821d))

## [1.142.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.142.1...@mxp/polaris-v1.142.2) (2024-12-11)

### fix

- **YAPF:13896:** filter by productTypeId ([#390](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/390)) ([e7bf8c2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e7bf8c27d81ea327344b242db1ed6fa1da18ab12))

## [1.142.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.142.0...@mxp/polaris-v1.142.1) (2024-12-10)

### fix

- **YAPF-15009:** Disabling select input of units into manually creating of new analysis method ([#389](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/389)) ([3630a16](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3630a168f0dad3909a9b2c97e3f49e68882960a1))

# [1.142.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.141.0...@mxp/polaris-v1.142.0) (2024-12-05)

### feat

- **YAPF-13897:** product recommendation application condition ([#385](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/385)) ([69943c7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/69943c7dfb0b389d954438a831f8b385cb5ce923))

# [1.141.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.140.4...@mxp/polaris-v1.141.0) (2024-12-05)

### feat

- updating splitting total days duration condition ([#386](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/386)) ([ec5a813](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ec5a8137f932b63a6d6ac2727c617ec35658d5e2))

## [1.140.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.140.3...@mxp/polaris-v1.140.4) (2024-12-05)

### fix

- disable select for the soil analysis units ([#384](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/384)) ([20fa0ba](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/20fa0bafbe14fd4cd936bf38a7b4360103beb2e0))

## [1.140.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.140.2...@mxp/polaris-v1.140.3) (2024-12-05)

### fix

- **YAPF-15011:** refactor validation in nue and iwn ([#382](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/382)) ([4a67a11](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4a67a1177488b1fa22981a8807955bbd6e515225))

## [1.140.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.140.1...@mxp/polaris-v1.140.2) (2024-12-04)

### fix

- sort by ordinal ([#381](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/381)) ([83c8646](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/83c86468c5696a8f79c73bcc67841edd4eb92954))

## [1.140.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.140.0...@mxp/polaris-v1.140.1) (2024-12-04)

### fix

- add comment to describe sort ([2f7d309](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2f7d309cfaf407c21a7fbb1411e14f038fc7b229))
- sort partners so external are at the end of the list ([b217c9f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b217c9fd57fbeaa86dc5dee79d37534c96ba8c16))

# [1.140.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.139.0...@mxp/polaris-v1.140.0) (2024-12-03)

### feat

- add mock data, translations, missing props and types, ([b9ad581](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b9ad5816c99adf91333bb11e274b77a9bd52786f))
- add popup component and table ([a75bce7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a75bce7c1d29d6288312cc9e42edae0d54851a8d))
- add tests ([6d9a3b8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6d9a3b85a67320dce046a1d57160dfae71f1dd67))
- integrate add product popup with product recommendation table and change addproducts component ([314fd65](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/314fd650942b2e14f027942b931d319c088ac51d))

### fix

- add id to type, fix potential errors ([885aeaf](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/885aeaf5b5489d30eac8ecd21b07fc4471df5288))
- css variable according to comment ([09d8e8d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/09d8e8d0359fa66371e5150448a7a7c9d337ce36))
- fix a bug, change structure in places where a bad merge happened ([9664248](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/96642487544899424446c4774e88ef7b09d06ab5))
- fix according to comments and desk check ([f088081](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f088081cdc843c1b515751677ddfe6c4bba74666))
- fix css var ([ca7c1ee](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ca7c1ee05131d2ba0216d6d7baa4a98bb57946d0))
- fixes due to comments on pr ([d19208f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d19208f47879574fe527867e812b7577b3369b3d))
- merge master and fix conflicts, additional comment fixes ([ce0c942](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ce0c942be248b8f6868d18785c9893aaebc07cc2))
- remove unnecessary classnames ([ab817be](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ab817be4ae340799c0f6fd8842fe85a4db712ae3))

# [1.139.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.138.0...@mxp/polaris-v1.139.0) (2024-12-03)

### feat

- **yapf-13898:** add foliar app table ([#380](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/380)) ([6c118e6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6c118e693ebfea6cd790923e68a2cab12ac7f92a))

# [1.138.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.137.0...@mxp/polaris-v1.138.0) (2024-12-02)

### feat

- adjusting foliar application method ([#379](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/379)) ([f5b6caa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f5b6caa20eb9ef6cd65ecf0d68f7bc1b3a8697fc))

# [1.137.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.136.1...@mxp/polaris-v1.137.0) (2024-11-27)

### feat

- **yapf-12670:** style adjustments in product recommendation table ([#377](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/377)) ([382fc93](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/382fc93be84b18197dce70ce69491b177e851fab))

## [1.136.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.136.0...@mxp/polaris-v1.136.1) (2024-11-27)

### fix

- **YAPF-12613:** Edit library name bug fixes. ([7d31739](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7d317390b0656902f58587aff947d4ac829a1370))

# [1.136.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.135.4...@mxp/polaris-v1.136.0) (2024-11-26)

### feat

- **YAPF-12670:** product recommendations table ([#363](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/363)) ([f84e23c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f84e23c1a402b38d269978ee2b913d3853078f2c))

## [1.135.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.135.3...@mxp/polaris-v1.135.4) (2024-11-25)

### fix

- filter issue on blur ([#372](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/372)) ([94b00b3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/94b00b38c4b6b03647213a641f25333e5dc32dad))

## [1.135.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.135.2...@mxp/polaris-v1.135.3) (2024-11-25)

### fix

- guideline libraries unit tests and fixes ([#371](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/371)) ([91bed55](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/91bed5516942260521c91fac04f2cd8646798ac5))

## [1.135.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.135.1...@mxp/polaris-v1.135.2) (2024-11-25)

### fix

- YAPF-000 Change min percentage value ([#370](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/370)) ([9970b76](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9970b76def4c535ff969210987b1c90dab4c9135))

## [1.135.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.135.0...@mxp/polaris-v1.135.1) (2024-11-25)

### fix

- **yapf-000:** removing column filter in analysis ([#368](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/368)) ([537c930](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/537c930cf720115beb2650cfb3769573dd699b31))

# [1.135.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.134.0...@mxp/polaris-v1.135.0) (2024-11-22)

### feat

- guideline libraries ([#362](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/362)) ([3ff0c72](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3ff0c72540045bbf0c44378ccd80eccd2fec88ac))

# [1.134.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.133.0...@mxp/polaris-v1.134.0) (2024-11-21)

### feat

- **YAPF-12613:** Edit guideline library ([#361](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/361)) ([9aeffe1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9aeffe181377324a5359d6083696ed0660664ded))

# [1.133.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.132.3...@mxp/polaris-v1.133.0) (2024-11-18)

### feat

- **YAPF-14809:** preserve state of demand calc modules when navigating ([#358](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/358)) ([7db47fb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7db47fbdd66b1f7b5259a28e5ec90758fa8a5c78))

## [1.132.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.132.2...@mxp/polaris-v1.132.3) (2024-11-12)

### fix

- style changes ([#356](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/356)) ([b02800a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b02800aebda98f066fa52ba1a60bc28cb84b4099))
- **YAPF-12657:** reset configuration after nutrient change ([#355](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/355)) ([744ef74](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/744ef743cabcf41272721f8712940cd29fc644da))

## [1.132.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.132.1...@mxp/polaris-v1.132.2) (2024-11-12)

### fix

- YAPF-12604 Fixing update issue ([#354](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/354)) ([63e99e7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/63e99e737cc99fd3d72107dcbb89a0eb83b0890e))

## [1.132.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.132.0...@mxp/polaris-v1.132.1) (2024-11-12)

### fix

- YAPF-12604 FE | Fertigation | Water Analysis | Configurations UI ([#353](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/353)) ([9376d4e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9376d4ea951cb500eb1fdfbcae19c855fb0aa21c))

# [1.132.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.131.0...@mxp/polaris-v1.132.0) (2024-11-11)

### feat

- **YAPF-12663:** dc irrigation water nutrient ([#344](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/344)) ([1e52ed2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1e52ed2632c189760a5937bd90765a8d8090bf4d))

# [1.131.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.130.0...@mxp/polaris-v1.131.0) (2024-11-11)

### feat

- **YAPF-12657:** crop nutrient demand module ([#350](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/350)) ([816d420](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/816d420b9e772e87b498a1eaf3a47e0ffda97845))

# [1.130.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.129.2...@mxp/polaris-v1.130.0) (2024-11-11)

### feat

- YAPF-12604 FE | Fertigation | Water Analysis | Configurations UI ([#349](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/349)) ([188b817](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/188b81766d5e0c014f10a16da09b53fe5f057312))

## [1.129.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.129.1...@mxp/polaris-v1.129.2) (2024-11-11)

### fix

- remove whitespaces in soil correction factors multiplier input value ([0e1179d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0e1179dc128cf454718b9ab3e40ad54b2bdf8566))

## [1.129.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.129.0...@mxp/polaris-v1.129.1) (2024-11-08)

### fix

- **yapf-12660:** adjusting space field ([#351](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/351)) ([323365c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/323365c09655973b09dcc959eea9c4bd85f6c3ee))

# [1.129.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.128.4...@mxp/polaris-v1.129.0) (2024-11-07)

### feat

- **YAPF-12660:** fertigation crop demand soil correction factor ([#340](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/340)) ([076a744](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/076a74403057bfdf63b8076754de129925e249fb))

## [1.128.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.128.3...@mxp/polaris-v1.128.4) (2024-11-06)

### fix

- **yapf-12660:** adjusting input value handler ([#348](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/348)) ([aafad3b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/aafad3bfcdd0bc9f201905e2cd450f7ca74d5034))

## [1.128.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.128.2...@mxp/polaris-v1.128.3) (2024-11-06)

### fix

- **YAPF-12665:** bug fixes ([#347](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/347)) ([d04c529](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d04c529dd0a3d873b3ac6e99e6eac33018d4b7da))

## [1.128.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.128.1...@mxp/polaris-v1.128.2) (2024-11-06)

## [1.128.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.128.0...@mxp/polaris-v1.128.1) (2024-11-06)

### fix

- snackbar not showing when updating parameter level that has the value 0 ([899c6aa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/899c6aaeeec4fd1f1c798fc4733609a1fde97e9b))

# [1.128.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.127.0...@mxp/polaris-v1.128.0) (2024-11-05)

### feat

- **yapf 12659:** dc nutrient use efficiency ([#341](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/341)) ([dd7704c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/dd7704ce414894eb3ec3170190843421d364be40))

# [1.127.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.126.0...@mxp/polaris-v1.127.0) (2024-10-30)

### feat

- **YAPF-12665:** initial setup fertigation splitting ([#337](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/337)) ([5ebbf2f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5ebbf2f44843a412d86d4838bd8310f92b70cb71))

# [1.126.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.125.1...@mxp/polaris-v1.126.0) (2024-10-30)

### feat

- **YAPF-12610:** fertigation plan demand calculation nutrient and nutrient forms ([#339](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/339)) ([cff3ae5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cff3ae5f872ef47d8e2d960dce250f34563b4ecd))

## [1.125.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.125.0...@mxp/polaris-v1.125.1) (2024-10-29)

### fix

- YAPF-14632 resolve qa bugs ([#338](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/338)) ([7b69e5a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7b69e5a98bf5c4ff8f7a3c2dc35a3409cbbe4ce5))

# [1.125.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.124.4...@mxp/polaris-v1.125.0) (2024-10-29)

### feat

- add fertigation leaf analysis component and necessary changes ([502b470](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/502b470fa8719ab10372f7260ba83d9fa9bf31b1))
- disable unit selection in leaf analysis ([b010462](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b0104629adf8672214746dbeebd8c81957c40ab0))
- make components and state more generic, fix bug with delete ([b78f21e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b78f21ea948ea857a03fbcdb5da60d6decaebfe4))
- make configuration and nutrient class hooks more generic, change usage places ([b519651](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b519651502cf451682d004ea40d96c32081a3511))
- make LeafAnalysis more generic ([b919701](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b919701b96f644c91d9e8e8ddbe89adbd9b1d612))
- rename configuration and nutrient classification types ([ab4b563](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ab4b5634c543b048ed92e58ce91748a4450a47fc))

### fix

- fix according to comments ([d3793d8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d3793d8049804ca58c5013fc428b94c0f7c814ad))
- remove analysis methods call ([35f53da](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/35f53dad09dcb3cf8c55c2a4f16ef9719b37c2ef))
- remove unused var, remove console logs ([629963d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/629963d2208cb0fd4a1cee7252771c40046c1683))

## [1.124.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.124.3...@mxp/polaris-v1.124.4) (2024-10-16)

### fix

- **YAPF-13794:** dropdown-dependency ([#334](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/334)) ([a11c410](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a11c410a544d91c69858a77eb2c1ee8a4c69c8b0))

## [1.124.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.124.2...@mxp/polaris-v1.124.3) (2024-10-15)

### chore

- **YAPF-0000:** revert tests on master ([#333](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/333)) ([840d255](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/840d255d97906abfb096bd14fefe3bb978d4c0e4))
- **YAPF-13794:** redeploy ([a9263e3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a9263e3c91857332c2bb9a485c91623354b9d1e2))
- **YAPF-13794:** trigger redeployment / add documentation ([f916f69](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f916f69e3a977542b19d8ad3623766cc201fbd84))

## [1.124.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.124.1...@mxp/polaris-v1.124.2) (2024-10-14)

### chore

- YAPF-13794 trigger redeploy ([#330](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/330)) ([1541e5a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1541e5a9055c70a54cbcc261fd201785004fd3ec))

## [1.124.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.124.0...@mxp/polaris-v1.124.1) (2024-10-14)

### fix

- **YAPF-0000:** add data-cy ([#329](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/329)) ([8289bd3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8289bd3d0ab450d210d24f2033bb48ac47e17174))

# [1.124.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.123.2...@mxp/polaris-v1.124.0) (2024-10-14)

### feat

- YAPF-13794 connect to endpoints ([#328](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/328)) ([834c1df](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/834c1df56cb9f5a4058f69c2adebbf4e6f63f8e9))

### fix

- add data-cy ([#324](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/324)) ([1b8640c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1b8640c3b3473121ae54894241bb6c3c5169b802))
- **YAPF-0000:** revert master ([#327](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/327)) ([f96efb1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f96efb1d194e578846cd1f2be98a69bec187361d))

## [1.123.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.123.1...@mxp/polaris-v1.123.2) (2024-10-08)

### fix

- **yapf-14001:** adjusting nutrient classification delete endpoint ([#323](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/323)) ([d6ab5b2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d6ab5b2e05d94710b7b75772509216c4250c9df9))

## [1.123.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.123.0...@mxp/polaris-v1.123.1) (2024-10-07)

### test

- **YAPF-0000:** initial cypress commit ([#322](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/322)) ([d27d2b9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d27d2b95b16c47830b12695f6eff785a57fc762c))

# [1.123.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.122.2...@mxp/polaris-v1.123.0) (2024-10-02)

### feat

- fertigation add analysis method - manual ([#321](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/321)) ([a2c9ad0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a2c9ad0191fc0ea780733d24306cdc8f468383e7))

## [1.122.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.122.1...@mxp/polaris-v1.122.2) (2024-09-30)

### fix

- **yapf-13745:** parameter level value dot ([#320](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/320)) ([5914c96](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5914c967e9f1f2b3aa6ce093e587fcdb0f8334bd))

## [1.122.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.122.0...@mxp/polaris-v1.122.1) (2024-09-30)

### fix

- **YAPF-13745:** dot issues ([#319](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/319)) ([a1474b1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a1474b1d1379267198251f47027f1fc9447d431a))

# [1.122.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.121.0...@mxp/polaris-v1.122.0) (2024-09-24)

### feat

- **YAPF-13116:** add analysis method manual ([#318](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/318)) ([e3c79b9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e3c79b9072d596a790de8603e52a87e54d0782e5))

# [1.121.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.120.1...@mxp/polaris-v1.121.0) (2024-09-24)

### feat

- **yapf-13578:** bug fixes ([#316](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/316)) ([f24b9c1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f24b9c19fd4ded0ada6b91b348ff967663587a28))

## [1.120.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.120.0...@mxp/polaris-v1.120.1) (2024-09-24)

### fix

- parameter level values ([#315](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/315)) ([59ba098](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/59ba0986ed581ec7be828a550fa102dc4dfa8b6e))

# [1.120.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.119.0...@mxp/polaris-v1.120.0) (2024-09-24)

### feat

- **YAPF-12563:** FP add analysis method autofill ([#314](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/314)) ([c68a521](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c68a5217e9edc4600d232afdca7c6b3181672cdb))

# [1.119.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.118.1...@mxp/polaris-v1.119.0) (2024-09-19)

### feat

- **yapf-13296:** initial setup fertigation soil analysis ([#311](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/311)) ([9338b95](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9338b954ba7cbf78e73d53ceda9e72830730c9e3))

## [1.118.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.118.0...@mxp/polaris-v1.118.1) (2024-09-18)

### chore

- remove console log ([679dce8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/679dce8608cca0e9fa05d0cab6bbf3dc742379be))

# [1.118.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.117.0...@mxp/polaris-v1.118.0) (2024-09-18)

### chore

- add className and align in meatballs menu ([75d972c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/75d972c174f9aeb2b78949ae72435eecfbb4d934))
- change file name ([e204cd7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e204cd7c75247cd7c354c54773e2df75f7b38fab))
- change some names ([f4abc90](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f4abc90c7c46e57e19b71a89d70d1352cb76f95b))
- remove console logs ([5a1c2ec](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5a1c2ecf80cab7a369cd9356c4fd6747690e28d4))
- remove duplicate envs, console log and wrong translations ([42d1c66](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/42d1c6608231520ca192c37c57bcaa2adfa76868))

### feat

- add generic tyypes, fixes inx sortUtils ([be3b6aa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/be3b6aab4e27cf2e4403cf6038ce801f1d913e6c))
- add mocks, mock handlers and types ([3e801e4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3e801e42625a33d50d1b35a2b648da929b364c11))
- adjust CMMMSoilAnalysis to new endpoints and structure/logic, add utils and tests ([925f3cd](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/925f3cd1ea90c0e77ac6d9455dd0618001b19eab))
- change custom fetcher to be able to parse content type text responses ([65ea6e1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/65ea6e14f65bfc7403e2a75368215d51497defbf))
- change multi requests hook, fix bug ([022932a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/022932a1ad804f578e6836e759c1c07ada42f8ec))
- change namings, change parameterUtils back to strings, cmmm soil analysis changes ([6639f38](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6639f388f36f9e0f4c8e52ba283129721cf0cd24))
- change nutrients call, change namings and fix some tests ([6bdb558](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6bdb558b0ca3284e170cae4fa1cc79b954dec1a8))
- create MeatballsMenu component ([17ef9bd](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/17ef9bddbd67fb7094ae5b7e2ebbcef0a2dd8177))
- extract DeleteAnalysisMethodPopup outside of SoilAnalysis, so it is shared ([627b80c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/627b80c0914824730af753dcf04143b8d79ae7b9))
- fix minor bug, revert to mock api for nutrition planning apis ([6bd2cd3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6bd2cd321fcb64fbcb75979c74b10b7a536f0d63))
- fix names and add filter call for analysis method ([b8bf886](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b8bf88681f5368e5562724445d4a1aa31d8156f6))
- integration and changes with BE, add analysis method popup, collapsible and other related ([ad20efe](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ad20efec66238c4c2398ca2538d6be8207ffcaf4))
- load cdas on nutrient change, add and delete, add delete functionality ([670571f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/670571f100ddf90fa1b8f09e6e72b76ed72986b0))
- move files nutrition params table for cmmm, change to new endpoints and structure ([8e73e4b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8e73e4be2793a14183326e37c3c7bb4fad32a13a))
- refactor some namings around hooks,use index imports, split logic for soil analysis ([f74d5d8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f74d5d8b8d870ef7ddc5f70a7e25305209c3cdbe))
- soil analysis related changes, tests ([fb9667d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fb9667deb0c10a102ea40f8a4fe67b0f07296a23))

### fix

- changes according to comments, fix namings etc ([249bd03](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/249bd039b57bba7700015c5fe036f758f817956f))
- fix according ot desk check result, change translation, remove asterisk, add test ([ef07661](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ef0766158e0e446ec1a75662dfd2774514d42518))
- fix bugs, changes according to comments ([55e6871](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/55e68717cb5fa6f091933e1ce6e5114fbf08675e))
- fix comments ([2cfc44c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2cfc44cff01a13ebd10a29bec4916855c3859c28))
- fix namings, split collapsible, many other fixes according to comments ([05011b2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/05011b2454e877a6c7c78ddb167947b927f21c15))
- fix numerous bugs around adding param levels and analysis methods ([785a599](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/785a5997816e6a7f4918b7d4d1fab692b4c84230))
- fix translatons for cerea soil analysis ([5f85ddd](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5f85ddd19b56ff11f6e9fb402fbb8a5e59b785cf))
- fix types, castings and namings ([4642bd7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4642bd783d80f3981f1398db5b5d1b74e05dc45f))
- move nutritionparamwithvaluestable ([c5d8883](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c5d8883c1644c9935504ec5fcb7ecefcb2ac01a7))
- namings, some bugs ([3b21440](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3b21440769fc5dfac6d26e73bee315b8d32decad))
- remove unnecessary lines from display table test ([1c05b33](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1c05b33abf93927fd8624327492627de1c0c3197))

# [1.117.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.116.2...@mxp/polaris-v1.117.0) (2024-09-18)

### feat

- YAPF-12615 save functionality and refactor to use context ([#310](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/310)) ([c19e6e4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c19e6e4be4f2ff98a4995ca79dfcbf320c114145))

## [1.116.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.116.1...@mxp/polaris-v1.116.2) (2024-09-18)

### chore

- **yapf-00000:** change env variable & css cleanup ([#312](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/312)) ([3359e82](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3359e8240142349ed8b599af25d228f9b17fecf8))

## [1.116.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.116.0...@mxp/polaris-v1.116.1) (2024-09-17)

### fix

- add translation descriptions ([#309](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/309)) ([24d49be](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/24d49be1063c587193057b892c9c89a634913628))

# [1.116.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.115.2...@mxp/polaris-v1.116.0) (2024-09-11)

### feat

- YAPF-13195 delete guideline ([#303](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/303)) ([758682e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/758682ec081b3c1a7bcc2cede646505fb07cbdab))

## [1.115.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.115.1...@mxp/polaris-v1.115.2) (2024-09-11)

### chore

- fix required mmm payload ([#306](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/306)) ([27fc8c9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/27fc8c9699feaa7001894f64b538d51b044f6ab2))

## [1.115.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.115.0...@mxp/polaris-v1.115.1) (2024-09-11)

### chore

- update mmm validation request ([#305](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/305)) ([5d4d7fe](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5d4d7fed42c5489edf638c4da953064437b41fc7))

# [1.115.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.114.0...@mxp/polaris-v1.115.0) (2024-09-09)

### feat

- **FertigationPlanDetails:** disable edit crop button if CNP is validated ([#302](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/302)) ([c85192d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c85192d9bd637638b29ab6f285a49eebbae6eacc))

# [1.114.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.113.0...@mxp/polaris-v1.114.0) (2024-09-09)

### feat

- **YAPF-13194:** add guideline functionality ([#300](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/300)) ([578679b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/578679b7aba3b589aafd70fe5727556e4b46fa61))

# [1.113.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.112.2...@mxp/polaris-v1.113.0) (2024-09-05)

### feat

- **yapf-13691:** connect mmm-validations endpoints to the MMM ms ([#299](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/299)) ([32a3ed0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/32a3ed0645ce11476725c6a9c66bcf16ba2e202f))

## [1.112.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.112.1...@mxp/polaris-v1.112.2) (2024-09-05)

### fix

- YAPF-11729 Adding additional validation ([#301](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/301)) ([c89a87a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c89a87a4ed6c00c5fb69bb406251bac0b278fd16))

## [1.112.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.112.0...@mxp/polaris-v1.112.1) (2024-09-04)

### fix

- YAPF-11729 Design fixes ([#298](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/298)) ([001916d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/001916d904f17030abb5e6a27f051e1a39c73bd8))

# [1.112.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.111.0...@mxp/polaris-v1.112.0) (2024-09-03)

### feat

- YAPF-11729 FE | CMMM | Nutrient Splitting ([#292](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/292)) ([39f5fcc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/39f5fcc08d80509982d12586873d4c4151fcfc53))

# [1.111.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.110.1...@mxp/polaris-v1.111.0) (2024-09-03)

### feat

- **yapf-13627:** switching cmmm yield and demand unit options in edit crop settings ([#289](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/289)) ([cdcfa0a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cdcfa0a57a1294a3caa829964107e036509eeecf))

## [1.110.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.110.0...@mxp/polaris-v1.110.1) (2024-09-02)

### fix

- **YAPF-13193:** guideline libraries edit ([#294](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/294)) ([75bb301](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/75bb301ad0ba5781e1099235cf7a6bceab3129e5))

# [1.110.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.109.1...@mxp/polaris-v1.110.0) (2024-09-02)

### feat

- **YAPF-12969:** Cereals MMM home BE connection ([#288](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/288)) ([ff304c3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ff304c3044cd90d2db742bd5b4db243da0687847))

## [1.109.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.109.0...@mxp/polaris-v1.109.1) (2024-08-30)

### fix

- add helper-text attribute ([#291](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/291)) ([e26819c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e26819c3f9c80ac4e3111d6f11a6525a1319eb7b))

# [1.109.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.108.1...@mxp/polaris-v1.109.0) (2024-08-30)

### feat

- **YAPF-13193:** guideline libraries edit ([#290](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/290)) ([64c5dd0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/64c5dd0fb0a4a5576ba1aa8dbce974cbcaf5e2fc))

## [1.108.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.108.0...@mxp/polaris-v1.108.1) (2024-08-30)

### fix

- **YAPF-13115:** tooltip margin and filtering ([#287](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/287)) ([2b3d896](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2b3d89652adf9e11affe6e0529b5d7ffc1f296f7))

# [1.108.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.107.0...@mxp/polaris-v1.108.0) (2024-08-29)

### feat

- **yapf-13152:** replace growth-scale api and fix issue ([#286](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/286)) ([1bfb78f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1bfb78fde30f52ca0406211a11b471c1eefd4d47))

# [1.107.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.106.0...@mxp/polaris-v1.107.0) (2024-08-28)

### feat

- **YAPF-13153:** FP partners for this crop displayName ([#284](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/284)) ([3cb427d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3cb427d7c7f1f1301d61780f9abde17c206fdfa4))

# [1.106.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.105.0...@mxp/polaris-v1.106.0) (2024-08-27)

### feat

- **YAPF-13367:** target yield as additional parameter ([#281](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/281)) ([1975687](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1975687e902ad380d1662940cbd9083a4cd68c21))

# [1.105.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.104.0...@mxp/polaris-v1.105.0) (2024-08-27)

### feat

- **yapf-13152:** home page be connection ([#283](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/283)) ([f12ec32](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f12ec328981ee4c1dd482f9de7532070a07a27d1))

# [1.104.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.103.2...@mxp/polaris-v1.104.0) (2024-08-27)

### feat

- **YAPF-13192:** guideline libraries initial state ([#278](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/278)) ([e1bb834](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e1bb834056aa22d8e0d950578f4f4b1757bf2794))

## [1.103.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.103.1...@mxp/polaris-v1.103.2) (2024-08-26)

### fix

- **yapf 13211:** manage parameter levels ([#279](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/279)) ([fd22a7b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fd22a7bd789e5fb2d0496f417390b8fb5628448d))

## [1.103.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.103.0...@mxp/polaris-v1.103.1) (2024-08-26)

### fix

- **yapf-13371:** adjusting partial crop presence ([#280](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/280)) ([8c880b1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8c880b1b6e743190ef54c3da26fd4f7b8f0b90b6))

# [1.103.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.102.0...@mxp/polaris-v1.103.0) (2024-08-26)

### feat

- **yapf-13371:** adjusting logic to avoid empty rq calls ([#277](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/277)) ([a882553](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a88255308920e8164172a6bd5cfc7eb5748708b8))

# [1.102.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.101.0...@mxp/polaris-v1.102.0) (2024-08-23)

### feat

- YAPF-12568 guideline libs general ([#276](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/276)) ([e21779d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e21779d6d8b847004e1335adc933a44c5e241201))

# [1.101.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.100.1...@mxp/polaris-v1.101.0) (2024-08-22)

### feat

- **yapf-13371:** navigation page switch to real backend ([#271](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/271)) ([e556a5b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e556a5b4124417cee36ba96ca53a90680c76fc10))

## [1.100.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.100.0...@mxp/polaris-v1.100.1) (2024-08-21)

### refactor

- **YAPF-00000:** fetch and filter nutrients from polaris ([#275](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/275)) ([5125782](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5125782367e2e5b959c4632edf0540ee008bb074))

# [1.100.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.99.0...@mxp/polaris-v1.100.0) (2024-08-21)

### feat

- **yapf-13211:** manage parameter levels ([#269](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/269)) ([55914ae](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/55914aebf8c39eb4104e7a7313e1d829e4e61cd5))

# [1.99.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.98.0...@mxp/polaris-v1.99.0) (2024-08-21)

### feat

- **YAPF-13115:** Create new analysis method ([#273](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/273)) ([4ca16cb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4ca16cb1f08d59532902910c1c17e826c864162d))

# [1.98.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.97.0...@mxp/polaris-v1.98.0) (2024-08-20)

### feat

- **yapf-11733:** adjusting to po feedback ([#274](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/274)) ([9f8547d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9f8547d11f6594a122e7c954007245850c37a460))

# [1.97.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.96.0...@mxp/polaris-v1.97.0) (2024-08-16)

### feat

- **YAPF-12588:** Fertigation soil analysis setup ([#272](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/272)) ([8b28385](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8b283854bf329daa133c5800c2236514be3ef9f0))

# [1.96.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.95.0...@mxp/polaris-v1.96.0) (2024-08-13)

### feat

- **yapf-11733:** trigger deployment ([#270](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/270)) ([ff57b24](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ff57b24074e9bad03a7367c6b93f77372b4c4b99))

# [1.95.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.94.1...@mxp/polaris-v1.95.0) (2024-08-13)

### feat

- **yapf-11733:** cmm org fertiliser products available initial setup ([#261](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/261)) ([cff185e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cff185e20bf51acc25bad917b4adec2f5098c456))

## [1.94.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.94.0...@mxp/polaris-v1.94.1) (2024-08-12)

### refactor

- **yapf-000:** refactoring cnp homepage ([#268](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/268)) ([1420d3c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1420d3ccfb582f8445f77f7176725aaf7ee7bd83))

# [1.94.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.93.0...@mxp/polaris-v1.94.0) (2024-08-09)

### feat

- **yapf-13378:** adjusting unit tags in fertigation plan ([#266](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/266)) ([5cdcb21](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5cdcb21410c714bb85d4bc792d19f5f7742591b7))

# [1.93.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.92.3...@mxp/polaris-v1.93.0) (2024-08-08)

### feat

- **yapf-13370:** adjusting plan invalidation popup texts ([#267](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/267)) ([f030d92](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f030d921a22c7156e81e5c6e4ec99df7d83b180e))

## [1.92.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.92.2...@mxp/polaris-v1.92.3) (2024-08-06)

### fix

- persist plan config tabs ([#265](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/265)) ([48ad90d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/48ad90d72e22fdd235c94ec4aa8ef15d3d71d12a))

## [1.92.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.92.1...@mxp/polaris-v1.92.2) (2024-08-06)

### refactor

- **YAPF-12871:** selected nutrients by config tabs ([#264](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/264)) ([1c3d2e8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1c3d2e868a71d263636974944599bef2da5937f0))

## [1.92.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.92.0...@mxp/polaris-v1.92.1) (2024-08-06)

### fix

- some issues with the state of data ([#263](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/263)) ([59fe03a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/59fe03a66ec4a24274a9d3475cf371bc5af70a41))

# [1.92.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.91.0...@mxp/polaris-v1.92.0) (2024-08-06)

### feat

- **YAPF-12871:** demand calculation top ([#260](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/260)) ([910a02b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/910a02bfdf12988d1e7b2807cff8d7672c1941d6))

# [1.91.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.90.3...@mxp/polaris-v1.91.0) (2024-08-02)

### feat

- **yapf-11719:** soil analysis ([#259](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/259)) ([b61c35f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b61c35fbca0609ef2027380ae7cff62a351e6bd2))

## [1.90.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.90.2...@mxp/polaris-v1.90.3) (2024-08-02)

### fix

- **yapf-000:** adjusting target yield label in fertigation home ([#262](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/262)) ([19b7d3a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/19b7d3a2270d0afdb04082567723e4a7c8e8aadd))

## [1.90.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.90.1...@mxp/polaris-v1.90.2) (2024-07-31)

### refactor

- export from index files ([#258](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/258)) ([d5add67](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d5add67ac5519281aa0c4c4ce7bfdfdf0cd972b7))

## [1.90.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.90.0...@mxp/polaris-v1.90.1) (2024-07-30)

### fix

- more QA fixes for CMMM (and Fertigation) ([#257](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/257)) ([182012d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/182012d9392b4983689b58ac50412dcd2b9bd5f2))

# [1.90.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.89.0...@mxp/polaris-v1.90.0) (2024-07-29)

### feat

- **YAPF-000:** refactoring of CNP, CMMM and FP components ([#254](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/254)) ([4ceacf5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4ceacf57cceae26cd4c797f7d66dcddccd338b99))

# [1.89.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.88.0...@mxp/polaris-v1.89.0) (2024-07-29)

### feat

- **YAPF-12560:** adding edit popup functionality in fertigation crop settings ([#252](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/252)) ([b3aee06](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b3aee0659717c56e44697bbd1c4daa807772e732))

# [1.88.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.87.3...@mxp/polaris-v1.88.0) (2024-07-29)

### feat

- **YAPF-12506:** initial setup Fertigation Plan homepage ([#250](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/250)) ([9b6962a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9b6962aa6f53127993511c517220b3f58f78825b))

## [1.87.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.87.2...@mxp/polaris-v1.87.3) (2024-07-26)

### fix

- add correct widget register URL for prod environment ([a4bc6aa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a4bc6aa3125ca627e9d74acc93a931befc078586))

## [1.87.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.87.1...@mxp/polaris-v1.87.2) (2024-07-25)

### fix

- **YAPF-11712:** CMMM homepage QA UI fixes ([#253](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/253)) ([6287ab4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6287ab4722a5ca13a4fca1abdecd48c36fd45b0d))

## [1.87.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.87.0...@mxp/polaris-v1.87.1) (2024-07-23)

### fix

- **YAPF-11712:** ui fixes in cmmm details and co ([#249](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/249)) ([df6d6f7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/df6d6f71105e7ac176a9b47eebd9dd51091644f4))

# [1.87.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.86.0...@mxp/polaris-v1.87.0) (2024-07-22)

### feat

- **yapf12965:** add main tab structure ([#247](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/247)) ([5b2caf9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5b2caf95ab1730847f5dc92d93f1fee644b38e8c))

# [1.86.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.8...@mxp/polaris-v1.86.0) (2024-07-22)

### feat

- **YAPF-11712:** CMMM Homepage ([#248](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/248)) ([691f5a2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/691f5a249ab7e09af7500c39822209a1f2ca3c08))

## [1.85.8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.7...@mxp/polaris-v1.85.8) (2024-06-28)

### docs

- add description to hooks ([#246](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/246)) ([ba96692](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ba96692f859ccc413ae6f537710cb5a81e3e1b0d))

## [1.85.7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.6...@mxp/polaris-v1.85.7) (2024-06-26)

### fix

- fix ordinal being NaN if there are no parameters in the list ([19ba090](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/19ba0909dc73707158fd203f658e6f0c6e54e271))

## [1.85.6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.5...@mxp/polaris-v1.85.6) (2024-06-26)

### fix

- **yapf-12343:** add custom hook to clear the pointer events ([#244](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/244)) ([20fa0d2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/20fa0d299c08e49e6d63a5d304aa6843ff2c6dde))

## [1.85.5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.4...@mxp/polaris-v1.85.5) (2024-06-26)

### fix

- fix default value validation ([6480975](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/64809756b1d317dcdb1311659a2e0778726b3439))
- fix ordinal bug when adding multiple ahdb params ([6dad60e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6dad60e53ef7e2c67e4b7bf9d2000a15566f5a6a))

## [1.85.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.3...@mxp/polaris-v1.85.4) (2024-06-25)

### fix

- default value error message and validation ([e6eaff3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e6eaff3a90ad1e4896228456ffb30ff7f5803961))

## [1.85.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.2...@mxp/polaris-v1.85.3) (2024-06-25)

### fix

- YAPF-12280 SoilAnalysis Empty state ([#223](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/223)) ([11b9acb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/11b9acba9330baa7bffaa957c093b33dbefb83c6))

## [1.85.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.1...@mxp/polaris-v1.85.2) (2024-06-25)

### fix

- remove ahdb prop from AhdbParameter comp usage ([23b3a55](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/23b3a551f77a109e22119bbec36c2df9dcc37ba6))

## [1.85.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.85.0...@mxp/polaris-v1.85.1) (2024-06-25)

### fix

- change selector to identify correct dialog ([#237](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/237)) ([f5816ca](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f5816ca83e1d8cdb4d534796bbb9bc7ee88f1c36))

# [1.85.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.84.1...@mxp/polaris-v1.85.0) (2024-06-25)

### chore

- remove console log ([72c38de](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/72c38de6763f396e5515b51fcce4da54dc869329))

### feat

- pre-post-crop-maps-in-ahdb-tooltip ([#235](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/235)) ([8d70fc2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8d70fc22596f4615c9251e001f4ce4207fdedfb3))

### fix

- fix naming of ahdb params and problem with ordinal ([fca7a22](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fca7a22ea098e1b5d832f7a69639cba96a8a1999))

## [1.84.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.84.0...@mxp/polaris-v1.84.1) (2024-06-25)

### chore

- fix tests ([3a65f57](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3a65f5738ca66b88f2063c011ef7512482b0588a))
- remove useMemo ([d58bf14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d58bf14d414aa7197d324ec02dd855177720d1cc))

### fix

- add validations for min/max and def val, remove tooltip ([9f39245](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9f392454b8b482c465637e4236388d7985699ff9))
- fix bugs related to def value error state ([79b19e7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/79b19e7561eb35a69c10391a340e0ad773d2fc0a))
- fix wrong validations and error messages ([c6fccab](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c6fccabccf820752360deed0f65fd71dbc7fe128))

# [1.84.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.83.0...@mxp/polaris-v1.84.0) (2024-06-25)

### feat

- YAPF-12325 update user management browser package ([#232](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/232)) ([256a73f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/256a73ff22031a1f2fe94f7c959f02f965ebddfc))

# [1.83.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.82.1...@mxp/polaris-v1.83.0) (2024-06-24)

### feat

- YAPF-10418-ahdb-param-management ([#230](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/230)) ([0f27830](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0f278303212fdf887abeae14c1db1afccc74e1f5))

## [1.82.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.82.0...@mxp/polaris-v1.82.1) (2024-06-24)

### fix

- YAPF-12408 refactor app context ([#228](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/228)) ([0dc82ce](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0dc82ce54dbd7902b1319426b2119017fbfe10a0))

# [1.82.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.81.3...@mxp/polaris-v1.82.0) (2024-06-24)

### feat

- YAPF-10301 copy paste formula ([#231](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/231)) ([41d1307](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/41d13072e668d9eb53738334b47f18db36f51fcd))

## [1.81.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.81.2...@mxp/polaris-v1.81.3) (2024-06-21)

### chore

- fix tests ([cc01cc2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cc01cc247d954c7f2fc949e75ca206f9f38d9bbc))
- remove useMemo ([ebee10e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ebee10e18427b811e945c78d9d2981a865ea060e))

### fix

- add validations for min/max and def val, remove tooltip ([6ddd098](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6ddd098031f417d1aec99e33615515ed81f26070))
- fix bugs related to def value error state ([031be2f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/031be2fec8aa71403d8703a53964ea87496812a3))

## [1.81.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.81.1...@mxp/polaris-v1.81.2) (2024-06-21)

### fix

- save button not visible issue ([#226](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/226)) ([115df50](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/115df50aa90c54a55fd4f9732b40100b4c7266ef))

## [1.81.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.81.0...@mxp/polaris-v1.81.1) (2024-06-20)

### fix

- YAPF-10793 location is not remembered across sessions as expected ([#227](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/227)) ([725971e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/725971e6101ff35b7824de52a44bbce47d8a7ea4))

# [1.81.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.80.2...@mxp/polaris-v1.81.0) (2024-06-20)

### feat

- YAPF-10418-ahdb-param-management ([#225](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/225)) ([0909ef4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0909ef4a14ab3d69c9727769625cf63cf6df40ba))

## [1.80.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.80.1...@mxp/polaris-v1.80.2) (2024-06-20)

### fix

- YAPF-10298 trigger refetch of the data after toggle of Secondary parameter ([#221](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/221)) ([7879df5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7879df518a14cda650edfb2f617867c261ccd692))

## [1.80.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.80.0...@mxp/polaris-v1.80.1) (2024-06-19)

### fix

- logout functionallity ([#224](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/224)) ([1ad3171](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1ad3171eb5c44bf8c5920fa1e492c8ecb267abb6))

# [1.80.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.79.0...@mxp/polaris-v1.80.0) (2024-06-19)

### feat

- add ahdb parameter generating func ([29dde8d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/29dde8dac5f531b3b2c818d5a4db67285553bd2a))
- add tests ([6105503](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6105503022ed669b81ae079d644cca667da47e9a))

### fix

- remove console log, edit comment ([bf4096b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bf4096bc8bcd9c6deb4897dcd09e5c96106c455c))

# [1.79.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.6...@mxp/polaris-v1.79.0) (2024-06-19)

### feat

- added nutrient elemental name ([#217](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/217)) ([7efddec](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7efddecf339a44f291e6ee6b7324750aec8e7b5a))

## [1.78.6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.5...@mxp/polaris-v1.78.6) (2024-06-18)

### style

- cursor to pointer for dropdown triger ([#220](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/220)) ([f88000d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f88000dd360e0940c54ecad5b428e05f3d9bd2e0))

## [1.78.5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.4...@mxp/polaris-v1.78.5) (2024-06-18)

### fix

- translation bug ([#218](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/218)) ([add9493](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/add9493bd359ab8886665d53afbc0b2c47c298d3))

## [1.78.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.3...@mxp/polaris-v1.78.4) (2024-06-17)

### fix

- YAPF-10298 remove deletion of analysis methods when copying ([#214](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/214)) ([3a25dfa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3a25dfa5bfa8cb409a83b5b383c26cfe8fadf9f1))

## [1.78.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.2...@mxp/polaris-v1.78.3) (2024-06-14)

### fix

- **yapf-12403:** analysis method delete ([#213](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/213)) ([bf675e7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bf675e74a913064639349f28fe8af3706918e352))

## [1.78.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.1...@mxp/polaris-v1.78.2) (2024-06-13)

### fix

- fix infinite calls bug by removing length from dep arr of call func ([cbe0657](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cbe0657584b40cf2b06684cd53aeb760e62bce47))

## [1.78.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.78.0...@mxp/polaris-v1.78.1) (2024-06-11)

### fix

- **YAPF 11022:** translation and ui issue ([#211](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/211)) ([31bb11f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/31bb11f7da2a8f26b51c3dd3a37099cfa993c5bf))

# [1.78.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.77.2...@mxp/polaris-v1.78.0) (2024-06-11)

### chore

- reintroduce wrongly deleted code ([0bdc9e5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0bdc9e51bfc691a026204bea0fd8451721a61430))

### feat

- add stateToLocalStorage file and hook ([161e1b5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/161e1b5c6f5a263e7a8376387a4ed4fe127205d9))
- create localStorage mock, use in useStateToLocalStorage tests ([2c03f91](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2c03f9120a76daf4bea093b7f418a6debe035afa))
- refactor back navigation logic in top bar ([20493b8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/20493b8c01549d9c76b0ac93313e6ef785f8b7c8))
- remove useSetNavbar from CropFeatures as it's just an outlet ([949842b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/949842b1a8f41de863a72d4c1a69a894a758442b))
- remove utils inside topbar, update exports improts ([abc7af3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/abc7af3e114bdfcb52e91f6822d9489991fd0208))
- rename hook file, remove unnecessary console logs ([602dbd9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/602dbd9cb2d07f278cf7fc706dde23dab4b471c4))
- set navbar translation key for crop features correctly ([d573d1d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d573d1d1361670aa107ad12c110fe24022cd1574))
- use useSetNavbar hook in CNPDetails, remove setActiveRoutePath ([86ad1e3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/86ad1e39cff4a96ac5e02657c88fcec3ad0a6732))

### fix

- fix QA comment ([7722384](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7722384b3859f51bd138b3dff19f90326c1e8ca5))

## [1.77.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.77.1...@mxp/polaris-v1.77.2) (2024-06-11)

### fix

- address QA comments ([#210](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/210)) ([d85e5f2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d85e5f20e9cc22f7cee816e4630c78fba42e92ba))

## [1.77.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.77.0...@mxp/polaris-v1.77.1) (2024-06-10)

### fix

- YAPF-10298 separate logic for new and default ([#209](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/209)) ([281d694](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/281d69408f8cc9905c05e45a018426438d17cb3e))

# [1.77.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.76.0...@mxp/polaris-v1.77.0) (2024-06-10)

### feat

- YAPF-11022 cnp soil analysis delete analysis method ([#204](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/204)) ([742fad9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/742fad9f2de1dc040272102a6c0c15bfc4bb1e20))

# [1.76.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.75.0...@mxp/polaris-v1.76.0) (2024-06-07)

### feat

- soil-classification-improvements ([#206](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/206)) ([27be4e8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/27be4e8002ba47fa4c4f6417160c8ba8b7c189d7))

# [1.75.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.74.1...@mxp/polaris-v1.75.0) (2024-06-07)

### feat

- YAPF-9926 product recommendation empty state ([#203](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/203)) ([86dcf45](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/86dcf456e2d9c545dd9057efffa535b87ab7f70c))

## [1.74.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.74.0...@mxp/polaris-v1.74.1) (2024-06-07)

### fix

- methods are getting created multiple times ([#208](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/208)) ([6f8f161](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6f8f16160292ef1b06afd22f260f052834d6e20b))

# [1.74.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.73.2...@mxp/polaris-v1.74.0) (2024-06-07)

### feat

- YAPF-10547 edit additional parameters ([#200](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/200)) ([1bc3405](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1bc3405525c12078f992b33aef39d476ebd61b25))

## [1.73.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.73.1...@mxp/polaris-v1.73.2) (2024-06-06)

### fix

- YAPF-10298 issues mentioned by QA ([#205](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/205)) ([e081033](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e08103319d38611187ae90b78b3d3e4a32bf9c66))

## [1.73.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.73.0...@mxp/polaris-v1.73.1) (2024-06-06)

### fix

- expression builder onselect bug ([#202](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/202)) ([6adc72a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6adc72a7e69b4b434931e13faf79dfd7e2a53f91))

# [1.73.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.72.0...@mxp/polaris-v1.73.0) (2024-06-06)

### feat

- add-new-parameter-validity-expression-fix ([#201](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/201)) ([53de47e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/53de47ec555eb107e2490fdacb3aaf82ce7170a3))

# [1.72.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.71.0...@mxp/polaris-v1.72.0) (2024-06-05)

### feat

- YAPF-10124-soil-classification-activated ([#198](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/198)) ([b674293](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b6742932d366c72acca513b238ba3c45e06b0c5e))

# [1.71.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.70.0...@mxp/polaris-v1.71.0) (2024-06-05)

### feat

- adds azure login support ([3f80cb6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3f80cb64b4fd60443bb4aad392d874b181601225))

# [1.70.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.69.1...@mxp/polaris-v1.70.0) (2024-06-04)

### feat

- YAPF-10298 copy all nutrients ([#196](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/196)) ([523fa94](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/523fa949c619dac828490e989205a5ae04b2e3a8))

## [1.69.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.69.0...@mxp/polaris-v1.69.1) (2024-06-04)

### style

- smaller font size for message ([#197](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/197)) ([5cd756c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5cd756ccd1f88e64c6f9e1e5f8d7e7b14141a49a))

# [1.69.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.68.2...@mxp/polaris-v1.69.0) (2024-06-03)

### feat

- YAPF-11006 demandcalculations empty state ([#193](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/193)) ([57c63d8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/57c63d80c4b28450e72e36d0265d75009f29c9e9))

## [1.68.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.68.1...@mxp/polaris-v1.68.2) (2024-06-03)

### fix

- add selector for AT ([#195](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/195)) ([91be4ca](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/91be4ca06f9a6786e0ea314b0d363de658c02dac))

## [1.68.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.68.0...@mxp/polaris-v1.68.1) (2024-05-31)

### fix

- add selector ([#194](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/194)) ([fc05498](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fc054986a83bed7bfef276bd1cad203d80b735c7))

# [1.68.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.67.0...@mxp/polaris-v1.68.0) (2024-05-30)

### feat

- add-parameter-refactor ([#191](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/191)) ([543ab27](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/543ab2769787ba034b02cf68d3abc542f7c37c36))

# [1.67.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.6...@mxp/polaris-v1.67.0) (2024-05-29)

### feat

- set the selected nutrient when copying ([#192](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/192)) ([bd36bfe](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bd36bfe3c0e4fa87106a93fcdc01ab77fee24ed4))

## [1.66.6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.5...@mxp/polaris-v1.66.6) (2024-05-28)

### refactor

- change inline snackbar styles to body styled component ([#190](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/190)) ([08241c4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/08241c4ae3ae29174705bc84a43dd31da62801fd))

## [1.66.5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.4...@mxp/polaris-v1.66.5) (2024-05-28)

### fix

- typos in translation and change button ([#189](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/189)) ([1b1a7b4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1b1a7b475c2155db634264886ff9c1bdd23b044e))

## [1.66.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.3...@mxp/polaris-v1.66.4) (2024-05-28)

### fix

- add word break and white space ([#188](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/188)) ([62a0f38](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/62a0f38427b8bbb457e574059578cb4db79b2510))

## [1.66.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.2...@mxp/polaris-v1.66.3) (2024-05-28)

### fix

- change styles based on QA comments ([#187](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/187)) ([5c1ba82](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5c1ba82b5956a3978646f13af62e7b29ca42a6c6))

## [1.66.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.1...@mxp/polaris-v1.66.2) (2024-05-27)

### fix

- revert changes to fix error ([#184](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/184)) ([4c58dfc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/4c58dfc168526f1167fb48763f57824311a39c3a))
- snapshot update ([#185](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/185)) ([8af0f82](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8af0f8223422f6c3a1226031d1219cd73ac33928))

## [1.66.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.66.0...@mxp/polaris-v1.66.1) (2024-05-27)

### fix

- add specific translations ([#182](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/182)) ([1bfe87e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1bfe87e2f6d14f7f97b032efa7dcad03eb047373))
- unit test build ([#183](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/183)) ([443c7c8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/443c7c8a27af71085a1874de7133678192da7563))

# [1.66.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.65.3...@mxp/polaris-v1.66.0) (2024-05-27)

### feat

- YAPF-10250-nutrient-in-add-popop-validity-expression-fix ([#178](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/178)) ([78612e1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/78612e14844d36cd01a3f4c8cda268aebabb4af9))

## [1.65.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.65.2...@mxp/polaris-v1.65.3) (2024-05-27)

### fix

- move ValueType logic to custom hook ([#181](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/181)) ([ef2742e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ef2742e9636a6c05e4a5ad35f9bffc9d83840eb9))

## [1.65.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.65.1...@mxp/polaris-v1.65.2) (2024-05-27)

### fix

- **YAPF-11668:** soil-analysis-dropdown-menu-position-fix ([#180](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/180)) ([2dda20c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2dda20c8df2bdfbac2fd691f74125d4ef62bba72))

## [1.65.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.65.0...@mxp/polaris-v1.65.1) (2024-05-27)

### fix

- translation ([#179](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/179)) ([5d4ece4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5d4ece49c8218809e1e9de77aba3a2e591958a8d))

# [1.65.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.64.0...@mxp/polaris-v1.65.0) (2024-05-27)

### feat

- YAPF-10299 copy nutrient ([#176](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/176)) ([3c3433b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3c3433befad1701dc8ab0d190849acff5a1ff557))

# [1.64.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.63.0...@mxp/polaris-v1.64.0) (2024-05-23)

### feat

- YAPF-10250-soil-analysis-add-row ([#172](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/172)) ([f65a4f9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f65a4f947405a8fe51058d539ae06235529ceafb))

# [1.63.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.62.0...@mxp/polaris-v1.63.0) (2024-05-23)

### feat

- **yapf-10122:** fix problems pointed out by QA ([ab1e9f9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ab1e9f9ad48e068caf480aa71ff3c1b5537e5bcc))

# [1.62.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.61.2...@mxp/polaris-v1.62.0) (2024-05-23)

### feat

- YAPF-10963 list additional parameter ([#171](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/171)) ([019e305](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/019e305c2a931fde491151ddd426e37415c9ca2a))

## [1.61.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.61.1...@mxp/polaris-v1.61.2) (2024-05-23)

### fix

- YAPF-10946 dont show tooltip after validation ([#169](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/169)) ([a35ab52](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a35ab52af86600ee6eac76aa79f36351a0ce2caf))

## [1.61.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.61.0...@mxp/polaris-v1.61.1) (2024-05-23)

### style

- add padding to cnp partners ([#174](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/174)) ([099e68a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/099e68a2fe8297c074f65b25c5d2370164162a67))

# [1.61.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.60.0...@mxp/polaris-v1.61.0) (2024-05-23)

### feat

- **YAPF-11668:** added-dropdown-menu-for-add-delete-analysis-method ([#175](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/175)) ([37b787a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/37b787abda763743add3c0ca8c6e985e7aca9ff4))

# [1.60.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.59.0...@mxp/polaris-v1.60.0) (2024-05-22)

### feat

- **yapf-10122:** additional crop params ([e3827b3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e3827b307a788691d16a4af7cc711f6b255fd554))

# [1.59.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.58.0...@mxp/polaris-v1.59.0) (2024-05-21)

### feat

- **YAPF-11978:** back nav improvements ([a76fac1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a76fac148b72296c256246f655e58706d8e84b40))

# [1.58.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.57.1...@mxp/polaris-v1.58.0) (2024-05-20)

### feat

- edit-non-yield-param-refactoring ([#166](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/166)) ([73e46f7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/73e46f721aa1a64b620bed947063d81a75e808bd))

## [1.57.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.57.0...@mxp/polaris-v1.57.1) (2024-05-17)

### style

- add pointer cursor to active validate switch btn ([#167](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/167)) ([e84b093](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e84b09365954285a2285689cf1fd6cf58eea1107))

# [1.57.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.56.1...@mxp/polaris-v1.57.0) (2024-05-14)

### feat

- pre-post-crop-helper-text-fix ([#165](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/165)) ([7e416bc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7e416bc01f2b656f84f2731f7aab6b06874d8448))

## [1.56.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.56.0...@mxp/polaris-v1.56.1) (2024-05-14)

### fix

- change selector in Snackbar ([#164](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/164)) ([333c259](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/333c259d24f988204f4de61f3e83dee8b3f4d04f))

# [1.56.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.55.0...@mxp/polaris-v1.56.0) (2024-05-14)

### feat

- YAPF-11009-edit-non-yield-param-fix ([#163](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/163)) ([acd18be](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/acd18be98b3cbd054efbbb2e85753a9d55775f0a))

# [1.55.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.54.0...@mxp/polaris-v1.55.0) (2024-05-14)

### feat

- YAPF-11009-edit-non-yield-parameters ([#159](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/159)) ([385741d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/385741d820c94bc8e46ef4953b9952bda9795622))

# [1.54.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.6...@mxp/polaris-v1.54.0) (2024-05-13)

### feat

- add close onclick of snackbar ([#162](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/162)) ([2e24816](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2e248165ec0fd50c8b2db5a21522975166a7579b))

## [1.53.6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.5...@mxp/polaris-v1.53.6) (2024-05-13)

### fix

- ahua error on passing undefined ([#161](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/161)) ([5ff91f7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5ff91f783915c9bc6019fe59046d16d61d72c1cc))

## [1.53.5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.4...@mxp/polaris-v1.53.5) (2024-05-13)

### fix

- change test selector ([#160](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/160)) ([5eb5b3d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5eb5b3de57296d1fc79209e6c5ceef92b16928e8))

## [1.53.4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.3...@mxp/polaris-v1.53.4) (2024-05-09)

### fix

- YAPF-10964 address QA comments ([#157](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/157)) ([647026c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/647026c9b574afa2f810f01038638ad75409a48b))

## [1.53.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.2...@mxp/polaris-v1.53.3) (2024-05-09)

### fix

- clear selected tab when configure is pressed ([#158](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/158)) ([1b56b6f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1b56b6f98ae0044929dd86a6bfbf013b7a979c4b))

## [1.53.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.1...@mxp/polaris-v1.53.2) (2024-05-09)

### fix

- YAPF-11792 handle tab selection on refresh ([#156](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/156)) ([36aa382](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/36aa38248ed9cd5ec24fa5c5801ac04cb5d7bb54))

## [1.53.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.53.0...@mxp/polaris-v1.53.1) (2024-05-08)

### fix

- small style fixes ([#155](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/155)) ([2170f81](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2170f81ea6f2a764379111f0782c58ed8fcf52f6))

# [1.53.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.52.0...@mxp/polaris-v1.53.0) (2024-05-08)

### feat

- YAPF-11792 add data to one object in storage ([#154](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/154)) ([a15119d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a15119d18eb72dee95ccad6c0881f6c550d96c32))

### fix

- add test selectors and refactor ([#153](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/153)) ([e3ae023](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e3ae023e32c8c0cbb8abd5a54ca789285ea1aa04))

# [1.52.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.51.0...@mxp/polaris-v1.52.0) (2024-05-08)

### feat

- table-pagination-space-fix-stage ([#152](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/152)) ([c249ada](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c249adaaf9ddd4980b32a92bd5ba8a780830a822))

# [1.51.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.50.0...@mxp/polaris-v1.51.0) (2024-05-07)

### feat

- YAPF-11767-table-improvements ([#151](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/151)) ([8f63734](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8f637341d500ff482d4a04ea00d07115c861b4f5))

# [1.50.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.49.0...@mxp/polaris-v1.50.0) (2024-05-07)

### feat

- implement adding additional parameters to cropRegion ([#143](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/143)) ([afe1caa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/afe1caa400c2aa8af4cd462368c38f11da2d78d1))

# [1.49.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.48.1...@mxp/polaris-v1.49.0) (2024-04-30)

### feat

- YAPF-11202 expression builder view mode ([#144](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/144)) ([ef93a8c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ef93a8cbd83235f53de2d4f751e0a0f66d6daa3f))

## [1.48.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.48.0...@mxp/polaris-v1.48.1) (2024-04-30)

### fix

- YAPF-10796 change translations labels ([#149](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/149)) ([6804e4c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6804e4c7ce3a23956b67a93891c8a3b94664235f))

# [1.48.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.47.0...@mxp/polaris-v1.48.0) (2024-04-30)

### feat

- pre-post-crop-tooltip-fix ([#147](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/147)) ([eeff964](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/eeff9645bdc839746c5825ed304cbf77b63d7242))

# [1.47.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.46.0...@mxp/polaris-v1.47.0) (2024-04-29)

### feat

- **yapf-10972:** fix topbar back button ([e460946](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e4609461337129aaa98d22aaaf566ee125e2b488))

# [1.46.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.45.0...@mxp/polaris-v1.46.0) (2024-04-29)

### feat

- YAPF-10796 delete and change parameter level ([#140](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/140)) ([b41b233](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b41b233aee4836095f4b0486a678519a7ea0816a))

# [1.45.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.44.1...@mxp/polaris-v1.45.0) (2024-04-26)

### feat

- tooltip fix ([#141](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/141)) ([04a9dcf](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/04a9dcf9957bf6c480af2f815666c2caae0089bc))

## [1.44.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.44.0...@mxp/polaris-v1.44.1) (2024-04-25)

### fix

- add dynamic autoResetPageIndexRef ([#142](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/142)) ([e61b9c8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e61b9c80afc82ed9caf18e50d6b36c8ca33e5d7d))

# [1.44.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.43.1...@mxp/polaris-v1.44.0) (2024-04-25)

### feat

- YAPF-10549-edit-pre-post-crop-fix ([#139](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/139)) ([311a603](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/311a603b582f28941d9e5d25c097d5c2239f408f))

## [1.43.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.43.0...@mxp/polaris-v1.43.1) (2024-04-24)

### fix

- remove errors from console and fix issue with empty state ([#137](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/137)) ([b9613b1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b9613b1880df03f217f940a81237102302a26e9b))

# [1.43.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.42.0...@mxp/polaris-v1.43.0) (2024-04-24)

### feat

- **yapf-10972:** fix topbar title ([cf1a802](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cf1a8021d97790ef476356e82fd57bd0deda13bc))

# [1.42.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.41.0...@mxp/polaris-v1.42.0) (2024-04-23)

### feat

- YAPF-10549-edit-pre-post-crop ([#136](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/136)) ([bc2aa5a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bc2aa5a5d77436c73cf077c5a6f5d4dc4127a600))

# [1.41.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.40.0...@mxp/polaris-v1.41.0) (2024-04-22)

### feat

- YAPF-11095 add disabled name and type inputs to edit yield popup ([#135](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/135)) ([e156220](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e1562207c3555cb801145e9f6a88a289837d2748))

# [1.40.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.39.0...@mxp/polaris-v1.40.0) (2024-04-22)

### feat

- YAPF-10922 search and sort functionality refactor ([#134](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/134)) ([3a284ef](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/3a284ef4e1c2691d9684e26cfe224fc83eda1429))

# [1.39.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.38.0...@mxp/polaris-v1.39.0) (2024-04-22)

### feat

- YAPF-10546 implement delete additional parameters functionality ([#133](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/133)) ([8af9925](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8af99256a1c290a91291a204cbdab194b6b5ca33))

# [1.38.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.37.2...@mxp/polaris-v1.38.0) (2024-04-19)

### feat

- update crop demand analysis ([#131](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/131)) ([fc18fd0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fc18fd01344075222cd03e15b6aff306815f065e))

## [1.37.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.37.1...@mxp/polaris-v1.37.2) (2024-04-18)

### fix

- styling issue when no results ([#132](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/132)) ([a8fe7ca](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a8fe7ca4d030279a0b41a43b183a41d4e4a27e50))

## [1.37.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.37.0...@mxp/polaris-v1.37.1) (2024-04-17)

### fix

- design comments ([#129](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/129)) ([b633d91](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b633d91f803952b98560c53bddc87785a56be902))

# [1.37.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.36.1...@mxp/polaris-v1.37.0) (2024-04-16)

### feat

- YAPF-10922 search and sort teable ([#109](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/109)) ([e6901a8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e6901a8d3d29da84cf4a81066bd16a60e864d436))

## [1.36.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.36.0...@mxp/polaris-v1.36.1) (2024-04-15)

### refactor

- empty translation key bug, delete unfinish test ([#128](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/128)) ([330f10f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/330f10fd38e01af9ad96be16c6e1462adb5fad52))

# [1.36.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.35.2...@mxp/polaris-v1.36.0) (2024-04-12)

### feat

- add pre post as first row and translations fix ([#125](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/125)) ([76aa075](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/76aa075ef8ec62c1299f747df86f645c79beeffb))

## [1.35.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.35.1...@mxp/polaris-v1.35.2) (2024-04-11)

### test

- enhance unit tests ([#126](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/126)) ([309fd61](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/309fd61e1b52fc0f7c112600a1640129e86eb963))

## [1.35.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.35.0...@mxp/polaris-v1.35.1) (2024-04-04)

### fix

- **yapf-10548:** change when empty state is shown, fix line height ([ec7f98b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ec7f98be9e9401fb045a17ee994f9e02cd46987c))

# [1.35.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.34.1...@mxp/polaris-v1.35.0) (2024-04-04)

### feat

- improve tests, add ellipsis and title to nutrient tile name ([#120](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/120)) ([af9659e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/af9659ec4a259e39a9d568f3bfb3ec5783145c72))

## [1.34.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.34.0...@mxp/polaris-v1.34.1) (2024-04-04)

### fix

- minor style observations ([#123](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/123)) ([100fd55](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/100fd5501ef886d3f5a4ba1b0b5051c8e6aa3e54))

# [1.34.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.33.1...@mxp/polaris-v1.34.0) (2024-04-03)

### feat

- refetching the lists after adding new pre/post crop ([#118](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/118)) ([6f90474](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6f904743283726ef6efe468c0f1ec95baa5f0ebd))

## [1.33.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.33.0...@mxp/polaris-v1.33.1) (2024-04-03)

### fix

- ragion change on country change ([#121](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/121)) ([e4eb394](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e4eb39472ececbb445aa840373d31b14ba5808fa))

# [1.33.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.32.0...@mxp/polaris-v1.33.0) (2024-04-02)

### feat

- YAPF-11021 analysis methods ([#116](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/116)) ([d47cba3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d47cba3659c308aa92a204c69635c7c218d33d75))

# [1.32.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.31.0...@mxp/polaris-v1.32.0) (2024-04-02)

### feat

- **yapf-10548:** fix refetch on add prepostcrop param ([6abb12c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/6abb12cd590d4f9fb99623752b4d034f0bb7f2e5))

# [1.31.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.30.0...@mxp/polaris-v1.31.0) (2024-04-02)

### feat

- **yapf-10548:** prepostcrop delete param ([f8daa3c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f8daa3c53f675bb819955d83f62bda352813acbb))

# [1.30.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.29.0...@mxp/polaris-v1.30.0) (2024-04-01)

### feat

- snackbar text fix ([#115](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/115)) ([086ee76](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/086ee760523a9c1ab7ec6ffe7a81989b818cafd8))
- YAPF-10248-add-pre-post-crop ([#108](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/108)) ([a643c09](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a643c096d85caa78f7d3be6e9c05eca466be5300))

# [1.29.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.28.3...@mxp/polaris-v1.29.0) (2024-04-01)

### feat

- YAPF-9922 nutrients list ([#110](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/110)) ([2e59669](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2e59669ebf8c23e42871530625042b56e1297b12))

## [1.28.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.28.2...@mxp/polaris-v1.28.3) (2024-03-29)

### fix

- add selectors ([#106](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/106)) ([0624943](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/06249436802c49a9f8ab114f75a6f24d47b590aa))

## [1.28.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.28.1...@mxp/polaris-v1.28.2) (2024-03-21)

### fix

- adjust initial default yieldUnitId logic ([#98](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/98)) ([e59263f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e59263f6befbe0cc5eafb96f4bce67ffe86da36e))

## [1.28.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.28.0...@mxp/polaris-v1.28.1) (2024-03-21)

### fix

- YAPF-10368 trim trailing dot ([#95](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/95)) ([a738d69](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a738d692dbf7e4d18a178a39b26495ec69e39665))

# [1.28.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.27.0...@mxp/polaris-v1.28.0) (2024-03-20)

### feat

- clear error state defaultValue for non-numeric values ([#92](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/92)) ([139fee8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/139fee8bba0ee17cc39f94dd84ede8987ef4687a))

# [1.27.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.26.0...@mxp/polaris-v1.27.0) (2024-03-20)

### feat

- add filtered units to crep settings ([#93](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/93)) ([5245c39](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/5245c39d74940d23ba5a544b997f88a98e3cce3f))

# [1.26.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.25.2...@mxp/polaris-v1.26.0) (2024-03-19)

### feat

- YAPF-10368 add calc parameters edit logic ([#84](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/84)) ([7996693](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7996693497d2ee6956b424f41578ea050606a5bb))

## [1.25.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.25.1...@mxp/polaris-v1.25.2) (2024-03-19)

### fix

- no-issue remove infinite loop on crop sequences ([#91](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/91)) ([d4c7fe4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/d4c7fe455a4eae6cc529099bcbea4c939f54e5a5))

## [1.25.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.25.0...@mxp/polaris-v1.25.1) (2024-03-19)

### style

- add fixed height of 56px to tr and collapsible ([#90](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/90)) ([f032bc8](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f032bc8c123a64d2c1e94582aafb29a94453d350))

# [1.25.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.24.3...@mxp/polaris-v1.25.0) (2024-03-19)

### feat

- YAPF-10444-pre-crop-and-post-crop-parameters ([#87](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/87)) ([e625029](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e625029e9fb27a774e8391c675fb935f355cb956))

## [1.24.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.24.2...@mxp/polaris-v1.24.3) (2024-03-18)

### fix

- /YAPF-9921 calculation parameteres ([#83](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/83)) ([68a347c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/68a347c756b45bda8636ed73359d0339e50dc42d))

## [1.24.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.24.1...@mxp/polaris-v1.24.2) (2024-03-18)

### fix

- empty state text opacity ([#89](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/89)) ([0d43c0e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/0d43c0eceb96b1c7412183d19d3d7097eaa5b1a4))

## [1.24.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.24.0...@mxp/polaris-v1.24.1) (2024-03-18)

### fix

- color opacity on table elements ([#88](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/88)) ([183a195](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/183a195c1c45904172499493b26bdac2080016a6))

# [1.24.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.23.0...@mxp/polaris-v1.24.0) (2024-03-18)

### feat

- YAPF-10445 additional parameters tab ([#80](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/80)) ([ee049ff](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ee049ffad2f42e8ffde2a6419357e42102ae86fb))

### fix

- YAPF-10971 loading skeleton in CNP ([#81](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/81)) ([8f963e6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8f963e6b0e567c68e28e3244d70f2de5f9685667))

# [1.23.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.22.1...@mxp/polaris-v1.23.0) (2024-03-14)

### feat

- YAPF-9921 calculation parameters ([#78](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/78)) ([faed1b6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/faed1b64f8202b0b7e49b835cf7da58e7a15227a))

## [1.22.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.22.0...@mxp/polaris-v1.22.1) (2024-03-14)

### fix

- refactoring ([#79](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/79)) ([ab9bccd](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ab9bccd1151828ee05937d25c4f512c8856b0dc8))

# [1.22.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.21.3...@mxp/polaris-v1.22.0) (2024-03-11)

### feat

- YAPF-table and collapsible reusable components ([#76](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/76)) ([1544308](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1544308a87cc376d55ec426cf3fd7aac2b1d7116))

## [1.21.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.21.2...@mxp/polaris-v1.21.3) (2024-03-05)

### fix

- growth scale name style ([#75](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/75)) ([20be2e7](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/20be2e72b118e37736a4a16b653eaba4b6134d74))

## [1.21.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.21.1...@mxp/polaris-v1.21.2) (2024-03-05)

### fix

- growth scale disable style ([#74](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/74)) ([fb0b8ac](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/fb0b8ac396a61f0f40657f0afe1088a17b5d418a))

## [1.21.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.21.0...@mxp/polaris-v1.21.1) (2024-03-05)

### fix

- configure home to not be subpage ([#73](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/73)) ([64ba557](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/64ba5575df203d8699aa4dea927dc2ce5c2aa582))

# [1.21.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.20.0...@mxp/polaris-v1.21.0) (2024-03-05)

### feat

- tab structure style fix ([#71](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/71)) ([be26cfa](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/be26cfac8dd44bb252e03e3562a41f06101e2999))

### fix

- **yapf-10245:** fix issue with leaf plan card ([#72](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/72)) ([a3c0c2f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a3c0c2fc84924201856f37d648f064ecd222f1e9))

# [1.20.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.19.0...@mxp/polaris-v1.20.0) (2024-03-05)

### feat

- YAPF-10244-edit-crop-setting ([#69](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/69)) ([acabf38](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/acabf38ff0a17923765e823e46d1eda79e42b9cd))

# [1.19.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.18.1...@mxp/polaris-v1.19.0) (2024-03-01)

### feat

- **YAPF-9920:** add plan configuration and tabs structure ([ee21045](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ee210456b6366636097dc05169e1837c25796863))

## [1.18.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.18.0...@mxp/polaris-v1.18.1) (2024-03-01)

### fix

- card space ([#70](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/70)) ([c3e7119](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c3e7119489d063f9e98f3f0cbfdb7590e9718a1c))

# [1.18.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.17.1...@mxp/polaris-v1.18.0) (2024-03-01)

### feat

- YAPF-10245 soil config validation ([#67](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/67)) ([bffdfa0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/bffdfa0cfed675c3fcc90c612909e47e05450ccf))

## [1.17.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.17.0...@mxp/polaris-v1.17.1) (2024-03-01)

### fix

- change split type to number ([#66](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/66)) ([44a1679](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/44a167960b6d75a54f321f6f6e1db142fc9c4c55))

# [1.17.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.16.3...@mxp/polaris-v1.17.0) (2024-03-01)

### feat

- YAPF-9917 CNPDetails page implementation ([#65](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/65)) ([82f67df](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/82f67df7e80898b4501bd5228692a14f46f36106))

## [1.16.3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.16.2...@mxp/polaris-v1.16.3) (2024-02-29)

### fix

- wrap Polaris widget with the providers ([#57](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/57)) ([8d082f3](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8d082f3f6faf5cf2860e454ef4b605682a7dcefe))

## [1.16.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.16.1...@mxp/polaris-v1.16.2) (2024-02-29)

### fix

- trigger commit ([#64](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/64)) ([48bddcc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/48bddccd754ff5415a7d597ad05e7d6f5c329d1a))

## [1.16.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.16.0...@mxp/polaris-v1.16.1) (2024-02-28)

### refactor

- YAPF-10844 usenavbar setters into custom hook ([#61](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/61)) ([122716f](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/122716f6575515a845f388f96232e6eedc51f301)), closes [#58](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/58)

### style

- YAPF-10814 backbutton right margin ([#63](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/63)) ([03f730b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/03f730b530c6582404d2a7c3f644ebfeec08bdbe))

# [1.16.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.15.0...@mxp/polaris-v1.16.0) (2024-02-27)

### feat

- YAPF-10420 go button ([#58](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/58)) ([2359994](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/23599941d4ec549c8989cdefa0b0c88aa02d6593))

# [1.15.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.14.2...@mxp/polaris-v1.15.0) (2024-02-26)

### feat

- **yapf-10609:** render link ([b783b01](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b783b01974389407846b1b5bf945e36671847c1a))

## [1.14.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.14.1...@mxp/polaris-v1.14.2) (2024-02-26)

### fix

- add right margin, delete unused component ([#60](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/60)) ([b06650a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/b06650a317677ceb16de28d7c1098c8e1db9b5e0))

## [1.14.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.14.0...@mxp/polaris-v1.14.1) (2024-02-22)

### fix

- no-jira global settings enable ([#51](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/51)) ([678ee91](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/678ee9157e9ee27099fef8c4d476bbc958474d21)), closes [#48](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/48)

# [1.14.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.13.0...@mxp/polaris-v1.14.0) (2024-02-22)

### feat

- merge development to master ([#49](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/49)) ([43d71eb](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/43d71eb3359a7927be8ae788db50cffe29a497a4)), closes [#48](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/48)

# [1.13.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.12.2...@mxp/polaris-v1.13.0) (2024-02-19)

### feat

- remove error state when popup is dismissed ([#45](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/45)) ([7433df5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7433df596f9ed61e1141360dc2fb2af764924c11))

## [1.12.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.12.1...@mxp/polaris-v1.12.2) (2024-02-16)

### fix

- YAPF-9918 feature dropdown style ([#44](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/44)) ([73fac5b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/73fac5b66152df18f89fe73f4b318b103d370af8))

## [1.12.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.12.0...@mxp/polaris-v1.12.1) (2024-02-16)

### style

- change padding to match design ([#43](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/43)) ([ede87a2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/ede87a21dcebd797216a1540ae81b39e0df94c6f))

# [1.12.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.11.0...@mxp/polaris-v1.12.0) (2024-02-16)

### feat

- YAPF-10557 add country select error state ([#40](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/40)) ([a9b7005](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a9b7005c7a8ab3781cb8687b5eea268af77db87d))

# [1.11.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.10.0...@mxp/polaris-v1.11.0) (2024-02-16)

### feat

- YAPF-9918 home page ([#36](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/36)) ([eae4b31](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/eae4b31d823fcd7bc77ffaf48c219aa2e02755c4))

# [1.10.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.9.0...@mxp/polaris-v1.10.0) (2024-02-15)

### feat

- YAPF-9913 move logout logic ([#35](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/35)) ([75199e6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/75199e660d38d8eeacfec4516beed31bfa66800b))

# [1.9.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.8.2...@mxp/polaris-v1.9.0) (2024-02-15)

### feat

- YAPF-9914 update location popup due to designer and QA feedback ([#38](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/38)) ([a1c1ce6](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/a1c1ce6c5d49f9a4b2e8e522b190f8285eb00328))

## [1.8.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.8.1...@mxp/polaris-v1.8.2) (2024-02-15)

### fix

- YAPF-9918 remove country error when updating sidebar ([#39](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/39)) ([c86cf84](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c86cf8472baf7481f96767b375e2c3dd08b71bc1))

## [1.8.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.8.0...@mxp/polaris-v1.8.1) (2024-02-15)

### fix

- YAPF-9918 crop dropdown data refactor ([#37](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/37)) ([cd1ff29](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/cd1ff29d52db18520f6a73beeb9d01d63d4dfa51))

# [1.8.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.7.0...@mxp/polaris-v1.8.0) (2024-02-15)

### feat

- side menu ([#23](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/23)) ([f8fed77](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f8fed77022f21bce85b3939ae9e3c1696fda1864)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.7.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.6.2...@mxp/polaris-v1.7.0) (2024-02-14)

### feat

- YAPF-9918 home screen ([f5ea745](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/f5ea7459b2a2c358cc102d0c9a6dea7680abdbc4)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

## [1.6.2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.6.1...@mxp/polaris-v1.6.2) (2024-02-12)

### fix

- YAPF-9913 display user pic from auth0, fallback change per design ([#21](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/21)) ([31125df](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/31125dfe3ee14ba8875f7ee50a6f13ba40db5a27))

## [1.6.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.6.0...@mxp/polaris-v1.6.1) (2024-02-12)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-polaris 1.0.0 [skip ci] ([7f59de0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7f59de0ca822300b2b2f8da43394d7cfd181078b)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.6.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.5.0...@mxp/polaris-v1.6.0) (2024-02-12)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-polaris 1.0.0 [skip ci] ([8b1e084](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8b1e084d18f26c3215c3011f0020e26f3ae3c82e)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

### feat

- **yapf-10609:** fix names inside libs/events ([1888b63](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1888b638ab28b1f983f66d563467ce2615228f94)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.5.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.4.0...@mxp/polaris-v1.5.0) (2024-02-12)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-das-agtech-web-experience-polaris 1.0.0 [skip ci] ([9a92062](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/9a92062f85b42a22ca93b43d2108ad436d0f7f0f)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)
- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-das-agtech-web-experience-polaris 1.0.0 [skip ci] ([e3bbf3e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/e3bbf3ed03b825710f274206818f4ee80a3887ef)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

### feat

- **yapf-10609:** fix urls ([1713f97](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1713f97a0d8d131fefc3b1e86d85293934f28fd0)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.4.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.3.1...@mxp/polaris-v1.4.0) (2024-02-12)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-das-agtech-web-experience-polaris 1.0.0 [skip ci] ([7e9405b](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7e9405b3305c99787a8fb259afeabce31bcb61bd)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

### feat

- **yapf-10609:** deployment test , remove console log and trigger pipeline ([048c7f2](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/048c7f25b34179a604cc19b91ff41c849b7409c9)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

## [1.3.1](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.3.0...@mxp/polaris-v1.3.1) (2024-02-09)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-das-agtech-web-experience-polaris 1.0.0 [skip ci] ([7dcaa6d](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7dcaa6de7460631d5fc038c75892fb24d1d53606)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.3.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.2.0...@mxp/polaris-v1.3.0) (2024-02-08)

### chore

- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-das-agtech-web-experience-polaris 1.0.0 [skip ci] ([69afdf4](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/69afdf494636419088f29ae86f52dae065f12118)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

### feat

- YAPF-9913 topbar ([#11](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/11)) ([2a8f962](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/2a8f962ac083e9d28bd5f2527d46c525a43781f9)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.2.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.1.0...@mxp/polaris-v1.2.0) (2024-02-07)

### feat

- **yapf-10609:** change repo url in package json ([#18](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/18)) ([27f656a](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/27f656a0028e5287083fa02367f10a0e1a27d7ad)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)
- **yapf-10609:** test to bump version ([#17](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/17)) ([beacc2c](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/beacc2cd37229fd007c85b348985cdd2ded9c96e)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# [1.1.0](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/compare/@mxp/polaris-v1.0.0...@mxp/polaris-v1.1.0) (2024-02-06)

### feat

- **yapf-10609:** add readableName to widgetrc ([32fd3a5](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/32fd3a5e5ca97d361f6483927a72485a0ccbfe7f)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)
- **yapf-10609:** change location array element value ([8be2722](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/8be27225832dd6570e6db56de0b6130a31b339f4)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

# 1.0.0 (2024-02-06)

### chore

- merge development to master ([#13](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/13)) ([1979f8e](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/1979f8efc4f5747382b7c2df4dece0188d30d1ed)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)
- **release:** [@yaradigitallabs](https://github.com/yaradigitallabs)/mxp-messages-das-agtech-web-experience-polaris 1.0.0 [skip ci] ([7b883cc](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/7b883cc4f25c2a587031e11905f460e1e4fead10)), closes [#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14) [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)

### feat

- **YAPF-10609:** fix repo urls ([#14](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/14)) ([c18eb10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/commit/c18eb10ea354df97fc68e1d297015317bfa6b823)), closes [#9](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/9) [#10](https://github.com/yaradigitallabs/das-agtech-web-experience-polaris/issues/10)
