/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
import {
  AnalysisConfiguration,
  BaseUnit,
  Country,
  CropDemandAnalysis,
  CropDescription,
  CropRegion,
  CropSettings,
  CropSubClass,
  Feature,
  DemandCalculation,
  DemandCalculationModule,
  FertigationNutrientWithNutrientForms,
  MMMValidation,
  ModuleNameToAccordionNutrientState,
  Nutrient,
  OrganicFertiliserConfigurationResponse,
  OrganicFertiliserConfigurationTypes,
  PlanValidation,
  ProductRecommendationApplicationTypes,
  Region,
  SelectedFeatureNutrients,
} from '@common/types';

import React, { useContext, createContext, ReactNode, useEffect, useReducer, useMemo } from 'react';
import { localStorageManager, sessionStorageManager } from '@src/utils/storage';
import { useFetchCountriesData } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import { ACTION_TYPES, appReducer } from './appReducer';

// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const initialIsCoachmarkVisible = localStorageManager.getItem<boolean>('coachmarkVisible', true)!;

export interface AppContext {
  selectedCountry: Country | null;
  selectedRegion: Region | null;
  selectedCrop: CropSubClass | null;
  selectedFeature: Feature | null;
  selectedCountryUnits: BaseUnit[] | null;
  countries: Country[] | [];
  regions: Region[] | [];
  crops: CropSubClass[] | [];
  features: Feature[] | [];
  isCoachmarkVisible: boolean;
  cropRegion: CropRegion | null;
  selectedCropDescription: CropDescription | null;
  selectedPlanValidation: PlanValidation | null;
  selectedNutrient: Nutrient | null;
  selectedFeatureNutrients: SelectedFeatureNutrients | null;
  selectedFeatureUnitSettings: CropSettings | null;
  selectedMMMValidation: MMMValidation | null;
  selectedPlanConfigTab: string | null;
  cropDemandAnalyses: CropDemandAnalysis[] | [];
  analysisConfigurations: AnalysisConfiguration[] | [];
  organicFertiliserConfigurations: OrganicFertiliserConfigurationResponse;
  demandCalculations: DemandCalculation[] | [];
  demandCalculationAccordion: ModuleNameToAccordionNutrientState | null;
  selectedAppType: ProductRecommendationApplicationTypes | null;
  fertigationDemandCalculationNutrientsWithNutrientForms: FertigationNutrientWithNutrientForms | null;
}
export interface AppProviderMethods {
  setCountry: (data: Country) => void;
  setRegion: (data: Region | null) => void;
  setSelectedCountryUnits: (data: BaseUnit[] | null) => void;
  setCrop: (data: CropSubClass | null) => void;
  setFeature: (data: Feature | null) => void;
  setIsCoachmarkVisible: (isVisible: boolean) => void;
  setCropRegion: (data: CropRegion | null) => void;
  setCropDescription: (data: CropDescription | null) => void;
  setPlanValidation: (data: PlanValidation | null) => void;
  setSelectedNutrient: (data: Nutrient | null) => void;
  setSelectedFeatureNutrients: (data: SelectedFeatureNutrients | null) => void;
  setSelectedFeatureUnitSettings: (data: CropSettings | null) => void;
  setSelectedMMMValidation: (data: MMMValidation | null) => void;
  setSelectedPlanConfigTab: (tab: string | null) => void;
  setCropDemandAnalyses: (data: CropDemandAnalysis[] | null) => void;
  updateCropDemandAnalyses: (data: CropDemandAnalysis) => void;
  filterCropDemandAnalyses: (data: CropDemandAnalysis[]) => void;
  addCropDemandAnalyses: (data: CropDemandAnalysis) => void;
  setAnalysisConfigurations: (data: AnalysisConfiguration[] | null) => void;
  updateAnalysisConfigurations: (data: AnalysisConfiguration) => void;
  filterAnalysisConfigurations: (data: AnalysisConfiguration[] | string[]) => void;
  addAnalysisConfigurations: (data: AnalysisConfiguration) => void;
  setOrganicFertiliserConfigurations: (data: OrganicFertiliserConfigurationResponse | null) => void;
  updateOrganicFertiliserConfigurations: (data: OrganicFertiliserConfigurationTypes) => void;
  filterOrganicFertiliserConfigurations: (
    data: OrganicFertiliserConfigurationResponse | string[],
  ) => void;
  addOrganicFertiliserConfigurations: (data: OrganicFertiliserConfigurationTypes) => void;
  setDemandCalculations: (data: DemandCalculation[]) => void;
  updateDemandCalculationModule: (data: DemandCalculationModule) => void;
  setDemandCalculationAccordion: (data: ModuleNameToAccordionNutrientState | null) => void;
  setSelectedAppType: (data: ProductRecommendationApplicationTypes | null) => void;
  setFertigationDemandCalculationNutrientsWithNutrientForms: (
    data: FertigationNutrientWithNutrientForms | null,
  ) => void;
}

export type AppContextWithMethods = AppContext & {
  methods: AppProviderMethods;
  dispatch: React.Dispatch<any>;
};

const initialAppContext: AppContextWithMethods = {
  selectedCountry: localStorageManager.getItem('selectedCountry') || null,
  selectedRegion: localStorageManager.getItem('selectedRegion') || null,
  selectedCountryUnits: localStorageManager.getItem('selectedCountryUnits') || null,
  selectedCrop: null,
  selectedFeature: null,
  cropRegion: null,
  selectedCropDescription: null,
  countries: [],
  regions: [],
  crops: [],
  features: [],
  isCoachmarkVisible: initialIsCoachmarkVisible,
  selectedPlanValidation: null,
  selectedNutrient: null,
  selectedFeatureNutrients: null,
  selectedFeatureUnitSettings: null,
  selectedMMMValidation: null,
  selectedPlanConfigTab: null,
  cropDemandAnalyses: [],
  analysisConfigurations: [],
  demandCalculations: [],
  demandCalculationAccordion: null,
  organicFertiliserConfigurations: [],
  selectedAppType: ProductRecommendationApplicationTypes.SOIL,
  fertigationDemandCalculationNutrientsWithNutrientForms: null,
  methods: {
    setCountry: () => null,
    setRegion: () => null,
    setSelectedCountryUnits: () => null,
    setCrop: () => null,
    setFeature: () => null,
    setIsCoachmarkVisible: () => null,
    setCropRegion: () => null,
    setCropDescription: () => null,
    setPlanValidation: () => null,
    setSelectedNutrient: () => null,
    setSelectedFeatureNutrients: () => null,
    setSelectedFeatureUnitSettings: () => null,
    setSelectedMMMValidation: () => null,
    setSelectedPlanConfigTab: () => null,
    setCropDemandAnalyses: () => null,
    updateCropDemandAnalyses: () => null,
    filterCropDemandAnalyses: () => null,
    addCropDemandAnalyses: () => null,
    setAnalysisConfigurations: () => null,
    updateAnalysisConfigurations: () => null,
    filterAnalysisConfigurations: () => null,
    addAnalysisConfigurations: () => null,
    setOrganicFertiliserConfigurations: () => null,
    updateOrganicFertiliserConfigurations: () => null,
    filterOrganicFertiliserConfigurations: () => null,
    addOrganicFertiliserConfigurations: () => null,
    setDemandCalculations: () => null,
    updateDemandCalculationModule: () => null,
    setDemandCalculationAccordion: () => null,
    setSelectedAppType: () => null,
    setFertigationDemandCalculationNutrientsWithNutrientForms: () => null,
  },
  dispatch: () => null,
};

interface AppProviderProps {
  children: ReactNode;
}

const AppContext = createContext<
  AppContext & { methods: AppProviderMethods; dispatch: React.Dispatch<any> }
>(initialAppContext);

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [appContext, dispatch] = useReducer(
    appReducer,
    sessionStorageManager.getItem('appContext') || initialAppContext,
  );
  const { trigger } = useFetchCountriesData();
  useEffect(() => {
    sessionStorageManager.setItem('appContext', appContext);
  }, [appContext]);

  useEffect(() => {
    methods.fetchAndSetCountries();
  }, []);

  const methods = useMemo(
    () => ({
      setCountry: (data: Country | null) => {
        dispatch({ type: ACTION_TYPES.SET_COUNTRY, payload: data });
        localStorageManager.setItem('selectedCountry', data);
      },
      setRegion: (data: Region | null) => {
        dispatch({ type: ACTION_TYPES.SET_REGION, payload: data });
        localStorageManager.setItem('selectedRegion', data);
      },
      setSelectedCountryUnits: (data: BaseUnit[] | null) => {
        dispatch({ type: ACTION_TYPES.SET_SELECTED_COUNTRY_UNITS, payload: data });
        localStorageManager.setItem('selectedCountryUnits', data);
      },
      setCrop: (data: CropSubClass | null) =>
        dispatch({ type: ACTION_TYPES.SET_CROP, payload: data }),
      setFeature: (data: Feature | null) =>
        dispatch({ type: ACTION_TYPES.SET_FEATURE, payload: data }),
      setIsCoachmarkVisible: (isVisible: boolean) => {
        localStorageManager.setItem('coachmarkVisible', isVisible);
        dispatch({
          type: ACTION_TYPES.SET_IS_COACHMARK_VISIBLE,
          payload: isVisible,
        });
      },
      setCropRegion: (data: CropRegion | null) =>
        dispatch({ type: ACTION_TYPES.SET_CROP_REGION, payload: data }),
      setCropDescription: (data: CropDescription | null) =>
        dispatch({ type: ACTION_TYPES.SET_CROP_DESCRIPTION, payload: data }),
      setPlanValidation: (data: PlanValidation | null) =>
        dispatch({ type: ACTION_TYPES.SET_PLAN_VALIDATION, payload: data }),
      setSelectedNutrient: (data: Nutrient | null) =>
        dispatch({ type: ACTION_TYPES.SET_SELECTED_NUTRIENT, payload: data }),
      setSelectedFeatureNutrients: (data: SelectedFeatureNutrients | null) =>
        dispatch({
          type: ACTION_TYPES.SET_SELECTED_FEATURE_NUTRIENTS,
          payload: data,
        }),
      setSelectedFeatureUnitSettings: (data: CropSettings | null) =>
        dispatch({
          type: ACTION_TYPES.SET_SELECTED_FEATURE_UNIT_SETTINGS,
          payload: data,
        }),
      setSelectedMMMValidation: (data: MMMValidation | null) =>
        dispatch({
          type: ACTION_TYPES.SET_SELECTED_MMM_VALIDATION,
          payload: data,
        }),
      setSelectedPlanConfigTab: (tab: string | null) =>
        dispatch({
          type: ACTION_TYPES.SET_SELECTED_PLAN_CONFIG_TAB,
          payload: tab,
        }),
      fetchAndSetCountries: async () => {
        const countriesData = await trigger({
          method: METHOD.POST,
          body: JSON.stringify({
            sorting: [{ column: 'name', orderDirection: 'ASC' }],
          }),
        });
        dispatch({
          type: ACTION_TYPES.SET_COUNTRIES,
          payload: countriesData?.entities,
        });
      },
      setCropDemandAnalyses: (data: CropDemandAnalysis[] | null) =>
        dispatch({
          type: ACTION_TYPES.SET_CROP_DEMAND_ANALYSES,
          payload: data,
        }),
      updateCropDemandAnalyses: (data: CropDemandAnalysis) => {
        dispatch({
          type: ACTION_TYPES.UPDATE_CROP_DEMAND_ANALYSES,
          payload: data,
        });
      },
      filterCropDemandAnalyses: (data: CropDemandAnalysis[]) => {
        dispatch({
          type: ACTION_TYPES.FILTER_CROP_DEMAND_ANALYSES,
          payload: data,
        });
      },
      addCropDemandAnalyses: (data: CropDemandAnalysis) => {
        dispatch({
          type: ACTION_TYPES.ADD_CROP_DEMAND_ANALYSES,
          payload: data,
        });
      },
      setAnalysisConfigurations: (data: AnalysisConfiguration[] | null) =>
        dispatch({
          type: ACTION_TYPES.SET_ANALYSIS_CONFIGURATIONS,
          payload: data,
        }),
      updateAnalysisConfigurations: (data: AnalysisConfiguration) => {
        dispatch({
          type: ACTION_TYPES.UPDATE_ANALYSIS_CONFIGURATIONS,
          payload: data,
        });
      },
      filterAnalysisConfigurations: (data: AnalysisConfiguration[] | string[]) => {
        dispatch({
          type: ACTION_TYPES.FILTER_ANALYSIS_CONFIGURATIONS,
          payload: data,
        });
      },
      addAnalysisConfigurations: (data: AnalysisConfiguration) => {
        dispatch({
          type: ACTION_TYPES.ADD_ANALYSIS_CONFIGURATIONS,
          payload: data,
        });
      },
      setOrganicFertiliserConfigurations: (data: OrganicFertiliserConfigurationResponse | null) =>
        dispatch({
          type: ACTION_TYPES.SET_ORGANIC_FERTILISER_CONFIGURATIONS,
          payload: data,
        }),
      updateOrganicFertiliserConfigurations: (data: OrganicFertiliserConfigurationTypes) => {
        dispatch({
          type: ACTION_TYPES.UPDATE_ORGANIC_FERTILISER_CONFIGURATIONS,
          payload: data,
        });
      },
      filterOrganicFertiliserConfigurations: (
        data: OrganicFertiliserConfigurationResponse | string[],
      ) => {
        dispatch({
          type: ACTION_TYPES.FILTER_ORGANIC_FERTILISER_CONFIGURATIONS,
          payload: data,
        });
      },
      addOrganicFertiliserConfigurations: (data: OrganicFertiliserConfigurationTypes) => {
        dispatch({
          type: ACTION_TYPES.ADD_ORGANIC_FERTILISER_CONFIGURATIONS,
          payload: data,
        });
      },

      setDemandCalculations: (data: DemandCalculation[] | null) =>
        dispatch({
          type: ACTION_TYPES.SET_DEMAND_CALCULATIONS,
          payload: data,
        }),
      updateDemandCalculationModule: (data: DemandCalculationModule) => {
        dispatch({
          type: ACTION_TYPES.UPDATE_DEMAND_CALCULATION_MODULE,
          payload: data,
        });
      },
      setDemandCalculationAccordion: (data: ModuleNameToAccordionNutrientState | null) => {
        dispatch({
          type: ACTION_TYPES.SET_DEMAND_CALCULATION_MODULES_ACCORDION,
          payload: data,
        });
      },
      setSelectedAppType: (data: ProductRecommendationApplicationTypes | null) => {
        dispatch({
          type: ACTION_TYPES.SET_SELECTED_APP_TYPE,
          payload: data,
        });
      },
      setFertigationDemandCalculationNutrientsWithNutrientForms: (
        data: FertigationNutrientWithNutrientForms | null,
      ) => {
        dispatch({
          type: ACTION_TYPES.SET_FERTIGATION_DC_NUTRIENTS_WITH_NUTRIENT_FORMS,
          payload: data,
        });
      },
    }),
    [],
  );
  const contextValue = useMemo(() => {
    return {
      ...appContext,
      methods,
      dispatch,
    };
  }, [appContext, methods, dispatch]);

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};

export const useAppContext = (): AppContext & {
  methods: AppProviderMethods;
  dispatch: React.Dispatch<any>;
} => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
