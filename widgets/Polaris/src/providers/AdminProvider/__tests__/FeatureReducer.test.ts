import { FeatureReducer, GLOBAL_FEATURE_ACTION_TYPES } from '../FeatureReducer';
import { AdminAction, Feature, FeatureAction } from '../AdminProvider.types';

describe('FeatureReducer', () => {
  const mockFeature: Feature = {
    id: '1',
    name: 'Test Feature',
    displayName: 'Test Feature Display',
    group: 'Test Group',
    code: 'TF001',
    modifiedBy: 'User1',
    created: new Date().toUTCString(),
    modified: new Date().toUTCString(),
    deleted: null,
  };

  const mockFeature2: Feature = {
    id: '2',
    name: 'Test Feature 2',
    displayName: 'Test Feature 2 Display',
    group: 'Test Group 2',
    code: 'TF002',
    modifiedBy: 'User2',
    created: new Date().toUTCString(),
    modified: new Date().toUTCString(),
    deleted: null,
  };

  const initialState: Feature[] = [];

  test('should return initial state for unknown action type', () => {
    const unknownAction: AdminAction<Feature, unknown> = {
      type: 'UNKNOWN_ACTION',
      payload: mockFeature,
    };

    const newState = FeatureReducer(initialState, unknownAction);
    expect(newState).toBe(initialState);
  });

  test('should initialize features with INIT action', () => {
    const features = [mockFeature, mockFeature2];
    const initAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.INIT_FEATURE,
      payload: features,
    };

    const newState = FeatureReducer(initialState, initAction);
    expect(newState).toEqual(features);
  });

  test('should add new feature with ADD action', () => {
    const addAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE,
      payload: mockFeature,
    };

    const newState = FeatureReducer(initialState, addAction);
    expect(newState).toEqual([mockFeature]);
  });

  test('should update existing feature with UPDATE action', () => {
    const initialStateWithFeature = [mockFeature];
    const updatedFeature = { ...mockFeature, name: 'Updated Feature' };

    const updateAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.UPDATE_FEATURE,
      payload: updatedFeature,
    };

    const newState = FeatureReducer(initialStateWithFeature, updateAction);
    expect(newState).toEqual([updatedFeature]);
  });

  test('should not update non-existing feature with UPDATE action', () => {
    const initialStateWithFeature = [mockFeature];
    const nonExistingFeature = {
      ...mockFeature2,
      name: 'Non-existing Feature',
    };

    const updateAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.UPDATE_FEATURE,
      payload: nonExistingFeature,
    };

    const newState = FeatureReducer(initialStateWithFeature, updateAction);
    expect(newState).toEqual(initialStateWithFeature);
  });

  test('should delete feature with DELETE action', () => {
    const initialStateWithFeatures = [mockFeature, mockFeature2];

    const deleteAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.DELETE_FEATURE,
      payload: mockFeature,
    };

    const newState = FeatureReducer(initialStateWithFeatures, deleteAction);
    expect(newState).toEqual([mockFeature2]);
  });

  test('should not modify state when deleting non-existing feature', () => {
    const initialStateWithFeature = [mockFeature];
    const nonExistingFeature = { ...mockFeature2 };

    const deleteAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.DELETE_FEATURE,
      payload: nonExistingFeature,
    };

    const newState = FeatureReducer(initialStateWithFeature, deleteAction);
    expect(newState).toEqual(initialStateWithFeature);
  });

  test('should handle empty initial state', () => {
    const addAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE,
      payload: mockFeature,
    };

    const newState = FeatureReducer(undefined, addAction);
    expect(newState).toEqual([mockFeature]);
  });
});
