import { adminReducer } from '../AdminReducer';
import { FeatureReducer, PartnerReducer } from '../';
import { GLOBAL_FEATURE_ACTION_TYPES } from '../FeatureReducer';
import { GLOBAL_PARTNER_ACTION_TYPES } from '../PartnerReducer';
import { AdminAction, FeatureAction, GlobalState, PartnerAction } from '../AdminProvider.types';

// Mock the reducers
jest.mock('../', () => ({
  FeatureReducer: jest.fn((state) => state),
  PartnerReducer: jest.fn((state) => state),
}));

describe('adminReducer', () => {
  // Setup mock data
  const mockInitialState: GlobalState = {
    partners: [],
    features: [],
    resources: [],
    applications: [],
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockPartner: any = {
    id: '1',
    name: 'Test Partner',
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockFeature: any = {
    id: '1',
    name: 'Test Feature',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should call both reducers with correct arguments', () => {
    const mockAction: AdminAction<unknown, unknown> = {
      type: 'TEST_ACTION',
      payload: null,
    };

    adminReducer(mockInitialState, mockAction);

    expect(PartnerReducer).toHaveBeenCalledTimes(0);
    expect(FeatureReducer).toHaveBeenCalledTimes(0);
  });

  test('should handle partner actions correctly', () => {
    const partnerAction: PartnerAction = {
      type: GLOBAL_PARTNER_ACTION_TYPES.ADD_PARTNER,
      payload: mockPartner,
    };

    adminReducer(mockInitialState, partnerAction);

    expect(PartnerReducer).toHaveBeenCalledWith(mockInitialState.partners, partnerAction);
  });

  test('should handle multiple partners action correctly', () => {
    const partnersAction: PartnerAction = {
      type: GLOBAL_PARTNER_ACTION_TYPES.ADD_PARTNER,
      payload: [mockPartner, mockPartner],
    };

    adminReducer(mockInitialState, partnersAction);

    expect(PartnerReducer).toHaveBeenCalledWith(mockInitialState.partners, partnersAction);
  });

  test('should handle feature actions correctly', () => {
    const featureAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE,
      payload: mockFeature,
    };

    adminReducer(mockInitialState, featureAction);

    expect(FeatureReducer).toHaveBeenCalledWith(mockInitialState.features, featureAction);
  });

  test('should handle multiple features action correctly', () => {
    const featuresAction: FeatureAction = {
      type: GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE,
      payload: [mockFeature, mockFeature],
    };

    adminReducer(mockInitialState, featuresAction);

    expect(FeatureReducer).toHaveBeenCalledWith(mockInitialState.features, featuresAction);
  });

  test('should return updated state object', () => {
    const mockAction: AdminAction<unknown, unknown> = {
      type: 'TEST_ACTION',
      payload: null,
    };

    const result = adminReducer(mockInitialState, mockAction);

    expect(result).toEqual({
      partners: mockInitialState.partners,
      features: mockInitialState.features,
      resources: mockInitialState.resources,
      applications: mockInitialState.applications,
    });
  });

  test('should maintain state structure', () => {
    const mockAction: AdminAction<unknown, unknown> = {
      type: 'TEST_ACTION',
      payload: null,
    };

    const result = adminReducer(mockInitialState, mockAction);

    expect(result).toHaveProperty('partners');
    expect(result).toHaveProperty('features');
  });
});
