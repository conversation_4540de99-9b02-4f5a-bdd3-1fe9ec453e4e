import React, { useContext } from 'react';
import { render, screen } from '@testing-library/react';
import { AdminContext, AdminProvider } from '../AdminProvider';
import { GLOBAL_FEATURE_ACTION_TYPES } from '../FeatureReducer';
import { renderHook } from '@testing-library/react-hooks';

// Mock child component to test context values
const TestConsumer = () => {
  const { state, dispatch } = useContext(AdminContext);
  return (
    <div>
      <div data-cy='partner-count'>{state.partners.length}</div>
      <div data-cy='feature-count'>{state.features.length}</div>
      <button
        data-cy='add-feature'
        onClick={() =>
          dispatch({
            type: GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE,
            payload: {
              id: '1',
              name: 'Test Feature',
              displayName: 'Test Feature',
              group: '',
              code: '',
              modifiedBy: '',
              modified: '',
              created: '',
              deleted: null,
            },
          })
        }
      >
        Add Feature
      </button>
    </div>
  );
};

describe('AdminProvider', () => {
  test('renders children without crashing', () => {
    render(
      <AdminProvider>
        <div data-cy='child'>Child Component</div>
      </AdminProvider>,
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  test('provides initial state correctly', () => {
    render(
      <AdminProvider>
        <TestConsumer />
      </AdminProvider>,
    );

    expect(screen.getByTestId('partner-count')).toHaveTextContent('0');
    expect(screen.getByTestId('feature-count')).toHaveTextContent('0');
  });

  test('provides dispatch function', () => {
    const { result } = renderHook(() => useContext(AdminContext), {
      wrapper: AdminProvider,
    });

    expect(result.current.dispatch).toBeDefined();
    expect(typeof result.current.dispatch).toBe('function');
  });

  test('maintains context value across re-renders', async () => {
    const { rerender } = render(
      <AdminProvider>
        <TestConsumer />
      </AdminProvider>,
    );

    expect(screen.getByTestId('partner-count')).toHaveTextContent('0');

    rerender(
      <AdminProvider>
        <TestConsumer />
      </AdminProvider>,
    );

    expect(screen.getByTestId('partner-count')).toHaveTextContent('0');
  });

  test('context value matches expected shape', () => {
    let contextValue;
    render(
      <AdminProvider>
        <AdminContext.Consumer>
          {(value) => {
            contextValue = value;
            return null;
          }}
        </AdminContext.Consumer>
      </AdminProvider>,
    );

    expect(contextValue).toEqual({
      state: {
        partners: [],
        features: [],
        resources: [],
        applications: [],
      },
      dispatch: expect.any(Function),
    });
  });

  test('multiple children rendering', () => {
    render(
      <AdminProvider>
        <div data-cy='child-1'>Child 1</div>
        <div data-cy='child-2'>Child 2</div>
      </AdminProvider>,
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
  });

  test('nested providers maintain separate contexts', () => {
    render(
      <AdminProvider>
        <div data-cy='outer'>
          <AdminProvider>
            <div data-cy='inner'>
              <TestConsumer />
            </div>
          </AdminProvider>
        </div>
      </AdminProvider>,
    );

    expect(screen.getByTestId('outer')).toBeInTheDocument();
    expect(screen.getByTestId('inner')).toBeInTheDocument();
  });
});
