import { Application, ApplicationAction } from './AdminProvider.types';

// Action Types
export const GLOBAL_APPLICATION_ACTION_TYPES = {
  INIT_APPLICATION: 'INIT_APPLICATION',
  ADD_APPLICATION: 'ADD_APPLICATION',
  UPDATE_APPLICATION: 'UPDATE_APPLICATION',
  DELETE_APPLICATION: 'DELETE_APPLICATION',
} as const;

/**
 * Reducer for Application
 * @param state  - Initial State
 * @param action - Action to be performed
 * @returns - Updated State
 */
export const ApplicationReducer = (
  state: Application[] = [],
  action: ApplicationAction,
): Application[] => {
  switch (action.type) {
    case GLOBAL_APPLICATION_ACTION_TYPES.INIT_APPLICATION:
      return Array.isArray(action.payload) ? action.payload : state;
    case GLOBAL_APPLICATION_ACTION_TYPES.ADD_APPLICATION:
      if (!Array.isArray(action.payload)) {
        // Add new Feature in index[0] for better UX
        return [action.payload, ...state];
      }
      return state;
    case GLOBAL_APPLICATION_ACTION_TYPES.UPDATE_APPLICATION:
      return state.map((app) =>
        'id' in action.payload && app.id === action.payload.id ? action.payload : app,
      );
    case GLOBAL_APPLICATION_ACTION_TYPES.DELETE_APPLICATION:
      return state.filter((app) => 'id' in action.payload && app.id !== action.payload.id);
    default:
      return state;
  }
};
