import { GlobalState, AdminAction } from './AdminProvider.types';
import {
  getApplicationReducer,
  getFeatureReducer,
  getPartnerReducer,
  getResourceReducer,
} from './utils/adminProviderUtils';

/**
 * Admin Reducer
 * @param state  - Initial State
 * @param action - Action to be performed
 * @returns  - Updated State
 */
export const adminReducer = (state: GlobalState, action: AdminAction): GlobalState =>
  <GlobalState>{
    partners: getPartnerReducer(state, action) || [],
    features: getFeatureReducer(state, action) || [],
    resources: getResourceReducer(state, action) || [],
    applications: getApplicationReducer(state, action) || [],
  };
