import { Partner, PartnerAction } from './AdminProvider.types';

// Action Types
export const GLOBAL_PARTNER_ACTION_TYPES = {
  INIT_PARTNER: 'INIT_PARTNER',
  ADD_PARTNER: 'ADD_PARTNER',
  UPDATE_PARTNER: 'UPDATE_PARTNER',
  DELETE_PARTNER: 'DELETE_PARTNER',
} as const;
/**
 * Reducer for Partner
 * @param state  - Initial State
 * @param action - Action to be performed
 * @returns - Updated State
 */
export const PartnerReducer = (state: Partner[] = [], action: PartnerAction): Partner[] => {
  switch (action.type) {
    case GLOBAL_PARTNER_ACTION_TYPES.INIT_PARTNER:
      return Array.isArray(action.payload) ? action.payload : state;
    case GLOBAL_PARTNER_ACTION_TYPES.ADD_PARTNER:
      if (!Array.isArray(action.payload)) {
        return [...state, action.payload];
      }
      return state;
    case GLOBAL_PARTNER_ACTION_TYPES?.UPDATE_PARTNER:
      return state.map((partner) =>
        'id' in action.payload && partner.id === action.payload.id ? action.payload : partner,
      );
    case GLOBAL_PARTNER_ACTION_TYPES.DELETE_PARTNER:
      return state.filter((partner) => 'id' in action.payload && partner.id !== action.payload.id);
    default:
      return state;
  }
};
