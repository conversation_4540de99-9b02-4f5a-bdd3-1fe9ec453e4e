import { Resource, ResourceAction } from './AdminProvider.types';

// Action Types
export const GLOBAL_RESOURCE_ACTION_TYPES = {
  INIT_RESOURCE: 'INIT_RESOURCE',
  ADD_RESOURCE: 'ADD_RESOURCE',
  UPDATE_RESOURCE: 'UPDATE_RESOURCE',
  DELETE_RESOURCE: 'DELETE_RESOURCE',
} as const;

/**
 * Reducer for Resource
 * @param state  - Initial State
 * @param action - Action to be performed
 * @returns - Updated State
 */
export const ResourceReducer = (state: Resource[] = [], action: ResourceAction): Resource[] => {
  switch (action.type) {
    case GLOBAL_RESOURCE_ACTION_TYPES.INIT_RESOURCE:
      return Array.isArray(action.payload) ? action.payload : state;
    case GLOBAL_RESOURCE_ACTION_TYPES.ADD_RESOURCE:
      if (!Array.isArray(action.payload)) {
        return [...state, action.payload];
      }
      return state;
    case GLOBAL_RESOURCE_ACTION_TYPES.UPDATE_RESOURCE:
      return state.map((resource) =>
        'id' in action.payload && resource.id === action.payload.id ? action.payload : resource,
      );
    case GLOBAL_RESOURCE_ACTION_TYPES.DELETE_RESOURCE:
      return state.filter(
        (resource) => 'id' in action.payload && resource.id !== action.payload.id,
      );
    default:
      return state;
  }
};
