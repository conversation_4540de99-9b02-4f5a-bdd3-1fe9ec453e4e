import {
  GlobalState,
  AdminAction,
  PartnerReducer,
  FeatureReducer,
  ResourceReducer,
  ApplicationReducer,
} from '../';
import { GLOBAL_APPLICATION_ACTION_TYPES } from '../ApplicationReducer';
import { GLOBAL_FEATURE_ACTION_TYPES } from '../FeatureReducer';
import { GLOBAL_PARTNER_ACTION_TYPES } from '../PartnerReducer';
import { GLOBAL_RESOURCE_ACTION_TYPES } from '../ResourceReducer';

export const getPartnerReducer = (state: GlobalState, action: AdminAction) => {
  if (
    action.type === GLOBAL_PARTNER_ACTION_TYPES.INIT_PARTNER ||
    action.type === GLOBAL_PARTNER_ACTION_TYPES.ADD_PARTNER ||
    action.type === GLOBAL_PARTNER_ACTION_TYPES.UPDATE_PARTNER ||
    action.type === GLOBAL_PARTNER_ACTION_TYPES.DELETE_PARTNER
  ) {
    return PartnerReducer(state.partners, action);
  }
};
export const getFeatureReducer = (state: GlobalState, action: AdminAction) => {
  if (
    action.type === GLOBAL_FEATURE_ACTION_TYPES.INIT_FEATURE ||
    action.type === GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE ||
    action.type === GLOBAL_FEATURE_ACTION_TYPES.UPDATE_FEATURE ||
    action.type === GLOBAL_FEATURE_ACTION_TYPES.DELETE_FEATURE
  ) {
    return FeatureReducer(state.features, action);
  }
};
export const getResourceReducer = (state: GlobalState, action: AdminAction) => {
  if (
    action.type === GLOBAL_RESOURCE_ACTION_TYPES.INIT_RESOURCE ||
    action.type === GLOBAL_RESOURCE_ACTION_TYPES.ADD_RESOURCE ||
    action.type === GLOBAL_RESOURCE_ACTION_TYPES.UPDATE_RESOURCE ||
    action.type === GLOBAL_RESOURCE_ACTION_TYPES.DELETE_RESOURCE
  ) {
    return ResourceReducer(state.resources, action);
  }
};
export const getApplicationReducer = (state: GlobalState, action: AdminAction) => {
  if (
    action.type === GLOBAL_APPLICATION_ACTION_TYPES.INIT_APPLICATION ||
    action.type === GLOBAL_APPLICATION_ACTION_TYPES.ADD_APPLICATION ||
    action.type === GLOBAL_APPLICATION_ACTION_TYPES.UPDATE_APPLICATION ||
    action.type === GLOBAL_APPLICATION_ACTION_TYPES.DELETE_APPLICATION
  ) {
    return ApplicationReducer(state.applications, action);
  }
};
