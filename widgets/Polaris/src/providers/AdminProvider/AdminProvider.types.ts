import { Nullable } from '@common/types';
import { GLOBAL_RESOURCE_ACTION_TYPES } from './ResourceReducer';
import { GLOBAL_PARTNER_ACTION_TYPES } from './PartnerReducer';
import { GLOBAL_FEATURE_ACTION_TYPES } from './FeatureReducer';
import { Dispatch } from 'react';
import { GLOBAL_APPLICATION_ACTION_TYPES } from './ApplicationReducer';

export type Action<T, K> = {
  type: K;
  payload: T;
};

export type PartnerActionTypes = keyof typeof GLOBAL_PARTNER_ACTION_TYPES;
export type FeatureActionTypes = keyof typeof GLOBAL_FEATURE_ACTION_TYPES;
export type ResourceActionTypes = keyof typeof GLOBAL_RESOURCE_ACTION_TYPES;
export type ApplicationActionTypes = keyof typeof GLOBAL_APPLICATION_ACTION_TYPES;

export type PartnerAction = Action<Partner | Partner[], PartnerActionTypes>;
export type FeatureAction = Action<Feature | Feature[], FeatureActionTypes>;
export type ResourceAction = Action<Resource | Resource[], ResourceActionTypes>;
export type ApplicationAction = Action<Application | Application[], ApplicationActionTypes>;

export type AdminAction = PartnerAction | FeatureAction | ResourceAction | ApplicationAction;

type Name = {
  id: string;
  name: string;
};
export type Partner = {
  id: string;
  name: string;
  displayName: string;
  code: string;
  isInternal: boolean;
  countries: Name[] | null;
  capabilities: Name[] | null;
  created: string;
  modified: string;
  modifiedBy: string;
  deleted: string | null;
};

interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export interface Metadata {
  polarisId: string; // Polaris internal ID
  displayName: string;
  group: string;
  code: string;
}
interface Capability {
  id: string;
  name: string;
  metadata: Metadata;
}
interface Country {
  id: string;
  name: string;
}
interface AppMetadata {
  code: string;
  countries?: Country[] | null;
  polarisId: string;
  isInternal: boolean;
  displayName: string;
  capabilities?: Capability[] | null;
}
export interface Application extends BaseEntity {
  name: string;
  code: string;
  managedByAppId: string;
  metadata: AppMetadata;
  userDeletionTimeIntervalInDays: number;
  createdBy: string;
  updatedBy: string;
  deletedBy: Nullable<string>;
  deletedAt: Nullable<string>;
  clientId: string;
}

export type Feature = {
  id: string;
  name: string;
  displayName: string;
  group: string;
  code: string;
  modifiedBy: string;
  modified: string;
  created: string;
  deleted: string | null;
};
export type GlobalState = {
  partners: Partner[];
  features: Feature[];
  resources: Resource[];
  applications: Application[];
};
export type AdminContextType = {
  state: GlobalState;
  dispatch: Dispatch<AdminAction>;
};

export interface Resource {
  id: string; // UserMS internal resource.id
  name: string;
  metadata: Metadata;
  updatedAt: string;
  updatedBy: string;
  createdAt: string;
  deletedAt: string | null;
  deletedBy: string | null;
}
