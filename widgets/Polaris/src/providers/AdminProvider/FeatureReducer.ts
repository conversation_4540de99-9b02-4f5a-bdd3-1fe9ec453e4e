import { Feature, FeatureAction } from './AdminProvider.types';

// Action Types
export const GLOBAL_FEATURE_ACTION_TYPES = {
  INIT_FEATURE: 'INIT_FEATURE',
  ADD_FEATURE: 'ADD_FEATURE',
  UPDATE_FEATURE: 'UPDATE_FEATURE',
  DELETE_FEATURE: 'DELETE_FEATURE',
} as const;

/**
 * Reducer for Feature
 * @param state  - Initial State
 * @param action - Action to be performed
 * @returns - Updated State
 */
export const FeatureReducer = (state: Feature[] = [], action: FeatureAction): Feature[] => {
  switch (action.type) {
    case GLOBAL_FEATURE_ACTION_TYPES.INIT_FEATURE:
      return Array.isArray(action.payload) ? action.payload : state;
    case GLOBAL_FEATURE_ACTION_TYPES.ADD_FEATURE:
      if (!Array.isArray(action.payload)) {
        // Add new Feature in index[0] for better UX
        return [action.payload, ...state];
      }
      return state;
    case GLOBAL_FEATURE_ACTION_TYPES.UPDATE_FEATURE: {
      return state.map((feature) =>
        'id' in action.payload && feature.id === action.payload.id ? action.payload : feature,
      );
    }
    case GLOBAL_FEATURE_ACTION_TYPES.DELETE_FEATURE:
      return state.filter((feature) => 'id' in action.payload && feature.id !== action.payload.id);
    default:
      return state;
  }
};
