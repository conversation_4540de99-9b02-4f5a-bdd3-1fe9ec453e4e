import { useEffect, useState } from 'react';
import { BaseUnit, ErrorResponse, UnitCountry, UnitCountries } from '@common/types';
import useSWRMutation, { SWRMutationConfiguration } from 'swr/mutation';
import { useCustomFetcher } from '@libs/SWRProvider';
import { POLARIS_API_GW_ENDPOINTS } from '@common/constants';

type UseUnitCountriesReturn = {
  unitCountriesData: UnitCountry[] | undefined;
  trigger: <SWRData = UnitCountries>(
    extraArgument: RequestInit,
    options?: SWRMutationConfiguration<UnitCountries, ErrorResponse, RequestInit, string, SWRData>,
  ) => Promise<UnitCountries | undefined>;
  error: ErrorResponse | undefined;
  isMutating: boolean;
};

/**
 * Set unit by id
 * @param unitsData
 * @param isEditModalOpened
 * @param selectedUnitId
 * @returns
 */
export function useStateUnits(
  unitsData: BaseUnit[] | undefined,
  isEditModalOpened: boolean,
  selectedUnitId: string | undefined,
  autoSelect = false,
): {
  selectedUnit: BaseUnit | null;
  setSelectedUnit: React.Dispatch<BaseUnit | null>;
} {
  const [selectedUnit, setSelectedUnit] = useState<BaseUnit | null>(null);

  useEffect(() => {
    // Filter units by selected yieldBaseUnitId or demandBaseUnitId
    const unitData = unitsData?.find((u: BaseUnit) => u?.id === selectedUnitId);
    if (unitData) {
      setSelectedUnit(unitData);
    } else if (autoSelect && unitsData) {
      // Needed in Fertigation Plan crop settings Edit dialog
      setSelectedUnit(unitsData[0]);
    }
  }, [unitsData, isEditModalOpened]);

  return { selectedUnit, setSelectedUnit };
}

/**
 * A function to filter given units by specific unit tag
 * @param units
 * @param tag
 * @returns
 */
export const filterUnitsByTag = (units: BaseUnit[] | null, tag: string) => {
  return units ? units.filter((el) => el.tags.includes(tag)) : [];
};

/**
 * Hook to get all units for a nutrient element
 * @param nutrientElement
 * @param filteredUnits
 * @returns {BaseUnit[] | undefined}
 */
export const useGetAllElementUnits = (
  nutrientElement: string | undefined,
  filteredUnits: BaseUnit[] | null,
): BaseUnit[] | undefined => {
  if (nutrientElement)
    return filteredUnits?.filter(({ tags }) => {
      const unitTags = tags.split(',');

      return (
        unitTags.includes(nutrientElement) &&
        (['pH', 'SMP'].includes(nutrientElement) || !unitTags.includes(`${nutrientElement}Unit`))
      );
    });
};

/**
 * A function to filter given units by specific nutrient element name
 * @param units
 * @param nutrientElement
 * @returns {BaseUnit[] | undefined}
 */
export const filterUnitsByNutrientElementName = (
  units: BaseUnit[] | null,
  nutrientElement: string | undefined,
): BaseUnit[] | undefined => {
  if (nutrientElement)
    return units?.filter(({ tags }) => {
      const unitTags = tags.split(',');

      return unitTags.includes(`${nutrientElement}Unit`);
    });
};

/**
 * A function to filter given units by specific unit id
 * @param units
 * @param id
 * @returns
 */
export const filterUnitsById = (units: BaseUnit[] | null, id: string | null | undefined) => {
  return units && id ? units.find((el) => el.id === id) : undefined;
};

/**
 * Hook to get UnitCountriesData from BE
 * @returns {
 *  unitCountriesData: UnitCountries | undefined,
 *  trigger: (options: RequestInit) => Promise<UnitCountries | undefined>,
 *  isMutating: boolean,
 *  error: ErrorResponse | undefined
 * }
 */
export const useUnitCountries = (): UseUnitCountriesReturn => {
  const fetcher = useCustomFetcher();
  const {
    data: unitCountriesResponse,
    trigger,
    isMutating,
    error,
  } = useSWRMutation<UnitCountries, ErrorResponse, string, RequestInit>(
    `${POLARIS_API_GW_ENDPOINTS.UNIT_API}/unit-countries/filter`,
    fetcher,
  );
  return {
    unitCountriesData: unitCountriesResponse?.entities,
    trigger,
    isMutating,
    error,
  };
};

/**
 * Hook that fetches all unit tags from BE
 * @param units - list of units
 * @returns {string[]} list of all unit tags
 */
export const getAllUnitTags = (units: BaseUnit[]): string[] => {
  const res = [];
  if (units?.length) {
    for (const unit of units) {
      const tags = unit.tags.split(',');
      for (const tag of tags) {
        res.push(tag);
      }
    }
  }
  // remove duplicates
  return [...new Set(res)];
};
