import { use<PERSON><PERSON>back, useReducer } from 'react';
import {
  additionalParameterReducer,
  createDefaultChangeHandler,
  createNameChangeHandler,
  createOrdinalChangeHandler,
  createNumberMinChangeHandler,
  createNumberMaxChangeHandler,
  createUnitTagsChangeHandler,
  createBaseUnitChangeHandler,
  createRequiredHandler,
  createValueTypeChangeHandler,
  validateValueType,
  convertToString,
} from '@widgets/Polaris/src/components/AdditionalParametersPopup/utils';
import {
  AdditionalParameterActions,
  AdditionalParameterState,
  ValueTypeOption,
} from '@widgets/Polaris/src/components';
import { CropRegion, Option } from '@common/types';

/**
 * This hook centralizes the state management and event handling for form elements
 * within the AdditionalParametersPopup component. Each handler is designed to manage
 * specific aspects of form interaction:
 *
 * - handleParameterNameChange: Updates and validates the 'parameterName' field.
 * - handleParameterOrdinalChange: Updates and validates the 'parameterOrdinal' field.
 * - handleParameterNumberChange: Updates and validates the 'numberMin' and 'numberMax' fields.
 * - handleCalendarDateChange: Manages state changes for date selection.
 * - handleRequiredChange: Toggles the 'isRequired' state based on checkbox interactions.
 * - handleValueTypeChange: Updates and validates the 'valueType' based on selection changes,
 *   also sets a default value corresponding to the selected type.
 * - handleBooleanChange: Updates the default value for boolean type fields.
 * - handleDefaultChange: Handles changes to the default value input  for non-list types.
 * - handleListDefaultValueChange: Manages changes to the default value specifically for list types.
 * Handlers are memoized using useCallback to prevent unnecessary re-renders.
 * Add more handlers here as new form inputs or requirements develop.
 */
export const useParameterFormState = (
  initialState: AdditionalParameterState,
  cropRegion: CropRegion | null,
  valueTypeOptions: ValueTypeOption[],
  isEditMode: boolean,
  currentEditingParameterName: string | null | undefined,
) => {
  const [state, dispatch] = useReducer(additionalParameterReducer, initialState);

  const handleParameterNameChange = useCallback(
    createNameChangeHandler(dispatch, cropRegion?.additionalProperties || []),
    [dispatch, cropRegion],
  );
  const handleParameterOrdinalChange = useCallback(
    createOrdinalChangeHandler(
      dispatch,
      cropRegion?.additionalProperties || [],
      isEditMode ? currentEditingParameterName : null,
    ),
    [dispatch, cropRegion, isEditMode, currentEditingParameterName],
  );
  const handleNumberMinChange = useCallback(
    createNumberMinChangeHandler(state.numberMax, convertToString(state.defaultValue), dispatch),
    [dispatch, state.numberMax, state.defaultValue],
  );
  const handleNumberMaxChange = useCallback(
    createNumberMaxChangeHandler(state.numberMin, convertToString(state.defaultValue), dispatch),
    [dispatch, state.numberMin, state.defaultValue],
  );
  const handleUnitTagsChange = useCallback(
    (selectedItem: string) => createUnitTagsChangeHandler(dispatch, selectedItem),
    [dispatch],
  );
  const handleBaseUnitChange = useCallback(
    (selectedItem: string) => createBaseUnitChangeHandler(dispatch, selectedItem),
    [dispatch],
  );

  const handleRequiredChange = useCallback(
    (checked: boolean) => createRequiredHandler(checked, dispatch),
    [dispatch],
  );

  const handleBooleanChange = useCallback(
    (newValue: string): void => {
      dispatch({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: newValue,
      });
    },
    [dispatch],
  );

  const handleDefaultChange = useCallback(
    createDefaultChangeHandler(state.valueType, state.numberMin, state.numberMax, dispatch),
    [dispatch, state.valueType, state.numberMin, state.numberMax],
  );

  const handleValueTypeChange = useCallback(
    (selectedItem: string): void => {
      createValueTypeChangeHandler(dispatch, validateValueType, valueTypeOptions, selectedItem);
    },
    [dispatch, valueTypeOptions],
  );

  const handleOptionChange = useCallback(
    (updatedOptions: Option[]) => {
      dispatch({
        type: AdditionalParameterActions.SET_OPTIONS,
        options: updatedOptions,
      });
    },
    [dispatch],
  );

  return {
    state,
    dispatch,
    handleParameterNameChange,
    handleParameterOrdinalChange,
    handleNumberMinChange,
    handleNumberMaxChange,
    handleUnitTagsChange,
    handleBaseUnitChange,
    handleRequiredChange,
    handleValueTypeChange,
    handleBooleanChange,
    handleDefaultChange,
    handleOptionChange,
  };
};
