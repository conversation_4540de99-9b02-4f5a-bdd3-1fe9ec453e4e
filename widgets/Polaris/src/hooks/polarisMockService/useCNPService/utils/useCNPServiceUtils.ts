import { PartnerTag } from '@common/types';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getPartnerTags = (partnerTags: any[]): PartnerTag[] => {
  const result: PartnerTag[] = [];
  for (const partnerTag of partnerTags) {
    if (
      'id' in partnerTag &&
      'name' in partnerTag &&
      'displayName' in partnerTag &&
      'code' in partnerTag &&
      'isInternal' in partnerTag &&
      'countries' in partnerTag &&
      'capabilities' in partnerTag &&
      'created' in partnerTag &&
      'modified' in partnerTag &&
      'modifiedBy' in partnerTag &&
      'deleted' in partnerTag
    ) {
      result.push(partnerTag);
    }
  }
  return result;
};
