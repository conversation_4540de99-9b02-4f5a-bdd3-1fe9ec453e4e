/**
 * Get list of cuontries from polaris API
 * @param filter
 * @returns
 */
import { ErrorResponse } from '@common/types';

import useSWRMutation from 'swr/mutation';

import { useCustomFetcher } from '@libs/SWRProvider';
import { useEffect } from 'react';
import { useMultipleRequests } from '../../useMultiFetcher/useMultiFetcher';
import { urlMapPartners } from '../../../screens/Home';
import { METHOD } from '@common/constants';
import { FilterType } from '@widgets/Polaris/src/types';
import { getPartnerTags } from './utils/useCNPServiceUtils';

/**
 * Get CNP Partner tagss from polaris API
 * @returns {SWRResponse<PartnerTag[], ErrorResponse>} - SWRResponse of the partner tags
 */
export const useCNPPartners = () => {
  const fetcher = useCustomFetcher();
  const {
    data: cnpPartners,
    trigger,
    isMutating,
    error: planValidationError,
  } = useSWRMutation<string[], ErrorResponse, string, RequestInit>(
    `${process.env.POLARIS_API}/grouped-crop-splits-by-tags-configuration/filter`,
    fetcher,
  );
  return {
    cnpPartners,
    trigger,
    isMutating,
    planValidationError,
  };
};

export const useFetchCNPPartners = (cropRegionId: string | undefined, splitType: 0 | 1) => {
  const { cnpPartners, trigger } = useCNPPartners();
  const fetcher = useCustomFetcher();
  useEffect(() => {
    const fetchCNPPartners = async () => {
      try {
        await trigger({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'cropRegionId',
                value: cropRegionId,
                type: FilterType.EQ,
              },
              {
                key: 'splitType',
                value: splitType,
                type: FilterType.EQ,
              },
            ],
          }),
        });
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    cropRegionId && fetchCNPPartners();
  }, [cropRegionId]);
  const partnersIDs = urlMapPartners(cnpPartners);

  const { data: partnerTags, isLoading } = useMultipleRequests(partnersIDs, fetcher);
  const tags = getPartnerTags(partnerTags || []);
  return { tags, isLoading };
};
