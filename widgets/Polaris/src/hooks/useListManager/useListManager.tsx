import { useState } from 'react';
import { ListManagerState } from '@widgets/Polaris/src/components/AdditionalParametersPopup/components/ListItems.type';

/**
 * useListManager is a generic hook for managing an array of items. It provides basic
 * CRUD operations on a list of items where each item must have a unique identifier
 * specified by the `identifier<PERSON>ey` in the config.
 *
 * This hook abstracts the state management logic needed for adding, editing, and
 * deleting items from the list, ensuring that components which use this hook can
 * focus on presentation and user interaction, rather than managing state consistency
 * and CRUD operations.
 *
 * Type Parameters:
 * - T: The type of items in the list. This should be an object type with a property
 *      specified by `identifier<PERSON>ey` which will be used as the unique identifier for
 *      CRUD operations.
 *
 * Parameters:
 * - initialItems: An initial array of items of type T.
 * - config: Configuration object specifying the `identifierKey`, the property name
 *           of the item that is used as a unique identifier.
 *
 * Returns:
 * - items: The current list of items.
 * - addItem: Function to add a new item to the list.
 * - editItem: Function to update an existing item in the list, identified by its unique identifier.
 * - deleteItem: Function to remove an item from the list, identified by its unique identifier.
 *
 * Example Usage:
 * const { items, addItem, editItem, deleteItem } = useListManager<MyType>([], { identifierKey: 'id' });
 *
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function useListManager<T extends Record<string, any>>(
  initialItems: T[],
  config: { identifierKey: keyof T },
  validate?: (formData: Partial<T>) => Partial<Record<keyof T, boolean>>,
) {
  const initialState: ListManagerState<T> = {
    items: initialItems,
    currentItem: null,
    isModalOpen: false,
    isEditMode: false,
    formData: {},
    formValidation: {},
  };

  const [state, setState] = useState<ListManagerState<T>>(initialState);

  const openModal = (item?: T): void => {
    setState((prev) => ({
      ...prev,
      currentItem: item || null,
      isModalOpen: true,
      isEditMode: Boolean(item),
      formData: item ? { ...item } : {},
      formValidation: item && validate ? validate(item) : {},
    }));
  };

  const closeModal = (): void => {
    setState((prev) => ({
      ...prev,
      currentItem: null,
      isModalOpen: false,
      isEditMode: false,
      formData: {},
      formValidation: {},
    }));
  };

  const handleInputChange = <V extends T[keyof T]>(field: keyof T, value: V): void => {
    const newFormData = { ...state.formData, [field]: value };
    const newValidation = validate ? validate(newFormData) : {};

    setState((prev) => ({
      ...prev,
      formData: newFormData,
      formValidation: { ...prev.formValidation, ...newValidation },
    }));
  };

  const addItem = (item: T) => {
    setState((prev) => ({
      ...prev,
      items: [...(prev.items || []), item],
      isModalOpen: false,
      formData: {},
      formValidation: {},
    }));
  };

  const editItem = (item: T): void => {
    setState((prev) => ({
      ...prev,
      items: prev?.items?.map((it) =>
        it[config.identifierKey] === prev?.currentItem?.[config.identifierKey] ? item : it,
      ),
      currentItem: null,
      isModalOpen: false,
      isEditMode: false,
      formData: {},
      formValidation: {},
    }));
  };

  const deleteItem = (identifierValue: string): void => {
    setState((prev) => ({
      ...prev,
      items: prev?.items?.filter((it) => it[config.identifierKey] !== identifierValue),
    }));
  };

  return {
    items: state.items,
    openModal,
    closeModal,
    addItem,
    editItem,
    deleteItem,
    handleInputChange,
    formData: state.formData,
    formValidation: state.formValidation,
    isModalOpen: state.isModalOpen,
    isEditMode: state.isEditMode,
    currentItem: state.currentItem,
  };
}
