/* eslint-disable @typescript-eslint/no-explicit-any */
import useSWRMutation from 'swr/mutation';
import { FetcherOptions, useCustomFetcher } from '@libs/SWRProvider/index';
import {
  FeatureConfigOptions,
  DeleteResponse,
  ErrorResponse,
  FeatureConfig,
  GrowthPhaseType,
  GrowthStageType,
  PaginatedResponse,
  FertigationPRecommendationGrowthPhase,
  ProductRecommendations,
  CerealsProductRecommendations,
} from '@common/types';
import { useEffect } from 'react';
import { GenericFilter } from '@widgets/Polaris/src/types';
import { HttpMethods } from 'msw';
import { METHOD, POLARIS_API_GW_ENDPOINTS } from '@common/constants';

const basePath = (configType: FeatureConfig) =>
  `${POLARIS_API_GW_ENDPOINTS.MASTER_MIND_MAP_API}/${configType}/product-recommendation/configurations`;

export function useGenericMutation<Data, Error = unknown>(basePath: string) {
  const fetcher = useCustomFetcher();
  return useSWRMutation<Data, Error, string, FetcherOptions>(basePath, fetcher);
}

export function useEntityOperation<T>(
  mutationFn: ReturnType<typeof useGenericMutation<T>>,
  buildExtraUrl: (...args: any[]) => string,
) {
  const { data, trigger: baseTrigger, isMutating, error } = mutationFn;

  const trigger = async (...args: any[]) => {
    const options: FetcherOptions = args.pop();
    const url = buildExtraUrl(...args);

    return await baseTrigger({
      ...options,
      extraUrl: url,
    });
  };

  return { data, trigger, isMutating, error };
}

/**
 *  Hook to clone product recommendation growth phase depends on type
 *
 * @param {GrowthPhaseType} growthPhaseType - growth Phase Type
 * @param {FertigationPRecommendationGrowthPhase} payload - body of the growth phase object
 * @returns {SWRResponse<FertigationPRecommendationGrowthPhase, ErrorResponse>} - SWRResponse of the growth phase configuration
 */
export const useCloneGrowthPhaseConfiguration = () => {
  const mutation = useGenericMutation<FertigationPRecommendationGrowthPhase>(
    basePath(FeatureConfigOptions.FERTIGATION),
  );

  return useEntityOperation(
    mutation,
    (growthPhaseType: GrowthPhaseType) => `/${growthPhaseType}/clone`,
  );
};

/**
 *  Hook to Delete product recommendation growth phase depends on type and productRecommendationId
 *
 * @param {string} productRecommendationId - product Recommendation Id
 * @param {GrowthPhaseType} growthPhaseType - growth Phase Type
 * @returns {SWRResponse<DeleteResponse, ErrorResponse>} - SWRResponse of the delete growth phase
 */
export const useDeleteGrowthPhaseConfiguration = () => {
  const mutation = useGenericMutation<DeleteResponse>(basePath(FeatureConfigOptions.FERTIGATION));

  const { trigger: baseTrigger, ...rest } = useEntityOperation(
    mutation,
    (productRecommendationId: string, growthPhaseType: GrowthPhaseType, phaseId: string) =>
      `/${productRecommendationId}/${growthPhaseType}/${phaseId}/perm`,
  );

  const trigger = (
    productRecommendationId: string,
    growthPhaseType: GrowthPhaseType,
    phaseId: string,
  ) =>
    baseTrigger(productRecommendationId, growthPhaseType, phaseId, {
      method: METHOD.DELETE,
    });

  return { trigger, ...rest };
};

/**
 *  Hook to Update product recommendation growth phase depends on type
 *
 * @param {string} productRecommendationId - product Recommendation Id
 * @param {GrowthPhaseType} growthPhaseType - growth Phase Type
 * @returns {SWRResponse<FertigationPRecommendationGrowthPhaseUpdateResponse, ErrorResponse>} - SWRResponse of the update growth phase
 */
export const useUpdateGrowthPhaseConfiguration = () => {
  const mutation = useGenericMutation<FertigationPRecommendationGrowthPhase>(
    basePath(FeatureConfigOptions.FERTIGATION),
  );

  return useEntityOperation(
    mutation,
    (productRecommendationId: string, growthPhaseType: GrowthPhaseType, phaseId: string) =>
      `/${productRecommendationId}/${growthPhaseType}/${phaseId}`,
  );
};

/**
 *  Hook to Update cereals product recommendation growth stage depends on type
 *
 * @param {string} stageId
 * @param {string} productRecommendationId - product Recommendation Id
 * @param {GrowthStageType} growthStageType - growth Stage Type
 * @returns {SWRResponse<CerealsSoilApplicationPRecommendation | CerealsFoliarApplicationPRecommendatio, ErrorResponse>} - SWRResponse of the update growth stage
 */
export const useUpdateCerealsGrowthStageConfiguration = () => {
  const mutation = useGenericMutation<FertigationPRecommendationGrowthPhase>(
    basePath(FeatureConfigOptions.CEREAL),
  );

  return useEntityOperation(
    mutation,
    (productRecommendationId: string, growthStageType: GrowthStageType, stageId: string) =>
      `/${productRecommendationId}/${growthStageType}/${stageId}`,
  );
};

/**
 * Use product recommendations data from the MMM MS
 * @returns {
 *  data: ProductRecommendations[] | undefined,
 *  trigger: () => Promise<PaginatedResponse<ProductRecommendations[]> | undefined>,
 *  isMutating: boolean,
 *  error: ErrorResponse | undefined
 * }
 */
export type ProductRecommendationsResponse<T extends FeatureConfig> =
  T extends FeatureConfigOptions.CEREAL
    ? CerealsProductRecommendations[]
    : ProductRecommendations[];

export const useProductRecommendations = <T extends FeatureConfig>(configType: T) => {
  const fetcher = useCustomFetcher();

  const { data, trigger, isMutating, error } = useSWRMutation<
    PaginatedResponse<ProductRecommendationsResponse<T>>,
    ErrorResponse,
    string,
    RequestInit
  >(`${basePath(configType)}/filter`, fetcher);

  return {
    data: data?.entities[0],
    trigger,
    isMutating,
    error,
  };
};

/**
 * Hook to fetch product recommendations data from the MMM MS
 * @param {GenericFilter[]} filter
 * @param {boolean} shouldFetch
 * @returns {
 *  data: ProductRecommendationsResponse<T> | undefined,
 * }
 */
export const useFetchProductRecommendations = <T extends FeatureConfig>(
  configType: T,
  filter: GenericFilter[] = [],
  shouldFetch = true,
) => {
  const { data, trigger } = useProductRecommendations<T>(configType);

  useEffect(() => {
    const fetchProductRecommendations = async () => {
      try {
        await trigger({
          method: HttpMethods.POST,
          body: JSON.stringify({
            filter,
          }),
        });
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    shouldFetch && fetchProductRecommendations();
  }, [trigger, shouldFetch, filter]);

  return { data, trigger };
};
