import { TFunction } from 'i18next';
import { ChangeEvent, KeyboardEvent } from 'react';
import { AdditionalParameterState, ValueTypeEnum } from '@widgets/Polaris/src/components';
import {
  convertToString,
  validateParameterIsMinLessThanMax,
  validateParameterWithinMinMaxRange,
} from '../../components/AdditionalParametersPopup/utils';
import { SelectOptions } from '@widgets/Polaris/src/types';

interface Config {
  value: string;
  label: string;
  variant?: string;
  dataCy?: string;
  helperText?: string;
  shouldShow?: boolean;
  type?: string;
  disabled?: boolean;
  cover?: string;
}

interface InputConfig extends Config {
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void;
}
interface SelectConfig<T> extends Config {
  items: SelectOptions<string>;
  ariaLabel: string;
  wrapperDataCy: string;
  onChange: (selectedItem: T) => void;
}

const preventDefaultForLetterE = (e: KeyboardEvent<HTMLInputElement>) =>
  /[eE]/g.test(e.key) && e.key !== 'Backspace' && e.preventDefault();

export const useAdditionalParameterConfigs = (
  t: TFunction,
  keyPrefix: string,
  state: AdditionalParameterState,
) => {
  const getNameOrdinalConfig = (
    handleParameterNameChange: (event: ChangeEvent<HTMLInputElement>) => void,
    handleParameterOrdinalChange: (event: ChangeEvent<HTMLInputElement>) => void,
    isEditMode: boolean,
  ): InputConfig[] => {
    let nameHelperText;
    if (state?.isNameInvalid) {
      if (state?.parameterName.trim() === '') {
        nameHelperText = t(`polaris.error.isRequired`, { name: 'Parameter' });
      } else {
        nameHelperText = t(`${keyPrefix}.parameterNameDuplicate`);
      }
    }

    let ordinalHelperText;
    if (state.isOrdinalInvalid) {
      if (state.parameterOrdinal.trim() === '') {
        ordinalHelperText = t(`polaris.error.isRequired`, { name: 'Ordinal' });
      } else if (isNaN(Number(state.parameterOrdinal))) {
        ordinalHelperText = t(`polaris.error.mustBeNumber`);
      } else {
        ordinalHelperText = t(`${keyPrefix}.parameterOrdinalDuplicate`);
      }
    }

    return [
      {
        value: state?.parameterName,
        type: 'text',
        onChange: handleParameterNameChange,
        variant: state?.isNameInvalid ? 'error' : 'default',
        helperText: nameHelperText,
        label: t(`${keyPrefix}.parameterName`),
        dataCy: 'additional-properties-parameter-field',
        disabled: isEditMode,
        cover: isEditMode ? 'fill' : 'outline',
      },
      {
        value: state?.parameterOrdinal,
        type: 'number',
        onChange: handleParameterOrdinalChange,
        onKeyDown: (e) => preventDefaultForLetterE(e),
        variant: state?.isOrdinalInvalid ? 'error' : 'default',
        helperText: ordinalHelperText,
        label: t(`${keyPrefix}.parameterOrdinal`),
        dataCy: 'additional-properties-ordinal-field',
      },
    ];
  };

  const getDefaultValConfig = (
    handleDefaultChange: (event: ChangeEvent<HTMLInputElement>) => void,
  ): InputConfig[] => {
    const isSameDefaultValue =
      !state.valueType ||
      state.valueType === ValueTypeEnum.Text ||
      state.valueType === ValueTypeEnum.List;
    const isNumberOrNumberWithUnit =
      state.valueType === ValueTypeEnum.Number || state.valueType === ValueTypeEnum.NumberWithUnit;

    let helperText;
    if (state.isDefaultValueInvalid) {
      if (state.defaultValue && isNaN(Number(state.defaultValue))) {
        helperText = t('polaris.error.mustBeNumber');
      }
      if (
        typeof state.defaultValue === 'string' &&
        !validateParameterWithinMinMaxRange(state.defaultValue, state.numberMin, state.numberMax)
      ) {
        helperText = t(`polaris.error.withinMinMaxRange`);
      }
    }

    return [
      {
        shouldShow: isSameDefaultValue,
        type: 'text',
        value: convertToString(state.defaultValue),
        onChange: handleDefaultChange,
        dataCy:
          state.valueType === ValueTypeEnum.Text
            ? 'additional-properties-default-value-field'
            : 'additional-properties-listType-value-field',
        label: t(`${keyPrefix}.defaultValue`),
        variant: 'default',
      },
      {
        shouldShow: isNumberOrNumberWithUnit,
        type: 'number',
        value: convertToString(state.defaultValue),
        onChange: handleDefaultChange,
        onKeyDown: (e) => preventDefaultForLetterE(e),
        helperText: helperText,
        variant: state.isDefaultValueInvalid ? 'error' : 'default',
        dataCy: 'additional-properties-default-value-number-field',
        label: t(`${keyPrefix}.defaultValue`),
      },
      {
        shouldShow: state.valueType === ValueTypeEnum.Date,
        type: 'date',
        value: convertToString(state.defaultValue),
        onChange: handleDefaultChange,
        variant: 'default',
        dataCy: 'additional-properties-date-field',
        label: t(`${keyPrefix}.calendarLabel`),
      },
    ];
  };

  /**
   * Used to configurate min and max numbers
   * @param handleNumberMinChange
   * @param handleNumberMaxChange
   * @returns The configuration for each number
   */
  const getNumberMinMaxConfig = (
    handleNumberMinChange: (event: ChangeEvent<HTMLInputElement>) => void,
    handleNumberMaxChange: (event: ChangeEvent<HTMLInputElement>) => void,
  ): InputConfig[] => {
    const emptyMin = state.numberMin.trim() === '';
    const emptyMax = state.numberMax.trim() === '';
    let isInvalidMinMax = false;
    if (!emptyMin && !emptyMax) {
      isInvalidMinMax = !validateParameterIsMinLessThanMax(state.numberMin, state.numberMax);
    }

    let numberMinHelperText;
    if (state.isNumberMinInvalid) {
      if (emptyMin) {
        numberMinHelperText = t(`polaris.error.isRequired`, {
          name: 'Min',
        });
      }
      if (state.numberMin && isNaN(Number(state.numberMin))) {
        numberMinHelperText = t(`polaris.error.mustBeNumber`);
      }
      if (isInvalidMinMax) {
        numberMinHelperText = t('polaris.error.minGreaterThanMax');
      }
    }

    let numberMaxHelperText;
    if (state.isNumberMaxInvalid) {
      if (emptyMax) {
        numberMaxHelperText = t(`polaris.error.isRequired`, {
          name: 'Max',
        });
      }
      if (state.numberMax && isNaN(Number(state.numberMax))) {
        numberMaxHelperText = t(`polaris.error.mustBeNumber`);
      }
      if (isInvalidMinMax) {
        numberMaxHelperText = t('polaris.error.maxLessThanMin');
      }
    }

    return [
      {
        value: state.numberMin || '',
        onChange: handleNumberMinChange,
        onKeyDown: (e) => preventDefaultForLetterE(e),
        dataCy: 'additional-properties-number-field-min',
        label: t(`polaris.common.min`),
        variant: state.isNumberMinInvalid ? 'error' : 'default',
        helperText: numberMinHelperText,
      },
      {
        value: state.numberMax || '',
        onChange: handleNumberMaxChange,
        onKeyDown: (e) => preventDefaultForLetterE(e),
        dataCy: 'additional-properties-number-field-max',
        label: t(`polaris.common.max`),
        variant: state.isNumberMaxInvalid ? 'error' : 'default',
        helperText: numberMaxHelperText,
      },
    ];
  };

  const getValueTypeBooleanConfig = (
    handleValueTypeChange: (selectedItem: string) => void,
    handleBooleanChange: (selectedItem: string) => void,
    valueTypeOptions: SelectOptions<string>,
    booleanTypeOptions: SelectOptions<string>,
  ): SelectConfig<string>[] => [
    {
      shouldShow: true,
      ariaLabel: 'Select value type',
      items: valueTypeOptions,
      variant: state.isValueTypeInvalid ? 'error' : 'default',
      helperText: state.isValueTypeInvalid
        ? t(`polaris.error.isRequired`, { name: 'Value type' })
        : undefined,
      label: t(`${keyPrefix}.valueType`),
      onChange: handleValueTypeChange,
      value: state.valueType || '',
      wrapperDataCy: 'additional-properties-value-type-select',
    },
    {
      shouldShow: state.valueType === ValueTypeEnum.Boolean,
      ariaLabel: 'Select boolean type',
      items: booleanTypeOptions,
      value: convertToString(state.defaultValue),
      onChange: handleBooleanChange,
      label: t(`${keyPrefix}.defaultValue`),
      variant: 'default',
      wrapperDataCy: 'additional-properties-boolean-select',
    },
  ];

  const getUnitTagsBaseUnitConfig = (
    unitTagsChangeHandler: (selectedItem: string) => void,
    handleBaseUnitChange: (selectedItem: string) => void,
    allUnitTagsByCountry: SelectOptions<string>,
    baseUnitsForSelectedTag: SelectOptions<string>,
  ): SelectConfig<string>[] => [
    {
      ariaLabel: 'Select a unit tag',
      items: allUnitTagsByCountry,
      variant: state.isUnitTagsInvalid ? 'error' : 'default',
      helperText: state.isUnitTagsInvalid
        ? t(`polaris.error.isRequired`, { name: 'Unit tags' })
        : undefined,
      label: t(`${keyPrefix}.unitTags`),
      onChange: unitTagsChangeHandler,
      value: state.unitTags || '',
      wrapperDataCy: 'additional-properties-number-type-field-unit-tags',
      cover: 'outline',
    },
    {
      ariaLabel: 'Select a base unit',
      items: baseUnitsForSelectedTag,
      variant: state.unitTags && state.isBaseUnitInvalid ? 'error' : 'default',
      helperText:
        state.unitTags && state.isBaseUnitInvalid
          ? t(`polaris.error.isRequired`, { name: 'Base unit' })
          : t(`${keyPrefix}.baseUnitHelperText`),
      value: state.baseUnit || '',
      onChange: handleBaseUnitChange,
      label: t(`${keyPrefix}.baseUnit`),
      wrapperDataCy: 'additional-properties-number-type-field-base-unit',
      disabled: !state.unitTags,
      cover: !state.unitTags ? 'fill' : 'outline',
    },
  ];

  return {
    getNameOrdinalConfig,
    getDefaultValConfig,
    getNumberMinMaxConfig,
    getValueTypeBooleanConfig,
    getUnitTagsBaseUnitConfig,
  };
};
