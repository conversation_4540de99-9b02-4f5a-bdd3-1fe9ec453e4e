import { CustomFetcherType } from '@libs/SWRProvider/index';
import useSWR from 'swr';

/**
 * Hook used to fetch multiple data by urls
 * @param {string[]} urls - urls to fetch
 * @param fetcher - function to fetch data from BE
 * @returns fetched data, boolean for error and boolean for loading
 */
export const useMultipleRequests = (urls: string[] | undefined, fetcher: CustomFetcherType) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const multiFetcher = (...args: any[]) => {
    const arrayUrls: string[] = Array.from(new Set(args[0]));
    const arrayPromise = arrayUrls.map((url: string) => fetcher(url));
    return Promise.all(arrayPromise);
  };

  const { data, error } = useSWR(urls ? urls : null, multiFetcher);
  return {
    data: data,
    isError: !!error,
    isLoading: !data && !error,
  };
};
