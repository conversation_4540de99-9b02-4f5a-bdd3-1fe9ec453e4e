import { BaseUnit, GrowthScale, CropRegion } from '@common/types';
import { useMemo } from 'react';
import { AhuaIconProps } from '@yaradigitallabs/ahua-react';
import { filterUnitsById } from '@polaris-hooks/index';
import { useAppContext } from '../../providers';

interface CropSettingsCardLogic {
  cropRegion: CropRegion | null;
  growthScalesData?: GrowthScale[] | undefined;
}

export interface SelectItem {
  icon: AhuaIconProps['icon'];
  value: string;
  text: string;
}

interface CropSettingsCardPrep {
  getItems: (
    unit: BaseUnit | GrowthScale | undefined,
    iconName: AhuaIconProps['icon'],
  ) => SelectItem[];
  getValue: (unit: BaseUnit | GrowthScale | undefined) => string;
}

/**
 * Hook to prepare crop settings units
 * @param {CropRegion | null} cropRegion - The crop region object for the selected country.
 * @returns {Object} Object containing functions for retrieving select field items and value
 */
const CropSettingsCardPrep = (): CropSettingsCardPrep => {
  const getItems = (unit: BaseUnit | GrowthScale | undefined, iconName: AhuaIconProps['icon']) => {
    const item = !unit
      ? {
          icon: iconName,
          value: 'error',
          text: '-',
        }
      : {
          icon: iconName,
          value: unit.id,
          text: unit.name,
        };

    return [item];
  };

  const getValue = (unit: BaseUnit | GrowthScale | undefined) => {
    return !unit ? 'error' : unit?.id;
  };

  return {
    getItems,
    getValue,
  };
};

/**
 * Hook to get crop settings units for Crop Nutrition Plan homepage
 * @param {CropRegion | null} cropRegion - The crop region object for the selected country.
 * @returns {Object} Object containing Crop Nutrition Plan crop settings units
 */
export const CropSettingsCNPCardLogic = ({ cropRegion }: CropSettingsCardLogic) => {
  const { selectedCountryUnits } = useAppContext();
  const { getItems, getValue } = CropSettingsCardPrep();

  const growthScale = useMemo(
    () => filterUnitsById(selectedCountryUnits, cropRegion?.growthScaleId),
    [selectedCountryUnits],
  );

  const yieldUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, cropRegion?.yieldBaseUnitId),
    [selectedCountryUnits],
  );

  const recommendationUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, cropRegion?.demandBaseUnitId),
    [selectedCountryUnits],
  );

  const growthScaleItems = useMemo(() => {
    return getItems(growthScale, 'CalendarDate');
  }, [growthScale]);

  const growthScaleValue = useMemo(() => {
    return getValue(growthScale);
  }, [growthScale]);

  const yieldItems = useMemo(() => {
    return getItems(yieldUnit, 'Crop');
  }, [yieldUnit]);

  const yieldValue = useMemo(() => {
    return getValue(yieldUnit);
  }, [yieldUnit]);

  const recommendationItems = useMemo(() => {
    return getItems(recommendationUnit, 'Boundary');
  }, [recommendationUnit]);

  const recommendationValue = useMemo(() => {
    return getValue(recommendationUnit);
  }, [recommendationUnit]);

  return {
    growthScaleItems,
    yieldItems,
    recommendationItems,

    growthScaleValue,
    yieldValue,
    recommendationValue,
  };
};
