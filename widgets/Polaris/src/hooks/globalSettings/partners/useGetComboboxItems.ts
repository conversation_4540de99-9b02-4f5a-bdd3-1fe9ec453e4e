import { useAppContext } from '@widgets/Polaris/src/providers';
import { orderBy } from 'lodash';
import { useEffect, useState } from 'react';
import { useFeatureServices } from '../features';
import { Country, Feature } from '@common/types';

export type CountryCombobox = {
  value: Country;
  label: string;
};
export type FeatureCombobox = {
  value: Feature;
  label: string;
  description: string;
};

export const useGetComboboxItems = () => {
  const { countries } = useAppContext();

  const { data: features } = useFeatureServices();
  const [countriesItems, setCountriesItems] = useState<CountryCombobox[]>();
  const [featuresItems, setFeaturesItems] = useState<FeatureCombobox[]>();

  useEffect(() => {
    if (features) {
      const items: FeatureCombobox[] = features.entities.map((f: Feature) => ({
        value: f,
        label: f.displayName,
        description: f.group,
      }));
      setFeaturesItems(orderBy(items, ['description'], ['asc']));
    }
  }, [features]);

  useEffect(() => {
    const items: CountryCombobox[] = countries.map((c: Country) => ({
      value: c,
      label: c.name,
    }));
    setCountriesItems(items);
  }, [countries]);

  return {
    countriesItems,
    featuresItems,
  };
};
