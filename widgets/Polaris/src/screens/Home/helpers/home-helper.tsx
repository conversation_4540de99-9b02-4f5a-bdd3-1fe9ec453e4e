import React from 'react';
import {
  Country,
  Feature,
  Region,
  Tag,
  CropDescription,
  CropSubClass,
  CropDropdownValue,
  CropRegion,
  CropSequence,
} from '@common/types';
import { GroupContext } from './group.styled';
import { POLARIS_API_GW_ENDPOINTS } from '@common/constants';

export const filteredCropDescriptionsByRegion = (
  cropDescs?: CropDescription[],
  cropRegions?: CropRegion[],
) => {
  if (cropDescs && cropRegions) {
    const searchValuesCropDescriptionids = cropRegions?.map((region) => region.cropDescriptionId);
    const filteredCropDescriptions = cropDescs?.filter((cropDesc) =>
      searchValuesCropDescriptionids.includes(cropDesc.id),
    );
    return filteredCropDescriptions;
  }
  return [];
};

export const cropDropdownValues = (
  cropResponse: CropSubClass[] | undefined,
  filteredCropDescriptions: CropDescription[] | undefined,
) => {
  const dropdownValues: CropDropdownValue[] = [];
  if (cropResponse && filteredCropDescriptions) {
    filteredCropDescriptions?.forEach((cropDesc) => {
      const cropSubClass = cropResponse?.find((csc) => cropDesc.cropSubClassId === csc.id);

      if (cropSubClass)
        dropdownValues.push({
          value: cropDesc.id,
          text: `${cropSubClass.name} ${cropDesc.name}`,
        });
    });
  }

  return dropdownValues.sort((a, b) => (a.text > b.text ? 1 : a.text < b.text ? -1 : 0));
};

export const findCrop = (value: string, cropResponse: CropSubClass[] | undefined) => {
  const selectedCropDetails = cropResponse?.find((crop) => crop.id === value);
  return selectedCropDetails;
};

export const createDropdownValues = (data: Country[] | Region[]) => {
  return data?.map((element) => {
    return { value: element.id, text: element.name };
  });
};
export const createFeatureDropdownValues = (data: Feature[]) => {
  return data
    ?.sort(function (a, b) {
      return a.group.localeCompare(b.group);
    })
    .map((element) => {
      return {
        value: element.id,
        text: element.displayName,
        context: <GroupContext>{element.group}</GroupContext>,
      };
    });
};
export const urlMapCropDesc = (cropRegions: CropRegion[] | CropSequence[] | undefined) => {
  return cropRegions
    ? cropRegions?.map(
        (region) =>
          `${POLARIS_API_GW_ENDPOINTS.CROP_API}/crop-descriptions/${region.cropDescriptionId}`,
      )
    : [];
};

export const urlMapCropSubClass = (cropDescriptions: CropDescription[] | undefined) => {
  return cropDescriptions
    ? cropDescriptions?.map(
        (cd) => `${POLARIS_API_GW_ENDPOINTS.CROP_API}/crop-sub-classes/${cd.cropSubClassId}`,
      )
    : [];
};

export const urlMapPartners = (partners: string[] | undefined) => {
  return partners && partners.length > 0
    ? partners
        .join(', ')
        .split(/,\s*/)
        .map((partnerId) => `${process.env.POLARIS_API}/partner-tags/${partnerId}`)
    : [];
};
export const findRegion = (
  regionsData: Region[] | undefined,
  value: string,
): Region | undefined => {
  const region = regionsData?.filter((region) => region.id === value)[0];
  return region;
};

export const findCountry = (
  countriesData: Country[] | undefined | null,
  value: string,
): Country | undefined => {
  const country = countriesData?.filter((country) => country.id === value)[0];
  return country;
};

export const findFeature = (
  featuresData: Feature[] | undefined,
  value: string,
): Feature | undefined => {
  const feature = featuresData?.filter((feat) => feat.id === value)[0];
  return feature;
};

export const filterFeaturesList = (
  allFeatures: Feature[],
  availableFeatures: Tag[] | undefined,
) => {
  return allFeatures.filter((feature) => availableFeatures?.some((val) => val.id === feature.id));
};
