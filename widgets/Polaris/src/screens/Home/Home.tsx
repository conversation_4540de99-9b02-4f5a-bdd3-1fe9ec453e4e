import React, { useState, useMemo, useEffect } from 'react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { Select, Title, Button, Headline } from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import {
  useCropRegions,
  useFeatureService,
  useGetCropDescriptions,
  useGetCropSubClasses,
  useSetNavbar,
  useFetchRegionsData,
  useUnitCountries,
} from '@polaris-hooks/index';
import './styles.scss';
import {
  CropDescription,
  CropSubClass,
  Country,
  Feature,
  ProductRecommendationApplicationTypes,
  Region,
} from '@common/types';
import { generatePath, useNavigate } from 'react-router-dom';
import { ROUTES } from '@src/routes';
import {
  createDropdownValues,
  createFeatureDropdownValues,
  cropDropdownValues,
  findCrop,
  findCountry,
  findFeature,
  findRegion,
  filterFeaturesList,
} from '.';
import { SelectWrapper } from '../../components/Select';
import { FEATURE_IDS, METHOD } from '@common/constants';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';

const Home: React.FC = () => {
  useSetNavbar(ROUTES.home, false, 'home');
  const { t } = useTranslation();
  const keyPrefix = 'polaris.homepage';
  const navigate = useNavigate();
  const [areFieldsDisabled, setAreFieldsDisabled] = useState(true);
  const {
    selectedCountry: country,
    selectedRegion: region,
    countries: countriesData,
    cropRegion,
    methods: {
      setCountry,
      setRegion,
      setCrop,
      setFeature,
      setIsCoachmarkVisible,
      setCropRegion,
      setCropDescription,
      setPlanValidation,
      setSelectedNutrient,
      setSelectedFeatureNutrients,
      setSelectedPlanConfigTab,
      setDemandCalculationAccordion,
      setSelectedAppType,
      setSelectedCountryUnits,
      setSelectedMMMValidation,
    },
  } = useAppContext();

  //values taken from the provider
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(country);
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(region);
  const [selectedCrop, setSelectedCrop] = useState<CropSubClass | null>(null);
  const [selectedFeature, setSelectedFeature] = useState<Feature | null>(null);
  const [selectedCropDescription, setSelectedCropDescription] = useState<CropDescription | null>(
    null,
  );
  const [hasErrorCountry, setHasErrorCountry] = useState<boolean>(false);

  const { cropRegionsData, trigger: getCropRegions } = useCropRegions();

  const fetchRegionsFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!selectedCountry?.id) return;
    return [
      {
        key: 'countryId',
        type: FilterType.EQ,
        value: selectedCountry.id,
      },
    ];
  }, [selectedCountry?.id]);

  const fetchCropDescriptionsFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!cropRegionsData?.length) return;
    const cropDescriptionIds = cropRegionsData.map((region) => region.cropDescriptionId);
    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: cropDescriptionIds.join(','),
      },
    ];
  }, [cropRegionsData]);

  const { regionsData } = useFetchRegionsData(fetchRegionsFilters, Boolean(selectedCountry?.id));
  const { unitCountriesData, trigger: getAllUnitsByCountryId } = useUnitCountries();

  const { cropDescriptions, isLoading } = useGetCropDescriptions(fetchCropDescriptionsFilters);

  const fetchCropSubclassesFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!cropDescriptions?.length) return;
    const cropSubClassIds = cropDescriptions.map((region) => region.cropSubClassId);
    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: cropSubClassIds.join(','),
      },
    ];
  }, [cropDescriptions]);

  const { cropSubClasses, isLoadingSC } = useGetCropSubClasses(fetchCropSubclassesFilters);
  const { featuresData, trigger: getFeaturesData } = useFeatureService();

  const cropChange = (value: string) => {
    cropDescriptions?.map((cd: CropDescription) => {
      if (cd.id === value) {
        const selectedCropRegion = cropRegionsData?.find(
          (cropRegion) => cropRegion.cropDescriptionId === cd.id,
        );
        selectedCropRegion && setCropRegion(selectedCropRegion);
        setSelectedCropDescription(cd);
        setPlanValidation(null);
        setSelectedMMMValidation(null);
        const selectedCropDetails = findCrop(cd.cropSubClassId, cropSubClasses);
        if (selectedCropDetails) setSelectedCrop(selectedCropDetails);
      }
    });
  };

  const regionChange = (value: string) => {
    const region = findRegion(regionsData, value);

    if (region) {
      setSelectedRegion(region);
      setSelectedFeature(null);
      setSelectedCrop(null);
      setSelectedCropDescription(null);
      setSelectedMMMValidation(null);
    }
  };

  const countryChange = (value: string) => {
    const foundCountry = findCountry(countriesData, value);

    if (foundCountry) {
      setSelectedCountry(foundCountry);
      setSelectedRegion(null);
    }
    setAreFieldsDisabled(false);
    setSelectedCrop(null);
    setSelectedCropDescription(null);
    setSelectedFeature(null);
    setHasErrorCountry(false);
    setSelectedMMMValidation(null);
  };

  const featureChange = (value: string) => {
    const feature = findFeature(featuresData, value);

    if (feature) {
      setSelectedFeature(feature);
      setSelectedNutrient(null);
      setSelectedFeatureNutrients(null);
      setDemandCalculationAccordion(null);
      setSelectedPlanConfigTab(null);
      setSelectedMMMValidation(null);
      setSelectedAppType(ProductRecommendationApplicationTypes.SOIL);
    }
  };

  const countriesDropdownData = useMemo(() => {
    if (!countriesData) return [];

    return createDropdownValues(countriesData);
  }, [countriesData]);

  const regionsDropdownData = useMemo(() => {
    if (!regionsData) return [];

    return createDropdownValues(regionsData);
  }, [regionsData, selectedCountry]);

  const cropDropdownData = useMemo(() => {
    if (!cropSubClasses || !cropDescriptions) return [];

    let finalCropSubClasses = cropSubClasses;
    let finalCropDescriptions = cropDescriptions;

    // Filter the crops dropdown on whether they have assigned the selected feature
    if (selectedFeature) {
      const selectedFeatureCropRegionDetails = cropRegionsData?.filter(
        (cropRegion) =>
          cropRegion.countryId === selectedCountry?.id &&
          cropRegion.regionId === selectedRegion?.id &&
          cropRegion.tagsConfiguration.featureTags?.some((tag) => tag.id === selectedFeature.id),
      );

      if (selectedFeatureCropRegionDetails) {
        const cropDescriptionIds = selectedFeatureCropRegionDetails.map(
          ({ cropDescriptionId }) => cropDescriptionId,
        );
        finalCropDescriptions = cropDescriptions.filter((cropDesc) =>
          cropDescriptionIds.includes(cropDesc.id),
        );
        finalCropSubClasses = cropSubClasses.filter((cropSubClass) =>
          finalCropDescriptions.some((cropDesc) => cropDesc.cropSubClassId === cropSubClass.id),
        );
      }
    }

    // Deselect crop if the selected feature doesn't have it
    if (
      selectedFeature &&
      selectedCropDescription &&
      !finalCropDescriptions.find((cropDesc) => cropDesc.id === selectedCropDescription.id)
    ) {
      setSelectedCrop(null);
      setSelectedCropDescription(null);
    }

    return cropDropdownValues(finalCropSubClasses, finalCropDescriptions);
  }, [
    isLoading,
    isLoadingSC,
    selectedRegion,
    selectedCountry,
    cropSubClasses,
    cropDescriptions,
    selectedFeature,
  ]);

  const featureDropdownData = useMemo(() => {
    if (!featuresData) return [];
    // @TODO
    // Add filter to filter the country features that the user has access too
    const filteredFeaturesByCountry = filterFeaturesList(
      featuresData,
      selectedCountry?.tagsConfiguration.featureTags,
    );

    const finalFilteredFeatures = filteredFeaturesByCountry;

    // Deselect feature if the selected crop doesn't have it
    if (
      selectedCrop &&
      selectedFeature &&
      !finalFilteredFeatures.find((features) => features.id === selectedFeature.id)
    ) {
      setSelectedFeature(null);
    }

    return createFeatureDropdownValues(finalFilteredFeatures);
  }, [selectedRegion, selectedCountry, selectedCropDescription, countryChange]);

  useEffect(() => {
    const fetchCropRegionsData = async () => {
      try {
        await getCropRegions({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'regionId',
                value: selectedRegion?.id,
                type: FilterType.EQ,
              },
            ],
          }),
        });
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    if (selectedRegion) fetchCropRegionsData();
  }, [selectedCountry, selectedRegion]);

  useEffect(() => {
    const fetchFeaturesData = async () => {
      try {
        await getFeaturesData({
          method: METHOD.POST,
          body: JSON.stringify({}),
        });
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    !areFieldsDisabled && fetchFeaturesData();
  }, [areFieldsDisabled]);

  // Initialization from session storage
  useEffect(() => {
    if (country) {
      setSelectedCountry(country);
      setHasErrorCountry(false);
      setAreFieldsDisabled(false);
    }
  }, [country]);
  // Initialization from session storage
  useEffect(() => {
    if (region?.id !== selectedRegion?.id) setSelectedRegion(region);
  }, [regionsData, region]);

  // Auto-select first region on local country change
  useEffect(() => {
    if (
      selectedCountry &&
      regionsData &&
      regionsData?.length > 0 &&
      (region?.id !== selectedRegion?.id || !region || country?.id !== selectedCountry?.id)
    ) {
      setSelectedRegion(regionsData[0]);
    }
  }, [selectedCountry, regionsData]);

  useEffect(() => {
    const fetchCountryUnits = async () => {
      try {
        await getAllUnitsByCountryId({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'countryId',
                value: selectedCountry?.id,
                type: FilterType.EQ,
              },
            ],
          }),
        });
      } catch (error) {
        console.error('Error fetching country units:', error);
      }
    };

    const isDifferentCountry =
      unitCountriesData && unitCountriesData[0]?.countryId !== selectedCountry?.id;
    if ((selectedCountry && !unitCountriesData) || isDifferentCountry) {
      fetchCountryUnits();
    }

    if (unitCountriesData) {
      const unitEntities = unitCountriesData.map((el) => el.unit);
      setSelectedCountryUnits(unitEntities || null);
    }
  }, [selectedCountry, unitCountriesData]);

  const handleGoButtonClick = () => {
    if (!selectedCountry) {
      setHasErrorCountry(true);
      return;
    }
    if (selectedCountry && selectedRegion && cropRegion) {
      setCountry(selectedCountry);
      setRegion(selectedRegion);
      setIsCoachmarkVisible(false);

      const setFeatureState = (featureSpecificRoute: string) => {
        setCrop(selectedCrop);
        setFeature(selectedFeature);
        setCropDescription(selectedCropDescription);
        setSelectedFeatureNutrients(null);
        setDemandCalculationAccordion(null);
        navigate(
          generatePath(`${ROUTES.cropFeatures}/${featureSpecificRoute}`, {
            cropRegionId: cropRegion.id,
          }),
        );
      };

      if (selectedCrop && selectedFeature?.id === FEATURE_IDS.CNP) {
        return setFeatureState(ROUTES.cropNutritionPlan);
      }
      if (selectedCrop && selectedFeature?.id === FEATURE_IDS.CMMM) {
        return setFeatureState(ROUTES.cerealsMasterMindMap);
      }
      if (selectedCrop && selectedFeature?.id === FEATURE_IDS.FP) {
        return setFeatureState(ROUTES.fertigationPlan);
      }

      navigate(ROUTES.overview);
    }
  };

  return (
    <div data-cy='home-content' className='content'>
      <div className='container-widget' data-cy='widget-container'>
        <div className='title-container'>
          <Title size='l' data-cy='onboarding-title'>
            {t(`${keyPrefix}.title`)}
          </Title>
          <Headline
            size='l'
            data-cy='onboarding-text'
            css={{ marginBottom: '32px', textAlign: 'center !important' }}
          >
            {t(`${keyPrefix}.text`)}
          </Headline>
        </div>
        <div className='dropdown-container'>
          <SelectWrapper dataCy='country-select-home'>
            <Select
              ariaLabel={t(`${keyPrefix}.countryDropdown.ariaLabel`)}
              cover='outline'
              css={{ width: '100%' }}
              value={selectedCountry ? selectedCountry.id : null}
              items={countriesDropdownData}
              loadingText={t(`${keyPrefix}.loadingText`)}
              placeholder={t(`${keyPrefix}.countryDropdown.placeholder`)}
              position='popper'
              size='n'
              variant={hasErrorCountry ? 'error' : 'default'}
              helper-text={
                hasErrorCountry ? t('polaris.error.isRequired', { name: 'Country' }) : null
              }
              onChange={countryChange}
              label={
                selectedCountry !== null ? t(`${keyPrefix}.countryDropdown.placeholder`) : null
              }
            />
          </SelectWrapper>
          <SelectWrapper dataCy='region-select-home'>
            <Select
              ariaLabel={t(`${keyPrefix}.regionDropdown.ariaLabel`)}
              cover={areFieldsDisabled ? 'fill' : 'outline'}
              css={{ width: '100%' }}
              items={regionsDropdownData}
              value={selectedRegion ? selectedRegion.id : null}
              loadingText={t(`${keyPrefix}.loadingText`)}
              placeholder={t(`${keyPrefix}.regionDropdown.placeholder`)}
              position='popper'
              size='n'
              variant='default'
              disabled={areFieldsDisabled}
              onChange={regionChange}
              label={selectedRegion !== null ? t(`${keyPrefix}.regionDropdown.placeholder`) : null}
            />
          </SelectWrapper>
          <SelectWrapper dataCy='crop-select-home'>
            <Select
              ariaLabel={t(`${keyPrefix}.cropDropdown.ariaLabel`)}
              cover={areFieldsDisabled ? 'fill' : 'outline'}
              css={{ width: '100%' }}
              items={cropDropdownData}
              value={selectedCropDescription ? selectedCropDescription.id : null}
              helper-text={t(`${keyPrefix}.cropDropdown.helperText`)}
              loadingText={t(`${keyPrefix}.loadingText`)}
              placeholder={t(`${keyPrefix}.cropDropdown.placeholder`)}
              position='popper'
              size='n'
              variant='default'
              disabled={areFieldsDisabled}
              onChange={cropChange}
              label={
                selectedCropDescription !== null ? t(`${keyPrefix}.cropDropdown.placeholder`) : null
              }
            />
          </SelectWrapper>
          <SelectWrapper dataCy='feature-select-home'>
            <Select
              ariaLabel={t(`${keyPrefix}.featureDropdown.ariaLabel`)}
              cover={areFieldsDisabled ? 'fill' : 'outline'}
              css={{ width: '100%' }}
              items={featureDropdownData}
              value={selectedFeature ? selectedFeature.id : null}
              loadingText={t(`${keyPrefix}.loadingText`)}
              placeholder={t(`${keyPrefix}.featureDropdown.placeholder`)}
              position='popper'
              size='n'
              variant='default'
              disabled={areFieldsDisabled}
              onChange={featureChange}
              label={
                selectedFeature !== null ? t(`${keyPrefix}.featureDropdown.placeholder`) : null
              }
            />
          </SelectWrapper>
          <Button
            data-cy='go-button'
            css={{ width: '100%' }}
            colorConcept='brand'
            label='Go'
            size='n'
            variant='primary'
            onClick={handleGoButtonClick}
          />
        </div>
      </div>
    </div>
  );
};

export default Home;
