import {
  Country,
  Feature,
  GrowthScaleStage,
  ProductFamily,
  ProductRegion,
  Region,
  Splitting,
  CropDescription,
  CropSubClass,
  PaginatedResponse,
  CalculationType,
  CropRegion,
  MMMValidation,
  PlanValidation,
  ValidationStatus,
  ConfigurationType,
  ProductRecommendationRateRules,
  ProductRecommendationSoilApplicationMethods,
  ProductRecommendationFoliarApplicationMethods,
  ProductRecommendations,
  DynamicPRecommendationApplicationRate,
} from '@common/types';

export const cropSubclassResponse: CropSubClass[] = [
  {
    id: 'e3cf8094-d3a5-4f40-8950-4359600e0543',
    cropClassId: 'f6111686-d61f-477f-9bb7-41f20fa2c6b6',
    faoId: 361,
    name: 'Almond<PERSON>',
    expressiveName: 'NUTS_ALMONDS',
    translationKey: 'crop.subclass.nuts_almonds',
    mediaUri: null,
    created: '2020-06-01T02:17:00.000Z',
    modified: '2022-04-27T11:20:43.053Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const regionResponse: Region[] = [
  {
    id: 'f932d668-0230-4da8-bc20-389fc30e3196',
    name: 'All',
    translationKey: 'region.germany_all',
    countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
    testRegion: false,
    created: '2020-12-01T23:15:26.000Z',
    modified: '2021-03-31T14:44:22.036Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];
export const countryResponse: Country[] = [
  {
    id: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
    name: 'Germany',
    translationKey: 'country.germany',
    countryCode: 'DE',
    productSetCode: '31',
    applicationTags: 'NUE,PA,Ntester,VRA Basic,VRA NSensor,GrassM,CNP,Yara Europe,Demo,Ayra',
    tagsConfiguration: {
      featureTags: [
        {
          id: 'f9b27375-31c8-4864-934b-66b29905eaf4',
          name: 'NUE',
        },
      ],
    },
    currencyId: '023e24b9-f657-4cf6-8481-2c839e67ba79',
    isDefaultUnitSystemMetric: true,
    windSpeedDefaultUnitId: '39e5474d-c32e-4bbd-91ed-d16ebd2c2803',
    precipitationAmountDefaultUnitId: '418c94b3-b21b-4aea-b988-e7018cf30d75',
    evapotranspirationRefDefaultUnitId: '418c94b3-b21b-4aea-b988-e7018cf30d75',
    qpfSnowAmountDefaultUnitId: 'eda717f3-59c7-42df-9d43-8baedfbbaaa6',
    temperatureDefaultUnitId: 'ecd6d8ea-fa7d-4440-b491-6c48c79d8d66',
    dewPointDefaultUnitId: 'ecd6d8ea-fa7d-4440-b491-6c48c79d8d66',
    availableNutrients: [
      {
        key: '6bf1ad43-461b-42ca-9763-6f478fcada0c',
        value: 'BpH',
      },
    ],
    created: '2020-12-01T23:15:20.000Z',
    modified: '2023-07-18T09:40:08.765Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];
export const featureResponse: Feature[] = [
  {
    id: '8347cace-39fd-4d99-9e8a-13f059d6d1ab',
    name: 'VRA NSensor XP',
    displayName: 'VRA N-sensor XP',
    group: 'Variable rate',
    code: 'F28',
    created: '2024-02-08T14:00:21.696Z',
    modified: '2024-02-08T14:00:21.696Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];
export const cropDescriptionResponse: CropDescription[] = [
  {
    id: '4c3e8463-9862-4bb2-9f94-6350269c16ec',
    cropSubClassId: 'ad4fcec3-c762-4907-a872-94db19e36794',
    name: ' ',
    expressiveName: 'GRASSLAND_TROPICAL_FORMATION',
    translationKey: 'crop.description.grassland__',
    mediaUri: null,
    chlorideSensitive: false,
    grainProteinPercentage: 0,
    atFarmCropType: 'grassland',
    created: '2023-05-15T18:05:45.691Z',
    modified: '2023-05-15T18:22:55.524Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];
export const cropRegionsResponse: CropRegion[] = [
  {
    id: '5e4750da-1ff4-4044-beb3-4275e0f994de',
    cropDescriptionId: '5ea575dc-0eac-4a71-a1c9-2e113f8b37c1',
    countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
    regionId: 'ca1d68f3-3d4c-414c-8ee4-30248d91453b',
    growthScaleId: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
    yieldBaseUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
    demandBaseUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    defaultSeedingDate: null,
    defaultHarvestDate: null,
    defaultYield: null,
    residuesRemovedN: null,
    residuesLeftN: null,
    residuesIncorporatedSpringN: null,
    residueNUptake: null,
    grainNUptake: null,
    residueFactor: null,
    nutrientUseEfficiency: null,
    applicationTags:
      'Nuptake,NDVI,Optimized map,VRA NSensor,FFDP Services,GEOS,MyKWS,Nuptake XP,Optimized map XP,VRA NSensor XP',
    tagsConfiguration: {
      featureTags: [
        {
          id: '4209630e-55cb-42ab-ae22-36eb295eeb3c',
          name: 'Nuptake',
        },
      ],
      partnerTags: [
        {
          id: 'eedf1b16-1d3f-4404-82fe-aedd0ea56ee1',
          name: 'FFDP Services',
        },
      ],
    },
    additionalProperties: null,
    agroCoreCode: null,
    calculationParameters: [
      {
        name: 'yield',
        shouldDisplay: true,
        defaultValue: 0,
        defaultUnitId: '00000000-0000-0000-0000-000000000000',
        type: 'number',
      },
    ],
    created: '2023-07-21T12:51:41.793Z',
    modified: '2023-09-25T12:55:22.892Z',
    deleted: null,
    modifiedBy: '<EMAIL>',
  },
  {
    id: '83255854-4b04-4af4-9922-c233d7b94f30',
    cropDescriptionId: '21d8bfa5-68f0-4c95-a8e3-53fba79aaa0c',
    countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
    regionId: 'f932d668-0230-4da8-bc20-389fc30e3196',
    growthScaleId: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
    yieldBaseUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
    demandBaseUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    defaultSeedingDate: null,
    defaultHarvestDate: null,
    defaultYield: 5.4,
    residuesRemovedN: 0,
    residuesLeftN: 0,
    residuesIncorporatedSpringN: 0,
    residueNUptake: 5,
    grainNUptake: 18.1,
    residueFactor: 0.8,
    nutrientUseEfficiency: 0.8,
    applicationTags:
      'VRA NSensor XP,Nuptake XP,Optimized map XP,NDVI,Weather forecast,Nuptake,Optimized map,VRA NSensor,CNP,MyKWS,MXP,FFDP Services,GEOS',
    tagsConfiguration: {
      featureTags: [
        {
          id: '8347cace-39fd-4d99-9e8a-13f059d6d1ab',
          name: 'VRA NSensor XP',
        },
        {
          id: 'f6df15de-c6f2-461c-8557-ceb1d21f5c3c',
          name: 'Nuptake XP',
        },
        {
          id: '2a4466fb-7e49-468b-8d5e-b53a36f71b7b',
          name: 'Optimized map XP',
        },
        {
          id: '8bbb6805-ba1b-47ab-b8f6-1927d5fddac2',
          name: 'NDVI',
        },
        {
          id: '66e76f3d-cef6-475e-98fd-80ee758feea3',
          name: 'Weather forecast',
        },
        {
          id: '4209630e-55cb-42ab-ae22-36eb295eeb3c',
          name: 'Nuptake',
        },
        {
          id: 'efb1ce81-fd08-45be-9ed3-b23419324127',
          name: 'Optimized map',
        },
        {
          id: 'd0a45944-aef5-4110-8a88-26e23b54157b',
          name: 'VRA NSensor',
        },
        {
          id: '92b5d200-0ccb-4d6a-a6f4-3651b935521c',
          name: 'CNP',
        },
      ],
      partnerTags: [
        {
          id: 'add177d1-7a9e-40c7-bfc7-940ee064e1d2',
          name: 'MyKWS',
        },
        {
          id: '5a1d0c06-4e7d-4a76-9213-69117e581887',
          name: 'MXP',
        },
        {
          id: 'eedf1b16-1d3f-4404-82fe-aedd0ea56ee1',
          name: 'FFDP Services',
        },
        {
          id: '6a29f237-227f-41b2-934a-b19827c89bc6',
          name: 'GEOS',
        },
        {
          id: 'beb37c1f-f429-40fa-b7ca-dc411ea1b316',
          name: 'AtFarm',
        },
      ],
    },
    additionalProperties: [
      {
        DefaultValue: '50',
        Min: 0,
        Required: true,
        ValueType: 'numberWithUnit',
        UnitTags: 'NMinUnit',
        CalculationType: CalculationType.ALL,
        Max: 250,
        Ordinal: 1,
        ApiPropertyName: 'Nmin',
        BaseUnit: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
        Name: 'N min',
        TranslationKey: 'additional.parameters.n_min',
      },
      {
        Options: [
          {
            Value: '<500',
            Name: '<500',
            TranslationKey: 'additional.parameters._500',
          },
          {
            Value: '>500',
            Name: '>500',
            TranslationKey: 'additional.parameters._500',
          },
        ],
        ValueType: 'list',
        Max: 0,
        ApiPropertyName: 'PrecipitationMm',
        BaseUnit: '',
        Name: 'Precipitation (mm)',
        DefaultValue: '>500',
        Min: 0,
        Required: true,
        CalculationType: CalculationType.ALL,
        UnitTags: '',
        Ordinal: 2,
        TranslationKey: 'additional.parameters.precipitation__mm_',
      },
      {
        Name: 'Do you want to cover the Mg demand?',
        Ordinal: 3,
        ValueType: 'list',
        DefaultValue: 'No',
        Required: true,
        BaseUnit: '',
        UnitTags: '',
        CalculationType: CalculationType.ALL,
        Min: 0,
        Max: 0,
        TranslationKey: 'additional.parameters.do_you_want_to_cover_the_mg_demand_',
        ApiPropertyName: 'DoYouWantToCoverTheMgDemand',
        Options: [
          {
            Value: 'No',
            Name: 'Nein',
            TranslationKey: 'additional.parameters.nein',
          },
          {
            Value: 'Yes',
            Name: 'Ja',
            TranslationKey: 'additional.parameters.ja',
          },
        ],
      },
    ],
    agroCoreCode: 9,
    created: '2021-03-02T13:20:46.821Z',
    modified: '2024-02-27T08:38:13.203Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
    calculationParameters: [
      {
        defaultValue: 4,
        name: 'yield',
        defaultUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
        type: 'number',
        shouldDisplay: true,
      },
      {
        defaultValue: '1109ec22-440d-4d45-b4d4-936db7669f7c',
        name: 'preCropTypeId',
        defaultUnitId: null,
        shouldDisplay: true,
        type: 'id',
      },
      {
        defaultValue: 0,
        name: 'preCropYield',
        defaultUnitId: '00000000-0000-0000-0000-000000000000',
        shouldDisplay: false,
        type: 'number',
      },
      {
        defaultValue: '168adfc2-74c2-4cb3-ab6d-09feb0629d2b',
        name: 'preCropResidueManagementId',
        defaultUnitId: null,
        shouldDisplay: true,
        type: 'id',
      },
      {
        defaultValue: '00000000-0000-0000-0000-000000000000',
        name: 'postCropTypeId',
        defaultUnitId: null,
        shouldDisplay: false,
        type: 'id',
      },
      {
        defaultValue: 0,
        name: 'postCropYield',
        defaultUnitId: '00000000-0000-0000-0000-000000000000',
        shouldDisplay: false,
        type: 'number',
      },
      {
        defaultValue: '00000000-0000-0000-0000-000000000000',
        name: 'postCropResidueManagementId',
        defaultUnitId: null,
        shouldDisplay: false,
        type: 'id',
      },
      {
        name: 'soilTypeId',
        shouldDisplay: true,
        defaultValue: '7afe2f77-46e1-45d7-9c2b-5fe7c1f16a01',
        defaultUnitId: null,
        type: 'id',
      },
      {
        defaultValue: '168adfc2-74c2-4cb3-ab6d-09feb0629d2b',
        name: 'cropResidueManagementId',
        defaultUnitId: null,
        shouldDisplay: true,
        type: 'id',
      },
      {
        defaultValue: [],
        name: 'previouslyAppliedProducts',
        defaultUnitId: null,
        shouldDisplay: true,
        type: 'array',
      },
    ],
  },
];

export const groupedPartners: string[] = [
  '5a1d0c06-4e7d-4a76-9213-69117e581887,8a5cb7fd-be7b-48c6-b26d-8c8b10e8dd17,beb37c1f-f429-40fa-b7ca-dc411ea1b316',
];

export const mmmValidations: { entities: MMMValidation[] } = {
  entities: [
    {
      id: '59a3a562-a4c7-4084-9dc2-5fe3e43361b2',
      countryId: '9473c999-f14c-4a2f-9f95-531aa9a1cf4e',
      cropRegionId: '2963d361-a089-4bd8-ba0b-ffd019799587',
      validationStatus: ValidationStatus.VALIDATED,
      configurationType: ConfigurationType.Cereal,
      created: '2024-02-08T19:54:51.221Z',
      modified: '2024-02-08T19:54:51.221Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
  ],
};

export const planValidations: { entities: PlanValidation[] } = {
  entities: [
    {
      id: '59a3a562-a4c7-4084-9dc2-5fe3e43361b2',
      countryId: '9473c999-f14c-4a2f-9f95-531aa9a1cf4e',
      cropRegionId: '2963d361-a089-4bd8-ba0b-ffd019799587',
      localName: null,
      soilAnalysisStatus: ValidationStatus.VALIDATED,
      leafAnalysisStatus: ValidationStatus.NOT_SET,
      created: '2024-02-08T19:54:51.221Z',
      modified: '2024-02-08T19:54:51.221Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
    {
      id: '1f9aad3a-9800-4c25-a77c-2268d7f41fe3',
      countryId: '9b932f75-e887-49ed-b054-229f18400dcd',
      cropRegionId: '26d3a3a2-158c-454c-a4b9-039eb42ed58b',
      localName: null,
      soilAnalysisStatus: ValidationStatus.VALIDATED,
      leafAnalysisStatus: ValidationStatus.NOT_SET,
      created: '2024-02-01T12:17:18.738Z',
      modified: '2024-02-29T12:05:32.875Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
    {
      id: '1f9aad3a-9800-4c25-a77c-2268d7f41fe3',
      countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
      cropRegionId: '5e4750da-1ff4-4044-beb3-4275e0f994de',
      localName: null,
      soilAnalysisStatus: ValidationStatus.VALIDATED,
      leafAnalysisStatus: ValidationStatus.NOT_SET,
      created: '2024-02-01T12:17:18.738Z',
      modified: '2024-02-29T12:05:32.875Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
    {
      id: '1f9aad3a-9800-4c25-a77c-2268d7f41fe3',
      countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
      cropRegionId: '83255854-4b04-4af4-9922-c233d7b94f30',
      localName: null,
      soilAnalysisStatus: ValidationStatus.VALIDATED,
      leafAnalysisStatus: ValidationStatus.NOT_SET,
      created: '2024-02-01T12:17:18.738Z',
      modified: '2024-02-29T12:05:32.875Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
    {
      id: '1f9aad3a-9800-4c25-a77c-2268d7f41fe3',
      countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
      cropRegionId: '111',
      localName: null,
      soilAnalysisStatus: ValidationStatus.NOT_SET,
      leafAnalysisStatus: ValidationStatus.NOT_SET,
      created: '2024-02-01T12:17:18.738Z',
      modified: '2024-02-29T12:05:32.875Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
  ],
};

export const planValidationResponse: PlanValidation = {
  id: '59a3a562-a4c7-4084-9dc2-5fe3e43361b2',
  countryId: '9473c999-f14c-4a2f-9f95-531aa9a1cf4e',
  cropRegionId: '2963d361-a089-4bd8-ba0b-ffd019799587',
  localName: null,
  soilAnalysisStatus: ValidationStatus.VALIDATED,
  leafAnalysisStatus: ValidationStatus.NOT_SET,
  created: '2024-02-08T19:54:51.221Z',
  modified: '2024-02-08T19:54:51.221Z',
  modifiedBy: '<EMAIL>',
  deleted: null,
};

export const productFamilyMock: ProductFamily = {
  id: 'ca7dc04a-0803-4ac0-a583-c13cb7fa44cb',
  name: 'YaraMila',
  translationKey: 'product.family.yaramila',
  mediaUri: [
    {
      key: 'DEFAULT',
      value: 'https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraMila.svg',
    },
    {
      key: 'WhiteLogo',
      value:
        'https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraMilaWhiteLogo.svg',
    },
  ],
  created: new Date('2020-11-30T19:56:55.000Z'),
  modified: new Date('2022-02-07T13:54:47.586Z'),
  modifiedBy: '<EMAIL>',
};
export const useProductsFamilyResponse = [productFamilyMock];

export const productRegionMock: ProductRegion = {
  id: 'a11847a1-21ba-49f6-9a9b-ff1c0d9169c1',
  countryId: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
  regionId: 'f932d668-0230-4da8-bc20-389fc30e3196',
  productId: '19798d39-6199-4c1f-81e2-bf6d8e8f6704',
  localizedName: 'YaraMila',
  isAvailable: true,
  applicationTags: 'GrassM',
  tagsConfiguration: {
    featureTags: [
      {
        id: 'dd06bb69-fb65-4f89-a905-f444bf28138c',
        name: 'GrassM',
      },
    ],
    partnerTags: [],
  },
  productFamilyId: 'ca7dc04a-0803-4ac0-a583-c13cb7fa44cb',
  productFamily: {
    id: 'ca7dc04a-0803-4ac0-a583-c13cb7fa44cb',
    name: 'YaraMila',
    translationKey: 'product.family.yaramila',
    mediaUri: [
      {
        key: 'DEFAULT',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraMila.svg',
      },
      {
        key: 'WhiteLogo',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraMilaWhiteLogo.svg',
      },
    ],
    created: new Date('2020-11-30T19:56:55.000Z'),
    modified: new Date('2022-02-07T13:54:47.586Z'),
    modifiedBy: '<EMAIL>',
  },
  name: 'YaraMila',
  translationKey: null,
  lowChloride: false,
  n: 25,
  nUnitId: '393f6e30-0af3-4336-a65f-953701d1173a',
  p: 7,
  pUnitId: '3eacac5f-d65d-4248-89c9-8d7f9337ebe9',
  k: 7,
  kUnitId: 'ae2132a8-f1e6-4170-877c-ab8ccc2c6656',
  mg: 0,
  mgUnitId: '004a54dc-c840-47ec-bbeb-833b78fb8872',
  s: 0,
  sUnitId: 'fe8dd085-1034-40b8-8891-af688b1a94fa',
  ca: 0,
  caUnitId: 'b04b24b7-43d7-4980-8e58-d2e1ca671d28',
  b: 0,
  bUnitId: '0ca37d40-bb10-45b4-b2a3-664072b4693b',
  zn: 0,
  znUnitId: '0a9927a7-c44d-417d-bdb1-c2e63b1689e3',
  mn: 0,
  mnUnitId: '46dc12da-31c5-4611-a7c3-a88344f7d682',
  cu: 0,
  cuUnitId: '168bc22c-a9ad-4cce-9ede-5b465aa48754',
  fe: 0,
  feUnitId: '9d3fc031-9c67-46c0-a259-126a2db5863d',
  mo: 0,
  moUnitId: '3a65cae7-d5cf-46e4-be63-b69171d367da',
  na: 0,
  naUnitId: '47216f05-d68e-4777-83ca-c97267db1cee',
  se: 0,
  seUnitId: 'a888bf4a-a438-4e11-862d-b1a6aea8ce5a',
  co: 0,
  coUnitId: 'e337da8e-6cbc-4831-8a5f-9fe180179d5f',
  no3: 0,
  nh4: 0,
  urea: 0,
  dhCode: null,
  url: null,
  productTypeId: '92385a38-2b92-4c35-b14b-f774e5491748',
  density: 1,
  utilizationN: 0,
  utilizationNH4: 0,
  dryMatter: 0,
  spreaderLoss: 0,
  tank: null,
  electricalConductivity: null,
  pH: null,
  solubility5C: null,
  solubility20C: null,
  skuNumber: '',
  created: new Date('2023-07-26T11:46:55.533Z'),
  modified: new Date('2023-07-26T11:46:55.533Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
};
const additionalProductRegion = {
  id: '94f90c60-aeae-4485-b91c-c6fd9546d333',
  countryId: '3e4964c8-22fd-4ea5-a209-65809fba3c34',
  regionId: 'e14a7383-a371-43ca-b5c4-b90ba939fd87',
  productId: 'e7d30943-e670-444f-adb1-ea99999f72e0',
  productFamilyId: '39cc281f-2da0-4b51-8657-7cf23475d762',
  productTypeId: '9e630e40-ffcc-4fb0-9d56-f4239d6939d7',
  name: 'Mais',
  localizedName: 'MAÍZ',
  isAvailable: true,
  lowChloride: false,
  translationKey: null,
  url: 'https://www.yara.es/nutricion-vegetal/productos/yaravita/yaravita-maiz/',
  dhCode: null,
  density: 1.49,
  n: 0,
  nUnitId: '393f6e30-0af3-4336-a65f-953701d1173a',
  p: 29.5,
  pUnitId: '3eacac5f-d65d-4248-89c9-8d7f9337ebe9',
  k: 5,
  kUnitId: 'ae2132a8-f1e6-4170-877c-ab8ccc2c6656',
  mg: 4.5,
  mgUnitId: '041057eb-e8d3-492f-b82f-656e93b32800',
  s: 0,
  sUnitId: '2a3d91f7-e1f2-4811-944b-32da80507d6b',
  ca: 0,
  caUnitId: 'c93686e7-d4c5-4b37-9733-c00a882e7d56',
  b: 0,
  bUnitId: '0ca37d40-bb10-45b4-b2a3-664072b4693b',
  zn: 3.1,
  znUnitId: '0a9927a7-c44d-417d-bdb1-c2e63b1689e3',
  mn: 0,
  mnUnitId: '46dc12da-31c5-4611-a7c3-a88344f7d682',
  cu: 0,
  cuUnitId: '168bc22c-a9ad-4cce-9ede-5b465aa48754',
  fe: 0,
  feUnitId: '9d3fc031-9c67-46c0-a259-126a2db5863d',
  mo: 0,
  moUnitId: '3a65cae7-d5cf-46e4-be63-b69171d367da',
  na: 0,
  naUnitId: '47216f05-d68e-4777-83ca-c97267db1cee',
  se: 0,
  seUnitId: 'a888bf4a-a438-4e11-862d-b1a6aea8ce5a',
  co: 0,
  coUnitId: 'e337da8e-6cbc-4831-8a5f-9fe180179d5f',
  co3: 0,
  co3UnitId: 'ef76d6c7-7a04-4ee0-bfe8-e9f8b6af8d06',
  cl: 0,
  clUnitId: 'a87fe875-3ab3-4188-8d14-105cb54ffb8a',
  no3: 0,
  nh4: 0,
  urea: 0,
  utilizationN: 100,
  utilizationNH4: 100,
  dryMatter: 0,
  spreaderLoss: 0,
  tank: null,
  electricalConductivity: null,
  pH: null,
  solubility5C: null,
  solubility20C: null,
  skuNumber: '',
  applicationTags: 'CNP,Memorandum,Ayra,MXP,AtFarm',
  tagsConfiguration: {
    featureTags: [
      {
        id: '92b5d200-0ccb-4d6a-a6f4-3651b935521c',
        name: 'CNP',
      },
    ],
    partnerTags: [
      {
        id: 'cfb4a1b2-b30c-415e-aead-e1aed03611d1',
        name: 'Memorandum',
      },
      {
        id: '8a953126-e7f0-4462-87b9-21eb3c7447b6',
        name: 'Ayra',
      },
      {
        id: '5a1d0c06-4e7d-4a76-9213-69117e581887',
        name: 'MXP',
      },
      {
        id: 'beb37c1f-f429-40fa-b7ca-dc411ea1b316',
        name: 'AtFarm',
      },
    ],
  },
  created: new Date('2022-08-24T14:20:09.203Z'),
  modified: new Date('2024-05-24T08:15:28.394Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
  productFamily: {
    id: '39cc281f-2da0-4b51-8657-7cf23475d762',
    name: 'YaraVita',
    translationKey: 'product.family.yaravita',
    mediaUri: [
      {
        key: 'DEFAULT',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraVita.svg',
      },
      {
        key: 'WhiteLogo',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraVitaWhiteLogo.svg',
      },
    ],
    created: new Date('2020-11-30T19:56:54.000Z'),
    modified: new Date('2021-08-17T08:42:31.759Z'),
    modifiedBy: '<EMAIL>',
  },
  productType: {
    id: '9e630e40-ffcc-4fb0-9d56-f4239d6939d7',
    name: 'Foliar',
    translationKey: 'product.type.foliar',
    expressiveName: 'FOLIAR',
    mediaUri: null,
    created: new Date('2020-11-30T19:56:47.000Z'),
    modified: new Date('2020-11-30T19:56:47.000Z'),
    modifiedBy: '<EMAIL>',
  },
};
export const useProductsByRegionResponse: ProductRegion[] = [
  productRegionMock,
  additionalProductRegion,
];
export const useProductsByRegionEmptyResponse: ProductRegion[] = [];
const productsByRegionMultiple = Array(6).fill(productRegionMock);
export const useProductsByRegionMultipleResponse: ProductRegion[] = productsByRegionMultiple;

export const mockGrowthScaleStages: GrowthScaleStage[] = [
  {
    translationKey: 'growthscalestage.bbch_cereals_tillering',
    abbreviatedName: 'Tillering',
    baseOrdinal: null,
    mediaUri: [
      {
        key: 'DEFAULT',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg',
      },
    ],
    created: new Date('2024-02-23T18:12:35.642Z'),
    modified: new Date('2024-02-23T18:12:35.642Z'),
    modifiedBy: '<EMAIL>',
    deleted: null,
    id: '2b0e89a2-569f-4f1f-8700-93d280460546',
    growthScaleId: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
    name: 'Tillering',
    ordinal: 21.2,
  },
  {
    translationKey: 'growthscalestage.bbch_cereals_stem_elongation',
    abbreviatedName: 'Stem elongation',
    baseOrdinal: null,
    mediaUri: [
      {
        key: 'DEFAULT',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg',
      },
    ],
    created: new Date('2025-02-07T11:18:46.302Z'),
    modified: new Date('2025-02-07T11:18:46.307Z'),
    modifiedBy: '<EMAIL>',
    deleted: null,
    id: '12edd160-2679-4485-8324-0ef708eb158f',
    growthScaleId: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
    name: 'Stem elongation',
    ordinal: 30.2,
  },
  {
    id: 'dc14bec7-0367-4b2c-b773-879e90bfacfd',
    growthScaleId: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
    name: 'Flag leaf',
    abbreviatedName: 'Flag leaf',
    translationKey: 'growthscalestage.bbch_cereals_flag_leaf',
    mediaUri: [
      {
        key: 'DEFAULT',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg',
      },
    ],
    ordinal: 37.1,
    baseOrdinal: null,
    created: new Date('2024-02-23T18:17:01.059Z'),
    modified: new Date('2024-02-23T18:34:41.828Z'),
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
  {
    translationKey: 'growthscalestage.bbch_cereals_flowering',
    abbreviatedName: 'Flowering',
    baseOrdinal: null,
    mediaUri: [
      {
        key: 'DEFAULT',
        value:
          'https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg',
      },
    ],
    created: new Date('2025-02-07T11:40:35.237Z'),
    modified: new Date('2025-02-07T11:41:35.149Z'),
    modifiedBy: '<EMAIL>',
    deleted: null,
    id: '31f6dc75-7cd8-4f51-927f-e13d03e59283',
    growthScaleId: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
    name: 'Flowering',
    ordinal: 60.2,
  },
];

export const mockGrowthScaleStagesResponse = {
  entities: mockGrowthScaleStages,
  cursor: 4,
  hasNextPage: false,
};

export const useFetchSplittingConfigurationResponse: PaginatedResponse<Splitting[]> = {
  entities: [
    {
      id: '6be86471-be7c-40f3-b068-71f080ed357c',
      countryId: '8e13df31-9519-4a61-937f-8f6952dacd8f',
      cropRegionId: '8e13df31-9519-4a61-937f-8f6952dacd8f',
      configuration: {
        data: [
          {
            name: '',
            splitId: '4e92604c-03da-4b6a-ac9e-a94ea1723a57',
            growthPhaseNo: 1,
            daysDuration: 0,
            mediaUri: [
              {
                key: 'DEFAULT',
                value:
                  'https://polaris-axial-static-medias.yarapolaris.com/growth_scale/sugar_cane_second_year/6.svg',
              },
            ],
            nutrientsSplit: {
              n: {
                splitPercent: 0,
                nutrientId: '880abcda-5ee5-4068-879c-94489be314d5',
                nutrientFormId: '880abcda-5ee5-4068-879c-94489be314d5',
              },
              p: {
                splitPercent: 0,
                nutrientId: 'f2c275c4-1522-4524-8747-08ace254b155',
                nutrientFormId: 'f2c275c4-1522-4524-8747-08ace254b155',
              },
              k: {
                splitPercent: 0,
                nutrientId: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
                nutrientFormId: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
              },
              ca: {
                splitPercent: 0,
                nutrientId: '5fe008ca-d2d7-48a6-921b-8b53c781a4ab',
                nutrientFormId: '5fe008ca-d2d7-48a6-921b-8b53c781a4ab',
              },
              mg: {
                splitPercent: 0,
                nutrientId: 'd764880a-c5d8-4a9a-ab49-a2b3ba59970d',
                nutrientFormId: 'd764880a-c5d8-4a9a-ab49-a2b3ba59970d',
              },
              s: {
                splitPercent: 0,
                nutrientId: '0163eeff-5d87-4749-bcf7-3e4be6732808',
                nutrientFormId: '0163eeff-5d87-4749-bcf7-3e4be6732808',
              },
              b: {
                splitPercent: 0,
                nutrientId: '6990195e-b1a3-4218-90db-f38c9cfae633',
                nutrientFormId: '6990195e-b1a3-4218-90db-f38c9cfae633',
              },
              cu: {
                splitPercent: 0,
                nutrientId: '578e38c1-e641-4aa9-9a8e-6a428e55f1ec',
                nutrientFormId: '578e38c1-e641-4aa9-9a8e-6a428e55f1ec',
              },
              fe: {
                splitPercent: 0,
                nutrientId: '5c7eb818-06c1-4013-b6d2-2f02f1e54d44',
                nutrientFormId: '5c7eb818-06c1-4013-b6d2-2f02f1e54d44',
              },
              mn: {
                splitPercent: 0,
                nutrientId: '45b614a2-cce3-4c85-9c63-b459396e9aef',
                nutrientFormId: '45b614a2-cce3-4c85-9c63-b459396e9aef',
              },
              zn: {
                splitPercent: 0,
                nutrientId: 'a85856aa-c3cf-433d-8357-4dbe0259d1f9',
                nutrientFormId: 'a85856aa-c3cf-433d-8357-4dbe0259d1f9',
              },
              mo: {
                splitPercent: 0,
                nutrientId: '953dcd7a-0de1-4997-8360-985a73af3cb6',
                nutrientFormId: '953dcd7a-0de1-4997-8360-985a73af3cb6',
              },
            },
          },
        ],
        startDate: new Date('2022-12-29T10:56:11.768Z'),
      },
      created: new Date('2022-12-29T10:56:11.768Z'),
      modified: new Date('2022-12-29T10:56:11.768Z'),
      modifiedBy: '<EMAIL>',
      deleted: null,
    },
  ],
  cursor: 0,
  hasNextPage: false,
};

export const mockSoilPhase = {
  id: '774f3d1f-31f2-4275-a8a3-45b4c75e0827',
  splitId: '67a68f19-1451-49c6-b9af-374c14dc4550',
  recommendedProducts: [
    '94f90c60-aeae-4485-b91c-c6fd9546d333',
    'a11847a1-21ba-49f6-9a9b-ff1c0d9169c1',
  ],
  applicationMethod: ProductRecommendationSoilApplicationMethods.BASE_DRESSING,
  applicationCondition: 'Optimal',
  phaseName: 'Phase name',
  phaseImage: [{ key: 'DEFAULT', value: 'string' }],
  phaseNumber: 1,
  ordinal: 1,
  rateRule: ProductRecommendationRateRules.DYNAMIC,
  dynamicApplicationRate: {
    minRate: 2,
    maxRate: 3,
    rateUnits: [],
    dynamicApplicationRateMethod: DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS,
    rateDefiningNutrients: [],
    productPlant: null,
    productPlantUnits: [],
  },
};

export const mockFoliarPhase = {
  id: '00d1a972-b76f-4efc-97ff-4ec8eee42766',
  splitId: 'b883cbc7-fd5d-4f33-8f83-95a351eb90ab',
  recommendedProducts: ['Product C', 'Product D'],
  applicationMethod: ProductRecommendationFoliarApplicationMethods.FOLIAR,
  applicationCondition: 'Dry weather',
  phaseName: 'Phase name',
  phaseImage: [{ key: 'DEFAULT', value: 'string' }],
  phaseNumber: 1,
  ordinal: 1,
  rateRule: ProductRecommendationRateRules.FIXED,
  fixedApplicationRate: {
    applicationValue: 100,
    applicationValueUnitsIds: ['id1', 'id2'],
  },
};

export const productRecommendationsMock: ProductRecommendations = {
  id: '2e13df31-9519-4a61-937f-8f6952dacd8f',
  countryId: '8e13df31-9519-4a61-937f-8f6952dacd8f',
  cropRegionId: '8e13df31-9519-4a61-937f-8f6952dacd8f',
  created: new Date('2022-12-29T10:56:11.768Z'),
  modified: new Date('2022-12-29T10:56:11.768Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
  configuration: {
    data: {
      soilApplications: [mockSoilPhase],
      foliarApplications: [mockFoliarPhase],
    },
  },
};

export const mockCerealsSoilStage = {
  id: '9dd86763-a97b-4a70-886f-e6686ea237bb',
  stageNo: 1,
  stageName: 'stage Name',
  stageImageUrl: 'url',
  productType: 'fc8c2645-898c-4e38-b077-79de5196a061',
  selectedProducts: ['94f90c60-aeae-4485-b91c-c6fd9546d333'],
  rateDefNutrientName: 'P',
  maxAppRate: null,
  minAppRate: 1,
  ruleName: 'REMINDER',
  rateRule: 'P2O5',
  updateInfo: 'string',
};

export const mockCerealsFoliarStage = {
  id: '0ddc2bde-2886-4ab7-b7ff-8658abfe685f',
  stageNo: 1,
  stageName: 'foliar stage name',
  stageImageUrl: 'url',
  productType: 'Foliar',
  selectedProducts: ['product'],
  fixedAppRate: 4.44,
  updateInfo: 'info',
};
