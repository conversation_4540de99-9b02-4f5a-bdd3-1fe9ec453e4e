// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: Home snapshot with provider and test go button click 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="content"
        data-cy="home-content"
      >
        <div
          class="container-widget"
          data-cy="widget-container"
        >
          <div
            class="title-container"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-iJzrTx-size-l c-iFoEyZ-iPJLV-css"
              data-cy="onboarding-title"
            >
              polaris.homepage.title
            </h1>
            <h1
              class="c-halGGX c-halGGX-jCttuB-size-l c-halGGX-idWpzxV-css"
              data-cy="onboarding-text"
            >
              polaris.homepage.text
            </h1>
          </div>
          <div
            class="dropdown-container"
          >
            <div
              data-cy="country-select-home"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-n c-iLfdtR-gBsWrO-iconPresent-false"
                  >
                    polaris.homepage.countryDropdown.placeholder
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-bkfUjw-selectSize-n"
                  >
                    polaris.homepage.countryDropdown.placeholder
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r10:"
                    aria-expanded="false"
                    aria-label="polaris.homepage.countryDropdown.ariaLabel"
                    class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      />
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="region-select-home"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-n c-iLfdtR-gBsWrO-iconPresent-false"
                  >
                    polaris.homepage.regionDropdown.placeholder
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-bkfUjw-selectSize-n"
                  >
                    polaris.homepage.regionDropdown.placeholder
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r11:"
                    aria-disabled="false"
                    aria-expanded="false"
                    aria-label="polaris.homepage.regionDropdown.ariaLabel"
                    class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      />
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="crop-select-home"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder"
                  >
                    polaris.homepage.cropDropdown.placeholder
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r12:"
                    aria-disabled="false"
                    aria-expanded="false"
                    aria-label="polaris.homepage.cropDropdown.ariaLabel"
                    class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      />
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                >
                  polaris.homepage.cropDropdown.helperText
                </p>
              </div>
            </div>
            <div
              data-cy="feature-select-home"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder"
                  >
                    polaris.homepage.featureDropdown.placeholder
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r13:"
                    aria-disabled="false"
                    aria-expanded="false"
                    aria-label="polaris.homepage.featureDropdown.ariaLabel"
                    class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      />
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <button
              class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary c-hRrCwb-ifGHEql-css"
              data-cy="go-button"
            >
              <span
                class="c-iepcqn"
              >
                Go
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="content"
      data-cy="home-content"
    >
      <div
        class="container-widget"
        data-cy="widget-container"
      >
        <div
          class="title-container"
        >
          <h1
            class="c-iFoEyZ c-iFoEyZ-iJzrTx-size-l c-iFoEyZ-iPJLV-css"
            data-cy="onboarding-title"
          >
            polaris.homepage.title
          </h1>
          <h1
            class="c-halGGX c-halGGX-jCttuB-size-l c-halGGX-idWpzxV-css"
            data-cy="onboarding-text"
          >
            polaris.homepage.text
          </h1>
        </div>
        <div
          class="dropdown-container"
        >
          <div
            data-cy="country-select-home"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-n c-iLfdtR-gBsWrO-iconPresent-false"
                >
                  polaris.homepage.countryDropdown.placeholder
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-bkfUjw-selectSize-n"
                >
                  polaris.homepage.countryDropdown.placeholder
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r10:"
                  aria-expanded="false"
                  aria-label="polaris.homepage.countryDropdown.ariaLabel"
                  class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    />
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="region-select-home"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-n c-iLfdtR-gBsWrO-iconPresent-false"
                >
                  polaris.homepage.regionDropdown.placeholder
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-bkfUjw-selectSize-n"
                >
                  polaris.homepage.regionDropdown.placeholder
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r11:"
                  aria-disabled="false"
                  aria-expanded="false"
                  aria-label="polaris.homepage.regionDropdown.ariaLabel"
                  class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    />
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="crop-select-home"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder"
                >
                  polaris.homepage.cropDropdown.placeholder
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r12:"
                  aria-disabled="false"
                  aria-expanded="false"
                  aria-label="polaris.homepage.cropDropdown.ariaLabel"
                  class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    />
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              >
                polaris.homepage.cropDropdown.helperText
              </p>
            </div>
          </div>
          <div
            data-cy="feature-select-home"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder"
                >
                  polaris.homepage.featureDropdown.placeholder
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r13:"
                  aria-disabled="false"
                  aria-expanded="false"
                  aria-label="polaris.homepage.featureDropdown.ariaLabel"
                  class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    />
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <button
            class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary c-hRrCwb-ifGHEql-css"
            data-cy="go-button"
          >
            <span
              class="c-iepcqn"
            >
              Go
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
