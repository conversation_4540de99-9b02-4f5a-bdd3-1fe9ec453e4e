/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { setupServer } from 'msw/node';
import {
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  featureResponse,
} from '@common/mocks';
import AppContext, {
  AppContext as AppInitialContext,
  AppProvider,
  AppProviderMethods,
  useAppContext,
} from '@widgets/Polaris/src/providers/AppProvider';
import {
  createDropdownValues,
  createFeatureDropdownValues,
  cropDropdownValues,
  filteredCropDescriptionsByRegion,
  findCountry,
  findCrop,
  urlMapCropDesc,
  urlMapCropSubClass,
  findFeature,
  findRegion,
} from '..';
import { Country, Feature, Region, CropRegion, CropDescription, CropSubClass } from '@common/types';
import { BrowserRouter as Router } from 'react-router-dom';
import { GroupContext } from '../helpers/group.styled';
import { NavbarProvider } from '@libs/nav-context';
import { POLARIS_API_GW_ENDPOINTS } from '@common/constants';
import { countryResponse, cropSubclassResponse, regionResponse } from '../mock-data/MockData';
import Home from '../Home';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('widget: Home', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  const { t } = useTranslation();
  const regionLabelText = t('polaris.homepage.regionDropdown.ariaLabel');
  const cropLabelText = t('polaris.homepage.cropDropdown.ariaLabel');
  const featureLabelText = t('polaris.homepage.featureDropdown.ariaLabel');
  const errorText = t('polaris.error.isRequired', { name: 'Country' });
  const mockAppProviderValue = {
    selectedCountry: {
      id: 'a8ccbf57-6056-44ec-9561-e60bd0f30abc',
      name: 'Germany',
      __typename: 'Country',
    },
    selectedRegion: {
      id: '1',
      name: 'Bavaria',
    },
    selectedCrop: {
      id: '1',
      name: 'Wheat',
    },
    selectedFeature: {
      id: '1',
      name: 'FX insight',
    },
    countries: [],
    regions: [],
    crops: [],
    features: [],
    isCoachmarkVisible: true,
    methods: {
      getCountry: jest.fn(),
      getRegion: jest.fn(),
      setCountry: jest.fn(),
      setRegion: jest.fn(),
      setCrop: jest.fn(),
      setFeature: jest.fn(),
      setIsCoachmarkVisible: jest.fn(),
      setCropDemandAnalyses: jest.fn(),
      updateCropDemandAnalyses: jest.fn(),
      filterCropDemandAnalyses: jest.fn(),
      addCropDemandAnalyses: jest.fn(),
      setSelectedCountryUnits: jest.fn(),
    },
    cropDemandAnalyses: [],
    dispatch: jest.fn(),
  } as unknown as AppInitialContext & {
    methods: AppProviderMethods;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    dispatch: React.Dispatch<any>;
  };

  it('widget: Home title', async () => {
    const component = render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );

    const homeWrapper = component.getByTestId('home-content');
    expect(homeWrapper).toBeInTheDocument();
    const title = component.getByText(t('polaris.homepage.title'));
    expect(title).toBeInTheDocument();
  });

  it('widget: Home dropdown select label', async () => {
    render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );
    const country = screen.getByLabelText(t('polaris.homepage.countryDropdown.ariaLabel'));
    expect(country).toBeTruthy();

    const region = screen.getByLabelText(regionLabelText);
    expect(region).toBeTruthy();

    const crop = screen.getByLabelText(cropLabelText);
    expect(crop).toBeTruthy();

    const feature = screen.getByLabelText(featureLabelText);
    expect(feature).toBeTruthy();
  });

  it('widget: Go button', () => {
    render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );
    expect(screen.getByTestId('go-button')).toBeInTheDocument();
  });

  it('widget: Home, country selection enables region, crop, and feature', async () => {
    const component = render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );
    const countryOptionSelect = component.getAllByRole('combobox')[0];
    expect(countryOptionSelect).toBeInTheDocument();

    expect(
      component
        .getByRole('combobox', {
          name: regionLabelText,
        })
        .getAttribute('aria-disabled'),
    ).toBe('true');

    expect(
      component
        .getByRole('combobox', {
          name: cropLabelText,
        })
        .getAttribute('aria-disabled'),
    ).toBe('true');

    expect(
      component
        .getByRole('combobox', {
          name: featureLabelText,
        })
        .getAttribute('aria-disabled'),
    ).toBe('true');

    fireEvent.change(countryOptionSelect, {
      target: { value: 'India' },
    });
    expect(countryOptionSelect).toHaveAttribute('value', 'India');
  });

  it('widget: Home region select', async () => {
    const component = render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );
    const regionOptionSelect = component.getByRole('combobox', {
      name: regionLabelText,
    });
    expect(regionOptionSelect).toBeInTheDocument();

    fireEvent.change(regionOptionSelect, {
      target: { value: 'all' },
    });
    expect(regionOptionSelect).toHaveAttribute('value', 'all');
  });

  it('widget: Home crop select', async () => {
    const component = render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );
    const cropOptionSelect = component.getByRole('combobox', {
      name: cropLabelText,
    });
    expect(cropOptionSelect).toBeInTheDocument();

    fireEvent.change(cropOptionSelect, {
      target: { value: 'Potato' },
    });
    expect(cropOptionSelect).toHaveAttribute('value', 'Potato');
  });

  it('widget: Home feature select', async () => {
    const component = render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );
    const featureOption = component.getByRole('combobox', {
      name: featureLabelText,
    });
    expect(featureOption).toBeInTheDocument();

    fireEvent.change(featureOption, {
      target: { value: 'Test feature' },
    });
    expect(featureOption).toHaveAttribute('value', 'Test feature');
  });

  it('should show error message if country is not selected before clicking Go button', async () => {
    const { getByText } = render(
      <AppProvider>
        <NavbarProvider>
          <Router>
            <Home />
          </Router>
        </NavbarProvider>
      </AppProvider>,
    );

    fireEvent.click(getByText('Go'));
    expect(getByText(errorText)).toBeInTheDocument();
  });

  it('snapshot with provider and test go button click', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <Home />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(component).toMatchSnapshot();

    fireEvent.click(component.getByText('Go'));
    expect(component.queryByText(errorText)).not.toBeInTheDocument();
  });

  //useAppContext
  it('throws an error when used outside of AppProvider', () => {
    const MockComponent: React.FC = () => {
      // Attempt to use the hook outside of AppProvider
      try {
        useAppContext();
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        expect(error.message).toBe('useAppContext must be used within an AppProvider');
      }
      return <div>Mock Component</div>;
    };

    // Render the component and expect it to throw an error
    render(<MockComponent />);
  });

  it('should initialize the context with default values', () => {
    const { getByText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AppContext.Consumer>
          {(context) => (
            <div>
              <span>Selected Country:{context.selectedCountry?.name}</span>
              <span>Selected Region:{context.selectedRegion?.name}</span>
            </div>
          )}
        </AppContext.Consumer>
      </AppContext.Provider>,
    );

    expect(
      getByText(`Selected Country:${mockAppProviderValue?.selectedCountry?.name}`),
    ).toBeInTheDocument();
    expect(
      getByText(`Selected Region:${mockAppProviderValue?.selectedRegion?.name}`),
    ).toBeInTheDocument();
  });

  it('should initialize the context with custom values', () => {
    const { getByText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AppContext.Consumer>
          {(context) => (
            <div>
              <span>{context.selectedCountry?.name}</span>
              <span>{context.selectedRegion?.name}</span>
              <span>{context.selectedCrop?.name}</span>
              <span>{context.selectedFeature?.name}</span>
            </div>
          )}
        </AppContext.Consumer>
      </AppContext.Provider>,
    );

    expect(getByText('Germany')).toBeInTheDocument();
    expect(getByText('Bavaria')).toBeInTheDocument();
    expect(getByText('Wheat')).toBeInTheDocument();
    expect(getByText('FX insight')).toBeInTheDocument();
  });

  //functions
  it('tests findCrop function', () => {
    const value = ' e3cf8094-d3a5-4f40-8950-4359600e0543';
    const cropResponse = cropSubclassResponse as unknown as CropSubClass[];
    const cropDetails = findCrop(value, cropResponse);
    expect(cropDetails).toBe(undefined);
  });

  it('tests dropdown value generetion', () => {
    const countriesData: Country[] = [];
    const dropdownData = createDropdownValues(countriesData);
    expect(dropdownData).toEqual([]);
  });

  it('tests dropdown value generetion', () => {
    const regionsData: Region[] = [];
    const dropdownData = createDropdownValues(regionsData);
    expect(dropdownData).toEqual([]);
  });

  it('tests dropdown values', () => {
    const cropRegions = [
      { id: 1, name: 'test' },
      { id: 3, name: 'test3' },
    ] as unknown as Region[];
    const dropdownData = createDropdownValues(cropRegions);
    expect(dropdownData).toEqual([
      { value: 1, text: 'test' },
      { value: 3, text: 'test3' },
    ]);
  });

  it('should return empty array when cropDescs or cropRegions are undefined', () => {
    const result = filteredCropDescriptionsByRegion(undefined, []);
    expect(result).toEqual([]);
  });

  it('should filter crop descriptions based on provided regions', () => {
    const cropDescs = [{ id: 1 }, { id: 2 }, { id: 3 }] as unknown as CropDescription[];
    const cropRegions = [
      { cropDescriptionId: 1 },
      { cropDescriptionId: 3 },
    ] as unknown as CropRegion[];
    const result = filteredCropDescriptionsByRegion(cropDescs, cropRegions);
    expect(result).toEqual([{ id: 1 }, { id: 3 }]);
  });

  it('should return an empty array when cropResponse is undefined', () => {
    const result = cropDropdownValues(undefined, []);
    expect(result).toEqual([]);
  });

  it('should return an array of dropdown values based on cropResponse and filteredCropDescriptions', () => {
    const cropResponse = [
      { id: '1', name: 'Crop1' },
      { id: '2', name: 'Crop2' },
    ] as unknown as CropSubClass[];
    const filteredCropDescriptions = [
      { cropSubClassId: '1', name: 'Description1', id: '3' },
      { cropSubClassId: '2', name: 'Description2', id: '4' },
    ] as unknown as CropDescription[];
    const result = cropDropdownValues(cropResponse, filteredCropDescriptions);
    expect(result).toEqual([
      { value: '3', text: 'Crop1 Description1' },
      { value: '4', text: 'Crop2 Description2' },
    ]);
  });

  it('should handle cases where cropDescription is not found for a cropSubClass', () => {
    const cropResponse = [
      { id: '1', name: 'Crop1' },
      { id: '2', name: 'Crop2' },
    ] as unknown as CropSubClass[];
    const filteredCropDescriptions = [
      { cropSubClassId: '1', name: 'Description1', id: '3' },
    ] as unknown as CropDescription[];
    const result = cropDropdownValues(cropResponse, filteredCropDescriptions);
    expect(result).toEqual([{ value: '3', text: 'Crop1 Description1' }]);
  });

  it('should return undefined when cropResponse is undefined', () => {
    const result = findCrop('1', undefined);
    expect(result).toBeUndefined();
  });

  it('should return undefined when no crop with given id is found', () => {
    const cropResponse = [
      { id: '1', name: 'Crop1' },
      { id: '2', name: 'Crop2' },
    ] as unknown as CropSubClass[];
    const result = findCrop('3', cropResponse);
    expect(result).toBeUndefined();
  });

  it('should return the crop with the given id', () => {
    const cropResponse = [
      { id: '1', name: 'Crop1' },
      { id: '2', name: 'Crop2' },
    ] as unknown as CropSubClass[];
    const result = findCrop('1', cropResponse);
    expect(result).toEqual({ id: '1', name: 'Crop1' });
  });

  it('should return an empty array when input is undefined', () => {
    expect(urlMapCropDesc(undefined)).toEqual([]);
  });

  it('should return an array of URLs when input contains valid crop regions', () => {
    const cropRegions = [
      { cropDescriptionId: 1 },
      { cropDescriptionId: 2 },
    ] as unknown as CropRegion[];
    const expectedUrls = cropRegions.map(
      (region) =>
        `${POLARIS_API_GW_ENDPOINTS.CROP_API}/crop-descriptions/${region.cropDescriptionId}`,
    );
    expect(urlMapCropDesc(cropRegions)).toEqual(expectedUrls);
  });

  it('should return an empty array when input is an empty array', () => {
    expect(urlMapCropDesc([])).toEqual([]);
  });

  it('should return an empty array when input is undefined', () => {
    expect(urlMapCropSubClass(undefined)).toEqual([]);
  });

  it('should return an array of URLs when input contains valid crop descriptions', () => {
    const cropDescriptions = [
      { cropSubClassId: 1 },
      { cropSubClassId: 2 },
    ] as unknown as CropDescription[];
    const expectedUrls = cropDescriptions.map(
      (cd) => `${POLARIS_API_GW_ENDPOINTS.CROP_API}/crop-sub-classes/${cd.cropSubClassId}`,
    );
    expect(urlMapCropSubClass(cropDescriptions)).toEqual(expectedUrls);
  });

  it('should return an empty array when input is an empty array', () => {
    expect(urlMapCropSubClass([])).toEqual([]);
  });

  it('should return an empty array when input is an empty array', () => {
    const result = createFeatureDropdownValues([]);
    expect(result).toEqual([]);
  });

  it('should create dropdown values correctly from valid input data', () => {
    const data = [
      { id: 1, displayName: 'Feature 1', group: 'Group A' },
      { id: 2, displayName: 'Feature 2', group: 'Group B' },
    ] as unknown as Feature[];
    const expectedDropdownValues = [
      {
        value: 1,
        text: 'Feature 1',
        context: <GroupContext>Group A</GroupContext>,
      },
      {
        value: 2,
        text: 'Feature 2',
        context: <GroupContext>Group B</GroupContext>,
      },
    ];
    const result = createFeatureDropdownValues(data);
    expect(result).toEqual(expectedDropdownValues);
  });
  it('should return undefined when no region is found with given id', () => {
    const value = 'b61e357-94d9-45f6-809c-1777d4ed0658';
    const region = regionResponse as unknown as Region[];

    const regionDetail = findRegion(region, value);
    expect(regionDetail).not.toBe(region);
  });

  it('should return a matched country with given id', () => {
    const value = 'a8ccbf57-6056-44ec-9561-e60bd0f30abc';
    const response = countryResponse as unknown as Country[];

    const country = findCountry(response, value);
    expect(country).toBe(response[0]);
  });

  it('should return a matched feature with given id', () => {
    const value = '92b5d200-0ccb-4d6a-a6f4-3651b935521c';
    const response = featureResponse as unknown as Feature[];

    const country = findFeature(response, value);
    expect(country).toBe(response[0]);
  });
});
