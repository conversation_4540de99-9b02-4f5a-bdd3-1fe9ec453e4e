import { BaseUnit, CropRegion, GrowthScale } from "@common/types";
import { AhuaIconProps } from "@yaradigitallabs/ahua-react";

export interface CropSettingsCardLogicProps {
	cropRegion: CropRegion | null;
	growthScalesData?: GrowthScale[] | undefined;
}

export interface SelectItem {
	icon: AhuaIconProps['icon'];
	value: string;
	text: string;
}

interface LabelAddition {
	key: string,
	value: string;
}

export type CropSettingsCardConfigurationInitial = {
	label: string;
	ariaLabel: string;
	dataCy: string;
};

export type CropSettingsCardConfigurationData = {
	data: {
		items: SelectItem[],
		value: string;
	};
	labelAddition?: LabelAddition;
};

export interface CropSettingsCardPrepPayload {
	getItems: (
		unit: BaseUnit | GrowthScale | undefined,
		iconName: AhuaIconProps['icon']
	) => SelectItem[];
	getValue: (unit: BaseUnit | GrowthScale | undefined) => string;
}
