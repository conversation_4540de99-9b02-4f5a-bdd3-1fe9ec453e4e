import React from 'react';
import { Caption, Card, Label, Select, Stack, Status } from '@yaradigitallabs/ahua-react';
import { EditButtonWrapper, StyledCropSettingsCardBody } from './CropSettingsCardBody.styled';
import { useTranslation } from 'react-i18next';
import { CropRegion, Partner } from '@common/types';
import { StyledCropSettingsCardTail } from '@widgets/Polaris/styles/Polaris/screens/NPDetails/page-details-crop-settings-edit.styled';
import {
  CerealsCropSettingsCard,
  CropSettingCardTextArgs,
  CropSettingCardTitleArgs,
} from '../../CMMM/Details/CropSettings.types';
import { FertigationCropSettingsCard } from '../../Fertigation/Details/CropSettings.types';
import { SelectWrapper, TooltipWrapper } from '@widgets/Polaris/src/components';

interface CropSettingsCardProps {
  cropSettings: CerealsCropSettingsCard | FertigationCropSettingsCard;
  cropRegion: CropRegion | null;
  setIsEditCropSettingOpened: React.Dispatch<boolean>;
  isEditingDisabled: boolean;
  filteredPartnerTags: Partner[] | undefined;
  titles: CropSettingCardTitleArgs;
  texts: CropSettingCardTextArgs;
}

export const CropSettingsCard: React.FC<CropSettingsCardProps> = ({
  titles,
  texts,
  cropSettings,
  cropRegion,
  setIsEditCropSettingOpened,
  isEditingDisabled,
  filteredPartnerTags,
}) => {
  const { t } = useTranslation();

  return (
    <Card data-cy='crop-settings-card'>
      <div className='card-header'>
        <Card.Head title={titles.head} data-cy={'crop-settings-card-header'}>
          <Card.HeadActions>
            <TooltipWrapper
              maxWidth={200}
              position='top'
              concept='inverse'
              showTooltip={isEditingDisabled}
              tooltipText={texts.headActionTooltip}
              dataCy={'plan-crop-settings-card-edit-button-tooltip'}
            >
              <div>
                <EditButtonWrapper
                  title={titles.editButton}
                  size='xs'
                  type='button'
                  variant='ghost'
                  iconLeading='Edit'
                  disabled={isEditingDisabled}
                  onClick={() => setIsEditCropSettingOpened(true)}
                  data-cy={'crop-settings-edit-button'}
                >
                  {titles.editButton}
                </EditButtonWrapper>
              </div>
            </TooltipWrapper>
          </Card.HeadActions>
        </Card.Head>
      </div>

      <Card.Divider />

      {cropSettings.config.map((row, index) => (
        <StyledCropSettingsCardBody
          key={`crop-settings-card-row-${index}`}
          orientation='horizontal'
          state={'contentFromStart'}
          data-cy='crop-settings-card-body'
        >
          {row.map(({ label, labelAddition, ariaLabel, data, dataCy }) => (
            <SelectWrapper key={label} dataCy={`${dataCy}-select`}>
              <Select
                size='s'
                label={t(
                  label,
                  labelAddition
                    ? {
                        [labelAddition.key]: labelAddition.value,
                      }
                    : {},
                )}
                ariaLabel={ariaLabel}
                items={data.items}
                value={data.value}
                readOnly
                position=''
                onFocus={null}
                onBlur={null}
              />
            </SelectWrapper>
          ))}
        </StyledCropSettingsCardBody>
      ))}

      <Card.Divider />

      <StyledCropSettingsCardTail orientation='vertical'>
        <Label className='partners-title' size='xs' data-cy='crop-setting-partners-title'>
          {titles.partner}
        </Label>

        <Stack className='stack' direction='horizontal' gap='$x2'>
          {filteredPartnerTags?.map((tag) => (
            <Status key={tag.id} className='stack-status' size='s' data-cy={`${tag.name}-status`}>
              {tag.displayName}
            </Status>
          ))}
          {cropRegion?.tagsConfiguration.partnerTags?.length === 0 && (
            <Caption className='partners-caption' size='s' data-cy='crop-setting-partners-caption'>
              {texts.partnerCaption}
            </Caption>
          )}
        </Stack>
      </StyledCropSettingsCardTail>
    </Card>
  );
};
