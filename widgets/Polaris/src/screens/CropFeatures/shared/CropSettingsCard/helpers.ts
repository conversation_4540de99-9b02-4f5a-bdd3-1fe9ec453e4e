import { BaseUnit, GrowthScale } from '@common/types';
import { CropSettingsCardPrepPayload } from './CropSettingsCard.types';
import { AhuaIconProps } from '@yaradigitallabs/ahua-react';

/** Hook to prepare crop settings units */
export const CropSettingsCardPrep = (): CropSettingsCardPrepPayload => {
  const getItems = (unit: BaseUnit | GrowthScale | undefined, iconName: AhuaIconProps['icon']) => {
    const item = !unit
      ? {
          icon: iconName,
          value: 'error',
          text: '-',
        }
      : {
          icon: iconName,
          value: unit.id,
          text: unit.name,
        };

    return [item];
  };

  const getValue = (unit: BaseUnit | GrowthScale | undefined) => {
    return !unit ? 'error' : unit?.id;
  };

  return {
    getItems,
    getValue,
  };
};
