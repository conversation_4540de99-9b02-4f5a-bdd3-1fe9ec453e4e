import { Card, styled } from "@yaradigitallabs/ahua-react";

export const StyledCropSettingsCardBody = styled(Card.Body, {
	height: '87px',
	paddingLeft: '0 !important',
	justifyContent: 'space-between',

	'&& > *': {
		paddingTop: '$x5',
		paddingBottom: '$x5',
		paddingLeft: '0',
		paddingRight: '0',
	},
	'&& > *:first-child': {
		paddingLeft: '0',
	},
	'&& > *:last-child': {
		paddingRight: '0',
	},
	'& > div label:first-child': {
		fontSize: '$scale0',
	},

	variants: {
		state: {
			contentFromStart: {
				justifyContent: 'start',
				'& > div': {
					width: '25% !important',
				},
			},
		},
	},
});

export const EditButtonWrapper = styled(Card.Action, {
	'&&': {
		fontSize: '$scale3',
		width: '90px',
		'&:not(:disabled)': {
			color: '$blue60',
		},
	},

	'& svg': {
		width: '$x4',
		height: '$x4',
		minWidth: '$x4',
		minHeight: '$x4',
		marginRight: '$x1',
		strokeWidth: '10%',
	},

	'& span': {
		height: '18px',
	},
});
