import {
  ApplicationConditionLogicalOperators,
  ApplicationConditionOperators,
  ApplicationConditionParameters,
} from '../../constants';

const OLD_NO_CONDITION = 'Condition not needed';

export interface ParsedApplicationCondition {
  parameter: string;
  operator: string;
  value: string;
}
export interface ParsedApplicationConditionsResult {
  logicalOperator: ApplicationConditionLogicalOperators | null;
  conditions: ParsedApplicationCondition[];
}

/**
 * Helper function that parses the string condition to a convenient object
 * which stores a logical operator and an array with one or two condition objects
 * @param condition string
 * @param noConditionText string
 * @returns {string|ParsedApplicationConditionsResult}
 */
export const parseApplicationCondition = (
  condition: string,
  noConditionText: string,
): string | ParsedApplicationConditionsResult => {
  // Returns a string if no condition is required
  if (condition === noConditionText || condition === OLD_NO_CONDITION) {
    return noConditionText;
  }

  // Check if the expression is valid (contains a parameter, an operator, and a value).
  const paramsPattern = Object.values(ApplicationConditionParameters)
    .map((val) => `(${val})`)
    .join('|');
  const operatorsPattern = Object.values(ApplicationConditionOperators)
    .reduce((acc, val) => (val.endsWith('=') ? `${acc}(${val}?)|` : `${acc}`), '')
    .slice(0, -1);
  const valuePattern = '\\d+[.,]?\\d*';
  const matchRegExpPattern = new RegExp(
    `(${paramsPattern})\\s+(${operatorsPattern})\\s+${valuePattern}`,
    'g',
  );
  const matchedExpressions = condition.match(matchRegExpPattern);

  if (!matchedExpressions) return noConditionText;

  // If there are more than 2 parts in the condition, remove everything beyond the 2nd part
  if (matchedExpressions.length > 2) matchedExpressions.splice(2);

  // Check for the logical AND/OR operator
  const logicalOperatorInCondition = Object.values(ApplicationConditionLogicalOperators).find(
    (logicalOp) => condition.includes(logicalOp),
  );

  // Build the first condition and return it if there's no second one
  const firstPartOfCondition: ParsedApplicationCondition = {
    parameter: matchedExpressions[0].match(new RegExp(paramsPattern))?.[0] || '',
    operator: matchedExpressions[0].match(new RegExp(operatorsPattern))?.[0] || '',
    value: matchedExpressions[0].match(new RegExp(valuePattern))?.[0] || '',
  };
  const result: ParsedApplicationConditionsResult = {
    logicalOperator: logicalOperatorInCondition || null,
    conditions: [firstPartOfCondition],
  };

  if (!logicalOperatorInCondition) {
    return result;
  }

  // Operator has to be between the two parts of the condition.
  const logicalOperatorIsInTheRightPlace =
    condition.indexOf(logicalOperatorInCondition) > condition.indexOf(matchedExpressions[0]) &&
    condition.indexOf(logicalOperatorInCondition) < condition.indexOf(matchedExpressions[1]);

  if (!logicalOperatorIsInTheRightPlace) return noConditionText;

  const secondPartOfCondition: ParsedApplicationCondition = {
    parameter: matchedExpressions[1].match(new RegExp(paramsPattern))?.[0] || '',
    operator: matchedExpressions[1].match(new RegExp(operatorsPattern))?.[0] || '',
    value: matchedExpressions[1].match(new RegExp(valuePattern))?.[0] || '',
  };
  result.conditions.push(secondPartOfCondition);
  return result;
};

/**
 * Helper method that transforms a condition object into an application condition string
 * @param {ParsedApplicationConditionsResult|null} applicationConditionObject
 * @param {string} noConditionRequired
 * @returns {string}
 */
export const buildConditionText = (
  applicationConditionObject: ParsedApplicationConditionsResult | null,
  noConditionRequired: string,
) => {
  let conditionText = '';
  if (!applicationConditionObject) {
    conditionText = noConditionRequired;
  } else {
    const conditionOne = applicationConditionObject.conditions[0];
    const firstPart = `${conditionOne.parameter} ${conditionOne.operator} ${conditionOne.value}`;
    conditionText = firstPart;

    const logicalOperator = applicationConditionObject.logicalOperator;

    if (logicalOperator) {
      const conditionTwo = applicationConditionObject.conditions[1] || null;
      const secondPart = `${conditionTwo.parameter} ${conditionTwo.operator} ${conditionTwo.value}`;
      conditionText = `(${firstPart}) ${logicalOperator} (${secondPart})`;
    }
  }
  return conditionText;
};
