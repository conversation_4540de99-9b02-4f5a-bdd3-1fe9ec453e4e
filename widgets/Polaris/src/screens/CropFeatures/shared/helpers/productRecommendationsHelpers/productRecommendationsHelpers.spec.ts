import {
  buildConditionText,
  parseApplicationCondition,
  ParsedApplicationConditionsResult,
} from '.';
import {
  ApplicationConditionLogicalOperators,
  ApplicationConditionOperators,
  ApplicationConditionParameters,
  NO_CONDITION_TEXT,
} from '../../constants';

describe('Product recommendations helpers: parseApplicationCondition', () => {
  it('returns a truthy value if provided with a valid-format string condition', () => {
    expect(parseApplicationCondition('Soil pH < 8.9', NO_CONDITION_TEXT)).toBeTruthy();
    expect(typeof parseApplicationCondition('Soil pH < 8.9', NO_CONDITION_TEXT)).not.toBe('string');
  });

  it('returns a string value when an application condition is not set', () => {
    expect(typeof parseApplicationCondition('Condition not needed', NO_CONDITION_TEXT)).toBe(
      'string',
    );
    expect(parseApplicationCondition('Condition not needed', NO_CONDITION_TEXT)).toBe(
      'No condition required',
    );
    expect(parseApplicationCondition('No condition required', NO_CONDITION_TEXT)).toBe(
      'No condition required',
    );
  });

  describe('returns an object with logical operator set to null and a single parsed condition if provided with a valid-format string condition', () => {
    it('option 1', () => {
      const conditionText = 'Soil pH < 8.9';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: null,
        conditions: [
          {
            parameter: ApplicationConditionParameters.SOIL_PH,
            operator: ApplicationConditionOperators.LOWER_THAN,
            value: '8.9',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBeNull();
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
      }
    });

    it('option 2', () => {
      const conditionText = 'Water pH <= 0.009';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: null,
        conditions: [
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.LOWER_OR_EQUAL,
            value: '0.009',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBeNull();
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
      }
    });

    it('option 3', () => {
      const conditionText = 'Soil OrgM > 222,15';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: null,
        conditions: [
          {
            parameter: ApplicationConditionParameters.SOIL_ORG_M,
            operator: ApplicationConditionOperators.GREATER_THAN,
            value: '222,15',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBeNull();
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
      }
    });
  });

  describe('returns an object with logical operator and two parsed conditions if provided with a valid-format string two-part condition', () => {
    it('option 1', () => {
      const conditionText = '(Soil pH < 8.9) AND (Soil pH >= 0.7)';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: ApplicationConditionLogicalOperators.AND,
        conditions: [
          {
            parameter: ApplicationConditionParameters.SOIL_PH,
            operator: ApplicationConditionOperators.LOWER_THAN,
            value: '8.9',
          },
          {
            parameter: ApplicationConditionParameters.SOIL_PH,
            operator: ApplicationConditionOperators.GREATER_OR_EQUAL,
            value: '0.7',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBe(expectedResult.logicalOperator);
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
        expect(result.conditions[1]).toMatchObject(expectedResult.conditions[1]);
      }
    });

    it('option 2', () => {
      const conditionText = '(Soil OrgM >= 22.109) OR (Water pH < 12)';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: ApplicationConditionLogicalOperators.OR,
        conditions: [
          {
            parameter: ApplicationConditionParameters.SOIL_ORG_M,
            operator: ApplicationConditionOperators.GREATER_OR_EQUAL,
            value: '22.109',
          },
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.LOWER_THAN,
            value: '12',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBe(expectedResult.logicalOperator);
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
        expect(result.conditions[1]).toMatchObject(expectedResult.conditions[1]);
      }
    });

    it('option 3', () => {
      const conditionText = '(Water pH <= 0,09) AND (Water pH > 0.02)';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: ApplicationConditionLogicalOperators.AND,
        conditions: [
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.LOWER_OR_EQUAL,
            value: '0,09',
          },
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.GREATER_THAN,
            value: '0.02',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBe(expectedResult.logicalOperator);
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
        expect(result.conditions[1]).toMatchObject(expectedResult.conditions[1]);
      }
    });

    it('option 4', () => {
      const conditionText = 'Water pH <= 0,09 AND Water pH > 0';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: ApplicationConditionLogicalOperators.AND,
        conditions: [
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.LOWER_OR_EQUAL,
            value: '0,09',
          },
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.GREATER_THAN,
            value: '0',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBe(expectedResult.logicalOperator);
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
        expect(result.conditions[1]).toMatchObject(expectedResult.conditions[1]);
      }
    });

    it('option 5', () => {
      const conditionText = 'Water pH <= 0,09 AND Water pH > 0 AND Soil pH > 0';
      const expectedResult: ParsedApplicationConditionsResult = {
        logicalOperator: ApplicationConditionLogicalOperators.AND,
        conditions: [
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.LOWER_OR_EQUAL,
            value: '0,09',
          },
          {
            parameter: ApplicationConditionParameters.WATER_PH,
            operator: ApplicationConditionOperators.GREATER_THAN,
            value: '0',
          },
        ],
      };
      const result = parseApplicationCondition(conditionText, NO_CONDITION_TEXT);
      expect(result).toBeInstanceOf(Object);
      if (typeof result === 'object') {
        expect(result.logicalOperator).toBe(expectedResult.logicalOperator);
        expect(result.conditions[0]).toMatchObject(expectedResult.conditions[0]);
        expect(result.conditions[1]).toMatchObject(expectedResult.conditions[1]);
      }
    });
  });

  it('returns the no-condition string if provided with an invalid-format condition', () => {
    const conditionText1 = 'Soil pH < AND (Soil pH >= 0.7)';
    const conditionText2 = '(OrgM < 8.9) AND (pH >= 0.7)';
    const conditionText3 = 'Some condition AND Another one';
    const conditionText4 = '';
    const conditionText5 = 'OR (Soil pH >= 0.7)';

    expect(parseApplicationCondition(conditionText1, NO_CONDITION_TEXT)).toBe(NO_CONDITION_TEXT);
    expect(parseApplicationCondition(conditionText2, NO_CONDITION_TEXT)).toBe(NO_CONDITION_TEXT);
    expect(parseApplicationCondition(conditionText3, NO_CONDITION_TEXT)).toBe(NO_CONDITION_TEXT);
    expect(parseApplicationCondition(conditionText4, NO_CONDITION_TEXT)).toBe(NO_CONDITION_TEXT);
    expect(parseApplicationCondition(conditionText5, NO_CONDITION_TEXT)).toBe(NO_CONDITION_TEXT);
  });
});

describe('Product recommendations helpers: buildConditionText', () => {
  const onePartCondition: ParsedApplicationConditionsResult = {
    logicalOperator: null,
    conditions: [
      {
        parameter: ApplicationConditionParameters.SOIL_PH,
        operator: ApplicationConditionOperators.LOWER_THAN,
        value: '8.9',
      },
    ],
  };

  const twoPartCondition: ParsedApplicationConditionsResult = {
    logicalOperator: ApplicationConditionLogicalOperators.AND,
    conditions: [
      {
        parameter: ApplicationConditionParameters.WATER_PH,
        operator: ApplicationConditionOperators.LOWER_OR_EQUAL,
        value: '0,09',
      },
      {
        parameter: ApplicationConditionParameters.WATER_PH,
        operator: ApplicationConditionOperators.GREATER_THAN,
        value: '0',
      },
    ],
  };
  it('takes an application condition object and transforms it into a string', () => {
    expect(buildConditionText(onePartCondition, NO_CONDITION_TEXT)).toBe('Soil pH < 8.9');
    expect(buildConditionText(twoPartCondition, NO_CONDITION_TEXT)).toBe(
      '(Water pH <= 0,09) AND (Water pH > 0)',
    );
    expect(buildConditionText(null, NO_CONDITION_TEXT)).toBe(NO_CONDITION_TEXT);
  });
});
