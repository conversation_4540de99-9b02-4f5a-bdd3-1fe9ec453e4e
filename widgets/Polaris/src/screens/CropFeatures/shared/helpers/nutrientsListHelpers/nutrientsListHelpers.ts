import {
  BaseUnit,
  DemandCalculation,
  FertigationNutrientWithNutrientForms,
  Nutrient,
} from '@common/types';
import { isEmpty } from 'lodash';

/**
 *
 * @param nutrientsResponse Available nutrients from the API call
 * @returns A pre-defined list of nutrients for the Demand Calculations feature of Cereals MMM
 */

export const getSortedListOfNutrients = (
  nutrientsResponse?: Nutrient[],
  listOfNames?: string[],
) => {
  if (!nutrientsResponse?.length || !listOfNames?.length) return;

  const filteredNutrients = nutrientsResponse?.filter(({ name }) => listOfNames.includes(name));
  const sortedNutrients = filteredNutrients.sort(
    (a, b) => listOfNames.indexOf(a.name) - listOfNames.indexOf(b.name),
  );

  return sortedNutrients;
};

/**
 * A very specific helper method that determines whether the Fertigation nutrient form tag is assigned to a given unit.
 * @param unit
 * @param nutrientName
 * @returns {boolean}
 */
export const isUnitWithNutrientFormTag = (unit: BaseUnit, nutrientName: string): boolean =>
  unit.tags.split(',').includes('Fertigation' + nutrientName + 'NutrientForm');

interface MapFertigationNutrientIdsToNutrientFormsArgs {
  nutrientList?: Nutrient[];
  unitList?: BaseUnit[];
  demandCalculationConfigs?: DemandCalculation[] | [];
}
/**
 * Helper function that returns an object with nutrient IDs as keys and nutrient form information (ID, name) as values.
 * Used in Fertigation Demand calculations and Fertigation Splitting schedule.
 * @param {Object} params - Object that contains properties:
 * @param {Nutrient[]|undefined} params.nutrientList - list of sorted nutrients
 * @param {BaseUnit[]|undefined} params.unitList - list of units to select nutrient forms from
 * @param {DemandCalculation[]|[]|undefined} params.demandCalculationConfigs - Demand Calculations configurations for the particular crop region and country
 * @returns {FertigationNutrientWithNutrientForms|undefined}
 */
export const mapFertigationNutrientIdsToNutrientForms = ({
  nutrientList,
  unitList,
  demandCalculationConfigs,
}: MapFertigationNutrientIdsToNutrientFormsArgs):
  | FertigationNutrientWithNutrientForms
  | undefined => {
  const noUnits = !unitList || isEmpty(unitList);
  const noNutrients = !nutrientList || isEmpty(nutrientList);
  const noDemandCalculationConfigs = !demandCalculationConfigs || isEmpty(demandCalculationConfigs);
  if (noUnits || noNutrients || noDemandCalculationConfigs) {
    return;
  }

  const selectedNutrientFormIds = demandCalculationConfigs.map(
    (demandCalculation) => demandCalculation.elementFormId,
  );

  const result = nutrientList.reduce<FertigationNutrientWithNutrientForms>((acc, nutrient) => {
    const nutrientForms = unitList.filter((unit) =>
      isUnitWithNutrientFormTag(unit, nutrient.elementalName),
    );

    acc[nutrient.id] = nutrientForms.map((nForm) => ({
      nutrientKey: nutrient.elementalName.toLowerCase(),
      nutrientFormId: nForm.id,
      nutrientFormName: nForm.name,
      selected: selectedNutrientFormIds.includes(nForm.id),
    }));
    return acc;
  }, {});

  return result;
};
