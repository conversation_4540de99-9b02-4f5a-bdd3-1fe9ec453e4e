import { BaseUnit } from '@common/types';
import {
  getSortedListOfNutrients,
  isUnitWithNutrientFormTag,
  mapFertigationNutrientIdsToNutrientForms,
} from './nutrientsListHelpers';
import {
  nutrientsMock,
  mockNutrientsNamesList,
  fertigationNutrientFormMockN,
  fertigationNutrientFormMockP,
  mockDemandCalculationFertigation,
} from '@common/mocks';

describe('nutrientsDemandCalculationsHelpers', () => {
  describe('getSortedListOfNutrients', () => {
    it('returns nothing if no nutrients are returned from the API', () => {
      expect(getSortedListOfNutrients(undefined, undefined)).toBeUndefined();
    });
    it('returns nothing if a nutrients API response is empty', () => {
      expect(getSortedListOfNutrients([], mockNutrientsNamesList)).toBeUndefined();
    });
    it('returns nothing if a list of nutrient names is empty', () => {
      expect(getSortedListOfNutrients(nutrientsMock, [])).toBeUndefined();
    });
    it('returns an array of 3 nutrients', () => {
      expect(getSortedListOfNutrients(nutrientsMock, mockNutrientsNamesList)).toHaveLength(3);
    });
    it('returns an array where the first nutrient has the same name as the first item in the nutrient names list', () => {
      expect(getSortedListOfNutrients(nutrientsMock, mockNutrientsNamesList)).toHaveLength(3);
      expect(getSortedListOfNutrients(nutrientsMock, mockNutrientsNamesList)?.[0]).toHaveProperty(
        'name',
        mockNutrientsNamesList[0],
      );
    });
  });

  describe('isUnitWithNutrientFormTag', () => {
    it('returns true if the unit has the correct nutrient form tag', () => {
      const unit = { tags: 'FertigationNNutrientForm,OtherTag' } as BaseUnit;
      expect(isUnitWithNutrientFormTag(unit, 'N')).toBe(true);
    });
    it('returns false if the unit does not have the correct nutrient form tag', () => {
      const unit = { tags: 'OtherTag' } as BaseUnit;
      expect(isUnitWithNutrientFormTag(unit, 'N')).toBe(false);
    });
    it('returns false if the unit tags are empty', () => {
      const unit = { tags: '' } as BaseUnit;
      expect(isUnitWithNutrientFormTag(unit, 'N')).toBe(false);
    });
  });

  describe('mapFertigationNutrientIdsToNutrientForms', () => {
    it('returns undefined if no units, nutrients, or demand calculation configs are provided', () => {
      expect(mapFertigationNutrientIdsToNutrientForms({})).toBeUndefined();
    });
    it('returns undefined if the unit list is empty', () => {
      expect(
        mapFertigationNutrientIdsToNutrientForms({
          nutrientList: nutrientsMock,
          unitList: [],
          demandCalculationConfigs: [],
        }),
      ).toBeUndefined();
    });
    it('returns undefined if the nutrient list is empty', () => {
      expect(
        mapFertigationNutrientIdsToNutrientForms({
          nutrientList: [],
          unitList: [fertigationNutrientFormMockN],
          demandCalculationConfigs: [],
        }),
      ).toBeUndefined();
    });
    it('returns undefined if the demand calculation configs are empty', () => {
      expect(
        mapFertigationNutrientIdsToNutrientForms({
          nutrientList: nutrientsMock,
          unitList: [fertigationNutrientFormMockN],
          demandCalculationConfigs: [],
        }),
      ).toBeUndefined();
    });
    it('returns a mapped object of nutrient IDs to nutrient forms', () => {
      const nutrientList = nutrientsMock.slice(-2);
      const unitList = [fertigationNutrientFormMockN, fertigationNutrientFormMockP];
      const demandCalculationConfigs = [
        { ...mockDemandCalculationFertigation, elementFormId: fertigationNutrientFormMockN.id },
        {
          ...mockDemandCalculationFertigation,
          elementFormId: fertigationNutrientFormMockP.id,
        },
      ];

      const result = mapFertigationNutrientIdsToNutrientForms({
        nutrientList,
        unitList,
        demandCalculationConfigs,
      });

      expect(result).toEqual({
        '880abcda-5ee5-4068-879c-94489be314d5': [
          {
            nutrientKey: 'n',
            nutrientFormId: '393f6e30-0af3-4336-a65f-953701d1173a',
            nutrientFormName: 'N',
            selected: true,
          },
        ],
        'f2c275c4-1522-4524-8747-08ace254b155': [
          {
            nutrientKey: 'p',
            nutrientFormId: '5191524f-c23c-437f-8795-50e49d41373b',
            nutrientFormName: 'P',
            selected: true,
          },
        ],
      });
    });
  });
});
