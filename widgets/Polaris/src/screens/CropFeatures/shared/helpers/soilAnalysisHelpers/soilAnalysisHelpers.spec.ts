import { soilAnalysisConfigurationsMock } from '@common/mocks';
import {
  addNutrientParameterUtility,
  getInitialValues,
  getDefaultAnalysisNutrientClassifications,
} from '.';
import { AddParamStateProps } from '../../components/NutritionParamsWithValuesTable/AddNutritionParamPopup';
import { AnalysisTypeOptions } from '@common/types';

describe('cerealSoilAnalysisHelpers', () => {
  describe('getDefaultAnalysisNutrientClassifications', () => {
    const mockParameters = [
      {
        soilAnalysisConfigurationId: '111id',
        nutrientClassification: 'VERY_LOW',
        isDefault: false,
        greaterOrEqual: 0,
        lowerThan: 0,
      },
      {
        soilAnalysisConfigurationId: '111id',
        nutrientClassification: 'LOW',
        isDefault: false,
        greaterOrEqual: 0,
        lowerThan: 0,
      },
      {
        soilAnalysisConfigurationId: '111id',
        nutrientClassification: 'MEDIUM',
        isDefault: true,
        greaterOrEqual: 0,
        lowerThan: 0,
      },
      {
        soilAnalysisConfigurationId: '111id',
        nutrientClassification: 'HIGH',
        isDefault: false,
        greaterOrEqual: 0,
        lowerThan: 0,
      },
      {
        soilAnalysisConfigurationId: '111id',
        nutrientClassification: 'VERY_HIGH',
        isDefault: false,
        greaterOrEqual: 0,
        lowerThan: 99999,
      },
    ];

    const mockConfiguration = {
      id: '111id',
      countryId: '111countryId',
      cropRegionId: '111cropRegionId',
      nutrientId: '111nutrientId',
      analysisMethodId: '111analysisMethodId',
      analysisBaseUnitId: '111analysisBaseUnitId',
      nutrientClassifications: [],
      considerSecondaryParameters: false,
    };

    it('returns correct structure and values with all parameters provided', () => {
      const result = getDefaultAnalysisNutrientClassifications(
        mockConfiguration,
        AnalysisTypeOptions.SOIL,
      );
      expect(result).toMatchObject(mockParameters);
    });

    it('marks the MEDIUM category as default', () => {
      const defaultAnalysisNutrientClassifications = getDefaultAnalysisNutrientClassifications(
        mockConfiguration,
        AnalysisTypeOptions.SOIL,
      );

      expect(
        defaultAnalysisNutrientClassifications?.find(
          (ncObj) => ncObj.nutrientClassification === 'MEDIUM',
        )?.isDefault,
      ).toBe(true);
      expect(
        defaultAnalysisNutrientClassifications
          ?.filter((ncObj) => ncObj.nutrientClassification !== 'MEDIUM')
          .every((ncObj) => !ncObj.isDefault),
      ).toBe(true);
    });

    it('handles undefined cereal soil analysis configuration id gracefully', () => {
      const mockConfigurationWithUndefinedId = {
        ...mockConfiguration,
        id: undefined,
      };
      const defaultAnalysisNutrientClassifications = getDefaultAnalysisNutrientClassifications(
        mockConfigurationWithUndefinedId,
        AnalysisTypeOptions.SOIL,
      );

      expect(
        defaultAnalysisNutrientClassifications.every(
          (ncObj) => ncObj.soilAnalysisConfigurationId === '',
        ),
      ).toBe(true);
    });
  });

  describe('addNutrientParameterUtility', () => {
    let mockSetAddParamState: jest.Mock;
    let mockSetDisplaySnackbar: jest.Mock;
    let mockSetIsAddParamOpened: jest.Mock;
    let mockTriggerCreateUpdateNutrientClassification: jest.Mock;
    let mockHandleOpenParamValuesInfoDialog: jest.Mock;
    let mockUpdateAnalysisConfigurations: jest.Mock;
    const mockSnackbarMsg = 'Operation successful';

    const addParamState: AddParamStateProps = {
      selectedAnalysisNutrientClassification: { id: '1' } as never,
      defaultAnalysisNutrientClassification: {
        id: '2',
      } as never,
      isDefaultChecked: false,
      parameters: [],
      error: false,
    };

    beforeEach(() => {
      mockSetAddParamState = jest.fn();
      mockSetDisplaySnackbar = jest.fn();
      mockSetIsAddParamOpened = jest.fn();
      mockTriggerCreateUpdateNutrientClassification = jest.fn().mockResolvedValue(undefined);
      mockHandleOpenParamValuesInfoDialog = jest.fn();
      mockUpdateAnalysisConfigurations = jest.fn();
    });

    it('sets error to true if selectedAnalysisNutrientClassification is not defined', async () => {
      const newAddParamState: AddParamStateProps = {
        ...addParamState,
        selectedAnalysisNutrientClassification: null,
        defaultAnalysisNutrientClassification: undefined,
      };

      await addNutrientParameterUtility(
        newAddParamState,
        soilAnalysisConfigurationsMock.entities[0],
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerCreateUpdateNutrientClassification,
        mockUpdateAnalysisConfigurations,
        mockHandleOpenParamValuesInfoDialog,
        mockSnackbarMsg,
      );

      expect(mockSetAddParamState).toHaveBeenCalledWith(expect.any(Function));
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(addParamState);
      expect(newState.error).toBe(true);
    });

    it('calls mockTriggerSoilAnalysisNutrientClassification once if isDefaultChecked is false', async () => {
      await addNutrientParameterUtility(
        addParamState,
        soilAnalysisConfigurationsMock.entities[0],
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerCreateUpdateNutrientClassification,
        mockUpdateAnalysisConfigurations,
        mockHandleOpenParamValuesInfoDialog,
        mockSnackbarMsg,
      );

      expect(mockTriggerCreateUpdateNutrientClassification).toHaveBeenCalledTimes(1);
      expect(mockTriggerCreateUpdateNutrientClassification).toHaveBeenCalledWith(
        expect.any(Object),
      );
    });

    fit('calls mockTriggerSoilAnalysisNutrientClassification twice if isDefaultChecked is true', async () => {
      const newAddParamState: AddParamStateProps = {
        ...addParamState,
        isDefaultChecked: true,
      };
      await addNutrientParameterUtility(
        newAddParamState,
        soilAnalysisConfigurationsMock.entities[0],
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerCreateUpdateNutrientClassification,
        mockUpdateAnalysisConfigurations,
        mockHandleOpenParamValuesInfoDialog,
        mockSnackbarMsg,
      );

      expect(mockTriggerCreateUpdateNutrientClassification).toHaveBeenCalledTimes(2);
      expect(mockTriggerCreateUpdateNutrientClassification).toHaveBeenCalledWith(
        expect.any(Object),
      );
    });

    it('handles errors in the try-catch block', async () => {
      mockTriggerCreateUpdateNutrientClassification.mockRejectedValueOnce(new Error('Test Error'));

      const addParamState: AddParamStateProps = {
        selectedAnalysisNutrientClassification: { id: '1' } as never,
        defaultAnalysisNutrientClassification: {
          id: '2',
        } as never,
        isDefaultChecked: false,
        parameters: [],
        error: false,
      };

      console.error = jest.fn();

      await addNutrientParameterUtility(
        addParamState,
        soilAnalysisConfigurationsMock.entities[0],
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerCreateUpdateNutrientClassification,
        mockUpdateAnalysisConfigurations,
        mockHandleOpenParamValuesInfoDialog,
        mockSnackbarMsg,
      );

      expect(mockTriggerCreateUpdateNutrientClassification).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith(
        'Error creating and updating soil analysis nutrient classification data:',
        expect.any(Error),
      );
    });
  });

  describe('getInitialValues', () => {
    it('gets initial values', () => {
      const expectedResult = {
        soilAnalysisConfigurationId: '',
        nutrientClassification: null,
        isDefault: false,
        greaterOrEqual: 0,
        lowerThan: 0,
      };
      const initialValues = getInitialValues(AnalysisTypeOptions.SOIL);
      expect(initialValues).toEqual(expectedResult);
    });
  });
});
