/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  SnackBarType,
  AnalysisConfiguration,
  NutrientClassification,
  NutrientClassificationWithError,
  AnalysisType,
  AnalysisTypeOptions,
  ParameterLevel,
  NutrientClassificationCreateUpdateResponse,
} from '@common/types';
import { METHOD, ORDER, ParameterLevelEnum } from '@common/constants';
import { cloneDeep } from 'lodash';
import { AddParamStateProps } from '../../NutritionParamsWithValuesTable/AddNutritionParamPopup';
import {
  CurrentMinPreviousMaxLevelErrors,
  ParameterLevelRange,
  SameLevelMinMaxErrors,
} from '../../NutritionParamsWithValuesTable';

/**
 * Returns the initial values for a nutrient classification
 * @returns {object} An object containing the initial values
 */
export const getInitialValues = (analysisType: AnalysisType): NutrientClassificationWithError => {
  const initialValues: NutrientClassificationWithError = {
    nutrientClassification: null,
    isDefault: false,
    greaterOrEqual: 0,
    lowerThan: 0,
  };

  switch (analysisType) {
    case AnalysisTypeOptions.SOIL:
      initialValues.soilAnalysisConfigurationId = '';
      break;
    case AnalysisTypeOptions.LEAF:
      initialValues.leafAnalysisConfigurationId = '';
      break;
    default:
      break;
  }
  return initialValues;
};

/**
 * Create soil analysis nutrient classifications
 * @param {string} analysisConfiguration Passed soil analysis configuration
 */

export function getDefaultAnalysisNutrientClassifications(
  analysisConfiguration: AnalysisConfiguration,
  analysisType: AnalysisType,
): NutrientClassification[] {
  const categories: ParameterLevelEnum[] = [
    ParameterLevelEnum.VERY_LOW,
    ParameterLevelEnum.LOW,
    ParameterLevelEnum.MEDIUM,
    ParameterLevelEnum.HIGH,
    ParameterLevelEnum.VERY_HIGH,
  ];

  const defaultAnalysisNutrientClassifications: NutrientClassification[] = categories.map(
    (category) => ({
      nutrientClassification: category,
      isDefault: category === ParameterLevelEnum.MEDIUM,
      greaterOrEqual: 0,
      lowerThan: category === ParameterLevelEnum.VERY_HIGH ? ParameterLevelRange.Max : 0,
    }),
  );

  switch (analysisType) {
    case AnalysisTypeOptions.SOIL:
      defaultAnalysisNutrientClassifications.forEach(
        (nutrientClassification) =>
          (nutrientClassification.soilAnalysisConfigurationId = analysisConfiguration.id || ''),
      );
      break;
    case AnalysisTypeOptions.LEAF:
      defaultAnalysisNutrientClassifications.forEach(
        (nutrientClassification) =>
          (nutrientClassification.leafAnalysisConfigurationId = analysisConfiguration.id || ''),
      );
      break;
    case AnalysisTypeOptions.WATER:
      defaultAnalysisNutrientClassifications.forEach((nutrientClassification) => {
        nutrientClassification.waterAnalysisConfigurationId = analysisConfiguration.id || '';
        nutrientClassification.warningMessage = '';
      });
      break;
    default:
      break;
  }

  return defaultAnalysisNutrientClassifications;
}

export const getOrderedNutrientClassifications = (
  nutrientClassifications: NutrientClassification[],
) => {
  if (!nutrientClassifications?.length) {
    return [];
  }
  return nutrientClassifications
    .slice()
    .sort((a, b) => ORDER[a.nutrientClassification] - ORDER[b.nutrientClassification]);
};

export const isLowestLevel = (
  nutrientClassificationToCheck: ParameterLevel | null,
  nutrientClassifications: NutrientClassification[],
) => {
  if (nutrientClassificationToCheck) {
    return (
      ORDER[nutrientClassificationToCheck] <
      ORDER[nutrientClassifications?.[0]?.nutrientClassification]
    );
  }
};

export const isHighestLevel = (
  nutrientClassificationToCheck: ParameterLevel | null,
  nutrientClassifications: NutrientClassification[],
) => {
  if (nutrientClassificationToCheck && nutrientClassifications) {
    return (
      ORDER[nutrientClassificationToCheck] >
      ORDER[nutrientClassifications?.[nutrientClassifications?.length - 1]?.nutrientClassification]
    );
  }
};

// USAGE: To add another nutrient level row to the table in the soil analysis, click on the 'Add another level' button and fill in the required details.
// Adding another nutrient level row with setAsDefault checked will add the new row as the default in the table and uncheck the existing default row.
export const addNutrientParameterUtility = async (
  addParamState: AddParamStateProps,
  analysisConfiguration: AnalysisConfiguration,
  setAddParamState: React.Dispatch<React.SetStateAction<AddParamStateProps>>,
  setDisplaySnackbar: React.Dispatch<React.SetStateAction<SnackBarType>>,
  setIsAddParamOpened: React.Dispatch<React.SetStateAction<boolean>>,
  triggerCreateUpdateNutrientClassification: (config: {
    method: string;
    body: string;
  }) => Promise<NutrientClassificationCreateUpdateResponse | string | undefined>,
  updateAnalysisConfigurations: (analysisConfiguration: AnalysisConfiguration) => void,
  handleOpenParamValuesInfoDialog: (error: any) => void,
  snackbarMsg: string,
  greater?: number,
  lower?: number,
): Promise<NutrientClassification | void> => {
  const {
    defaultAnalysisNutrientClassification,
    selectedAnalysisNutrientClassification,
    isDefaultChecked,
  } = addParamState;

  if (!selectedAnalysisNutrientClassification) {
    setAddParamState((paramState) => ({
      ...paramState,
      error: true,
    }));
    return;
  }

  const newNutrientClassification = {
    ...defaultAnalysisNutrientClassification,
    greaterOrEqual: Number(defaultAnalysisNutrientClassification?.greaterOrEqual),
    lowerThan: Number(defaultAnalysisNutrientClassification?.lowerThan),
    isDefault: false,
  };
  delete newNutrientClassification.id;
  const updateSoilAnalysisNutrientClassification = {
    method: METHOD.PUT,
    body: JSON.stringify(newNutrientClassification),
    extraUrl: `/${defaultAnalysisNutrientClassification?.id}`,
  };

  delete selectedAnalysisNutrientClassification?.id;
  delete selectedAnalysisNutrientClassification?.error;
  const createSoilAnalysisNutrientClassification = {
    method: METHOD.POST,
    body: JSON.stringify({
      ...selectedAnalysisNutrientClassification,
      isDefault: isDefaultChecked,
      greaterOrEqual: greater ?? 0,
      lowerThan: lower ?? 0,
    }),
  };

  try {
    let responseUpdatePreviousDefault: NutrientClassification | undefined;
    if (isDefaultChecked) {
      const updatePreviousDefaultResponse = await triggerCreateUpdateNutrientClassification(
        updateSoilAnalysisNutrientClassification,
      );
      if (typeof updatePreviousDefaultResponse === 'object') {
        responseUpdatePreviousDefault =
          updatePreviousDefaultResponse?.actionTriggerParameterLevelResult;
      }
    }
    const createResponse = await triggerCreateUpdateNutrientClassification(
      createSoilAnalysisNutrientClassification,
    );
    if (typeof createResponse === 'object') {
      const responseCreate = createResponse?.actionTriggerParameterLevelResult;

      const successUpdateDefault = isDefaultChecked ? responseUpdatePreviousDefault : true;
      if (responseCreate && successUpdateDefault) {
        setDisplaySnackbar({
          title: snackbarMsg,
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 4000,
          open: true,
        });

        updateAnalysisConfigurations({
          ...analysisConfiguration,
          nutrientClassifications: [
            responseCreate,
            ...(createResponse?.impactedParameterLevels || []),
          ],
        });

        setIsAddParamOpened(false);
        return responseCreate;
      }
    }
  } catch (err) {
    console.error('Error creating and updating soil analysis nutrient classification data:', err);
    handleOpenParamValuesInfoDialog(err);
  }
};

export const validateParameterLevelMinWithPreviousLevelMax = (
  nutrientClassifications: NutrientClassification[],
  currentMinPreviousMaxLevelErrors: CurrentMinPreviousMaxLevelErrors,
  setCurrentMinPreviousMaxLevelErrors: (data: CurrentMinPreviousMaxLevelErrors) => void,
) => {
  const newCurrentMinPreviousMaxLevelErrors = cloneDeep(currentMinPreviousMaxLevelErrors);

  for (const [index, nutrientClassificationData] of nutrientClassifications.entries()) {
    if (index > 0) {
      const previousNutrientClassificationData = nutrientClassifications[index - 1];
      const { greaterOrEqualErrors, lowerThanErrors } = newCurrentMinPreviousMaxLevelErrors;

      if (
        previousNutrientClassificationData.lowerThan !== nutrientClassificationData.greaterOrEqual
      ) {
        greaterOrEqualErrors.push(nutrientClassificationData.nutrientClassification);
        lowerThanErrors.push(previousNutrientClassificationData.nutrientClassification);
      } else {
        newCurrentMinPreviousMaxLevelErrors.greaterOrEqualErrors = greaterOrEqualErrors.filter(
          (level: ParameterLevel) => level !== nutrientClassificationData.nutrientClassification,
        );
        newCurrentMinPreviousMaxLevelErrors.lowerThanErrors = lowerThanErrors.filter(
          (level: ParameterLevel) =>
            level !== previousNutrientClassificationData.nutrientClassification,
        );
      }

      setCurrentMinPreviousMaxLevelErrors(newCurrentMinPreviousMaxLevelErrors);
    }
  }
};

export const validateParameterLevelMinMax = (
  nutrientClassifications: NutrientClassification[],
  minMaxErrors: SameLevelMinMaxErrors,
  setMinMaxErrors: (data: SameLevelMinMaxErrors) => void,
) => {
  const newMinMaxErrors = cloneDeep(minMaxErrors);

  nutrientClassifications.forEach((nutrientClassificationData, index, array) => {
    const { greaterOrEqualErrors, lowerThanErrors } = newMinMaxErrors;

    if (nutrientClassificationData.greaterOrEqual >= nutrientClassificationData.lowerThan) {
      greaterOrEqualErrors.push(nutrientClassificationData.nutrientClassification);
      lowerThanErrors.push(nutrientClassificationData.nutrientClassification);
    } else if (
      index !== array.length - 1 &&
      nutrientClassificationData.lowerThan === ParameterLevelRange.Max
    ) {
      lowerThanErrors.push(nutrientClassificationData.nutrientClassification);
    } else {
      newMinMaxErrors.greaterOrEqualErrors = greaterOrEqualErrors.filter(
        (level: ParameterLevel) => level !== nutrientClassificationData.nutrientClassification,
      );
      newMinMaxErrors.lowerThanErrors = lowerThanErrors.filter(
        (level: ParameterLevel) => level !== nutrientClassificationData.nutrientClassification,
      );
    }

    setMinMaxErrors(newMinMaxErrors);
  });
};
