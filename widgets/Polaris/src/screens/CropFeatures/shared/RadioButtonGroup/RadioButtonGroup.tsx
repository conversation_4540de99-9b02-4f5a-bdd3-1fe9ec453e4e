import React, { useEffect, useState } from 'react';

import { RadioButtonGroupContainer, RadioElementWrapper } from './RadioButtonGroup.styled';
import { Label, RadioButton, Subtitle } from '@yaradigitallabs/ahua-react';

export enum BtnTypes {
  LeftType = 'leftType',
  RightType = 'RightType',
}

interface RadioButtonGroupProps {
  title: string;
  leftLabelText: string;
  rightLabelText: string;
  leftDataCy?: string;
  rightDataCy?: string;
  defaultType: BtnTypes;
  onChange: (open: BtnTypes) => void;
}
export const RadioButtonGroup = ({
  title,
  leftLabelText,
  rightLabelText,
  leftDataCy,
  rightDataCy,
  defaultType,
  onChange,
}: RadioButtonGroupProps) => {
  const [selectedType, setSelectedType] = useState(defaultType);

  useEffect(() => {
    if (!defaultType) return;
    setSelectedType(defaultType);
  }, [defaultType]);

  return (
    <>
      <Subtitle
        size='s'
        css={{ paddingLeft: '$x4', paddingBottom: '$x2' }}
        data-cy='radio-btn-title'
      >
        {title}
      </Subtitle>
      <RadioButtonGroupContainer
        defaultValue={defaultType}
        value={selectedType}
        onValueChange={(value: BtnTypes) => {
          setSelectedType(value);
          onChange(value);
        }}
      >
        <RadioElementWrapper
          key={BtnTypes.LeftType}
          checked={selectedType === BtnTypes.LeftType}
          data-cy={leftDataCy ? leftDataCy : 'left-button'}
        >
          <Label htmlFor={BtnTypes.LeftType} size='s'>
            {leftLabelText}
          </Label>
          <RadioButton value={BtnTypes.LeftType} id={BtnTypes.LeftType} />
        </RadioElementWrapper>
        <RadioElementWrapper
          key={BtnTypes.RightType}
          checked={selectedType === BtnTypes.RightType}
          data-cy={rightDataCy ? rightDataCy : 'right-button'}
        >
          <Label htmlFor={BtnTypes.RightType} size='s'>
            {rightLabelText}
          </Label>
          <RadioButton value={BtnTypes.RightType} id={BtnTypes.RightType} />
        </RadioElementWrapper>
      </RadioButtonGroupContainer>
    </>
  );
};
