import React from 'react';
import { render, screen } from '@testing-library/react';
import { BtnTypes, RadioButtonGroup } from '../RadioButtonGroup';
import userEvent from '@testing-library/user-event';

describe('RadioButtonGroup Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <RadioButtonGroup
        title='Radio btn title'
        leftLabelText='Left label text'
        rightLabelText='Right label text'
        defaultType={BtnTypes.LeftType}
        onChange={jest.fn()}
      />,
    );
    expect(screen.getByTestId('radio-btn-title')).toBeInTheDocument();
    expect(screen.getByTestId('left-button')).toBeInTheDocument();
    expect(screen.getByTestId('right-button')).toBeInTheDocument();
  });

  it('renders with passing custom data-cy and default type - right', () => {
    render(
      <RadioButtonGroup
        title='Radio btn title'
        leftLabelText='Left label text'
        rightLabelText='Right label text'
        leftDataCy='custom-data-cy-left'
        rightDataCy='custom-data-cy-right'
        defaultType={BtnTypes.RightType}
        onChange={jest.fn()}
      />,
    );

    const leftRadioBtn = screen.getByTestId('custom-data-cy-left').querySelector('button');
    const rightRadioBtn = screen.getByTestId('custom-data-cy-right').querySelector('button');

    expect(screen.getByTestId('radio-btn-title')).toBeInTheDocument();
    expect(leftRadioBtn).toBeInTheDocument();
    expect(rightRadioBtn).toBeInTheDocument();

    expect(leftRadioBtn).toHaveAttribute('aria-checked', 'false');
    expect(rightRadioBtn).toHaveAttribute('aria-checked', 'true');
  });

  it('handles change event of radio buttons correctly', async () => {
    const user = userEvent.setup();
    const onChangeMock = jest.fn();

    render(
      <RadioButtonGroup
        title='Radio btn title'
        leftLabelText='Left label text'
        rightLabelText='Right label text'
        leftDataCy='custom-data-cy-left'
        rightDataCy='custom-data-cy-right'
        defaultType={BtnTypes.LeftType}
        onChange={onChangeMock}
      />,
    );

    const rightBtn = screen.getByTestId('custom-data-cy-right').querySelector('button');

    expect(rightBtn).toBeInTheDocument();

    await user.click(rightBtn!);

    expect(onChangeMock).toHaveBeenCalledTimes(1);

    const leftRadioBtn = screen.getByTestId('custom-data-cy-left').querySelector('button');
    const rightRadioBtn = screen.getByTestId('custom-data-cy-right').querySelector('button');

    expect(leftRadioBtn).toHaveAttribute('aria-checked', 'false');
    expect(rightRadioBtn).toHaveAttribute('aria-checked', 'true');
  });
});
