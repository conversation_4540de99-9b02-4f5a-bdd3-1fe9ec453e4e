import { RadioButtonGroup, styled } from '@yaradigitallabs/ahua-react';

export const RadioButtonGroupContainer = styled(RadioButtonGroup, {
  width: '672px',
  flexDirection: 'row',
  gap: '$x4',
  paddingLeft: '$x4',
  paddingBottom: '$x6',
});

export const RadioElementWrapper = styled('div', {
  display: 'flex',
  alignItems: 'center',
  maxWidth: '100%',
  flexBasis: '100%',
  justifyContent: 'space-between',
  paddingLeft: '$x3',
  height: '$x12',
  boxSizing: 'border-box',
  lineHeight: '$scale4',
  variants: {
    checked: {
      true: {
        border: '2px solid $blue50',
        borderRadius: '$m',
        backgroundColor: '$blue0',
      },
      false: {
        border: '1px solid $black20',
        borderRadius: '$m',
        '& > label + div:hover': {
          backgroundColor: 'none',
        },
      },
    },
  },
  '& > div:hover': {
    backgroundColor: 'unset',
  },
  '& button': {
    cursor: 'auto',
  },
});
