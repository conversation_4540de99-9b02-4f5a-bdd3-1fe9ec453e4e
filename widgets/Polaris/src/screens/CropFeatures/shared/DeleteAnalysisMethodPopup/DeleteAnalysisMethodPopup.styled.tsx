import { styled, Combobox } from '@yaradigitallabs/ahua-react';

export const ContentStyle = {
  minHeight: '236px',
  maxWidth: '100%',
  width: '560px',
};

export const HelperTextWrapper = styled('div', {
  paddingLeft: '$x3',
  gap: '$x1',
});

export const StyledCombobox = styled(Combobox, {
  'div div div': {
    fontSize: 'var(--fontSizes-scale1)',
  },
  '& .ahua-combobox__option, & .ahua-combobox__option:hover': {
    backgroundColor: 'transparent',
    cursor: 'pointer',
  },
  '& .ahua-combobox__multi-value__label': {
    color: 'var(--colors-black100)',
  },
});
