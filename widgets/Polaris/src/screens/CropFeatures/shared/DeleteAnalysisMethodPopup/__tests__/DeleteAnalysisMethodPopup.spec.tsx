/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { DeleteAnalysisMethodPopup, DeleteAnalysisMethodPopupProps } from '..';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockCMMMAppProviderValue, analysisMethodsMock } from '@common/mocks';

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

describe('widget: DeleteAnalysisMethodPopup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const deleteAnalysisMethodPopupProps: DeleteAnalysisMethodPopupProps = {
    showDialog: true,
    onOpenChange: jest.fn(),
    analysisMethods: [...analysisMethodsMock.entities],
    onSave: jest.fn(),
  };

  it('DeleteAnalysisMethodPopup: should show modal when showDialog is true and match snapshot', () => {
    const props = { ...deleteAnalysisMethodPopupProps };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <DeleteAnalysisMethodPopup {...props} />
      </AppContext.Provider>,
    );
    expect(screen.getByTestId('delete-analysis-method-popup-content')).toBeInTheDocument();
    expect(component).toMatchSnapshot();
  });

  it('DeleteAnalysisMethodPopup: should not show modal when showDialog is false', async () => {
    const props = { ...deleteAnalysisMethodPopupProps, showDialog: false };
    const { queryByTestId } = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <DeleteAnalysisMethodPopup {...props} />
      </AppContext.Provider>,
    );
    expect(queryByTestId('delete-analysis-method-popup-content')).not.toBeInTheDocument();
  });
});
