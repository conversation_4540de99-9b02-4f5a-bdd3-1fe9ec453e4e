// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: DeleteAnalysisMethodPopup DeleteAnalysisMethodPopup: should show modal when showDialog is true and match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    />
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:r2:"
      aria-labelledby="radix-:r1:"
      class="c-cPoUYR c-cPoUYR-ieoIBVf-css delete-analysis-method-dialog"
      data-cy="delete-analysis-method-popup-content"
      data-state="open"
      id="radix-:r0:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-cVIuYM dialog-header-main"
      >
        <h2
          class="c-bALNxX"
          data-cy="dialog-title"
          id="radix-:r1:"
        >
          title
        </h2>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs"
          data-cy="dialog-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-jndegn c-jndegn-igwRkrt-css dialog-middle"
      >
        <div
          data-cy="delete-analysis-method-popup-combobox"
        >
          <div
            class="c-jYQeGj c-bblQfh css-b62m3t-container"
          >
            <span
              class="css-1f43avz-a11yText-A11yText"
              id="react-select-2-live-region"
            />
            <span
              aria-atomic="false"
              aria-live="polite"
              aria-relevant="additions text"
              class="css-1f43avz-a11yText-A11yText"
              role="log"
            />
            <div
              class="c-PJLV c-PJLV-drnaRT-size-n c-PJLV-dkZyGE-cv ahua-combobox__control css-gxiu7s"
            >
              <div
                class="ahua-combobox__value-container ahua-combobox__value-container--is-multi css-1nopl1u"
              >
                <div
                  class="ahua-combobox__placeholder css-ic9nqm"
                >
                  placeholder
                </div>
                <div
                  class="c-jszBNd"
                >
                  <div
                    class="ahua-combobox__input-container css-n9qnu9"
                    data-value=""
                  >
                    <input
                      aria-activedescendant=""
                      aria-autocomplete="list"
                      aria-describedby="react-select-2-placeholder"
                      aria-expanded="false"
                      aria-haspopup="true"
                      aria-label="comboBoxAriaLabel"
                      aria-required="true"
                      autocapitalize="none"
                      autocomplete="off"
                      autocorrect="off"
                      class="ahua-combobox__input"
                      id="react-select-2-input"
                      role="combobox"
                      spellcheck="false"
                      style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                      tabindex="0"
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
              <div
                class="ahua-combobox__indicators css-1wy0on6"
              >
                <div
                  aria-hidden="true"
                  class="ahua-combobox__indicator ahua-combobox__dropdown-indicator css-1sk3cuq"
                >
                  <svg
                    aria-hidden="true"
                    class="css-tj5bde-Svg"
                    focusable="false"
                    height="20"
                    viewBox="0 0 20 20"
                    width="20"
                  >
                    <path
                      d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <input
              aria-hidden="true"
              class="css-5kkxb2-requiredInput-RequiredInput"
              required=""
              tabindex="-1"
              value=""
            />
          </div>
        </div>
        <div
          class="c-cqpvPn"
        >
          <div
            class="c-eBRMFw c-eBRMFw-cjUlPD-colorConcept-neutral"
            data-cy="delete-analysis-method-popup-helperText"
          >
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-igGEGP"
            >
              dropdownHelperText
            </p>
          </div>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-ctwkvW"
      >
        <div
          class="c-hcqlDB"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary"
            data-cy="dialog-save-btn"
          >
            <span
              class="c-iepcqn"
            >
              saveChanges
            </span>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
