import { ModalDialog } from '@widgets/Polaris/src/components';
import { ComboboxOption, HelperText } from '@yaradigitallabs/ahua-react';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { HelperTextWrapper, StyledCombobox } from './DeleteAnalysisMethodPopup.styled';
import { DeleteAnalysisMethodPopupProps } from './DeleteAnalysisMethodPopup.type';
import './styles.scss';

export const DeleteAnalysisMethodPopup: FC<DeleteAnalysisMethodPopupProps> = ({
  showDialog,
  onOpenChange,
  analysisMethods,
  onSave,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.deleteAnalysisMethodPopup',
  });
  const [selectedAnalysisMethodsToDelete, setSelectedAnalysisMethodsToDelete] = useState<
    ComboboxOption[]
  >([]);
  const [selected, setSelected] = useState<boolean>(false);
  const [hasEmptySelection, setHasEmptySelection] = useState<boolean>(false);

  const handleSave = (): void => {
    if (selectedAnalysisMethodsToDelete.length === 0) {
      setHasEmptySelection(true);
      return;
    }

    onSave(selectedAnalysisMethodsToDelete);

    if (analysisMethods?.length !== selectedAnalysisMethodsToDelete?.length) {
      onOpenChange(false);
    }
  };

  const analysisMethodsData =
    analysisMethods?.map((item) => ({
      label: item.name,
      value: item.id,
    })) || [];

  const onSelect = (selectedValue: ComboboxOption[]): void => {
    setSelected(selectedValue.length > 0);
    if (selectedValue.length > 0 && hasEmptySelection) {
      setHasEmptySelection(false);
    }
    if (selectedValue) {
      setSelectedAnalysisMethodsToDelete(selectedValue);
    }
  };

  useEffect(() => {
    if (!showDialog) {
      setSelected(false);
      setHasEmptySelection(false);
      setSelectedAnalysisMethodsToDelete([]);
    }
  }, [showDialog]);

  if (!showDialog) return null;

  return (
    <ModalDialog
      title={t('title')}
      isOpen={showDialog}
      onChange={onOpenChange}
      dataCy={'delete-analysis-method-popup-content'}
      onSaveClick={handleSave}
      className='delete-analysis-method-dialog'
    >
      <div data-cy='delete-analysis-method-popup-combobox'>
        <StyledCombobox
          aria-label={t('comboBoxAriaLabel')}
          variant={hasEmptySelection ? 'error' : 'default'}
          cover={'outline'}
          options={analysisMethodsData}
          placeholder={t('placeholder')}
          selectedItemsMoreText={'more'}
          size={'n'}
          isMulti={true}
          onChange={onSelect}
          required
        />
      </div>
      <HelperTextWrapper>
        {hasEmptySelection ? (
          <HelperText
            colorConcept='destructive'
            data-cy={'delete-analysis-method-popup-hasEmptySelection'}
          >
            {t('dropdownDeleteErrorText')}
          </HelperText>
        ) : (
          !selected && (
            <HelperText colorConcept='neutral' data-cy={'delete-analysis-method-popup-helperText'}>
              {t('dropdownHelperText')}
            </HelperText>
          )
        )}
      </HelperTextWrapper>
    </ModalDialog>
  );
};
