import { AnalysisConfiguration, NutrientClassification } from '@common/types';

export const getConfigurationWithUpdatedNutrientClassification = (
  soilAnalysisConfiguration: AnalysisConfiguration,
  updatedNutrientClassification: NutrientClassification,
): AnalysisConfiguration | undefined => {
  if (!soilAnalysisConfiguration) return;
  if (!updatedNutrientClassification) return soilAnalysisConfiguration;

  const updatedNutrientClassificationIndex =
    soilAnalysisConfiguration?.nutrientClassifications.findIndex(
      ({ id }) => id === updatedNutrientClassification?.id,
    );

  const replacedNutrientClassification = {
    ...soilAnalysisConfiguration,
    nutrientClassifications: [
      ...(soilAnalysisConfiguration.nutrientClassifications?.slice(
        0,
        updatedNutrientClassificationIndex,
      ) || []),
      updatedNutrientClassification,
      ...(soilAnalysisConfiguration.nutrientClassifications?.slice(
        updatedNutrientClassificationIndex + 1,
      ) || []),
    ],
  };
  return replacedNutrientClassification;
};
