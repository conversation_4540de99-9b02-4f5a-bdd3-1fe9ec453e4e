import { NutrientClassification } from '@common/types';
import {
  AddParamStateProps,
  Parameter,
} from '../../../components/NutritionParamsWithValuesTable/AddNutritionParamPopup';
import {
  getParametersName,
  setAnalysisNutrientClassification,
  setRemainingNutrientParams,
} from '../addNutritionParamPopupUtils';
import { Dispatch, SetStateAction } from 'react';
import { act } from '@testing-library/react';

const parameters: NutrientClassification[] = [
  {
    id: 'test1',
    soilAnalysisConfigurationId: '101',
    nutrientClassification: 'LOW',
    isDefault: false,
    greaterOrEqual: 0,
    lowerThan: 0,
  },
  {
    id: 'test2',
    soilAnalysisConfigurationId: '101',
    nutrientClassification: 'MEDIUM',
    isDefault: true,
    greaterOrEqual: 0,
    lowerThan: 0,
  },
];
const mockSetAddParamState: Dispatch<SetStateAction<AddParamStateProps>> = jest.fn();
const SoilAnalysisNutrientClassificationData: NutrientClassification[] = [
  {
    id: 'test1',
    soilAnalysisConfigurationId: '101',
    nutrientClassification: 'LOW',
    isDefault: false,
    greaterOrEqual: 0,
    lowerThan: 0,
    created: new Date('2023-01-01'),
    modified: new Date('2023-01-02'),
    modifiedBy: 'user1',
    deleted: null,
  },
  {
    id: 'test3',
    soilAnalysisConfigurationId: '301',
    nutrientClassification: 'HIGH',
    isDefault: true,
    greaterOrEqual: 0,
    lowerThan: 0,
    created: new Date('2023-02-01'),
    modified: new Date('2023-02-02'),
    modifiedBy: 'user2',
    deleted: null,
  },
];

describe('addNutritionParamPopupUtils', () => {
  describe('setRemainingNutrientParams', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should filter out nutrients that are already in SoilAnalysisNutrientClassificationData and set defaultAnalysisNutrientClassification', () => {
      const initialState: AddParamStateProps = {
        parameters: [],
        selectedAnalysisNutrientClassification: null,
        defaultAnalysisNutrientClassification: undefined,
        isDefaultChecked: false,
        error: false,
      };
      setRemainingNutrientParams(
        mockSetAddParamState,
        SoilAnalysisNutrientClassificationData,
        parameters,
      );
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(initialState);

      const expectedParameters: NutrientClassification[] = [
        JSON.parse(JSON.stringify(parameters[1])),
      ];

      expect(newState.parameters).toEqual(expectedParameters);
      expect(newState.defaultAnalysisNutrientClassification).toEqual(
        SoilAnalysisNutrientClassificationData[1],
      );
    });

    it('should set parameters to defaultAnalysisNutrientClassification if SoilAnalysisNutrientClassificationData is undefined', () => {
      const initialState: AddParamStateProps = {
        parameters: [],
        selectedAnalysisNutrientClassification: null,
        defaultAnalysisNutrientClassification: undefined,
        isDefaultChecked: false,
        error: false,
      };
      setRemainingNutrientParams(mockSetAddParamState, undefined, undefined);
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(initialState);

      expect(newState.defaultAnalysisNutrientClassification).toBeUndefined();
    });

    it('should set parameters to an empty array if defaultAnalysisNutrientClassification is undefined', () => {
      const initialState: AddParamStateProps = {
        parameters: [],
        selectedAnalysisNutrientClassification: null,
        defaultAnalysisNutrientClassification: undefined,
        isDefaultChecked: false,
        error: false,
      };

      setRemainingNutrientParams(
        mockSetAddParamState,
        SoilAnalysisNutrientClassificationData,
        undefined,
      );
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(initialState);

      expect(newState.parameters).toEqual([]);
    });
  });

  describe('getParametersName', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should return an array of parameters with text and value fields', () => {
      const expected: Parameter[] = [
        { text: 'LOW', value: 'LOW' },
        { text: 'MEDIUM', value: 'MEDIUM' },
      ];

      const result = getParametersName(parameters);

      expect(result).toEqual(expected);
    });

    it('should return an empty array if parameters is undefined', () => {
      // @ts-ignore
      const result = getParametersName(undefined);
      expect(result).toEqual([]);
    });

    it('should return an empty array if parameters is an empty array', () => {
      const result = getParametersName([]);
      expect(result).toEqual([]);
    });
  });

  describe('setSoilAnalysisNutrientClassification', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should update the state with the selected crop demand analysis if found', () => {
      // @ts-ignore
      const addParamState: AddParamStateProps = {
        parameters,
        selectedAnalysisNutrientClassification: null,
        error: true,
      };

      const expectedSelectedAnalysisNutrientClassifications: NutrientClassification = parameters[0];

      act(() => {
        setAnalysisNutrientClassification('LOW', mockSetAddParamState, 'leaf');
      });

      expect(mockSetAddParamState).toHaveBeenCalledWith(expect.any(Function));
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(addParamState);

      expect(newState.selectedAnalysisNutrientClassification).toEqual(
        expectedSelectedAnalysisNutrientClassifications,
      );
      expect(newState.error).toBe(false);
    });
  });
});
