import { getConfigurationWithUpdatedNutrientClassification } from '../nutritionParameterTableHelpers';
import { ParameterLevelEnum } from '@common/constants';

describe('nutritionParameterTableHelpers', () => {
  describe('getConfigurationWithUpdatedNutrientClassification', () => {
    const mockNutrientClassification = {
      id: 'test111',
      soilAnalysisConfigurationId: '111id',
      nutrientClassification: ParameterLevelEnum.LOW,
      isDefault: false,
      greaterOrEqual: 0,
      lowerThan: 0,
    };

    const mockConfiguration = {
      id: '111id',
      countryId: '111countryId',
      cropRegionId: '111cropRegionId',
      nutrientId: '111nutrientId',
      analysisMethodId: '111analysisMethodId',
      analysisBaseUnitId: '111analysisBaseUnitId',
      nutrientClassifications: [
        {
          id: 'test111',
          soilAnalysisConfigurationId: '111id',
          nutrientClassification: ParameterLevelEnum.HIGH,
          isDefault: false,
          greaterOrEqual: 0,
          lowerThan: 0,
        },
      ],
      considerSecondaryParameters: false,
    };

    it('returns correct structure and values with all parameters provided', () => {
      const expectedResult = {
        ...mockConfiguration,
        nutrientClassifications: [mockNutrientClassification],
      };
      const result = getConfigurationWithUpdatedNutrientClassification(
        mockConfiguration,
        mockNutrientClassification,
      );
      expect(result).toMatchObject(expectedResult);
    });

    it('returns undefined if the first argument is falsy', () => {
      const result = getConfigurationWithUpdatedNutrientClassification(
        undefined,
        mockNutrientClassification,
      );

      expect(result).toBeUndefined();
    });

    it('returns the first argumant if the second argument is falsy', () => {
      const result = getConfigurationWithUpdatedNutrientClassification(
        mockConfiguration,
        undefined,
      );

      expect(result).toMatchObject(mockConfiguration);
    });
  });
});
