import {
  AnalysisActions,
  UpdateAnalysisActions,
  UpdateAnalysisState,
} from '../NutritionParamsWithValuesTable.type';

export const analysisInitialState: UpdateAnalysisState = {
  selectedNutrientClassification: null,
  updateType: '',
};

/**
 * Reduces the actions to update the state of the analysis management.
 * This function handles state transitions based on dispatched actions,
 * allowing the state to be updated safely in response to changes in the application.
 *
 * @param {UpdateAnalysisState} state - The current state of the analysis management.
 * @param {AnalysisActions} action - The action dispatched to update the state.
 * @returns {UpdateAnalysisState} The updated state after applying the action.
 */
export function analysisReducer(
  state: UpdateAnalysisState,
  action: AnalysisActions,
): UpdateAnalysisState {
  switch (action.type) {
    case UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION:
      return { ...state, selectedNutrientClassification: action.payload };
    case UpdateAnalysisActions.SET_UPDATE_TYPE:
      return { ...state, updateType: action.payload };
    default:
      return state;
  }
}
