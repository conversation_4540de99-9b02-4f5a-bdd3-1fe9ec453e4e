import { Dispatch, SetStateAction } from 'react';
import {
  AnalysisConfiguration,
  AnalysisType,
  AnalysisTypeOptions,
  NutrientClassification,
  NutrientClassificationWithError,
  ParameterLevel,
} from '@common/types';
import { AddParamStateProps, Parameter } from '../AddNutritionParamPopup';

export const setRemainingNutrientParams = (
  setAddParamState: Dispatch<SetStateAction<AddParamStateProps>>,
  analysisNutrientClassificationsData: NutrientClassification[] | undefined,
  defaultAnalysisNutrientClassifications: NutrientClassification[] = [],
): void => {
  const defaultAnalysisNutrientClassificationsData = defaultAnalysisNutrientClassifications?.filter(
    (defaultNutrientClassification) =>
      !analysisNutrientClassificationsData?.some(
        (nutrientClassification) =>
          defaultNutrientClassification.nutrientClassification ===
          nutrientClassification?.nutrientClassification,
      ),
  );

  const nutrientAnalysis = analysisNutrientClassificationsData?.find((n) => n.isDefault);

  setAddParamState((addParamState) => ({
    ...addParamState,
    parameters: defaultAnalysisNutrientClassificationsData,
    defaultAnalysisNutrientClassification: nutrientAnalysis,
  }));
};

export const getParametersName = (parameters: NutrientClassification[]): Parameter[] => {
  return (
    parameters?.map((parameter) => ({
      text: parameter?.nutrientClassification || '',
      value: parameter?.nutrientClassification || '',
    })) || []
  );
};

export const setAnalysisNutrientClassification = (
  value: ParameterLevel,
  setAddParamState: Dispatch<SetStateAction<AddParamStateProps>>,
  analysisType: AnalysisType,
  analysisConfiguration: AnalysisConfiguration,
): void => {
  setAddParamState((addParamState) => {
    const existingAnalysisNutrientClassification =
      addParamState.selectedAnalysisNutrientClassification;
    const selectedAnalysisNutrientClassification: NutrientClassificationWithError =
      addParamState.parameters?.find((item) => item.nutrientClassification === value) || {
        ...existingAnalysisNutrientClassification,
        isDefault: existingAnalysisNutrientClassification?.isDefault || false,
        nutrientClassification: value,
        greaterOrEqual: Number(existingAnalysisNutrientClassification?.greaterOrEqual) ?? 0,
        lowerThan: Number(existingAnalysisNutrientClassification?.lowerThan) ?? 0,
        error: Boolean(value),
      };

    switch (analysisType) {
      case AnalysisTypeOptions.SOIL:
        selectedAnalysisNutrientClassification.soilAnalysisConfigurationId =
          existingAnalysisNutrientClassification?.soilAnalysisConfigurationId ||
          analysisConfiguration.id ||
          '';
        break;
      case AnalysisTypeOptions.LEAF:
        selectedAnalysisNutrientClassification.leafAnalysisConfigurationId =
          existingAnalysisNutrientClassification?.leafAnalysisConfigurationId ||
          analysisConfiguration.id ||
          '';
        break;
      case AnalysisTypeOptions.WATER:
        selectedAnalysisNutrientClassification.waterAnalysisConfigurationId =
          existingAnalysisNutrientClassification?.waterAnalysisConfigurationId ||
          analysisConfiguration.id ||
          '';
        selectedAnalysisNutrientClassification.warningMessage =
          existingAnalysisNutrientClassification?.warningMessage || '';
        break;
      default:
        break;
    }

    return {
      ...addParamState,
      error: false,
      selectedAnalysisNutrientClassification: selectedAnalysisNutrientClassification,
    };
  });
};
