import {
  AnalysisActions,
  UpdateAnalysisActions,
  UpdateAnalysisState,
} from '../../../../shared/NutritionParamsWithValuesTable';
import { analysisReducer } from '../analysisReducer';
import { NutrientClassification } from '@common/types';

const soilAnalysisNutrientClassificationsData: NutrientClassification[] = [
  {
    id: '201',
    soilAnalysisConfigurationId: '301',
    nutrientClassification: 'LOW',
    isDefault: true,
    greaterOrEqual: 0,
    lowerThan: 0,
    created: new Date('2023-01-01'),
    modified: new Date('2023-01-02'),
    modifiedBy: 'user1',
    deleted: null,
  },
  {
    id: '202',
    soilAnalysisConfigurationId: '301',
    nutrientClassification: 'MEDIUM',
    isDefault: false,
    greaterOrEqual: 0,
    lowerThan: 0,
    created: new Date('2023-02-01'),
    modified: new Date('2023-02-02'),
    modifiedBy: 'user2',
    deleted: null,
  },
];

describe('analysisReducer', () => {
  const initialState: UpdateAnalysisState = {
    selectedNutrientClassification: null,
    updateType: '',
  };

  it('should handle SET_SELECTED_NUTRIENT action', () => {
    const action: AnalysisActions = {
      type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION,
      // @ts-ignore
      payload: soilAnalysisNutrientClassificationsData[0],
    };
    const expectedState = {
      ...initialState,
      selectedNutrientClassification: soilAnalysisNutrientClassificationsData[0],
    };
    const result = analysisReducer(initialState, action);
    expect(result).toEqual(expectedState);
  });

  it('should handle SET_UPDATE_TYPE action', () => {
    const action: AnalysisActions = {
      type: UpdateAnalysisActions.SET_UPDATE_TYPE,
      payload: 'DEFAULT',
    };
    const expectedState = {
      ...initialState,
      updateType: 'DEFAULT',
    };
    const result = analysisReducer(initialState, action);
    expect(result).toEqual(expectedState);
  });

  it('should return the current state when action type is unknown', () => {
    const unknownAction = {
      type: 'UNKNOWN_ACTION',
      payload: {},
    };
    // @ts-ignore
    const stateAfterUnknownAction = analysisReducer(initialState, unknownAction);
    expect(stateAfterUnknownAction).toBe(initialState);
  });
});
