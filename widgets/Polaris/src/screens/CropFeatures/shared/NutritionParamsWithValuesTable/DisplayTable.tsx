/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useEffect, useState } from 'react';
import { Table, Input, Button, RadioButton, RadioButtonGroup } from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import {
  CurrentMinPreviousMaxLevelErrors,
  ParameterLevelRange,
  paramLevelHeaders,
  PropertyKeyEnum,
  SameLevelMinMaxErrors,
  waterParamLevelHeaders,
} from './NutritionParamsWithValuesTable.type';
import { WaterAnalysisWarningMessageTableCell } from './WaterAnalysisWarningMessage/WaterAnalysisWarningMessageTableCell';
import {
  FeatureConfig,
  AnalysisType,
  NutrientClassification,
  AnalysisTypeOptions,
  ParameterLevel,
  AnalysisConfiguration,
} from '@common/types';
import {
  validateParameterLevelMinMax,
  validateParameterLevelMinWithPreviousLevelMax,
} from '../helpers';
import { PARAMETER_LEVEL_MAX_DISPLAY_VALUE } from '@common/constants';
import { TableContainer } from '../../Fertigation/DemandCalculations';
import { ParameterLevelCell } from '@widgets/Polaris/src/components';

type DisplayTableProps<T extends NutrientClassification> = {
  analysisNutrientClassifications: T[] | undefined;
  handleDefaultChange: (nutrientClassification: T) => void;
  handleValueFocused: (
    param: keyof NutrientClassification,
    nutrientClassification: NutrientClassification,
  ) => void;
  handleValueChanged: (param: string, value: any, nutrientClassification: T) => void;
  handleValueUpdate: (
    param: string,
    value: string,
    selectedValue: string | undefined,
    targetField: string | null,
  ) => void;
  handleOpenDeleteDialog: (soilAnalysisNutrientClassification: T) => void;
  handleIsAddPopupOpened: (open: boolean) => void;
  configuration: AnalysisConfiguration;
  keyPrefix: string;
  configType: FeatureConfig;
  analysisType: AnalysisType;
  updateAnalysisConfigurations: (analysisConfiguration: AnalysisConfiguration) => void;
  hasActionFailed: boolean;
};

const DisplayTable = <T extends NutrientClassification>({
  analysisNutrientClassifications,
  handleDefaultChange,
  handleValueFocused,
  handleValueChanged,
  handleValueUpdate,
  handleOpenDeleteDialog,
  handleIsAddPopupOpened,
  configuration,
  keyPrefix,
  configType,
  analysisType,
  updateAnalysisConfigurations,
  hasActionFailed,
}: DisplayTableProps<T>) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: keyPrefix,
  });
  const [selectedValue, setSelectedValue] = useState<string>();
  const [selectedLevel, setSelectedLevel] = useState<ParameterLevel>();
  const [currentMinPreviousMaxErrors, setCurrentMinPreviousMaxErrors] =
    useState<CurrentMinPreviousMaxLevelErrors>({
      greaterOrEqualErrors: [],
      lowerThanErrors: [],
    });
  const [minMaxErrors, setMinMaxErrors] = useState<SameLevelMinMaxErrors>({
    greaterOrEqualErrors: [],
    lowerThanErrors: [],
  });

  const { LowerThan } = PropertyKeyEnum;

  const paramLevelHeadersUpdated =
    analysisType !== AnalysisTypeOptions.WATER ? paramLevelHeaders : waterParamLevelHeaders;

  const checkForGreaterOrEqualErrors = useCallback(
    (level: ParameterLevel) =>
      currentMinPreviousMaxErrors.greaterOrEqualErrors.includes(level) ||
      minMaxErrors.greaterOrEqualErrors.includes(level),
    [currentMinPreviousMaxErrors, minMaxErrors],
  );
  const checkForLowerThanErrors = useCallback(
    (level: ParameterLevel) =>
      currentMinPreviousMaxErrors.lowerThanErrors.includes(level) ||
      minMaxErrors.lowerThanErrors.includes(level),
    [currentMinPreviousMaxErrors, minMaxErrors],
  );

  useEffect(() => {
    if (analysisNutrientClassifications) {
      validateParameterLevelMinWithPreviousLevelMax(
        analysisNutrientClassifications,
        currentMinPreviousMaxErrors,
        setCurrentMinPreviousMaxErrors,
      );
      validateParameterLevelMinMax(analysisNutrientClassifications, minMaxErrors, setMinMaxErrors);
    }
  }, [analysisNutrientClassifications, hasActionFailed]);

  const displayLowerThanClassificationValue = (
    nutrientClassification: NutrientClassification,
    index: number,
  ) => {
    if (
      !analysisNutrientClassifications ||
      !analysisNutrientClassifications.length ||
      !nutrientClassification
    ) {
      return;
    }
    if (
      index === analysisNutrientClassifications.length - 1 &&
      nutrientClassification.lowerThan === ParameterLevelRange.Max
    ) {
      return PARAMETER_LEVEL_MAX_DISPLAY_VALUE;
    }
    if (selectedLevel === nutrientClassification.nutrientClassification && selectedValue === '') {
      return '';
    }
    return nutrientClassification.lowerThan ?? '0';
  };

  const displayGreaterThanClassificationValue = (
    nutrientClassification: NutrientClassification,
    index: number,
  ) => {
    if (
      !analysisNutrientClassifications ||
      !analysisNutrientClassifications.length ||
      !nutrientClassification
    ) {
      return;
    }
    if (index === 0) {
      return '0';
    }
    return nutrientClassification.greaterOrEqual ?? '0';
  };

  return (
    <TableContainer data-cy='parameter-levels-table-values'>
      <RadioButtonGroup
        value={analysisNutrientClassifications?.find((item) => item.isDefault)}
        onValueChange={(value: T) => {
          if (value) {
            handleDefaultChange(value);
          }
        }}
        data-cy='nutrient-classification-radio-button-group'
      >
        <Table className='table'>
          <thead>
            <Table.Row className='header-row'>
              {paramLevelHeadersUpdated.map(({ title, className }, i) => (
                <Table.Head className={className} key={title + i}>
                  {t(`header.${title}`)}
                </Table.Head>
              ))}
            </Table.Row>
          </thead>

          <tbody>
            {analysisNutrientClassifications &&
              analysisNutrientClassifications.map((nutrientClassification, index) => (
                <Table.Row key={`${index}_${nutrientClassification.id}`} className='table-body-row'>
                  <Table.Cell>
                    <RadioButton
                      value={nutrientClassification}
                      id={nutrientClassification.id}
                      concept='success'
                      data-cy={`nutrient-${index}-radio`}
                      tabIndex={index}
                      aria-label={t('nutrientClassificationRadioButtonLabel', {
                        nutrientClassification: nutrientClassification.nutrientClassification,
                      })}
                    />
                  </Table.Cell>
                  <Table.Cell>
                    <ParameterLevelCell
                      analysisNutrientClassification={nutrientClassification}
                      analysisNutrientClassificationData={analysisNutrientClassifications}
                      configuration={configuration}
                      configType={configType}
                      analysisType={analysisType}
                      updateAnalysisConfigurations={updateAnalysisConfigurations}
                    />
                  </Table.Cell>
                  <Table.Cell>
                    <Input
                      size='xs'
                      value={displayGreaterThanClassificationValue(nutrientClassification, index)}
                      variant={
                        checkForGreaterOrEqualErrors(nutrientClassification.nutrientClassification)
                          ? 'error'
                          : 'default'
                      }
                      tabIndex={index}
                      disabled
                      data-cy='greater-or-equal-input'
                    />
                  </Table.Cell>
                  <Table.Cell>
                    <Input
                      size='xs'
                      value={displayLowerThanClassificationValue(nutrientClassification, index)}
                      variant={
                        checkForLowerThanErrors(nutrientClassification.nutrientClassification)
                          ? 'error'
                          : 'default'
                      }
                      tabIndex={index}
                      disabled={index === analysisNutrientClassifications.length - 1}
                      onFocus={() => {
                        setSelectedValue(
                          nutrientClassification.lowerThan === 0
                            ? ''
                            : String(nutrientClassification.lowerThan),
                        );
                        setSelectedLevel(nutrientClassification.nutrientClassification);
                        handleValueFocused(LowerThan, nutrientClassification);
                      }}
                      onChange={({ target: { value } }) => {
                        setSelectedLevel(undefined);
                        handleValueChanged(LowerThan, value, nutrientClassification);
                      }}
                      onBlur={(e) => {
                        const relatedTarget =
                          e.relatedTarget &&
                          'tabIndex' in e.relatedTarget &&
                          e.relatedTarget.tabIndex === e.target.tabIndex &&
                          e.relatedTarget
                            ? e.relatedTarget.role
                            : null;
                        setSelectedLevel(undefined);
                        handleValueUpdate(LowerThan, e.target.value, selectedValue, relatedTarget);
                      }}
                      data-cy='lower-than-input'
                    />
                  </Table.Cell>
                  {analysisType === AnalysisTypeOptions.WATER && (
                    <WaterAnalysisWarningMessageTableCell
                      classification={nutrientClassification}
                      selectedValue={selectedValue}
                      setSelectedValue={setSelectedValue}
                      handleValueFocused={handleValueFocused}
                      handleValueUpdate={handleValueUpdate}
                    />
                  )}
                  <Table.ActionsCell>
                    <Table.Action
                      icon='Delete'
                      onClick={() => handleOpenDeleteDialog(nutrientClassification)}
                      data-cy={`delete-nutrient-${index}`}
                    />
                  </Table.ActionsCell>
                </Table.Row>
              ))}
          </tbody>
        </Table>
      </RadioButtonGroup>

      <Button
        type='button'
        iconLeading='Plus'
        label='Add level'
        disabled={analysisNutrientClassifications?.length === 5}
        size='s'
        variant='ghost'
        onClick={() => {
          // This condition will be removed when secondary parameter activation is implemented
          if (
            analysisNutrientClassifications?.length &&
            analysisNutrientClassifications?.length < 5
          ) {
            handleIsAddPopupOpened(true);
          }
        }}
        data-cy='add-level-button'
      >
        {t('addLevelTableButton')}
      </Button>
    </TableContainer>
  );
};

export default DisplayTable;
