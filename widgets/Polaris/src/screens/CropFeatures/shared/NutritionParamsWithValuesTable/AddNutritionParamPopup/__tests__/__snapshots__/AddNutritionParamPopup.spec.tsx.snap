// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: AddPrePostCrop should match snapshot when isAddPopupOpened is true 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    />
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:rp:"
      aria-labelledby="radix-:ro:"
      class="c-cPoUYR c-cPoUYR-ieoIBVf-css"
      data-cy="opened-add another level"
      data-state="open"
      id="radix-:rn:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-cVIuYM dialog-header-main"
      >
        <h2
          class="c-bALNxX"
          data-cy="dialog-polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.addAnotherLevel"
          id="radix-:ro:"
        >
          polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.addAnotherLevel
        </h2>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs"
          data-cy="dialog-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-jndegn c-jndegn-igwRkrt-css dialog-middle"
      >
        <div
          class="c-jnTsVE"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-ubosY-textType-labelAsPlaceholder"
              >
                polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.parameterLevel
              </label>
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-hDbASB-selectSize-s"
              />
              <button
                aria-autocomplete="none"
                aria-controls="radix-:rq:"
                aria-expanded="false"
                aria-label="Select parameter level"
                class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="0"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
          <div
            class="c-cjwGZw"
          >
            <div
              class="c-gJoajD c-gJoajD-ifGHEql-css"
            >
              <input
                class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                data-cy="greater-or-equal-input"
                disabled=""
                placeholder="polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.greaterOrEqual"
                value="0"
              />
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
              >
                polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.greaterOrEqual
              </label>
              <span
                class="c-fcBbhr"
              />
            </div>
            <div
              class="c-gJoajD c-gJoajD-ifGHEql-css"
            >
              <input
                class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                data-cy="lower-than-input"
                placeholder="polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.lowerThan"
                value="0"
              />
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
              >
                polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.lowerThan
              </label>
              <span
                class="c-fcBbhr"
              />
            </div>
          </div>
          <h1
            class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css"
            data-cy="set-as-default-caption"
          >
            polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.setAsDefault
          </h1>
          <div
            class="c-cnNYRG"
          >
            <label
              class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
              data-cy="polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.yesSetAsDefault"
              for="option undefined"
            >
              polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog.yesSetAsDefault
            </label>
            <div
              class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
            >
              <button
                aria-checked="false"
                aria-label="Checkbox false"
                class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                data-cy="set-as-default"
                data-state="unchecked"
                role="checkbox"
                type="button"
                value="on"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-ctwkvW"
      >
        <div
          class="c-hcqlDB"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary"
            data-cy="dialog-save-btn"
          >
            <span
              class="c-iepcqn"
            >
              save
            </span>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
