import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Input, Select, Title } from '@yaradigitallabs/ahua-react';

import { ACTIONS, PARAMETER_LEVEL_MAX_DISPLAY_VALUE } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { LabelWithCheckbox, ModalDialog } from '@widgets/Polaris/src/components';
import { AddNutritionParamPopupProps, Parameter, LevelColumnMain, InputWrapper } from './index';

import { validateParameterNumber, formatValidTreeDecimalsNumber } from '@widgets/Polaris/utils';
import {
  addNutrientParameterUtility,
  getDefaultAnalysisNutrientClassifications,
  getInitialValues,
  getOrderedNutrientClassifications,
  isHighestLevel,
  isLowestLevel,
} from '../../helpers';
import { MAX_LIMIT_NUMBER } from '../../constants';
import { ParameterLevel } from '@common/types';
import { ParameterLevelRange, PropertyKeyEnum } from '../NutritionParamsWithValuesTable.type';
import {
  getParametersName,
  setAnalysisNutrientClassification,
  setRemainingNutrientParams,
} from '../utils/addNutritionParamPopupUtils';

export const AddNutritionParamPopup: React.FC<AddNutritionParamPopupProps> = ({
  isAddPopupOpened,
  setIsAddPopupOpened,
  analysisConfiguration,
  addParamState,
  setAddParamState,
  analysisNutrientClassifications,
  triggerCreateUpdateNutrientClassification,
  analysisType,
  handleOpenParamValuesInfoDialog,
}) => {
  const { t } = useTranslation();
  const { setDisplaySnackbar } = useSnackbar();

  const prefix = 'polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog';
  const {
    selectedFeatureNutrients,
    selectedPlanConfigTab,
    methods: { updateAnalysisConfigurations },
  } = useAppContext();
  const [greater, setGreater] = useState<string>('0');
  const [lower, setLower] = useState<string>('0');
  const selectedNutrientId = useMemo(
    () => selectedPlanConfigTab && selectedFeatureNutrients?.[selectedPlanConfigTab],
    [selectedPlanConfigTab, selectedFeatureNutrients],
  );
  const { GreaterOrEqual, LowerThan } = PropertyKeyEnum;

  useEffect(() => {
    if (selectedNutrientId && analysisConfiguration) {
      setRemainingNutrientParams(
        setAddParamState,
        analysisNutrientClassifications,
        getDefaultAnalysisNutrientClassifications(analysisConfiguration, analysisType),
      );
    }
  }, [isAddPopupOpened, selectedNutrientId, analysisConfiguration]);

  const analysisNutrientClassificationParameterLevels: Parameter[] = useMemo(() => {
    if (!addParamState?.parameters?.length) return [];

    return getParametersName(addParamState?.parameters).map((param) => ({
      ...param,
      text: t(`polaris.common.parameterLevel.${param.text}`),
    }));
  }, [addParamState?.parameters]);

  const updateAnalysisConfiguration = (value: ParameterLevel): void => {
    setAnalysisNutrientClassification(value, setAddParamState, analysisType, analysisConfiguration);
  };

  const addNewParameterHandler = useCallback(async (): Promise<void> => {
    if (!addParamState.selectedAnalysisNutrientClassification?.nutrientClassification) {
      return setAddParamState((prevState) => ({
        ...prevState,
        error: true,
      }));
    }
    const levelAddedMessage = () => {
      const paramLevel =
        addParamState.selectedAnalysisNutrientClassification?.nutrientClassification;
      const paramLevelLocalized = t(`polaris.common.parameterLevel.${paramLevel}`);

      return t(`polaris.common.levelAdded`, {
        paramLevel: paramLevelLocalized,
      });
    };

    const nutrientClassification = await addNutrientParameterUtility(
      addParamState,
      analysisConfiguration,
      setAddParamState,
      setDisplaySnackbar,
      setIsAddPopupOpened,
      triggerCreateUpdateNutrientClassification,
      updateAnalysisConfigurations,
      handleOpenParamValuesInfoDialog,
      levelAddedMessage(),
      Number(greater),
      Number(lower),
    );
    if (!nutrientClassification) return;
  }, [addParamState, analysisConfiguration, greater, lower]);

  useEffect(() => {
    if (!isAddPopupOpened) {
      setGreater('0');
      setLower('0');
    }
    if (isAddPopupOpened && selectedNutrientId) {
      const initialValues = getInitialValues(analysisType);
      setAddParamState((prevState) => ({
        ...prevState,
        selectedAnalysisNutrientClassification: {
          ...prevState.selectedAnalysisNutrientClassification,
          ...initialValues,
        },
      }));
    }
  }, [isAddPopupOpened, selectedNutrientId, analysisType]);

  const handleValueChanged = (value: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;

    value = formatValidTreeDecimalsNumber(value);

    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;
    setLower(value);
  };

  const orderedNutrientClassifications = getOrderedNutrientClassifications(
    analysisConfiguration?.nutrientClassifications,
  );

  const greaterOrEqualValue: string = useMemo(() => {
    const updatedParameterLevel = addParamState.selectedAnalysisNutrientClassification;
    let value = '0';
    const nutrientClassificationLevel = updatedParameterLevel?.nutrientClassification;

    if (updatedParameterLevel && nutrientClassificationLevel) {
      const availableLevels = analysisConfiguration?.nutrientClassifications || [];
      availableLevels.push({
        ...updatedParameterLevel,
        nutrientClassification: nutrientClassificationLevel,
      });

      // Assume how the order will be if the selection is indeed added
      const availableLevelsOrdered = getOrderedNutrientClassifications(availableLevels);
      const selectedLevelOrderedIndex = availableLevelsOrdered.findIndex(
        (el) => el.nutrientClassification === updatedParameterLevel.nutrientClassification,
      );
      const previousOrderedLevel = orderedNutrientClassifications[selectedLevelOrderedIndex - 1];

      if (previousOrderedLevel && previousOrderedLevel.lowerThan !== ParameterLevelRange.Max) {
        value = previousOrderedLevel.lowerThan.toString();
      }
    }

    setGreater(value);
    return value;
  }, [addParamState?.selectedAnalysisNutrientClassification]);

  const lowestLevelSelected = isLowestLevel(
    addParamState?.selectedAnalysisNutrientClassification?.nutrientClassification || null,
    orderedNutrientClassifications,
  );
  const highestLevelSelected = isHighestLevel(
    addParamState?.selectedAnalysisNutrientClassification?.nutrientClassification || null,
    orderedNutrientClassifications.filter((el) => el.id), // check if the selected is highest among the already existing
  );

  useEffect(() => {
    const lowerValue = highestLevelSelected
      ? String(ParameterLevelRange.Max)
      : String(ParameterLevelRange.Min);

    setLower(lowerValue);
  }, [lowestLevelSelected, highestLevelSelected]);

  return (
    <ModalDialog
      isOpen={isAddPopupOpened}
      onChange={setIsAddPopupOpened}
      title={t(`${prefix}.addAnotherLevel`, {
        actionName: ACTIONS.ADD,
      })}
      dataCy={`${isAddPopupOpened ? 'opened' : 'closed'}-add another level`}
      onSaveClick={addNewParameterHandler}
      actionType={ACTIONS.ADD}
    >
      <LevelColumnMain>
        <Select
          ariaLabel='Select parameter level'
          cover='outline'
          items={analysisNutrientClassificationParameterLevels || []}
          position='popper'
          size='s'
          label={t(`${prefix}.parameterLevel`)}
          onChange={updateAnalysisConfiguration}
          value={
            addParamState?.selectedAnalysisNutrientClassification &&
            addParamState.selectedAnalysisNutrientClassification.nutrientClassification
              ? addParamState.selectedAnalysisNutrientClassification.nutrientClassification
              : undefined
          }
          variant={addParamState?.error ? 'error' : 'default'}
          helper-text={addParamState?.error ? t(`${prefix}.selectError`) : ''}
          onFocus={null}
          onBlur={null}
        />
        <InputWrapper>
          <Input
            size='s'
            value={greaterOrEqualValue}
            label={t(`${prefix}.${GreaterOrEqual}`)}
            disabled
            data-cy='greater-or-equal-input'
          />
          <Input
            size='s'
            value={
              Number(lower) === ParameterLevelRange.Max ? PARAMETER_LEVEL_MAX_DISPLAY_VALUE : lower
            }
            label={t(`${prefix}.${LowerThan}`)}
            onFocus={({ target: { value } }) => {
              value === '0' && setLower('');
            }}
            onChange={({ target: { value } }) => {
              handleValueChanged(value);
            }}
            disabled={highestLevelSelected}
            data-cy='lower-than-input'
          />
        </InputWrapper>

        <Title size='xs' data-cy='set-as-default-caption'>
          {t(`${prefix}.setAsDefault`)}
        </Title>

        <LabelWithCheckbox
          labelText={t(`${prefix}.yesSetAsDefault`)}
          checked={addParamState?.isDefaultChecked}
          dataCy='set-as-default'
          checkboxClickHandler={() => {
            setAddParamState((paramState) => ({
              ...paramState,
              isDefaultChecked: !paramState.isDefaultChecked,
            }));
          }}
        />
      </LevelColumnMain>
    </ModalDialog>
  );
};
