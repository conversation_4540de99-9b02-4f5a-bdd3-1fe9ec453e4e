import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  cerealSoilAnalysisNutrientClassificationsHandler,
  updateCropRegionHandler,
  getAnalysisMethodsHandler,
  mockCMMMAppProviderValue,
  snackbarInitialStateMock,
  soilAnalysisNutrientClassificationsMock,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
} from '@common/mocks';
import { AddNutritionParamPopup, AddNutritionParamPopupProps } from '..';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';

import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';

const server = setupServer(
  updateCropRegionHandler,
  cerealSoilAnalysisNutrientClassificationsHandler,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
  getAnalysisMethodsHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

describe('widget: AddPrePostCrop', () => {
  const mockSetDisplaySnackbar = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const addNutritionParamPopup = {
    isAddPopupOpened: false,
    setIsAddPopupOpened: jest.fn(),
    analysisNutrientClassifications: soilAnalysisNutrientClassificationsMock.entities,
    analysisConfiguration: {},
    setAddParamState: jest.fn(),
    addParamState: {
      defaultAnalysisNutrientClassification: undefined,
      selectedAnalysisNutrientClassification: {
        soilAnalysisConfigurationId: 'testid1',
        isDefault: true,
        nutrientClassification: 'LOW',
        greaterOrEqual: 0,
        lowerThan: 0,
      },
      isDefaultChecked: false,
      parameters: [],
      error: false,
    },
  } as unknown as AddNutritionParamPopupProps;
  const prefixText = 'polaris.cmmmDetails.soilAnalysis.parameterLevelsTable.dialog';
  test('AddNutritionParamPopup: should not show modal when isAddPopupOpened is false', () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: false };
    render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <AddNutritionParamPopup {...props} />
      </AppContext.Provider>,
    );
    expect(screen.queryByText(prefixText)).not.toBeInTheDocument();
  });

  test('AddNutritionParamPopup: should not show modal when isAddPopupOpened is true', async () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <AddNutritionParamPopup {...props} />
      </AppContext.Provider>,
    );
    expect(screen.getByText(`${prefixText}.parameterLevel`)).toBeInTheDocument();
    expect(screen.getByText(`${prefixText}.addAnotherLevel`)).toBeInTheDocument();
    expect(screen.getByText(`${prefixText}.setAsDefault`)).toBeInTheDocument();
    expect(screen.getByText(`${prefixText}.yesSetAsDefault`)).toBeInTheDocument();
  });

  test('AddNutritionParamPopup: should be checked default parameter event', async () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SnackbarContext.Provider
          value={{
            displaySnackbar: snackbarInitialStateMock,
            setDisplaySnackbar: mockSetDisplaySnackbar,
          }}
        >
          <AddNutritionParamPopup {...props} />
        </SnackbarContext.Provider>
      </AppContext.Provider>,
    );
    const greaterOrEqualInput = screen.getByTestId('greater-or-equal-input');
    const lowerThanInput = screen.getByTestId('lower-than-input');
    const defaultCheckbox = screen.getByTestId('set-as-default');

    expect(greaterOrEqualInput).toBeInTheDocument();
    expect(lowerThanInput).toBeInTheDocument();
    expect(defaultCheckbox).toBeInTheDocument();
  });

  test('AddNutritionParamPopup: greaterOrEqual Input should be disabled', () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <AddNutritionParamPopup {...props} />
      </AppContext.Provider>,
    );
    const greaterOrEqualInput = component.getByTestId('greater-or-equal-input');
    expect(greaterOrEqualInput).toBeInTheDocument();
    expect(greaterOrEqualInput).toBeDisabled();
  });

  test('AddNutritionParamPopup: should change lower-than input', () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <AddNutritionParamPopup {...props} />
      </AppContext.Provider>,
    );
    const lowerThanInput = component.getByTestId('lower-than-input');
    expect(lowerThanInput).toBeInTheDocument();
    fireEvent.click(lowerThanInput, {
      target: { value: 0 },
    });
    fireEvent.change(lowerThanInput, { target: { value: '5' } });
    expect(lowerThanInput).toHaveValue('5');
  });

  test('should match snapshot when isAddPopupOpened is true', async () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SnackbarContext.Provider
          value={{
            displaySnackbar: snackbarInitialStateMock,
            setDisplaySnackbar: mockSetDisplaySnackbar,
          }}
        >
          <AddNutritionParamPopup {...props} />
        </SnackbarContext.Provider>
      </AppContext.Provider>,
    );
    expect(component).toMatchSnapshot();
  });
});
