import { Dispatch, SetStateAction } from 'react';
import {
  AnalysisType,
  AnalysisConfiguration,
  NutrientClassification,
  NutrientClassificationWithError,
  ParameterLevel,
  NutrientClassificationCreateUpdateResponse,
} from '@common/types';

export interface AddParamStateProps {
  defaultAnalysisNutrientClassification: NutrientClassification | undefined;
  selectedAnalysisNutrientClassification: NutrientClassificationWithError | null;
  isDefaultChecked: boolean;
  parameters: NutrientClassification[];
  error: boolean;
}

export interface AddNutritionParamPopupProps {
  isAddPopupOpened: boolean;
  setIsAddPopupOpened: Dispatch<SetStateAction<boolean>>;
  analysisConfiguration: AnalysisConfiguration;
  addParamState: AddParamStateProps;
  setAddParamState: Dispatch<SetStateAction<AddParamStateProps>>;
  analysisNutrientClassifications: NutrientClassification[] | undefined;
  triggerCreateUpdateNutrientClassification: (config: {
    method: string;
    body: string;
  }) => Promise<NutrientClassificationCreateUpdateResponse | string | undefined>;
  analysisType: AnalysisType;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleOpenParamValuesInfoDialog: (error: any) => void;
}

export interface Parameter {
  text: ParameterLevel;
  value: ParameterLevel;
}
