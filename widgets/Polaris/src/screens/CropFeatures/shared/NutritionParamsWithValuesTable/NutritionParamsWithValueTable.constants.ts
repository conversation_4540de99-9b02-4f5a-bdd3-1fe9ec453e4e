import { AddParamStateProps } from './AddNutritionParamPopup';

export const addNewParamState: AddParamStateProps = {
  defaultAnalysisNutrientClassification: undefined,
  selectedAnalysisNutrientClassification: null,
  isDefaultChecked: false,
  parameters: [],
  error: false,
};

export enum UpdateType {
  DEFAULT = 'DEFAULT',
  PASTE = 'PASTE',
  OTHER = 'OTHER',
}

export const UPDATE_LEVEL_ERROR_MESSAGE_FRAGMENTS = {
  NEXT_UPPER_LIMIT: 'next upper limit',
  PREVIOUS_UPPER_LIMIT: 'previous upper limit',
};
