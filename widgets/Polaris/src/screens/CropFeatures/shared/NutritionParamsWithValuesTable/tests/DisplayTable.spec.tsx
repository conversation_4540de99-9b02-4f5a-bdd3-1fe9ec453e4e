import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountriesHandler,
  updateCropRegionHandler,
  allUnitsHandler,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
  updateCerealSoilAnalysisNutrientClassificationMediumHandler,
  updateCerealSoilAnalysisNutrientClassificationHighHandler,
  updateFertigationWaterAnalysisNutrientClassificationLowHandler,
  updateFertigationSoilAnalysisNutrientClassificationHighHandler,
  soilAnalysisConfigurationsMock,
  soilAnalysisNutrientClassificationsMock,
  waterAnalysisConfigurationsMock,
} from '@common/mocks';
import DisplayTable from '../DisplayTable';
import { FeatureConfigOptions, AnalysisTypeOptions } from '@common/types';

const server = setupServer(
  updateCropRegionHandler,
  unitCountriesHandler,
  allUnitsHandler,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
  updateCerealSoilAnalysisNutrientClassificationMediumHandler,
  updateCerealSoilAnalysisNutrientClassificationHighHandler,
  updateFertigationWaterAnalysisNutrientClassificationLowHandler,
  updateFertigationSoilAnalysisNutrientClassificationHighHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

const mockHandleValueChanged = jest.fn();

describe('DisplayTable', () => {
  it('should render without crashing', async () => {
    const component = render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    expect(screen.getByText('header.default')).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should click checkbox', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    waitFor(() => {
      expect(screen.getByTestId('nutrient-1-checkbox')).toBeInTheDocument();
      const secondCheckbox = screen.getByTestId('nutrient-1-checkbox');
      fireEvent.click(secondCheckbox);
      expect(secondCheckbox).toBeChecked();
    });
  });

  it('should update greaterOrEqual input value', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={mockHandleValueChanged}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );

    const greaterOrEqualInputs = screen.getAllByTestId(
      'greater-or-equal-input',
    ) as HTMLInputElement[];

    waitFor(() => {
      fireEvent.focus(greaterOrEqualInputs[0]);
      fireEvent.change(greaterOrEqualInputs[0], { target: { value: 5 } });
      fireEvent.blur(greaterOrEqualInputs[0]);

      expect(mockHandleValueChanged).toHaveBeenCalledWith(
        'greaterOrEqual',
        '5',
        waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0],
      );
      expect(greaterOrEqualInputs[0].value).toBe(5);
    });
  });

  it('should update lowerThan input value', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={mockHandleValueChanged}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    const lowerThanInputs = screen.getAllByTestId('lower-than-input') as HTMLInputElement[];

    waitFor(() => {
      fireEvent.focus(lowerThanInputs[0]);
      fireEvent.change(lowerThanInputs[0], { target: { value: 3 } });
      fireEvent.blur(lowerThanInputs[0]);

      expect(mockHandleValueChanged).toHaveBeenCalledWith(
        'greaterOrEqual',
        '3',
        waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0],
      );
      expect(lowerThanInputs[0].value).toBe(3);
    });
  });

  it('should display snackbar on value update', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    waitFor(() => {
      const greaterOrEqualInput = screen.getByTestId('greater-or-equal-input');
      fireEvent.change(greaterOrEqualInput, { target: { value: 7 } });
      fireEvent.blur(greaterOrEqualInput);
      const snackbar = screen.getByText('common.changeSaved');
      expect(snackbar).toBeInTheDocument();
    });
  });

  it('should NOT display snackbar if the initial value is null', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    waitFor(() => {
      const greaterOrEqualInput = screen.getByTestId('greater-or-equal-input');
      fireEvent.change(greaterOrEqualInput, { target: { value: 7 } });
      fireEvent.blur(greaterOrEqualInput);
      const snackbar = screen.getByText('common.changeSaved');
      expect(snackbar).not.toBeInTheDocument();
    });
  });

  it('should display snackbar if the initial value is grater than 0', async () => {
    const analysisNutrientClassificationsMock = [
      {
        ...soilAnalysisNutrientClassificationsMock.entities[0],
        greaterOrEqual: 2,
      },
    ];
    render(
      <DisplayTable
        analysisNutrientClassifications={analysisNutrientClassificationsMock}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    waitFor(() => {
      const greaterOrEqualInput = screen.getByTestId('greater-or-equal-input');
      fireEvent.change(greaterOrEqualInput, { target: { value: 7 } });
      fireEvent.blur(greaterOrEqualInput);
      const snackbar = screen.getByText('common.changeSaved');
      expect(snackbar).toBeInTheDocument();
    });
  });

  it('should NOT display snackbar if the initial value is  0', async () => {
    const analysisNutrientClassificationsMock = [
      {
        ...soilAnalysisNutrientClassificationsMock.entities[0],
        greaterOrEqual: 0,
      },
    ];
    render(
      <DisplayTable
        analysisNutrientClassifications={analysisNutrientClassificationsMock}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    waitFor(() => {
      const greaterOrEqualInput = screen.getByTestId('greater-or-equal-input');
      fireEvent.change(greaterOrEqualInput, { target: { value: 7 } });
      fireEvent.blur(greaterOrEqualInput);
      const snackbar = screen.getByText('common.changeSaved');
      expect(snackbar).not.toBeInTheDocument();
    });
  });

  it("should have error state in first level's lowerThan and the second level's greaterOrEqual inputs", async () => {
    const analysisNutrientClassificationsMock = [
      {
        ...soilAnalysisNutrientClassificationsMock.entities[0],
        lowerThan: 2,
      },
      {
        ...soilAnalysisNutrientClassificationsMock.entities[1],
        greaterOrEqual: 3,
      },
    ];
    render(
      <DisplayTable
        analysisNutrientClassifications={analysisNutrientClassificationsMock}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    waitFor(() => {
      const firstLevelLowerThanInput = screen.getByTestId('lower-than-input');
      const secondLevelGreaterOrEqualInput = screen.getAllByTestId('greater-or-equal-input');

      expect(firstLevelLowerThanInput).toHaveValue(2);
      expect(secondLevelGreaterOrEqualInput).toHaveValue(3);

      expect(firstLevelLowerThanInput).toHaveAttribute('variant', 'error');
      expect(secondLevelGreaterOrEqualInput).toHaveAttribute('variant', 'error');
    });
  });

  it('should open and close delete dialog', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );

    const deleteIcon = screen.getByTestId('delete-nutrient-1');

    waitFor(() => {
      fireEvent.click(deleteIcon);

      const deleteDialog = screen.getByText('dialog.title');
      expect(deleteDialog).toBeInTheDocument();

      const cancelButton = screen.getByText('common.cancel');
      fireEvent.click(cancelButton);
      expect(deleteDialog).not.toBeInTheDocument();
    });
  });

  it('should open and close info dialog', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.CEREAL}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );

    const deleteIcon = screen.getByTestId('delete-nutrient-0');

    waitFor(() => {
      fireEvent.click(deleteIcon);

      const infoDialog = screen.getByText('dialog.info.title');
      expect(infoDialog).toBeInTheDocument();

      const okButton = screen.getByText('common.ok');
      expect(screen.getByText('common.cancel')).toBeInTheDocument();
      fireEvent.click(okButton);
      expect(infoDialog).not.toBeInTheDocument();
    });
  });

  it('should NOT render table with warningMessage cell when not water', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={soilAnalysisNutrientClassificationsMock.entities}
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.FERTIGATION}
        analysisType={AnalysisTypeOptions.SOIL}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    const warningMessageCell = screen.queryByTestId('warning-message-textarea-MEDIUM');

    expect(warningMessageCell).not.toBeInTheDocument();
  });

  it('should render table with warningMessage cell when water', async () => {
    render(
      <DisplayTable
        analysisNutrientClassifications={
          waterAnalysisConfigurationsMock.entities[0].nutrientClassifications
        }
        handleDefaultChange={jest.fn()}
        handleValueChanged={jest.fn()}
        handleValueUpdate={jest.fn()}
        handleOpenDeleteDialog={jest.fn()}
        handleIsAddPopupOpened={jest.fn()}
        handleValueFocused={jest.fn()}
        keyPrefix='translation.key'
        configType={FeatureConfigOptions.FERTIGATION}
        analysisType={AnalysisTypeOptions.WATER}
        configuration={soilAnalysisConfigurationsMock.entities[0]}
        updateAnalysisConfigurations={jest.fn()}
        hasActionFailed={false}
      />,
    );
    const warningMessageCell = screen.getByTestId('warning-message-textarea-MEDIUM');

    expect(warningMessageCell).toBeInTheDocument();
  });
});
