// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DisplayTable should render without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <table
        class="c-kwAGqj c-lgRixu"
        data-cy="parameter-levels-table-values"
      >
        <div
          aria-required="false"
          class="c-fixGjY"
          data-cy="nutrient-classification-radio-button-group"
          dir="ltr"
          role="radiogroup"
          style="outline: none;"
          tabindex="0"
        >
          <table
            class="c-kwAGqj table"
          >
            <thead>
              <tr
                class="c-eDGYZe header-row"
              >
                <th
                  class="c-kxWgPf"
                >
                  header.default
                </th>
                <th
                  class="c-kxWgPf parameter-level"
                >
                  header.parameterLevel
                </th>
                <th
                  class="c-kxWgPf"
                >
                  header.greaterThan
                </th>
                <th
                  class="c-kxWgPf"
                >
                  header.lessThan
                </th>
                <th
                  class="c-kxWgPf table-actions"
                >
                  header.action
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                class="c-eDGYZe table-body-row"
              >
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                  >
                    <button
                      aria-checked="false"
                      aria-label="nutrientClassificationRadioButtonLabel"
                      class="c-thNAo c-thNAo-glsNLl-concept-success"
                      data-cy="nutrient-0-radio"
                      data-radix-collection-item=""
                      data-state="unchecked"
                      id="182220c5-f922-4448-8a48-af3a68165bea"
                      role="radio"
                      tabindex="0"
                      type="button"
                      value="[object Object]"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-UazGY"
                    data-cy="nutrient-classification-cell"
                  >
                    <span
                      class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                      data-cy="nutrient-classification-colored-circle"
                    />
                    <div
                      data-cy="nutrient-classification-select"
                      style="display: inline-block; width: 100%;"
                    >
                      <div
                        class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                      >
                        <div
                          class="c-kFLrJl"
                        >
                          <label
                            class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                          >
                            polaris.common.parameterLevel.MEDIUM
                          </label>
                          <button
                            aria-autocomplete="none"
                            aria-controls="radix-:r1:"
                            aria-expanded="false"
                            aria-label="Select nutrient classification-MEDIUM"
                            class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                            data-state="closed"
                            dir="ltr"
                            role="combobox"
                            tabindex="0"
                            type="button"
                          >
                            <span
                              style="pointer-events: none;"
                            >
                              <div
                                class="c-fSebPZ"
                              />
                            </span>
                            <svg
                              aria-hidden="true"
                              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M18 9.75l-5 6h-2l-5-6"
                              />
                            </svg>
                          </button>
                        </div>
                        <p
                          class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                        />
                      </div>
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="greater-or-equal-input"
                      disabled=""
                      tabindex="0"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="lower-than-input"
                      tabindex="0"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-cIAltW"
                >
                  <button
                    class="c-bvmDGo"
                    data-cy="delete-nutrient-0"
                    icon="Delete"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                      />
                    </svg>
                  </button>
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-body-row"
              >
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                  >
                    <button
                      aria-checked="true"
                      aria-label="nutrientClassificationRadioButtonLabel"
                      class="c-thNAo c-thNAo-glsNLl-concept-success"
                      data-cy="nutrient-1-radio"
                      data-radix-collection-item=""
                      data-state="checked"
                      id="43eb7644-ac4e-4c15-90c0-1bb5119e7d91"
                      role="radio"
                      tabindex="1"
                      type="button"
                      value="[object Object]"
                    >
                      <span
                        class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                        data-state="checked"
                      />
                    </button>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-UazGY"
                    data-cy="nutrient-classification-cell"
                  >
                    <span
                      class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                      data-cy="nutrient-classification-colored-circle"
                    />
                    <div
                      data-cy="nutrient-classification-select"
                      style="display: inline-block; width: 100%;"
                    >
                      <div
                        class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                      >
                        <div
                          class="c-kFLrJl"
                        >
                          <label
                            class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                          >
                            polaris.common.parameterLevel.LOW
                          </label>
                          <button
                            aria-autocomplete="none"
                            aria-controls="radix-:r3:"
                            aria-expanded="false"
                            aria-label="Select nutrient classification-LOW"
                            class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                            data-state="closed"
                            dir="ltr"
                            role="combobox"
                            tabindex="0"
                            type="button"
                          >
                            <span
                              style="pointer-events: none;"
                            >
                              <div
                                class="c-fSebPZ"
                              />
                            </span>
                            <svg
                              aria-hidden="true"
                              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M18 9.75l-5 6h-2l-5-6"
                              />
                            </svg>
                          </button>
                        </div>
                        <p
                          class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                        />
                      </div>
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="greater-or-equal-input"
                      disabled=""
                      tabindex="1"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="lower-than-input"
                      tabindex="1"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-cIAltW"
                >
                  <button
                    class="c-bvmDGo"
                    data-cy="delete-nutrient-1"
                    icon="Delete"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                      />
                    </svg>
                  </button>
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-body-row"
              >
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                  >
                    <button
                      aria-checked="false"
                      aria-label="nutrientClassificationRadioButtonLabel"
                      class="c-thNAo c-thNAo-glsNLl-concept-success"
                      data-cy="nutrient-2-radio"
                      data-radix-collection-item=""
                      data-state="unchecked"
                      id="f6db9367-c16f-444d-8aff-8c21d82e6a2a"
                      role="radio"
                      tabindex="2"
                      type="button"
                      value="[object Object]"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-UazGY"
                    data-cy="nutrient-classification-cell"
                  >
                    <span
                      class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                      data-cy="nutrient-classification-colored-circle"
                    />
                    <div
                      data-cy="nutrient-classification-select"
                      style="display: inline-block; width: 100%;"
                    >
                      <div
                        class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                      >
                        <div
                          class="c-kFLrJl"
                        >
                          <label
                            class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                          >
                            polaris.common.parameterLevel.VERY_LOW
                          </label>
                          <button
                            aria-autocomplete="none"
                            aria-controls="radix-:r5:"
                            aria-expanded="false"
                            aria-label="Select nutrient classification-VERY_LOW"
                            class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                            data-state="closed"
                            dir="ltr"
                            role="combobox"
                            tabindex="0"
                            type="button"
                          >
                            <span
                              style="pointer-events: none;"
                            >
                              <div
                                class="c-fSebPZ"
                              />
                            </span>
                            <svg
                              aria-hidden="true"
                              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M18 9.75l-5 6h-2l-5-6"
                              />
                            </svg>
                          </button>
                        </div>
                        <p
                          class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                        />
                      </div>
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="greater-or-equal-input"
                      disabled=""
                      tabindex="2"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="lower-than-input"
                      tabindex="2"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-cIAltW"
                >
                  <button
                    class="c-bvmDGo"
                    data-cy="delete-nutrient-2"
                    icon="Delete"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                      />
                    </svg>
                  </button>
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-body-row"
              >
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                  >
                    <button
                      aria-checked="false"
                      aria-label="nutrientClassificationRadioButtonLabel"
                      class="c-thNAo c-thNAo-glsNLl-concept-success"
                      data-cy="nutrient-3-radio"
                      data-radix-collection-item=""
                      data-state="unchecked"
                      id="d99aecc0-fbf6-400a-8eab-91cb20bff706"
                      role="radio"
                      tabindex="3"
                      type="button"
                      value="[object Object]"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-UazGY"
                    data-cy="nutrient-classification-cell"
                  >
                    <span
                      class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                      data-cy="nutrient-classification-colored-circle"
                    />
                    <div
                      data-cy="nutrient-classification-select"
                      style="display: inline-block; width: 100%;"
                    >
                      <div
                        class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                      >
                        <div
                          class="c-kFLrJl"
                        >
                          <label
                            class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                          >
                            polaris.common.parameterLevel.HIGH
                          </label>
                          <button
                            aria-autocomplete="none"
                            aria-controls="radix-:r7:"
                            aria-expanded="false"
                            aria-label="Select nutrient classification-HIGH"
                            class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                            data-state="closed"
                            dir="ltr"
                            role="combobox"
                            tabindex="0"
                            type="button"
                          >
                            <span
                              style="pointer-events: none;"
                            >
                              <div
                                class="c-fSebPZ"
                              />
                            </span>
                            <svg
                              aria-hidden="true"
                              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M18 9.75l-5 6h-2l-5-6"
                              />
                            </svg>
                          </button>
                        </div>
                        <p
                          class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                        />
                      </div>
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="greater-or-equal-input"
                      disabled=""
                      tabindex="3"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                      data-cy="lower-than-input"
                      disabled=""
                      tabindex="3"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-cIAltW"
                >
                  <button
                    class="c-bvmDGo"
                    data-cy="delete-nutrient-3"
                    icon="Delete"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                      />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <button
          class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost"
          data-cy="add-level-button"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.072 6v12M18 12H6"
            />
          </svg>
          <span
            class="c-iepcqn"
          >
            addLevelTableButton
          </span>
        </button>
      </table>
    </div>
  </body>,
  "container": <div>
    <table
      class="c-kwAGqj c-lgRixu"
      data-cy="parameter-levels-table-values"
    >
      <div
        aria-required="false"
        class="c-fixGjY"
        data-cy="nutrient-classification-radio-button-group"
        dir="ltr"
        role="radiogroup"
        style="outline: none;"
        tabindex="0"
      >
        <table
          class="c-kwAGqj table"
        >
          <thead>
            <tr
              class="c-eDGYZe header-row"
            >
              <th
                class="c-kxWgPf"
              >
                header.default
              </th>
              <th
                class="c-kxWgPf parameter-level"
              >
                header.parameterLevel
              </th>
              <th
                class="c-kxWgPf"
              >
                header.greaterThan
              </th>
              <th
                class="c-kxWgPf"
              >
                header.lessThan
              </th>
              <th
                class="c-kxWgPf table-actions"
              >
                header.action
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                >
                  <button
                    aria-checked="false"
                    aria-label="nutrientClassificationRadioButtonLabel"
                    class="c-thNAo c-thNAo-glsNLl-concept-success"
                    data-cy="nutrient-0-radio"
                    data-radix-collection-item=""
                    data-state="unchecked"
                    id="182220c5-f922-4448-8a48-af3a68165bea"
                    role="radio"
                    tabindex="0"
                    type="button"
                    value="[object Object]"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="nutrient-classification-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                    data-cy="nutrient-classification-colored-circle"
                  />
                  <div
                    data-cy="nutrient-classification-select"
                    style="display: inline-block; width: 100%;"
                  >
                    <div
                      class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                    >
                      <div
                        class="c-kFLrJl"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                        >
                          polaris.common.parameterLevel.MEDIUM
                        </label>
                        <button
                          aria-autocomplete="none"
                          aria-controls="radix-:r1:"
                          aria-expanded="false"
                          aria-label="Select nutrient classification-MEDIUM"
                          class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                          data-state="closed"
                          dir="ltr"
                          role="combobox"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            style="pointer-events: none;"
                          >
                            <div
                              class="c-fSebPZ"
                            />
                          </span>
                          <svg
                            aria-hidden="true"
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M18 9.75l-5 6h-2l-5-6"
                            />
                          </svg>
                        </button>
                      </div>
                      <p
                        class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                      />
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="greater-or-equal-input"
                    disabled=""
                    tabindex="0"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="lower-than-input"
                    tabindex="0"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-cIAltW"
              >
                <button
                  class="c-bvmDGo"
                  data-cy="delete-nutrient-0"
                  icon="Delete"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                    />
                  </svg>
                </button>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                >
                  <button
                    aria-checked="true"
                    aria-label="nutrientClassificationRadioButtonLabel"
                    class="c-thNAo c-thNAo-glsNLl-concept-success"
                    data-cy="nutrient-1-radio"
                    data-radix-collection-item=""
                    data-state="checked"
                    id="43eb7644-ac4e-4c15-90c0-1bb5119e7d91"
                    role="radio"
                    tabindex="1"
                    type="button"
                    value="[object Object]"
                  >
                    <span
                      class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                      data-state="checked"
                    />
                  </button>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="nutrient-classification-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                    data-cy="nutrient-classification-colored-circle"
                  />
                  <div
                    data-cy="nutrient-classification-select"
                    style="display: inline-block; width: 100%;"
                  >
                    <div
                      class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                    >
                      <div
                        class="c-kFLrJl"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                        >
                          polaris.common.parameterLevel.LOW
                        </label>
                        <button
                          aria-autocomplete="none"
                          aria-controls="radix-:r3:"
                          aria-expanded="false"
                          aria-label="Select nutrient classification-LOW"
                          class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                          data-state="closed"
                          dir="ltr"
                          role="combobox"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            style="pointer-events: none;"
                          >
                            <div
                              class="c-fSebPZ"
                            />
                          </span>
                          <svg
                            aria-hidden="true"
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M18 9.75l-5 6h-2l-5-6"
                            />
                          </svg>
                        </button>
                      </div>
                      <p
                        class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                      />
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="greater-or-equal-input"
                    disabled=""
                    tabindex="1"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="lower-than-input"
                    tabindex="1"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-cIAltW"
              >
                <button
                  class="c-bvmDGo"
                  data-cy="delete-nutrient-1"
                  icon="Delete"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                    />
                  </svg>
                </button>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                >
                  <button
                    aria-checked="false"
                    aria-label="nutrientClassificationRadioButtonLabel"
                    class="c-thNAo c-thNAo-glsNLl-concept-success"
                    data-cy="nutrient-2-radio"
                    data-radix-collection-item=""
                    data-state="unchecked"
                    id="f6db9367-c16f-444d-8aff-8c21d82e6a2a"
                    role="radio"
                    tabindex="2"
                    type="button"
                    value="[object Object]"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="nutrient-classification-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                    data-cy="nutrient-classification-colored-circle"
                  />
                  <div
                    data-cy="nutrient-classification-select"
                    style="display: inline-block; width: 100%;"
                  >
                    <div
                      class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                    >
                      <div
                        class="c-kFLrJl"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                        >
                          polaris.common.parameterLevel.VERY_LOW
                        </label>
                        <button
                          aria-autocomplete="none"
                          aria-controls="radix-:r5:"
                          aria-expanded="false"
                          aria-label="Select nutrient classification-VERY_LOW"
                          class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                          data-state="closed"
                          dir="ltr"
                          role="combobox"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            style="pointer-events: none;"
                          >
                            <div
                              class="c-fSebPZ"
                            />
                          </span>
                          <svg
                            aria-hidden="true"
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M18 9.75l-5 6h-2l-5-6"
                            />
                          </svg>
                        </button>
                      </div>
                      <p
                        class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                      />
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="greater-or-equal-input"
                    disabled=""
                    tabindex="2"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="lower-than-input"
                    tabindex="2"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-cIAltW"
              >
                <button
                  class="c-bvmDGo"
                  data-cy="delete-nutrient-2"
                  icon="Delete"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                    />
                  </svg>
                </button>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-hcxFDL c-hcxFDL-ivZjnK-concept-success"
                >
                  <button
                    aria-checked="false"
                    aria-label="nutrientClassificationRadioButtonLabel"
                    class="c-thNAo c-thNAo-glsNLl-concept-success"
                    data-cy="nutrient-3-radio"
                    data-radix-collection-item=""
                    data-state="unchecked"
                    id="d99aecc0-fbf6-400a-8eab-91cb20bff706"
                    role="radio"
                    tabindex="3"
                    type="button"
                    value="[object Object]"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="nutrient-classification-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                    data-cy="nutrient-classification-colored-circle"
                  />
                  <div
                    data-cy="nutrient-classification-select"
                    style="display: inline-block; width: 100%;"
                  >
                    <div
                      class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
                    >
                      <div
                        class="c-kFLrJl"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
                        >
                          polaris.common.parameterLevel.HIGH
                        </label>
                        <button
                          aria-autocomplete="none"
                          aria-controls="radix-:r7:"
                          aria-expanded="false"
                          aria-label="Select nutrient classification-HIGH"
                          class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                          data-state="closed"
                          dir="ltr"
                          role="combobox"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            style="pointer-events: none;"
                          >
                            <div
                              class="c-fSebPZ"
                            />
                          </span>
                          <svg
                            aria-hidden="true"
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M18 9.75l-5 6h-2l-5-6"
                            />
                          </svg>
                        </button>
                      </div>
                      <p
                        class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                      />
                    </div>
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="greater-or-equal-input"
                    disabled=""
                    tabindex="3"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv"
                    data-cy="lower-than-input"
                    disabled=""
                    tabindex="3"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-cIAltW"
              >
                <button
                  class="c-bvmDGo"
                  data-cy="delete-nutrient-3"
                  icon="Delete"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.5 6.5V21h11V6.5m-11 0h-2m2 0h11m0 0h2M9.5 3h5M10 11.2v5.6m4-5.6v5.6"
                    />
                  </svg>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <button
        class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost"
        data-cy="add-level-button"
        type="button"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.072 6v12M18 12H6"
          />
        </svg>
        <span
          class="c-iepcqn"
        >
          addLevelTableButton
        </span>
      </button>
    </table>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
