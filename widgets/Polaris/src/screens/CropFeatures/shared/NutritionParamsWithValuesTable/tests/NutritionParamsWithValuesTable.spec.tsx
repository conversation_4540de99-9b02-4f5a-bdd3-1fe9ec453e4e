import React from 'react';
import { render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';

import { FeatureConfigOptions, AnalysisTypeOptions } from '@common/types';
import {
  unitCountriesHandler,
  updateCropRegionHandler,
  cerealSoilAnalysisNutrientClassificationsHandler,
  allUnitsHandler,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
  cerealSoilAnalysisConfigurationHandler,
  deleteCerealSoilAnalysisNutrientClassificationHandler,
  soilAnalysisConfigurationsMock,
  mockCMMMAppProviderValue,
} from '@common/mocks';
import { NutritionParamsWithValuesTable } from '../NutritionParamsWithValuesTable';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';

const server = setupServer(
  updateCropRegionHandler,
  unitCountriesHandler,
  allUnitsHandler,
  cerealSoilAnalysisNutrientClassificationsHandler,
  updateCerealSoilAnalysisNutrientClassification<PERSON>ow<PERSON>andler,
  cerealSoilAnalysisConfiguration<PERSON>and<PERSON>,
  deleteCerealSoilAnalysisNutrientClassificationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('NutritionParamsWithValuesTable', () => {
  it('should render without crashing', async () => {
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <NutritionParamsWithValuesTable
          configuration={soilAnalysisConfigurationsMock.entities[0]}
          configType={FeatureConfigOptions.CEREAL}
          analysisType={AnalysisTypeOptions.SOIL}
          keyPrefix='translation.key'
        />
      </AppContext.Provider>,
    );
    expect(screen.getByText('header.default')).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should match snapshot', () => {
    const { asFragment } = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <NutritionParamsWithValuesTable
          configuration={soilAnalysisConfigurationsMock.entities[0]}
          configType={FeatureConfigOptions.CEREAL}
          analysisType={AnalysisTypeOptions.SOIL}
          keyPrefix='translation.key'
        />
      </AppContext.Provider>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
