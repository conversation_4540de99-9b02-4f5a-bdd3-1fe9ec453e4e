import React, { useMemo, useState } from 'react';
import {
  StyledLabel,
  StyledTextArea,
  StyledWarningMessageTableCell,
} from './WaterAnalysisWarningMessage.styled';
import './styles.scss';
import { NutrientClassification } from '@common/types';
import { useTranslation } from 'react-i18next';
import { PropertyKeyEnum } from '../NutritionParamsWithValuesTable.type';
import { WARNING_MESSAGE_MAX_LENGTH } from '../../../Fertigation/WaterAnalysis/constants';

export const WaterAnalysisWarningMessageTableCell = ({
  classification,
  selectedValue,
  setSelectedValue,
  handleValueFocused,
  handleValueUpdate,
}: {
  classification: NutrientClassification;
  selectedValue: string | undefined;
  setSelectedValue: React.Dispatch<React.SetStateAction<string | undefined>>;
  handleValueFocused: (
    param: keyof NutrientClassification,
    nutrientClassification: NutrientClassification,
  ) => void;
  handleValueUpdate: (
    param: string,
    value: string,
    selectedValue: string | undefined,
    targetField: string | null,
  ) => void;
}) => {
  const { WarningMessage } = PropertyKeyEnum;
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.waterAnalysis',
  });

  const [incorrectRuleFields, setIncorrectRuleFields] = useState<
    {
      classificationId: string | undefined;
      message: string;
    }[]
  >([]);

  const incorrectWarningMessageField = useMemo(() => {
    return incorrectRuleFields.find((field) => field.classificationId === classification.id);
  }, [incorrectRuleFields, classification]);

  const textAreaChangeHandler = (args: {
    classificationId: string | undefined;
    event: React.ChangeEvent<HTMLDivElement>;
  }): void => {
    const { event, classificationId } = args;
    const value = event.target.textContent;
    const incorrectField = incorrectRuleFields.find(
      (field) => field.classificationId === classificationId,
    );

    try {
      // Validate field
      if (value && value.length > WARNING_MESSAGE_MAX_LENGTH) {
        throw new Error(t('errors.incorrectMessageLength'));
      }

      if (incorrectField) {
        const filteredIncorrectFields = incorrectRuleFields.filter(
          (field) => field.classificationId !== classificationId,
        );

        setIncorrectRuleFields(filteredIncorrectFields);
      }
    } catch (err) {
      if (err && typeof err === 'object' && 'message' in err && typeof err.message === 'string') {
        if (!incorrectField) {
          setIncorrectRuleFields([
            ...incorrectRuleFields,
            {
              classificationId,
              message: err.message,
            },
          ]);
        }
      }
    }
  };

  return (
    <StyledWarningMessageTableCell>
      <StyledTextArea
        className={incorrectWarningMessageField ? 'text-area-error-state-styles' : ''}
        suppressContentEditableWarning={true}
        contentEditable='true'
        onInput={(event: React.ChangeEvent<HTMLDivElement>) =>
          textAreaChangeHandler({
            classificationId: classification.id,
            event,
          })
        }
        onFocus={() => {
          setSelectedValue(classification?.warningMessage || '');
          handleValueFocused(WarningMessage, classification);
        }}
        onBlur={(e: React.FocusEvent<HTMLDivElement>) => {
          !incorrectWarningMessageField &&
            handleValueUpdate(WarningMessage, e.target.textContent || '', selectedValue, null);
        }}
        data-cy={`warning-message-textarea-${classification.nutrientClassification}`}
      >
        {classification.warningMessage}
      </StyledTextArea>
      {incorrectWarningMessageField ? (
        <StyledLabel data-cy='warning-message-error-label'>
          {incorrectWarningMessageField.message}
        </StyledLabel>
      ) : null}
    </StyledWarningMessageTableCell>
  );
};
