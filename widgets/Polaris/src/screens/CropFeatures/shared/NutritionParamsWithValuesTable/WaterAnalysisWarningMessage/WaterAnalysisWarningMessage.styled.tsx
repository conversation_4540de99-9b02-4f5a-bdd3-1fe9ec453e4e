import { Label, styled, Table } from '@yaradigitallabs/ahua-react';

export const StyledWarningMessageTableCell = styled(Table.Cell, {
  width: '30%',
});

export const StyledTextArea = styled('div', {
  minHeight: '22.4px',
  padding: '$x2 $x3',
  borderRadius: '$m',
  lineHeight: '$scale5',
  border: '1px solid $black40',
  verticalAlign: 'middle',
  fontWeight: '$semiBold',
  fontFamily: '$default',
  fontSize: '$scale4',
  outlineColor: '$brand-base',
  margin: '$x2 0px',
});

export const StyledLabel = styled(Label, {
  fontSize: '$scale2 !important',
  fontWeight: '$regular',
  color: '$red60',
  paddingLeft: '$x3',
});
