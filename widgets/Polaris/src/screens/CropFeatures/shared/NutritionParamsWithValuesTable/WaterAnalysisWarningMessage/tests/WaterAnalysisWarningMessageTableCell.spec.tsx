import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountriesHandler,
  updateCropRegionHandler,
  allUnitsHandler,
  waterAnalysisConfigurationsMock,
} from '@common/mocks';
import { WARNING_MESSAGE_MAX_LENGTH } from '../../../../Fertigation/WaterAnalysis/constants';
import { WaterAnalysisWarningMessageTableCell } from '../WaterAnalysisWarningMessageTableCell';

const server = setupServer(updateCropRegionHandler, unitCountriesHandler, allUnitsHandler);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

const mockHandleValueFocused = jest.fn();
const mockHandleValueUpdate = jest.fn();
const mockSetSelectedValue = jest.fn();

describe('WaterAnalysisWarningMessageTableCell', () => {
  it('should update warningMessage successfully when cell loses focus', async () => {
    render(
      <WaterAnalysisWarningMessageTableCell
        classification={waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0]}
        selectedValue=''
        setSelectedValue={mockSetSelectedValue}
        handleValueFocused={mockHandleValueFocused}
        handleValueUpdate={mockHandleValueUpdate}
      />,
    );
    const warningMessageDataCy = 'warning-message-textarea-MEDIUM';
    const warningMessageCell = screen.getByTestId(warningMessageDataCy);
    const userInputMock = 'A message';

    await waitFor(() => {
      fireEvent.input(warningMessageCell, {
        target: { textContent: userInputMock },
      });

      const warningMessageTextAreaAfterChange = screen.getByTestId(
        warningMessageDataCy,
      ) as HTMLTextAreaElement;

      expect(warningMessageTextAreaAfterChange).toBeInTheDocument();
      expect(warningMessageTextAreaAfterChange.textContent).toBe(userInputMock);
    });
  });

  it('should HAVE error state when warningMessage exceeds threshold', async () => {
    render(
      <WaterAnalysisWarningMessageTableCell
        classification={waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0]}
        selectedValue=''
        setSelectedValue={mockSetSelectedValue}
        handleValueFocused={mockHandleValueFocused}
        handleValueUpdate={mockHandleValueUpdate}
      />,
    );
    const warningMessageDataCy = 'warning-message-textarea-MEDIUM';
    const warningMessageCell = screen.getByTestId(warningMessageDataCy);
    const userInvalidInputMock = 'A'.repeat(WARNING_MESSAGE_MAX_LENGTH + 1);

    expect(warningMessageCell).not.toHaveClass('text-area-error-state-styles');
    await waitFor(() => {
      fireEvent.input(warningMessageCell, {
        target: { textContent: userInvalidInputMock },
      });

      const warningMessageTextAreaAfterChange = screen.getByTestId(
        warningMessageDataCy,
      ) as HTMLTextAreaElement;

      expect(warningMessageTextAreaAfterChange).toBeInTheDocument();
      expect(warningMessageTextAreaAfterChange.textContent).toBe(userInvalidInputMock);
      expect(warningMessageTextAreaAfterChange).toHaveClass('text-area-error-state-styles');
    });
  });

  it('should resolve error state on valid warningMessage', async () => {
    render(
      <WaterAnalysisWarningMessageTableCell
        classification={waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0]}
        selectedValue=''
        setSelectedValue={mockSetSelectedValue}
        handleValueFocused={mockHandleValueFocused}
        handleValueUpdate={mockHandleValueUpdate}
      />,
    );
    const warningMessageDataCy = 'warning-message-textarea-MEDIUM';
    const warningMessageCell = screen.getByTestId(warningMessageDataCy);
    const userInvalidInputMock = 'A'.repeat(WARNING_MESSAGE_MAX_LENGTH + 1);
    const userValidInputMock = 'Testing';

    await waitFor(() => {
      // Supply invalid input to trigger error state
      fireEvent.input(warningMessageCell, {
        target: { textContent: userInvalidInputMock },
      });

      const warningMessageTextAreaAfterChange = screen.getByTestId(
        warningMessageDataCy,
      ) as HTMLTextAreaElement;

      expect(warningMessageTextAreaAfterChange).toHaveClass('text-area-error-state-styles');

      // Supply valid input to clear error state
      fireEvent.input(warningMessageCell, {
        target: { textContent: userValidInputMock },
      });

      const warningMessageTextAreaAfterValidValue = screen.getByTestId(
        warningMessageDataCy,
      ) as HTMLTextAreaElement;
      expect(warningMessageTextAreaAfterValidValue.textContent).toBe(userValidInputMock);
      expect(warningMessageTextAreaAfterValidValue).not.toHaveClass('text-area-error-state-styles');
    });
  });

  it('should set selected value on warningMessage focus', async () => {
    render(
      <WaterAnalysisWarningMessageTableCell
        classification={waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0]}
        selectedValue=''
        setSelectedValue={mockSetSelectedValue}
        handleValueFocused={mockHandleValueFocused}
        handleValueUpdate={mockHandleValueUpdate}
      />,
    );
    const warningMessageCell = screen.getByTestId('warning-message-textarea-MEDIUM');

    await waitFor(() => {
      fireEvent.focus(warningMessageCell);

      expect(mockSetSelectedValue).toHaveBeenCalledTimes(1);
      expect(mockHandleValueFocused).toHaveBeenCalledTimes(1);
      expect(warningMessageCell.textContent).toEqual(
        waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0].warningMessage,
      );
    });
  });

  it('should trigger update on valid warningMessage blur', async () => {
    render(
      <WaterAnalysisWarningMessageTableCell
        classification={waterAnalysisConfigurationsMock.entities[0].nutrientClassifications[0]}
        selectedValue=''
        setSelectedValue={mockSetSelectedValue}
        handleValueFocused={mockHandleValueFocused}
        handleValueUpdate={mockHandleValueUpdate}
      />,
    );
    const warningMessageCell = screen.getByTestId('warning-message-textarea-MEDIUM');

    await waitFor(() => {
      fireEvent.blur(warningMessageCell);

      expect(mockHandleValueUpdate).toHaveBeenCalledTimes(1);
    });
  });
});
