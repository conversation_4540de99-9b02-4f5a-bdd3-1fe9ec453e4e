/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useEffect, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  addNewParamState,
  UPDATE_LEVEL_ERROR_MESSAGE_FRAGMENTS,
  UpdateType,
} from './NutritionParamsWithValueTable.constants';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  useNutrientClassifications,
  usePermDeleteNutrientClassification,
} from '@polaris-hooks/index';

import {
  capitalizeFirstLetter,
  createNumberDecimalChangeHandler,
  displaySnackbarMessage,
  formatValidTreeDecimalsNumber,
  validateParameterNumber,
} from '@widgets/Polaris/utils';
import DisplayTable from './DisplayTable';
import { AddNutritionParamPopup } from './AddNutritionParamPopup';
import {
  NutritionParametersTableProps,
  PropertyKeyEnum,
  UpdateAnalysisActions,
} from './NutritionParamsWithValuesTable.type';
import { ConfirmationDialog } from '@widgets/Polaris/src/components';
import { METHOD, ParameterLevelEnum } from '@common/constants';
import { NutrientClassification, NutrientClassificationCreateUpdateResponse } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { getOrderedNutrientClassifications } from '../helpers';
import { analysisInitialState, analysisReducer } from './utils/analysisReducer';

export const NutritionParamsWithValuesTable = ({
  configuration,
  configType,
  analysisType,
  keyPrefix,
}: NutritionParametersTableProps) => {
  const { t, i18n } = useTranslation('polaris', {
    keyPrefix: keyPrefix,
  });
  const commonT = (key: string) => i18n.t(`polaris.common.${key}`);
  const { WarningMessage } = PropertyKeyEnum;
  const [state, dispatch] = useReducer(analysisReducer, analysisInitialState);
  const {
    methods: { updateAnalysisConfigurations },
  } = useAppContext();

  const { setDisplaySnackbar } = useSnackbar();
  const [currentDeleteItem, setCurrentDeleteItem] = useState<NutrientClassification | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteDialogTitle, setDeleteDialogTitle] = useState('');
  const [openInfoDialog, setOpenInfoDialog] = useState(false);
  const [isAddPopupOpened, setIsAddPopupOpened] = useState<boolean>(false);
  const [openParamValuesInfoDialog, setOpenParamValuesInfoDialog] = useState(false);
  const [paramValuesInfoDialogDescription, setParamValuesInfoDialogDescription] = useState<
    string | null
  >(null);
  const [addParamState, setAddParamState] = useState(addNewParamState);
  const [shouldShowSnackbar, setShouldShowSnackbar] = useState<boolean>(false);
  const [orderedAnalysisNutrientClassifications, setOrderedAnalysisNutrientClassifications] =
    useState<NutrientClassification[]>();

  const { trigger: triggerCreateUpdateNutrientClassification } =
    useNutrientClassifications<NutrientClassificationCreateUpdateResponse>(
      configType,
      analysisType,
    );

  const { triggerDeleteNutrientClassification, isMutating: isNutrientClassificationDeleting } =
    usePermDeleteNutrientClassification(configType, analysisType, currentDeleteItem?.id);

  useEffect(() => {
    if (configuration && configuration?.nutrientClassifications?.length) {
      const orderedData = getOrderedNutrientClassifications(configuration.nutrientClassifications);
      setOrderedAnalysisNutrientClassifications(orderedData);
    }
  }, [configuration]);

  const handleDeleteItem = async () => {
    const response = await triggerDeleteNutrientClassification({
      method: METHOD.DELETE,
    });

    if (response?.success) {
      setDisplaySnackbar({
        title: t('dialog.deleteMessageLevel'),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
      const updatedAnalysisConfiguration = {
        ...configuration,
        nutrientClassifications: response.impactedParameterLevels,
      };
      updateAnalysisConfigurations(updatedAnalysisConfiguration);
    }
  };

  const confirmDelete = async () => {
    await handleDeleteItem();
    setOpenDeleteDialog(false);
  };

  const closeInfo = () => {
    setOpenInfoDialog(false);
  };

  const closeParamValuesInfoDialog = () => {
    setOpenParamValuesInfoDialog(false);
    setParamValuesInfoDialogDescription(null);
  };

  const handleOpenDeleteDialog = (analysisNutrient: NutrientClassification) => {
    if (analysisNutrient.isDefault || orderedAnalysisNutrientClassifications?.length === 1) {
      return setOpenInfoDialog(true);
    }

    setCurrentDeleteItem(analysisNutrient);
    const levelName = commonT(`parameterLevel.${analysisNutrient.nutrientClassification}`);

    setDeleteDialogTitle(
      t('dialog.title', {
        level: levelName,
      }),
    );
    setOpenDeleteDialog(true);
  };

  const handleDefaultChange = (analysesNutrient: NutrientClassification): void => {
    dispatch({
      type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION,
      payload: analysesNutrient,
    });
    dispatch({
      type: UpdateAnalysisActions.SET_UPDATE_TYPE,
      payload: UpdateType.DEFAULT,
    });
  };

  const handleDefaultUpdate = useCallback(async () => {
    if (!state.selectedNutrientClassification) return;

    const hasMoreThanFive =
      orderedAnalysisNutrientClassifications && orderedAnalysisNutrientClassifications?.length > 5;
    const existingDefaultParamNutrient = orderedAnalysisNutrientClassifications?.find(
      (nutrientClassification) => nutrientClassification.isDefault,
    );
    //TODO: this should be removed later if/when the validations for not having more than 5 levels are in place
    if (hasMoreThanFive && existingDefaultParamNutrient) {
      dispatch({
        type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION,
        payload: existingDefaultParamNutrient,
      });
    }

    const previousDefaultNutrientClassification = orderedAnalysisNutrientClassifications?.find(
      (nutrientClassification) =>
        nutrientClassification?.isDefault &&
        nutrientClassification?.id !== state?.selectedNutrientClassification?.id,
    );

    if (previousDefaultNutrientClassification) {
      const { greaterOrEqual, lowerThan, isDefault, ...rest } =
        previousDefaultNutrientClassification;
      const updatePreviousDefaultNutrientClassification: NutrientClassification = {
        ...rest,
        greaterOrEqual: Number(greaterOrEqual),
        lowerThan: Number(lowerThan),
        isDefault: !isDefault,
      };

      delete updatePreviousDefaultNutrientClassification?.id;

      await triggerCreateUpdateNutrientClassification({
        method: METHOD.PUT,
        body: JSON.stringify(updatePreviousDefaultNutrientClassification),
        extraUrl: `/${previousDefaultNutrientClassification.id}`,
      });
    }

    const { greaterOrEqual, lowerThan, isDefault, ...stateRest } =
      state.selectedNutrientClassification;

    const newDefaultNutritionClassification: NutrientClassification = {
      ...stateRest,
      greaterOrEqual: Number(greaterOrEqual),
      lowerThan: Number(lowerThan),
      isDefault: !isDefault,
    };

    delete newDefaultNutritionClassification?.id;
    const updatedNutrientClassificationResponse = await triggerCreateUpdateNutrientClassification({
      method: METHOD.PUT,
      body: JSON.stringify(newDefaultNutritionClassification),
      extraUrl: `/${state?.selectedNutrientClassification?.id}`,
    });

    if (typeof updatedNutrientClassificationResponse === 'object') {
      updateAnalysisConfigurations({
        ...configuration,
        nutrientClassifications: [
          updatedNutrientClassificationResponse.actionTriggerParameterLevelResult,
          ...updatedNutrientClassificationResponse.impactedParameterLevels,
        ],
      });
    }
  }, [orderedAnalysisNutrientClassifications, state.selectedNutrientClassification]);

  useEffect(() => {
    if (state.updateType === UpdateType.DEFAULT) handleDefaultUpdate();
  }, [state.selectedNutrientClassification]);

  const handleValueChanged = useCallback(createNumberDecimalChangeHandler(dispatch), [dispatch]);

  const handleValueUpdate = async (
    param: string,
    value: string,
    selectedValue: string | undefined,
    targetField: string | null,
  ): Promise<void> => {
    if (selectedValue === value) {
      setShouldShowSnackbar(false);
    } else {
      let newNutrientClassification;
      if (param === WarningMessage) {
        newNutrientClassification = {
          ...state?.selectedNutrientClassification,
          [param]: value,
        };
      } else {
        const isValid = validateParameterNumber(value);
        if (!isValid) return;

        value = formatValidTreeDecimalsNumber(value);

        newNutrientClassification = {
          ...state?.selectedNutrientClassification,
          [param]: isNaN(Number(value)) ? 0 : Number(value),
        };
      }

      delete newNutrientClassification.id;

      try {
        const updatedNutrientClassification = await triggerCreateUpdateNutrientClassification({
          method: METHOD.PUT,
          body: JSON.stringify(newNutrientClassification),
          extraUrl: `/${state?.selectedNutrientClassification?.id}`,
        });

        if (typeof updatedNutrientClassification === 'object') {
          updateAnalysisConfigurations({
            ...configuration,
            nutrientClassifications: [
              updatedNutrientClassification.actionTriggerParameterLevelResult,
              ...updatedNutrientClassification.impactedParameterLevels,
            ],
          });
        }

        if (shouldShowSnackbar) {
          displaySnackbarMessage(commonT('changeSaved'), setDisplaySnackbar);
          setShouldShowSnackbar(false);
        }

        if (targetField === 'checkbox' && typeof updatedNutrientClassification === 'object') {
          handleDefaultChange(updatedNutrientClassification.actionTriggerParameterLevelResult);
        }
      } catch (error: any) {
        handleOpenParamValuesInfoDialog(error);
      }
    }
  };

  const handleOpenParamValuesInfoDialog = (error: any) => {
    if ('message' in error) {
      applyParamValuesInfoDialogDescription(error.message);
      setOpenParamValuesInfoDialog(true);
    }
  };

  const applyParamValuesInfoDialogDescription = (message: string) => {
    let paramLevelLocalized = '';
    Object.values(ParameterLevelEnum).forEach((level) => {
      const levelRegex = new RegExp(`\\b${level}\\b`);
      if (levelRegex.test(message)) {
        paramLevelLocalized = commonT(`parameterLevel.${level}`);
      }
    });

    let localizationKey;
    if (message.includes(UPDATE_LEVEL_ERROR_MESSAGE_FRAGMENTS.NEXT_UPPER_LIMIT)) {
      localizationKey = 'dialog.paramValues.description.nextUpperLimit';
    } else if (message.includes(UPDATE_LEVEL_ERROR_MESSAGE_FRAGMENTS.PREVIOUS_UPPER_LIMIT)) {
      localizationKey = 'dialog.paramValues.description.previousUpperLimit';
    }

    setParamValuesInfoDialogDescription(
      localizationKey
        ? t(localizationKey, {
            level: paramLevelLocalized,
            analysisType: capitalizeFirstLetter(analysisType.split('-').join(' ')),
          })
        : null,
    );
  };

  const handleValueFocused = (
    param: keyof NutrientClassification,
    nutrient: NutrientClassification,
  ) => {
    const paramValue = nutrient[param];
    setShouldShowSnackbar(Boolean(typeof paramValue === 'number' && paramValue > 0));
    dispatch({
      type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION,
      payload: nutrient,
    });
    dispatch({
      type: UpdateAnalysisActions.SET_UPDATE_TYPE,
      payload: UpdateType.OTHER,
    });
  };

  const handleIsAddPopupOpened = () => {
    setAddParamState(addNewParamState);
    setIsAddPopupOpened(true);
  };

  return (
    <>
      {!isNutrientClassificationDeleting && (
        <DisplayTable
          analysisNutrientClassifications={orderedAnalysisNutrientClassifications}
          handleDefaultChange={handleDefaultChange}
          handleValueFocused={handleValueFocused}
          handleValueChanged={handleValueChanged}
          handleValueUpdate={handleValueUpdate}
          handleOpenDeleteDialog={handleOpenDeleteDialog}
          handleIsAddPopupOpened={handleIsAddPopupOpened}
          configuration={configuration}
          keyPrefix={keyPrefix}
          configType={configType}
          analysisType={analysisType}
          updateAnalysisConfigurations={updateAnalysisConfigurations}
          hasActionFailed={openParamValuesInfoDialog}
        />
      )}
      <ConfirmationDialog
        open={openDeleteDialog}
        title={deleteDialogTitle}
        description={t('dialog.description')}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={commonT('yesDelete')}
        okButtonConcept='destructive'
        cancelButton={commonT('cancel')}
        onOk={confirmDelete}
        onCancel={() => setOpenDeleteDialog(false)}
        isLoading={isNutrientClassificationDeleting}
      />
      <ConfirmationDialog
        open={openInfoDialog}
        title={t('dialog.info.title')}
        description={t('dialog.info.description')}
        icon='Info'
        iconColorConcept='brand'
        okButton={commonT('ok')}
        onOk={closeInfo}
        hideCancelButton
      />
      <ConfirmationDialog
        open={openParamValuesInfoDialog}
        title={t('dialog.paramValues.title')}
        description={paramValuesInfoDialogDescription || ''}
        icon='Info'
        iconColorConcept='brand'
        okButton={commonT('ok')}
        onOk={closeParamValuesInfoDialog}
        hideCancelButton
      />
      <AddNutritionParamPopup
        isAddPopupOpened={isAddPopupOpened}
        setIsAddPopupOpened={setIsAddPopupOpened}
        analysisConfiguration={configuration}
        addParamState={addParamState}
        setAddParamState={setAddParamState}
        analysisNutrientClassifications={orderedAnalysisNutrientClassifications}
        triggerCreateUpdateNutrientClassification={triggerCreateUpdateNutrientClassification}
        analysisType={analysisType}
        handleOpenParamValuesInfoDialog={handleOpenParamValuesInfoDialog}
      />
    </>
  );
};
