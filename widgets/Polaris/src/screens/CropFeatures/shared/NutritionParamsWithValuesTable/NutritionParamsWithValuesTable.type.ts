import {
  FeatureConfig,
  AnalysisConfiguration,
  AnalysisType,
  NutrientClassification,
  ParameterLevel,
} from '@common/types';

export interface NutritionParametersTableProps {
  configuration: AnalysisConfiguration;
  configType: FeatureConfig;
  analysisType: AnalysisType;
  keyPrefix: string;
}
export enum UpdateAnalysisActions {
  SET_SELECTED_NUTRIENT_CLASSIFICATION = 'SET_SELECTED_NUTRIENT_CLASSIFICATION',
  SET_UPDATE_TYPE = 'SET_UPDATE_TYPE',
}

export interface UpdateAnalysisState {
  selectedNutrientClassification: NutrientClassification | null;
  updateType: string;
}

export interface SetSelectedNutrientAction {
  type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT_CLASSIFICATION;
  payload: NutrientClassification | null;
}

export interface SetUpdateTypeAction {
  type: UpdateAnalysisActions.SET_UPDATE_TYPE;
  payload: string;
}

export type AnalysisActions = SetSelectedNutrientAction | SetUpdateTypeAction;

export enum PropertyKeyEnum {
  GreaterOrEqual = 'greaterOrEqual',
  LowerThan = 'lowerThan',
  WarningMessage = 'warningMessage',
}

export const paramLevelHeaders = [
  {
    title: 'default',
    className: '',
  },
  {
    title: 'parameterLevel',
    className: 'parameter-level',
  },
  {
    title: 'greaterThan',
  },
  {
    title: 'lessThan',
    className: '',
  },
  {
    title: 'action',
    className: 'table-actions',
  },
];

export const waterParamLevelHeaders = [...paramLevelHeaders];
waterParamLevelHeaders.splice(waterParamLevelHeaders.length - 1, 0, {
  title: 'warning',
  className: '',
});

export enum ParameterLevelRange {
  Min = 0,
  Max = 99999,
}

export interface IncorrectFields {
  currentMinPreviousMaxErrors: {
    greaterOrEqualErrors: ParameterLevel[];
    lowerThanErrors: ParameterLevel[];
  };
  minMaxErrors: {
    greaterOrEqualErrors: ParameterLevel[];
    lowerThanErrors: ParameterLevel[];
  };
}

export interface CurrentMinPreviousMaxLevelErrors {
  greaterOrEqualErrors: ParameterLevel[];
  lowerThanErrors: ParameterLevel[];
}
export interface SameLevelMinMaxErrors {
  greaterOrEqualErrors: ParameterLevel[];
  lowerThanErrors: ParameterLevel[];
}
