import React from 'react';
import { Status, Subtitle, Title } from '@yaradigitallabs/ahua-react';
import { HeaderSection } from './HeaderForCollapsible.styled';
import { useTranslation } from 'react-i18next';
interface HeaderForCollapsibleProps {
  title: string;
  subtitle: string;
  dataCy?: string;
  isStatusEnabled?: boolean;
}

export const HeaderForCollapsible = ({
  title,
  subtitle,
  dataCy,
  isStatusEnabled,
}: HeaderForCollapsibleProps) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.common',
  });
  return (
    <HeaderSection>
      <div>
        <Title size='s'>{title}</Title>
        <Subtitle size='xs'>{subtitle}</Subtitle>
      </div>

      {isStatusEnabled !== undefined && (
        <Status
          css={{ height: 'fit-content' }}
          colorType={isStatusEnabled ? 'green' : 'gray'}
          size='s'
          data-cy={dataCy ? dataCy : 'is-status-enabled'}
        >
          {t(`${isStatusEnabled ? 'statusEnabled' : 'statusDisabled'}`)}
        </Status>
      )}
    </HeaderSection>
  );
};
