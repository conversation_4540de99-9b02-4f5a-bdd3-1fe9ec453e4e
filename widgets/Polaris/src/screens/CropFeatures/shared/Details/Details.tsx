import React from 'react';
import { useTranslation } from 'react-i18next';
import { Title } from '@yaradigitallabs/ahua-react';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { DetailsContainer } from '@widgets/Polaris/styles/Polaris/screens/NPDetails/PageDetails.styled';
import { useSetNavbar } from '@widgets/Polaris/src/hooks';
import { generatePath } from 'react-router';
import { ROUTES } from '@src/routes';

interface DetailsProps {
	children?: React.ReactNode;
	route: string;
	featureDetails: string;
}

const Details: React.FC<DetailsProps> = ({ children, route, featureDetails }: DetailsProps) => {
	const { t } = useTranslation();

	const {
		selectedFeature,
		selectedCropDescription,
		selectedCrop,
		cropRegion,
		selectedRegion,
		selectedCountry,
	} = useAppContext();

	useSetNavbar(
		generatePath(`${ROUTES.cropFeatures}/${route}`, {
			cropRegionId: cropRegion?.id || '',
		}),
		true,
		featureDetails,
	);

	return (
		<DetailsContainer data-cy='mmm-details-content'>
			<Title data-cy='mmm-details-title' size='n' css={{ paddingBottom: '$x4' }}>
				{t(`polaris.cmmmDetails.title`, {
					defaultValue: 'Polaris',
					selectedCropSubclassName: selectedCrop?.name,
					selectedCropDescriptionName: selectedCropDescription?.name,
					selectedFeatureName: selectedFeature?.displayName,
					selectedRegionName: selectedRegion?.name,
					selectedCountryName: selectedCountry?.name,
					interpolation: { escapeValue: false },
				})}
			</Title>
			{children}
		</DetailsContainer>
	);
};

export default Details;
