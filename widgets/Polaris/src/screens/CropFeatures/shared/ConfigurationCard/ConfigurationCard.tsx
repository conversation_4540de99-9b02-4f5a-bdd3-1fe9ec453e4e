import React, { useCallback, useEffect, useMemo } from 'react';
import { Card } from '@yaradigitallabs/ahua-react';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { useFetchMMMValidations, useUpdateMMMValidation } from '@widgets/Polaris/src/hooks';
import {
  CardHeadTitleWrapper,
  MMMCardHeaderTitle,
  MMMCardHeadWrapper,
  PlanCardTitleCaption,
  StyledCardTail,
} from '@widgets/Polaris/styles/Polaris/screens/NPDetails/PageDetails.styled';
import { formatDateString, getUpdateDetails } from '@widgets/Polaris/utils';
import { useTranslation } from 'react-i18next';
import {
  ConfigurationType,
  MMMValidationUpdate,
  MMMValidationUpdateResult,
  ProductRecommendationApplicationTypes,
  ValidationStatus,
} from '@common/types';
import { METHOD } from '@common/constants';
import { useAzure } from '@yaradigitallabs/sh-user-management-browser';
import { generatePath, useNavigate } from 'react-router';
import { ROUTES } from '@src/routes';
import {
  TooltipWrapper,
  SoilValidationSwitch as PlanValidationSwitch,
} from '@widgets/Polaris/src/components';
import {
  ConfigurationCardLabelArgs,
  ConfigurationCardTitleArgs,
  ConfigurationCardTooltipArgs,
} from './ConfigurationCard.types';

interface ConfigurationCardProps {
  titles: ConfigurationCardTitleArgs;
  tooltips: ConfigurationCardTooltipArgs;
  labels: ConfigurationCardLabelArgs;
  showConfigureButtonTooltip: boolean;
  contentDescription: string;
  isConfigureButtonDisable: boolean;
  featurePlanConfigurationRoute: string;
  featurePlanName: string;
  featureTranslationKeyPrefix: string;
  configurationType: ConfigurationType;
  validateMMM?: (isValidated: boolean) => Promise<MMMValidationUpdateResult>;
}

const ConfigurationCard: React.FC<ConfigurationCardProps> = ({
  titles,
  tooltips,
  labels,
  showConfigureButtonTooltip,
  contentDescription,
  isConfigureButtonDisable,
  featurePlanConfigurationRoute,
  featurePlanName,
  featureTranslationKeyPrefix,
  configurationType,
}) => {
  const { t } = useTranslation();
  const { user } = useAzure();
  const navigate = useNavigate();
  const {
    cropRegion,
    selectedMMMValidation,
    methods: { setSelectedMMMValidation, setSelectedPlanConfigTab, setSelectedAppType },
  } = useAppContext();

  const validationsFilter: GenericFilter[] | undefined = useMemo(() => {
    if (!cropRegion?.id) return;

    return [
      {
        key: 'cropRegionId',
        value: cropRegion.id,
        type: FilterType.EQ,
      },
      {
        key: 'configurationType',
        value: configurationType,
        type: FilterType.EQ,
      },
    ];
  }, [cropRegion?.id]);

  const mmmValidations = useFetchMMMValidations(validationsFilter, Boolean(cropRegion?.id));
  const { trigger: triggerMMMValidationUpdate } = useUpdateMMMValidation(selectedMMMValidation?.id);

  const isMMMValidated = selectedMMMValidation?.validationStatus === ValidationStatus.VALIDATED;

  useEffect(() => {
    if (!mmmValidations?.length) return;

    const currentPlanValidation = mmmValidations?.[0];

    setSelectedMMMValidation(currentPlanValidation || null);
  }, [mmmValidations]);

  const updatedDetails = useMemo(() => {
    if (!selectedMMMValidation) return;

    return getUpdateDetails(
      selectedMMMValidation,
      selectedMMMValidation?.validationStatus as ValidationStatus,
      formatDateString,
      t,
    );
  }, [selectedMMMValidation]);

  const mmmValidationUpdated = useCallback(
    async (isValidated: boolean) => {
      const result: MMMValidationUpdateResult = {
        isSuccessful: false,
        errorLocations: [],
      };

      if (!selectedMMMValidation) {
        console.error('Failed to update plan validation: no selected validation');
        return result;
      }

      const { countryId, cropRegionId, configurationType } = selectedMMMValidation;
      const payload: MMMValidationUpdate = {
        countryId,
        cropRegionId,
        configurationType,
        validationStatus: isValidated ? ValidationStatus.VALIDATED : ValidationStatus.INVALIDATED,
      };

      try {
        const response = await triggerMMMValidationUpdate({
          method: METHOD.PUT,
          body: JSON.stringify(payload),
        });

        if (response) {
          const { entity: updatedMMMValidation, errorLocations } = response;

          setSelectedMMMValidation(updatedMMMValidation);

          if (updatedMMMValidation.validationStatus === ValidationStatus.FAILED) {
            result.errorLocations = errorLocations;
            return result;
          }
        }

        result.isSuccessful = true;

        return result;
      } catch (error) {
        console.error('Failed to update plan validation:', error);

        return result;
      }
    },
    [selectedMMMValidation?.id, triggerMMMValidationUpdate, user?.email],
  );

  const handlePlanConfigurationNavigation = (planName: string) => {
    // Clear the previously selected tab in Plan Validation
    setSelectedPlanConfigTab(null);

    // Select the default product recommendation application
    setSelectedAppType(ProductRecommendationApplicationTypes.SOIL);

    // Navigate to the specific plan configuration
    const path = generatePath(`${ROUTES.cropFeatures}/${featurePlanConfigurationRoute}`, {
      planName,
      cropRegionId: cropRegion?.id || '',
    });

    navigate(path, { state: { planName } });
  };

  return (
    <Card data-cy='mmm-configuration-card' css={{ maxWidth: '562px', marginTop: '$x6' }}>
      <MMMCardHeadWrapper>
        <CardHeadTitleWrapper>
          <MMMCardHeaderTitle>{titles.header}</MMMCardHeaderTitle>

          <TooltipWrapper
            maxWidth={400}
            concept={'inverse'}
            showTooltip={true}
            tooltipText={updatedDetails ?? ''}
            dataCy={'updated-details-tooltip'}
          >
            <PlanCardTitleCaption maxLineCount={1} size='s'>
              {updatedDetails}
            </PlanCardTitleCaption>
          </TooltipWrapper>
        </CardHeadTitleWrapper>

        <PlanValidationSwitch
          isInitiallyActive={isMMMValidated}
          featureTransKeyPrefix={featureTranslationKeyPrefix}
          canToggle={Boolean(selectedMMMValidation)}
          onTogglePlanValidation={mmmValidationUpdated}
        />
      </MMMCardHeadWrapper>

      <Card.Divider />

      <Card.Body className='plan-details-configure-nav-content'>
        <Card.Content
          css={{ paddingBottom: '$x4', paddingTop: '$x4' }}
          title={titles.content}
          description={contentDescription}
        />
      </Card.Body>

      <Card.Divider />

      <StyledCardTail>
        <TooltipWrapper
          maxWidth={160}
          concept='inverse'
          showTooltip
          tooltipText={tooltips.testHarnessButton}
          dataCy={'configuration-card-test-harness-button-tooltip'}
        >
          <div style={{ flex: '1 1 0%' }}>
            <Card.Action
              label={labels.testHarnessAction}
              variant={'outline'}
              disabled
              data-cy={'configuration-card-test-harness-button'}
            />
          </div>
        </TooltipWrapper>

        <TooltipWrapper
          maxWidth={160}
          concept='inverse'
          showTooltip={showConfigureButtonTooltip}
          tooltipText={tooltips.configureButton}
          dataCy={'configuration-card-configure-button-tooltip'}
        >
          <div style={{ flex: '1 1 0%' }}>
            <Card.Action
              label={labels.configureButton}
              variant='primary'
              disabled={isConfigureButtonDisable}
              onClick={() => handlePlanConfigurationNavigation(featurePlanName)}
              data-cy={'configuration-card-configure-button'}
            />
          </div>
        </TooltipWrapper>
      </StyledCardTail>
    </Card>
  );
};

export default ConfigurationCard;
