import { Collapsible, styled } from '@yaradigitallabs/ahua-react';

export const CollapsibleContainer = styled(Collapsible, {
  '& > button': {
    fontSize: '$scale4',
    lineHeight: '$scale5',
    '& > div:last-of-type': {
      width: '$x10',
    },
    h2: {
      color: '$black60',
      fontWeight: 'regular',
    },
  },
  '& > div': {
    padding: '$x6 $x4 $x6 $x4',
  },
});

export const CollapsibleNoPaddingContainer = styled(CollapsibleContainer, {
  '& > div': {
    padding: '0',
  },
});

export const HeaderSection = styled('div', {
  display: 'flex',
  width: '100%',
  justifyContent: 'space-between',
});

export const Bold = styled('span', {
  fontWeight: '$bold',
});
