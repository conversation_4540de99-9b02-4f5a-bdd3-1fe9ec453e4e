import { FEATURE_IDS, NUTRIENT_IDS } from '@common/constants';
import {
  CNPPlanConfigurationTabs,
  CMMMPlanConfigurationTabs,
  FertigationPlanConfigurationTabs,
  TabsWithNutrients,
  FertigationModuleNameToAccordionState,
  CerealsModuleNameToAccordionState,
  CerealsPreCropResidual,
  CerealsPreCropYield,
  CerealsDemandCalculationModule,
  FertigationDemandCalculationModule,
} from '@common/types';
import { TFunction } from 'i18next';

/*
Configuration tabs for CNP, CMMM and Fertigation
*/

export const CNP_PLAN_CONFIGURATION_TABS: CNPPlanConfigurationTabs = {
  PARAMETERS: 'Parameters',
  SOIL_ANALYSIS: 'Soil analysis',
  DEMAND_CALC: 'Demand calculations',
  PRODUCT_REC: 'Product recommendations',
};
export const CMMM_PLAN_CONFIGURATION_TABS: CMMMPlanConfigurationTabs = {
  SOIL_ANALYSIS: 'Soil analysis',
  DEMAND_CALC: 'Demand calculations',
  ORGANIC_FERTILISERS: 'Organic fertilisers',
  N_SPLITTING_SCHEDULE: 'N splitting schedule',
  PRODUCT_REC: 'Product recommendations',
};
export const FERTIGATION_PLAN_CONFIGURATION_TABS: FertigationPlanConfigurationTabs = {
  SOIL_ANALYSIS: 'Soil analysis',
  LEAF_ANALYSIS: 'Leaf analysis',
  WATER_ANALYSIS: 'Water analysis',
  DEMAND_CALC: 'Demand calculations',
  SPLITTING_SCHEDULE: 'Splitting schedule',
  PRODUCT_REC: 'Product recommendations',
};
export const CONFIGURATION_TABS_WITH_NUTRIENTS: TabsWithNutrients = {
  SOIL_ANALYSIS: 'Soil analysis',
  LEAF_ANALYSIS: 'Leaf analysis',
  WATER_ANALYSIS: 'Water analysis',
  DEMAND_CALC: 'Demand calculations',
};

/**
 * Hard-coded list of nutrient Ids for Cereals MMM Soil analysis.
 * It is used in the filter in the body of the API request
 * + to sort the received nutrients in the order they appear in this list.
 */

export const CMMM_SOIL_ANALYSIS_NUTRIENTS = [
  'f2c275c4-1522-4524-8747-08ace254b155', // Phosphorus
  '7a1d7c09-13fa-4ea0-b72b-8290663c31d5', // Potassium
  'd764880a-c5d8-4a9a-ab49-a2b3ba59970d', // Magnesium
  '35d95f91-1a57-42d3-810c-65ddadf8cf5b', // Organic Matter
];

/**
 * Hard-coded list of nutrient Ids for Cereals MMM Demand calculations.
 * It is used in the filter in the body of the API request
 * + to sort the received nutrients in the order they appear in this list.
 */

export const CMMM_DEMAND_CALCULATION_NUTRIENTS = [
  '880abcda-5ee5-4068-879c-94489be314d5', // Nitrogen
  'f2c275c4-1522-4524-8747-08ace254b155', // Phosphorus
  '7a1d7c09-13fa-4ea0-b72b-8290663c31d5', // Potassium
  'd764880a-c5d8-4a9a-ab49-a2b3ba59970d', // Magnesium
  '0163eeff-5d87-4749-bcf7-3e4be6732808', // Sulphur
];

export const CMMM_DEMAND_CALCULATION_NUTRIENT_FORMS = [
  '393f6e30-0af3-4336-a65f-953701d1173a', // N
  '3eacac5f-d65d-4248-89c9-8d7f9337ebe9', // P2O5
  'ae2132a8-f1e6-4170-877c-ab8ccc2c6656', // K2O
  '041057eb-e8d3-492f-b82f-656e93b32800', // MgO
  '2a3d91f7-e1f2-4811-944b-32da80507d6b', // SO3
];

export enum CerealsCropNDemandInputs {
  NutrientRemoval = 'removalValue',
  NutrientUptake = 'uptakeValue',
}

export enum CropNDemandInputs {
  NutrientRemoval = 'removalValue',
  NutrientUptake = 'uptakeValue',
}

export enum CerealsSomInputs {
  SomThreshold = 'somThreshold',
  SomThresholdUnitId = 'somThresholdUnitId',
  Deduct = 'deduct',
  SomDeductUnitId = 'deductUnitId',
  SomEvery = 'somEvery',
  SomEveryUnitId = 'somEveryUnitId',
  SomDefault = 'somDefault',
  SomDefaultUnitId = 'somDefaultUnitId',
}

export type SomUnitsState = Record<
  | `${CerealsSomInputs.SomThreshold}UnitName`
  | `${CerealsSomInputs.Deduct}UnitName`
  | `${CerealsSomInputs.SomEvery}UnitName`
  | `${CerealsSomInputs.SomDefault}UnitName`,
  string
>;

export const SOM_INPUTS = {
  somThresholdUnitName: '',
  deductUnitName: '',
  somEveryUnitName: '',
  somDefaultUnitName: '',
};

export const ELEMENT_N = 'N';
export const DEFAULT_NUMBER_VALUE = 0;
export const MIN_PERCENT_VALUE = '1';
export const MAX_PERCENT_VALUE = '100';
export const DEFAULT_INPUT_VALUE = '0';

/**
 * Hard-coded list of nutrient Ids for Fertigation Soil analysis.
 * It is used in the filter in the body of the API request
 * + to sort the received nutrients in the order they appear in this list.
 */

export const FERTIGATION_SOIL_ANALYSIS_NUTRIENTS = [
  '84490c73-d6a8-487e-9fcc-cbdd8a0be609', // pH
  'ede6c4f5-2e87-4179-92ee-d6a09966e51e', // CEC
  '35d95f91-1a57-42d3-810c-65ddadf8cf5b', // Organic Matter
  '880abcda-5ee5-4068-879c-94489be314d5', // Nitrogen
  'f2c275c4-1522-4524-8747-08ace254b155', // Phosphorus
  '7a1d7c09-13fa-4ea0-b72b-8290663c31d5', // Potassium
  '5fe008ca-d2d7-48a6-921b-8b53c781a4ab', // Calcium
  'd764880a-c5d8-4a9a-ab49-a2b3ba59970d', // Magnesium
  '0163eeff-5d87-4749-bcf7-3e4be6732808', // Sulphur
  '6990195e-b1a3-4218-90db-f38c9cfae633', // Boron
  '578e38c1-e641-4aa9-9a8e-6a428e55f1ec', // Copper
  '5c7eb818-06c1-4013-b6d2-2f02f1e54d44', // Iron
  '45b614a2-cce3-4c85-9c63-b459396e9aef', // Manganese
  'a85856aa-c3cf-433d-8357-4dbe0259d1f9', // Zinc
];

export const FERTIGATION_WATER_ANALYSIS_NUTRIENTS = [
  NUTRIENT_IDS.PH,
  NUTRIENT_IDS.EC,
  NUTRIENT_IDS.NO3_ION,
  NUTRIENT_IDS.H2PO4_ION,
  NUTRIENT_IDS.SO4_ION,
  NUTRIENT_IDS.CL_ION,
  NUTRIENT_IDS.HCO3_ION,
  NUTRIENT_IDS.CO3_ION,
  NUTRIENT_IDS.NH4_ION,
  NUTRIENT_IDS.K_ION,
  NUTRIENT_IDS.CA_ION,
  NUTRIENT_IDS.MG_ION,
  NUTRIENT_IDS.NA_ION,
  NUTRIENT_IDS.B,
  NUTRIENT_IDS.CU,
  NUTRIENT_IDS.FE,
  NUTRIENT_IDS.MN,
  NUTRIENT_IDS.ZN,
  NUTRIENT_IDS.MO,
  NUTRIENT_IDS.SAR,
];

/**
 * Hard-coded list of nutrient Ids for Fertigation Leaf analysis and Demand calculations.
 * It is used in the filter in the body of the API request
 * + to sort the received nutrients in the order they appear in this list.
 */

export const FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS = [
  '880abcda-5ee5-4068-879c-94489be314d5', // Nitrogen
  'f2c275c4-1522-4524-8747-08ace254b155', // Phosphorus
  '7a1d7c09-13fa-4ea0-b72b-8290663c31d5', // Potassium
  '5fe008ca-d2d7-48a6-921b-8b53c781a4ab', // Calcium
  'd764880a-c5d8-4a9a-ab49-a2b3ba59970d', // Magnesium
  '0163eeff-5d87-4749-bcf7-3e4be6732808', // Sulphur
  '6990195e-b1a3-4218-90db-f38c9cfae633', // Boron
  '578e38c1-e641-4aa9-9a8e-6a428e55f1ec', // Copper
  '5c7eb818-06c1-4013-b6d2-2f02f1e54d44', // Iron
  '45b614a2-cce3-4c85-9c63-b459396e9aef', // Manganese
  'a85856aa-c3cf-433d-8357-4dbe0259d1f9', // Zinc
  '953dcd7a-0de1-4997-8360-985a73af3cb6', // Molybdenum
];

export const MAX_LIMIT_NUMBER = 9999999999;
export const MIN_LIMIT_NUMBER = -9999999999;
export const MAX_PHASE_LENGTH = 10;
export const MIN_PHASE_LENGTH = 1;
export const CROP_DEMAND_MAX_DECIMAL_PLACES = 10;

export enum DefaultClassificationEnum {
  DEFAULT = 'DEFAULT',
}

// Hard-coded units for CMMM Organic Fertiliser's Market Regulations module
export enum MarketRegulationUnits {
  /** Unit for maximum amount per application (t/ha) */
  MaxAmountPerApplicationUnit = '394e8163-636e-48dd-8ded-a9fd6436d335',
  /** Unit for maximum n amount per application (kg/ha) */
  MaxNAmountPerApplicationUnit = '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  /** Unit for maximum p amount per application (kg/ha) */
  MaxPAmountPerApplicationUnit = '53f8ea06-3244-4a0f-ae83-6914bac5f235',
}

export const MARKET_REGULATION_UNIT_INDEX_TO_IDS: Record<number, MarketRegulationUnits> = {
  0: MarketRegulationUnits.MaxAmountPerApplicationUnit,
  1: MarketRegulationUnits.MaxNAmountPerApplicationUnit,
  2: MarketRegulationUnits.MaxPAmountPerApplicationUnit,
};

// Hard-coded units for Cereals Demand Calculations
export enum CerealsCropNutrientDemandUnits {
  /** Unit for uptake demand (kg/t) or nutrient uptake - uptakeUnitId */
  NutrientUptakeId = '2429df29-4f5e-4cdb-b02a-25c7e3693664',
  /** Unit for nutrient removal (kg/t) - removalUnitId */
  NutrientRemovalId = '2429df29-4f5e-4cdb-b02a-25c7e3693664',
}

export enum CerealsSomUnits {
  ThresholdUnitId = '64d915ec-e4ed-437f-aad3-f9ac2ca92373', // %
  DeductUnitId = '394e8163-636e-48dd-8ded-a9fd6436d335', // kg N/ha
  EveryUnitId = '64d915ec-e4ed-437f-aad3-f9ac2ca92373', // %
  DefaultUnitId = '64d915ec-e4ed-437f-aad3-f9ac2ca92373', // %
}

// This is the initial state of the collapsibles (accordion) for modules
// inside the Demand Calculations config tab in Fertigation
export const FERTIGATION_MODULES_TO_STATES_INITIAL_MAP: FertigationModuleNameToAccordionState = {
  [FertigationDemandCalculationModule.CROP_DEMAND]: true,
  [FertigationDemandCalculationModule.USE_EFFICIENCY]: false,
  [FertigationDemandCalculationModule.SOIL_CORRECTION_FACTOR]: false,
  [FertigationDemandCalculationModule.IRRIGATION_WATER_NUTRIENT]: false,
};

// This is the initial state of the collapsibles (accordion) for modules
// inside the Demand Calculations config tab in CerealsMMM
export const CEREALS_MODULES_TO_STATES_INITIAL_MAP: CerealsModuleNameToAccordionState = {
  [CerealsDemandCalculationModule.CropDemand]: true,
  [CerealsDemandCalculationModule.UseEfficiency]: true,
  [CerealsDemandCalculationModule.SoilCorrectionFactor]: true,
  [CerealsDemandCalculationModule.SoilOrganicMatter]: true,
  [CerealsDemandCalculationModule.SoilMineral]: true,
  [CerealsDemandCalculationModule.PreCrop]: true,
};

export enum ApplicationConditionParameters {
  SOIL_PH = 'Soil pH',
  WATER_PH = 'Water pH',
  SOIL_ORG_M = 'Soil OrgM',
}

export enum ApplicationConditionOperators {
  GREATER_THAN = '>',
  LOWER_THAN = '<',
  GREATER_OR_EQUAL = '>=',
  LOWER_OR_EQUAL = '<=',
}

export enum ApplicationConditionLogicalOperators {
  AND = 'AND',
  OR = 'OR',
}

export const NO_CONDITION_TEXT = 'No condition required';

export const NUTRIENT_IDS_BY_TAB = {
  [FEATURE_IDS.CMMM]: {
    [CMMM_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS]: CMMM_SOIL_ANALYSIS_NUTRIENTS,
    [CMMM_PLAN_CONFIGURATION_TABS.DEMAND_CALC]: CMMM_DEMAND_CALCULATION_NUTRIENTS,
  },
  [FEATURE_IDS.FP]: {
    [FERTIGATION_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS]: FERTIGATION_SOIL_ANALYSIS_NUTRIENTS,
    [FERTIGATION_PLAN_CONFIGURATION_TABS.LEAF_ANALYSIS]:
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS,
    [FERTIGATION_PLAN_CONFIGURATION_TABS.WATER_ANALYSIS]: FERTIGATION_WATER_ANALYSIS_NUTRIENTS,
    [FERTIGATION_PLAN_CONFIGURATION_TABS.DEMAND_CALC]:
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS,
  },
};

export const DEFAULT_PRE_CROP_RESIDUAL_UNIT = 'Kg/ha';
export const DEFAULT_PRE_CROP_YIELD_UNIT = 't/ha';
export const DEFAULT_PRE_CROP_RESIDUAL_YIELD_UNIT = 'kg/t';

export type CerealsPreCropItem = CerealsPreCropResidual | CerealsPreCropYield;

export enum PreCropTypes {
  PreCropResidual = 'preCropResidual',
  PreCropYield = 'preCropYield',
}

export interface AddPreCropConfig {
  id: string;
  residualValue: string;
  yieldValue: string;
}

export interface TableConfigProps {
  t: TFunction;
  preCropType: PreCropTypes;
}

export enum PreCropArrayKeys {
  Residuals = 'residuals',
  ResidualsWithYield = 'residualsWithYield',
}

export enum PreCropInputValueKeys {
  ResidualValue = 'residualValue',
  PreCropYield = 'preCropYield',
}
