import { styled } from '@yaradigitallabs/ahua-react';

export const EquationContainer = styled('div', {
  gap: '$x2',
  display: 'flex',
  flexDirection: 'column',
});

export const EquationTitle = styled('h2', {
  fontFamily: '$default',
  fontWeight: '$medium',
  fontSize: '$scale3',
});

export const EquationDescription = styled('p', {
  fontFamily: '$default',
  fontSize: '$scale2',
  padding: '$x2 0',
  color: '$black60',
  lineHeight: '$scale5',
});

export const EquationComment = styled('p', {
  color: '$black70',
  fontWeight: '$medium',
  fontSize: '$scale2',
  lineHeight: '$scale5',
});

export const EquationContentWrapper = styled('div', {
  padding: '$x2 $x3',
  backgroundColor: '$gray5',
});

export const EquationContent = styled('p', {
  fontFamily: 'Inconsolata, monospace',
  fontWeight: '$regular',
  fontSize: '$scale4',
  whiteSpace: 'pre-wrap',
  lineHeight: '$scale7',
});
