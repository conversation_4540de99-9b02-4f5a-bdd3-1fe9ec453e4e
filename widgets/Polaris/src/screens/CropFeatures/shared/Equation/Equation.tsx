import React, { ReactNode } from 'react';

import {
  EquationComment,
  EquationContainer,
  EquationContent,
  EquationContentWrapper,
  EquationDescription,
  EquationTitle,
} from './Equation.styled';

interface EquationProps {
  title: string;
  description?: string;
  equation: string | ReactNode;
  dataCy: string;
  comment?: string;
}

export const Equation = ({ title, description, equation, dataCy, comment }: EquationProps) => {
  return (
    <EquationContainer data-cy={dataCy}>
      <EquationTitle data-cy={`${dataCy}-title`}>{title}</EquationTitle>
      {description && (
        <EquationDescription data-cy={`${dataCy}-description`}>{description}</EquationDescription>
      )}
      <EquationContentWrapper data-cy={`${dataCy}-content-wrapper`}>
        <EquationContent data-cy={`${dataCy}-content`}>{equation}</EquationContent>
      </EquationContentWrapper>
      {comment && <EquationComment data-cy={`${dataCy}-comment`}>{comment}</EquationComment>}
    </EquationContainer>
  );
};
