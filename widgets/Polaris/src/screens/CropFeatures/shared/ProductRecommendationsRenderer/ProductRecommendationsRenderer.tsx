import React from 'react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { ProductRecommendations } from '../../CNP/ProductRecommendations/ProductRecommendations';
import CMMMProductRecommendations from '../../CMMM/ProductRecommendations/ProductRecommendations';
import FertigationProductRecommendations from '../../Fertigation/ProductRecommendations/ProductRecommendations';

const ProductRecommendationsRenderer = () => {
  const { selectedFeature } = useAppContext();

  const componentMapping: Record<string, JSX.Element> = {
    CNP: <ProductRecommendations />,
    CMMM: <CMMMProductRecommendations />,
    FP: <FertigationProductRecommendations />,
  };

  return (
    <>
      {selectedFeature && componentMapping[selectedFeature.name] ? (
        componentMapping[selectedFeature.name]
      ) : (
        <div>No Product Recommendation Available</div>
      )}
    </>
  );
};

export default ProductRecommendationsRenderer;
