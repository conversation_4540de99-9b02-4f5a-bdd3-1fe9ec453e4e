// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DemandCalculationsEmptyState should initialize the component and match snapshot with all the components part of the empty state 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <a
        class="c-wzBoY card-wrapper"
        data-cy="product-recommendations-emtpy-state-card"
      >
        <div
          class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical"
        >
          <div
            class="c-hPExTj"
            data-cy="product-recommendations-card-emtpy-state-content"
          >
            <div
              class="c-htRpEE"
              data-cy="product-recommendations-empty-state"
            >
              <div
                class="c-ghqIYf"
              >
                <div
                  class="c-jBEZvR c-jBEZvR-iTKOFX-orientation-vertical"
                >
                  <div
                    class="c-jxdTCW"
                  >
                    <div
                      class="c-eHLrry c-eHLrry-KMkko-size-n c-eHLrry-kxdaWf-cv"
                      data-cy="empty-state-info-icon"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-gmtoDF-iconSize-x8 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
                        data-cy="empty-state-info-icon"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div
                    class="c-htcaFI c-htcaFI-gjdJOs-orientation-vertical"
                  >
                    <h1
                      class="c-iFoEyZ c-iFoEyZ-iJzrTx-size-l c-iFoEyZ-iPJLV-css c-fGHEql c-fGHEql-gjdJOs-orientation-vertical"
                      style="font-weight: 700; font-size: 20px;"
                    >
                      title
                    </h1>
                    <p
                      class="c-gIhYmC c-gIhYmC-dDOYgV-size-l c-gIhYmC-iPJLV-css c-PJLV c-PJLV-gjdJOs-orientation-vertical c-ebEscy"
                      data-cy="empty-screen-text"
                      style="line-height: var(--space-x6);"
                    >
                      message
                    </p>
                  </div>
                  <div
                    class="c-duLxUO c-duLxUO-bICGYT-orientation-vertical"
                  >
                    <button
                      class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-qswFQ-variant-outline"
                      data-cy="product-recommendations-empty-state-action-button"
                    >
                      <span
                        class="c-iepcqn"
                      >
                        createFirstSplitButton
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a>
    </div>
  </body>,
  "container": <div>
    <a
      class="c-wzBoY card-wrapper"
      data-cy="product-recommendations-emtpy-state-card"
    >
      <div
        class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical"
      >
        <div
          class="c-hPExTj"
          data-cy="product-recommendations-card-emtpy-state-content"
        >
          <div
            class="c-htRpEE"
            data-cy="product-recommendations-empty-state"
          >
            <div
              class="c-ghqIYf"
            >
              <div
                class="c-jBEZvR c-jBEZvR-iTKOFX-orientation-vertical"
              >
                <div
                  class="c-jxdTCW"
                >
                  <div
                    class="c-eHLrry c-eHLrry-KMkko-size-n c-eHLrry-kxdaWf-cv"
                    data-cy="empty-state-info-icon"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-gmtoDF-iconSize-x8 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
                      data-cy="empty-state-info-icon"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </div>
                </div>
                <div
                  class="c-htcaFI c-htcaFI-gjdJOs-orientation-vertical"
                >
                  <h1
                    class="c-iFoEyZ c-iFoEyZ-iJzrTx-size-l c-iFoEyZ-iPJLV-css c-fGHEql c-fGHEql-gjdJOs-orientation-vertical"
                    style="font-weight: 700; font-size: 20px;"
                  >
                    title
                  </h1>
                  <p
                    class="c-gIhYmC c-gIhYmC-dDOYgV-size-l c-gIhYmC-iPJLV-css c-PJLV c-PJLV-gjdJOs-orientation-vertical c-ebEscy"
                    data-cy="empty-screen-text"
                    style="line-height: var(--space-x6);"
                  >
                    message
                  </p>
                </div>
                <div
                  class="c-duLxUO c-duLxUO-bICGYT-orientation-vertical"
                >
                  <button
                    class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-qswFQ-variant-outline"
                    data-cy="product-recommendations-empty-state-action-button"
                  >
                    <span
                      class="c-iepcqn"
                    >
                      createFirstSplitButton
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
