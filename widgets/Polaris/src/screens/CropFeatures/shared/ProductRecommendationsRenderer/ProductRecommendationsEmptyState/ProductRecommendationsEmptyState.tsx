import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  EmptyStateMessageStyle,
  EmptyStateTitleStyle,
} from './ProductRecommendationsEmptyState.styled';
import { Card } from '@yaradigitallabs/ahua-react';
import './styles.scss';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';

export const ProductRecommendationsEmptyState: () => JSX.Element = () => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cnpDetails.planConfiguration.productRecommendations.emptyState',
  });

  const createFirstSplit: () => void = () => {
    //TODO: Create first split logic here
    console.log('createFirstSplit');
  };

  return (
    <>
      <Card data-cy='product-recommendations-emtpy-state-card' className='card-wrapper'>
        <Card.Body>
          <Card.Content data-cy='product-recommendations-card-emtpy-state-content'>
            <EmptyStateComponent
              title={t('title')}
              message={t('message')}
              dataCy='product-recommendations-empty-state'
              styles={{
                titleStyles: EmptyStateTitleStyle,
                messageStyles: EmptyStateMessageStyle,
              }}
              actionText={t('createFirstSplitButton')}
              onActionClick={createFirstSplit}
              buttonSize='xs'
            />
          </Card.Content>
        </Card.Body>
      </Card>
    </>
  );
};
