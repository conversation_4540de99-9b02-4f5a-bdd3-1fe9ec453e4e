import React from 'react';
import { render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import {
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import ProductRecommendationsRenderer from '../ProductRecommendationsRenderer';

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('ProductRecommendationsRenderer Component', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <ProductRecommendationsRenderer />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });
});
