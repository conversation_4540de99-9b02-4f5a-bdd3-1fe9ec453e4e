import React from 'react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import DemandCalculations from '../../CNP/DemandCalculations/DemandCalculations';
import FertigationDemandCalculations from '../../Fertigation/DemandCalculations/DemandCalculations';
import CMMMDemandCalculations from '../../CMMM/DemandCalculations/DemandCalculations';

interface DemandCalcProps {
  setActiveTab: (tab: string) => void;
}

const DemandCalculationsRenderer = (props: DemandCalcProps) => {
  const { selectedFeature } = useAppContext();

  const componentMapping: Record<string, JSX.Element> = {
    CNP: <DemandCalculations {...props} />,
    CMMM: <CMMMDemandCalculations />,
    FP: <FertigationDemandCalculations />,
  };

  return (
    <>
      {selectedFeature && componentMapping[selectedFeature.name] ? (
        componentMapping[selectedFeature.name]
      ) : (
        <div>No Demand Calculations Available</div>
      )}
    </>
  );
};

export default DemandCalculationsRenderer;
