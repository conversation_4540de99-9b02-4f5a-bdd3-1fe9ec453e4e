import React from 'react';
import { render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { BrowserRouter as Router } from 'react-router-dom';
import { NavbarProvider } from '@libs/nav-context';
import {
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { DemandCalculationsEmptyState } from '../DemandCalculationsEmptyState';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('DemandCalculationsEmptyState', () => {
  const setActiveTabMock = jest.fn();

  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });

  it('should initialize the component and match snapshot with all the components part of the empty state', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => <DemandCalculationsEmptyState setActiveTab={setActiveTabMock} />}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();

    expect(component.getByTestId('demand-calculations-empty-state-header')).toBeInTheDocument();
    expect(component.getByTestId('demand-calculations-empty-state-title')).toBeInTheDocument();
    expect(component.getByTestId('demand-calculations-empty-state-copyAll')).toBeInTheDocument();
    expect(
      component.getByTestId('demand-calculations-card-emtpy-state-content'),
    ).toBeInTheDocument();
    expect(component.getByTestId('demand-calculations-empty-state')).toBeInTheDocument();
  });

  it('should render the Go to soil analysis button', () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => <DemandCalculationsEmptyState setActiveTab={setActiveTabMock} />}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const goToSoilAnalysisButton = screen.getByTestId(
      'demand-calculations-empty-state-action-button',
    );
    expect(goToSoilAnalysisButton).toBeInTheDocument();
  });
});
