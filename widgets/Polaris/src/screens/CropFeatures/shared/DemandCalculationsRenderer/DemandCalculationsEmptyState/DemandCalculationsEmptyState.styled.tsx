import { Button, styled } from '@yaradigitallabs/ahua-react';
import { CSSProperties } from 'react';

export const EmptyStateTitleStyle: CSSProperties = {
  fontWeight: 700,
  color: '$black70',
  fontSize: 20,
};

export const EmptyStateMessageStyle: CSSProperties = {
  lineHeight: 'var(--space-x6)',
  fontSize: 'var(--fontSizes-scale3)',
};

export const HeaderWrapper = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '$x4',
  width: '100%',
  gap: '$x2',
});

export const TitleStyle = {
  fontWeight: 500,
};

export const CopyButtonStyled = styled(Button, {
  flex: '0 0 86px',
  '& > span': {
    overflow: 'unset',
  },
});
