import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CopyButtonStyled,
  EmptyStateMessageStyle,
  EmptyStateTitleStyle,
  HeaderWrapper,
  TitleStyle,
} from './DemandCalculationsEmptyState.styled';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { Card, Title } from '@yaradigitallabs/ahua-react';
import './styles.scss';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';
import { CNP_PLAN_CONFIGURATION_TABS } from '../../constants';

interface DemandCalculationsEmptyStateProps {
  setActiveTab: (tab: string) => void;
}

export const DemandCalculationsEmptyState: FC<DemandCalculationsEmptyStateProps> = ({
  setActiveTab,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.demandCalculations.emptyState',
  });

  const {
    methods: { setSelectedPlanConfigTab },
  } = useAppContext();

  const goToSoilAnalysisTab: () => void = () => {
    CNP_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS &&
      setActiveTab(CNP_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS);
    setSelectedPlanConfigTab(CNP_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS ?? null);
  };

  return (
    <>
      <HeaderWrapper data-cy='demand-calculations-empty-state-header'>
        <Title data-cy='demand-calculations-empty-state-title' style={TitleStyle} size='xs'>
          {t('nutritionParameters')}
        </Title>
        <CopyButtonStyled
          size={'s'}
          colorConcept='brand'
          label='Label'
          variant='outline'
          disabled={true}
          data-cy='demand-calculations-empty-state-copyAll'
        >
          {t('copy')}
        </CopyButtonStyled>
      </HeaderWrapper>
      <Card data-cy='demand-calculations-emtpy-state-card' className='card-wrapper'>
        <Card.Body>
          <Card.Content data-cy='demand-calculations-card-emtpy-state-content'>
            <EmptyStateComponent
              title={t('title')}
              message={t('message')}
              dataCy='demand-calculations-empty-state'
              styles={{
                titleStyles: EmptyStateTitleStyle,
                messageStyles: EmptyStateMessageStyle,
              }}
              actionText={t('actionButtonText')}
              onActionClick={goToSoilAnalysisTab}
              buttonSize='xs'
            />
          </Card.Content>
        </Card.Body>
      </Card>
    </>
  );
};
