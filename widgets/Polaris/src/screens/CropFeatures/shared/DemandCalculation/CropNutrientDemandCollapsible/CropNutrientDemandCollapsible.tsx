import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import { AhuaIcon, Subtitle, Title, Tooltip, Input } from '@yaradigitallabs/ahua-react';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { useUpdateDemandCalculationModule } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import {
  BaseUnit,
  FeatureConfigOptions,
  ModuleNameToAccordionNutrientState,
  Nutrient,
  CerealsCropDemand,
  FertigationCropDemand,
  DemandCalculationModule,
} from '@common/types';
import { InputContainer, InputsBlock } from './CropNutrientdemandCollapsible.styled';
import {
  Equation,
  DEFAULT_NUMBER_VALUE,
  ELEMENT_N,
  MAX_LIMIT_NUMBER,
  CROP_DEMAND_MAX_DECIMAL_PLACES,
  CropNDemandInputs,
  DEFAULT_INPUT_VALUE,
  Bold,
  CollapsibleContainer,
} from '../../../shared';

type NutrientDemandData = CerealsCropDemand | FertigationCropDemand;

interface CropNutrientDemandCollapsibleProps {
  nutrientDemandModule: DemandCalculationModule;
  selectedNutrient?: Nutrient;
  units: BaseUnit[] | undefined;
  isModuleExpanded: boolean;
  configType: FeatureConfigOptions;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const CropNutrientDemandCollapsible = ({
  nutrientDemandModule,
  selectedNutrient,
  units,
  isModuleExpanded,
  setCurrentModulesAccordionState,
  configType,
}: CropNutrientDemandCollapsibleProps) => {
  const {
    methods: { updateDemandCalculationModule },
  } = useAppContext();

  const { CEREAL, FERTIGATION } = FeatureConfigOptions;
  const { NutrientRemoval, NutrientUptake } = CropNDemandInputs;

  const keyPrefix = useMemo(
    () =>
      `polaris.${
        configType === CEREAL ? 'cmmmDetails' : 'fpDetails'
      }.demandCalculations.cropNutrientDemand`,
    [configType],
  );
  const { t } = useTranslation('polaris', {
    keyPrefix,
  });

  const { trigger: triggerUpdateModule } = useUpdateDemandCalculationModule(
    configType,
    nutrientDemandModule.id,
  );

  const nutrientDemandData: NutrientDemandData = useMemo(() => {
    const data = nutrientDemandModule.configuration.data;
    if ('uptakeValue' in data && 'uptakeUnitId' in data) {
      return data;
    }

    throw new Error('Invalid nutrient demand data shape');
  }, [nutrientDemandModule.configuration.data, configType]);

  const elementalName: string = useMemo(() => {
    return selectedNutrient?.elementalName || 'nutrient';
  }, [selectedNutrient]);
  const textSulfix: string = useMemo(() => {
    if (configType === FERTIGATION) return '';

    return elementalName === ELEMENT_N ? elementalName : 'Default';
  }, [elementalName]);

  const [cropNutrientDemandValues, setCropNutrientDemandValues] = useState<
    Record<CropNDemandInputs, number | string>
  >({
    [NutrientRemoval]: nutrientDemandData.removalValue || DEFAULT_NUMBER_VALUE,
    [NutrientUptake]: nutrientDemandData.uptakeValue || DEFAULT_NUMBER_VALUE,
  });

  useEffect(() => {
    setCropNutrientDemandValues({
      [NutrientRemoval]: nutrientDemandData.removalValue || DEFAULT_NUMBER_VALUE,
      [NutrientUptake]: nutrientDemandData.uptakeValue || DEFAULT_NUMBER_VALUE,
    });
  }, [nutrientDemandData]);

  const removalUnit = units?.find((unit) => unit.id === nutrientDemandData.removalUnitId)?.name;
  const uptakeUnit = units?.find((unit) => unit.id === nutrientDemandData.uptakeUnitId)?.name;

  const decimalValue = configType === CEREAL ? 3 : CROP_DEMAND_MAX_DECIMAL_PLACES;

  const handleChangeValues = (value: string, name: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;

    const decimalValue = configType === CEREAL ? 3 : CROP_DEMAND_MAX_DECIMAL_PLACES;
    const validatedValue = formatValidDecimalsNumber(value, decimalValue);
    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;

    setCropNutrientDemandValues((prev) => ({
      ...prev,
      [name]: validatedValue === '' ? DEFAULT_INPUT_VALUE : validatedValue,
    }));
  };

  const handleUpdateValues = async (prop: string) => {
    const cropNDemandKey: CropNDemandInputs | null =
      CropNDemandInputs.NutrientRemoval === prop || CropNDemandInputs.NutrientUptake === prop
        ? prop
        : null;

    if (cropNDemandKey) {
      const currentValue = cropNutrientDemandValues[cropNDemandKey].toString();
      const isValid = validateParameterNumber(currentValue);
      if (!isValid) return;

      const validatedValue = formatValidDecimalsNumber(currentValue, decimalValue);

      const sanitizedValue = isNaN(Number(validatedValue))
        ? DEFAULT_NUMBER_VALUE
        : Number(validatedValue);

      if (nutrientDemandData?.[cropNDemandKey] === sanitizedValue) return;

      const newNutrientDemandModule = cloneDeep(nutrientDemandModule);
      const newNutrientDemandData = {
        ...newNutrientDemandModule.configuration.data,
        [cropNDemandKey]: sanitizedValue,
      };
      newNutrientDemandModule.configuration.data = newNutrientDemandData;
      try {
        const updatedNDemandModule = await triggerUpdateModule({
          method: METHOD.PUT,
          body: JSON.stringify(newNutrientDemandModule),
        });
        updatedNDemandModule && updateDemandCalculationModule(newNutrientDemandModule);
      } catch (error) {
        console.error('Failed to update Crop Nutrient Demand module:', error);
      }
    }
  };

  const limitInputCharacters = (e: React.KeyboardEvent<HTMLInputElement>) => {
    ['ArrowUp', 'ArrowDown'].includes(e.key) && e.preventDefault();
  };

  return (
    <CollapsibleContainer
      open={isModuleExpanded}
      className='collapsible-section'
      data-cy='crop-nutrient-demand-collapsible'
      header={
        <div>
          <Title size='s'>{t('header.title', { nutrient: elementalName })}</Title>
          <Subtitle size='xs'>
            {t(`header.subtitle${textSulfix}`, {
              nutrient: elementalName,
            })}
          </Subtitle>
        </div>
      }
      onOpenChange={() =>
        setCurrentModulesAccordionState((prev) => {
          const modules = {
            ...prev.accordionState,
            [nutrientDemandModule.name]: !isModuleExpanded,
          };
          return {
            nutrientId: prev.nutrientId,
            accordionState: modules,
          };
        })
      }
    >
      <Equation
        title={t('equation.title')}
        equation={
          <Trans
            i18nKey={t(`equation.body${textSulfix}`, {
              nutrient: elementalName,
            })}
            t={() =>
              t(`equation.body${textSulfix}`, {
                nutrient: elementalName,
              })
            }
            components={{
              bold: <Bold />,
            }}
          />
        }
        dataCy='crop-nutrient-demand-equation'
        comment={t('equation.description')}
      />
      <InputsBlock>
        <Subtitle size='s' css={{ display: 'flex', gap: '$x2', alignItems: 'center' }}>
          {t('inputs.subtitle')}
          {configType === CEREAL && elementalName !== ELEMENT_N && (
            <Tooltip
              data-cy='tooltip'
              concept='inverse'
              maxWidth={315}
              minWidth={315}
              position='right'
              text={t('inputs.tooltip')}
            >
              <AhuaIcon colorConcept='neutral' icon='Info' iconSize='x4' />
            </Tooltip>
          )}
        </Subtitle>
        <InputContainer>
          <Input
            size='s'
            label={t('inputs.label.nutrientUptake', {
              nutrient: elementalName,
              unit: uptakeUnit,
            })}
            name={NutrientUptake}
            aria-label={NutrientUptake}
            data-cy={`crop-nutrient-demand-${NutrientUptake}-input`}
            value={cropNutrientDemandValues[NutrientUptake] || DEFAULT_NUMBER_VALUE}
            onChange={({ target: { value, name } }) =>
              handleChangeValues(value.replace(/\s/g, ''), name)
            }
            onBlur={({ target: { name } }) => handleUpdateValues(name)}
            onKeyDown={limitInputCharacters}
          />
          {(elementalName !== ELEMENT_N || configType === FERTIGATION) && (
            <Input
              size='s'
              label={t('inputs.label.nutrientRemoval', {
                nutrient: elementalName,
                unit: removalUnit,
              })}
              name={NutrientRemoval}
              aria-label={NutrientRemoval}
              data-cy={`crop-nutrient-demand-${NutrientRemoval}-input`}
              value={cropNutrientDemandValues[NutrientRemoval] || DEFAULT_NUMBER_VALUE}
              onChange={({ target: { value, name } }) => {
                handleChangeValues(value.replace(/\s/g, ''), name);
              }}
              onBlur={({ target: { name } }) => {
                handleUpdateValues(name);
              }}
              onKeyDown={limitInputCharacters}
            />
          )}
        </InputContainer>
      </InputsBlock>
    </CollapsibleContainer>
  );
};
