import React from 'react';
import { setupServer } from 'msw/node';
import userEvent from '@testing-library/user-event';
import { fireEvent, render } from '@testing-library/react';
import {
  cropNutrientDemandUnitsMock,
  mockAppProviderValue,
  nutrientMock,
  updateFertigationDemandCalculationCNDModuleHandler,
  updateFertigationDemandCalculationHandler,
  updateFertigationDemandCalculationModuleHandler,
  mockNutrientDemandModule,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { BrowserRouter } from 'react-router-dom';
import { CropNutrientDemandCollapsible } from '../CropNutrientDemandCollapsible';
import { FeatureConfigOptions } from '@common/types';

const server = setupServer(
  updateFertigationDemandCalculationModuleHandler,
  updateFertigationDemandCalculation<PERSON><PERSON><PERSON>,
  updateFertigationDemandCalculationCNDModuleHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

describe('Crop Nutrient Demand collapsible component', () => {
  const user = userEvent.setup();

  it('renders without crashing', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <BrowserRouter>
            <AppContext.Consumer>
              {() => (
                <CropNutrientDemandCollapsible
                  nutrientDemandModule={mockNutrientDemandModule}
                  units={cropNutrientDemandUnitsMock}
                  selectedNutrient={nutrientMock}
                  isModuleExpanded={true}
                  setCurrentModulesAccordionState={jest.fn}
                  configType={FeatureConfigOptions.CEREAL}
                />
              )}
            </AppContext.Consumer>
          </BrowserRouter>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(component.container).toBeInTheDocument();
    expect(component).toMatchSnapshot();
  });

  it('renders the CropNutrientDemandCollapsible collapsible component', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <BrowserRouter>
            <AppContext.Consumer>
              {() => (
                <CropNutrientDemandCollapsible
                  nutrientDemandModule={mockNutrientDemandModule}
                  units={cropNutrientDemandUnitsMock}
                  selectedNutrient={nutrientMock}
                  isModuleExpanded={true}
                  setCurrentModulesAccordionState={jest.fn}
                  configType={FeatureConfigOptions.CEREAL}
                />
              )}
            </AppContext.Consumer>
          </BrowserRouter>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(getByTestId('crop-nutrient-demand-collapsible')).toBeInTheDocument();
  });

  it('displays the equation text correctly', () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <BrowserRouter>
            <CropNutrientDemandCollapsible
              nutrientDemandModule={mockNutrientDemandModule}
              units={cropNutrientDemandUnitsMock}
              selectedNutrient={nutrientMock}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
              configType={FeatureConfigOptions.FERTIGATION}
            />
          </BrowserRouter>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(getByTestId('crop-nutrient-demand-equation')).toBeInTheDocument();
    expect(getByTestId('crop-nutrient-demand-equation-content')).toBeInTheDocument();
  });

  it('renders the input fields with correct data attributes', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <BrowserRouter>
            <CropNutrientDemandCollapsible
              nutrientDemandModule={mockNutrientDemandModule}
              units={cropNutrientDemandUnitsMock}
              selectedNutrient={nutrientMock}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
              configType={FeatureConfigOptions.FERTIGATION}
            />
          </BrowserRouter>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component.getByRole('textbox', { name: 'removalValue' })).toBeInTheDocument();
    expect(component.getByRole('textbox', { name: 'uptakeValue' })).toBeInTheDocument();
  });

  it('triggers onChange and onBlur events correctly for input fields', async () => {
    const { getByRole } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <BrowserRouter>
            <CropNutrientDemandCollapsible
              nutrientDemandModule={mockNutrientDemandModule}
              units={cropNutrientDemandUnitsMock}
              selectedNutrient={nutrientMock}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
              configType={FeatureConfigOptions.FERTIGATION}
            />
          </BrowserRouter>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const input = getByRole('textbox', { name: 'removalValue' });
    await user.type(input, '50');
    fireEvent.blur(input);

    expect(input).toHaveValue('50');
  });
});
