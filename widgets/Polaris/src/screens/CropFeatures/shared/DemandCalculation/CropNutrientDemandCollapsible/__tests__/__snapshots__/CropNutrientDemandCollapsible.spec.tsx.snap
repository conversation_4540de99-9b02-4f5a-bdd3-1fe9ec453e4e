// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Crop Nutrient Demand collapsible component renders without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eNnZw c-bFZoXt collapsible-section"
        data-cy="crop-nutrient-demand-collapsible"
        data-state="open"
      >
        <button
          aria-controls="radix-:r0:"
          aria-expanded="true"
          class="c-cUgXyc"
          data-state="open"
          type="button"
        >
          <div>
            <h1
              class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
            >
              header.title
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-iPJLV-css"
            >
              header.subtitleDefault
            </h2>
          </div>
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 15.75l-5-6h-2l-5 6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="open"
          id="radix-:r0:"
          style="transition-duration: 0s; animation-name: none;"
        >
          <div
            class="c-dRXZPK"
            data-cy="crop-nutrient-demand-equation"
          >
            <h2
              class="c-hHXobm"
              data-cy="crop-nutrient-demand-equation-title"
            >
              equation.title
            </h2>
            <div
              class="c-hKYIrC"
              data-cy="crop-nutrient-demand-equation-content-wrapper"
            >
              <p
                class="c-fzsHAk"
                data-cy="crop-nutrient-demand-equation-content"
              />
            </div>
            <p
              class="c-fttsAu"
              data-cy="crop-nutrient-demand-equation-comment"
            >
              equation.description
            </p>
          </div>
          <div
            class="c-kgdOqo"
          >
            <h2
              class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ickdgZW-css"
            >
              inputs.subtitle
              <svg
                class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                data-state="closed"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                />
              </svg>
            </h2>
            <div
              class="c-ijoMEM"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  aria-label="uptakeValue"
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                  data-cy="crop-nutrient-demand-uptakeValue-input"
                  name="uptakeValue"
                  placeholder="inputs.label.nutrientUptake"
                  value="0.222"
                />
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                >
                  inputs.label.nutrientUptake
                </label>
                <span
                  class="c-fcBbhr"
                />
              </div>
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  aria-label="removalValue"
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                  data-cy="crop-nutrient-demand-removalValue-input"
                  name="removalValue"
                  placeholder="inputs.label.nutrientRemoval"
                  value="0"
                />
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                >
                  inputs.label.nutrientRemoval
                </label>
                <span
                  class="c-fcBbhr"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eNnZw c-bFZoXt collapsible-section"
      data-cy="crop-nutrient-demand-collapsible"
      data-state="open"
    >
      <button
        aria-controls="radix-:r0:"
        aria-expanded="true"
        class="c-cUgXyc"
        data-state="open"
        type="button"
      >
        <div>
          <h1
            class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
          >
            header.title
          </h1>
          <h2
            class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-iPJLV-css"
          >
            header.subtitleDefault
          </h2>
        </div>
        <div
          class="c-irPLE"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18 15.75l-5-6h-2l-5 6"
            />
          </svg>
        </div>
      </button>
      <div
        class="c-fLVWxk"
        data-state="open"
        id="radix-:r0:"
        style="transition-duration: 0s; animation-name: none;"
      >
        <div
          class="c-dRXZPK"
          data-cy="crop-nutrient-demand-equation"
        >
          <h2
            class="c-hHXobm"
            data-cy="crop-nutrient-demand-equation-title"
          >
            equation.title
          </h2>
          <div
            class="c-hKYIrC"
            data-cy="crop-nutrient-demand-equation-content-wrapper"
          >
            <p
              class="c-fzsHAk"
              data-cy="crop-nutrient-demand-equation-content"
            />
          </div>
          <p
            class="c-fttsAu"
            data-cy="crop-nutrient-demand-equation-comment"
          >
            equation.description
          </p>
        </div>
        <div
          class="c-kgdOqo"
        >
          <h2
            class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ickdgZW-css"
          >
            inputs.subtitle
            <svg
              class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
              data-state="closed"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
              />
            </svg>
          </h2>
          <div
            class="c-ijoMEM"
          >
            <div
              class="c-gJoajD c-gJoajD-ifGHEql-css"
            >
              <input
                aria-label="uptakeValue"
                class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                data-cy="crop-nutrient-demand-uptakeValue-input"
                name="uptakeValue"
                placeholder="inputs.label.nutrientUptake"
                value="0.222"
              />
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
              >
                inputs.label.nutrientUptake
              </label>
              <span
                class="c-fcBbhr"
              />
            </div>
            <div
              class="c-gJoajD c-gJoajD-ifGHEql-css"
            >
              <input
                aria-label="removalValue"
                class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                data-cy="crop-nutrient-demand-removalValue-input"
                name="removalValue"
                placeholder="inputs.label.nutrientRemoval"
                value="0"
              />
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
              >
                inputs.label.nutrientRemoval
              </label>
              <span
                class="c-fcBbhr"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
