import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountriesHandler,
  updateCropRegionHandler,
  cerealSoilAnalysisNutrientClassificationsHandler,
  allUnitsHandler,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
  cerealSoilAnalysisConfigurationHandler,
  deleteCerealSoilAnalysisNutrientClassificationHandler,
  updateFertigationDemandCalculationModuleHandler,
  mockCMMMAppProviderValue,
  mockCerealsSoilCorrectionFactorModule,
  soilCorrectionFactorMockData,
} from '@common/mocks';

import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { SoilCorrectionFactorCollapsible } from '../SoilCorrectionFactorCollapsible';
import { FeatureConfigOptions } from '@common/types';

const server = setupServer(
  updateCropRegionHandler,
  unitCountriesHandler,
  allUnits<PERSON>andler,
  cerealSoilAnalysisNutrientClassificationsHandler,
  updateCerealSoilAnalysisNutrientClassificationLowHandler,
  cerealSoilAnalysisConfigurationHandler,
  deleteCerealSoilAnalysisNutrientClassificationHandler,
  updateFertigationDemandCalculationModuleHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

describe('MultiplierCorrectionFactorTable', () => {
  it('should render without crashing', async () => {
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SoilCorrectionFactorCollapsible
          keyPrefix='keyPrefix'
          soilCorrectionFactorModule={soilCorrectionFactorMockData}
          isModuleExpanded={true}
          setCurrentModulesAccordionState={jest.fn}
          configType={FeatureConfigOptions.CEREAL}
        />
      </AppContext.Provider>,
    );
    expect(screen.getByTestId('soil-correction-factor-collapsible')).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should match snapshot', () => {
    const { asFragment } = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SoilCorrectionFactorCollapsible
          keyPrefix='keyPrefix'
          soilCorrectionFactorModule={mockCerealsSoilCorrectionFactorModule}
          isModuleExpanded={true}
          setCurrentModulesAccordionState={jest.fn}
          configType={FeatureConfigOptions.CEREAL}
        />
      </AppContext.Provider>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should click checkbox', async () => {
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SoilCorrectionFactorCollapsible
          keyPrefix='keyPrefix'
          soilCorrectionFactorModule={mockCerealsSoilCorrectionFactorModule}
          isModuleExpanded={true}
          setCurrentModulesAccordionState={jest.fn}
          configType={FeatureConfigOptions.CEREAL}
        />
      </AppContext.Provider>,
    );

    waitFor(() => {
      const dataCYStatus = 'is-enable-soil-correction-factor-status';
      expect(component.getByTestId(dataCYStatus)).toBeInTheDocument();
      const dataCyCheckbox = 'soil-correction-factor-enable-for-use-checkbox';
      const checkbox = screen.getByTestId(dataCyCheckbox);
      fireEvent.click(checkbox);
      expect(checkbox).toBeChecked();
      const dataCyCheckboxLabel = 'soil-correction-factor-enable-for-use-checkbox-label';
      expect(component.getByTestId(dataCyCheckboxLabel)).toBeInTheDocument();
    });
  });

  it('should be renedered radio buttons for miltiplier and fixed quantity correction', async () => {
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SoilCorrectionFactorCollapsible
          keyPrefix='keyPrefix'
          soilCorrectionFactorModule={mockCerealsSoilCorrectionFactorModule}
          isModuleExpanded={true}
          setCurrentModulesAccordionState={jest.fn}
          configType={FeatureConfigOptions.CEREAL}
        />
      </AppContext.Provider>,
    );

    waitFor(() => {
      const dataCyMultiplierCorrectionBtn = component.getByTestId(
        'multiplier-correction-type-button',
      );
      expect(dataCyMultiplierCorrectionBtn).toBeInTheDocument();
      const dataCyFixedQtyCorrectionBtn = component.getByTestId('fixed-qty-correction-button');
      expect(dataCyFixedQtyCorrectionBtn).toBeInTheDocument();
      fireEvent.click(dataCyFixedQtyCorrectionBtn);
      expect(dataCyFixedQtyCorrectionBtn).toBeChecked();
    });
  });
});
