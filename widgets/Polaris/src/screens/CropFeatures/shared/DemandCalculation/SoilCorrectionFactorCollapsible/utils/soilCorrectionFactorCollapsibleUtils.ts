import { CerealsSoilCorrectionFactor, FertigationSoilCorrectionFactor } from '@common/types';

export const getCerealsSoilCorrectionFactorData = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any,
): CerealsSoilCorrectionFactor | undefined => {
  if (
    typeof data === 'object' &&
    'isFixed' in data &&
    'multiplierCorrection' in data &&
    'fixedQuantityCorrection' in data
  ) {
    return data;
  }
};

export const getFertigationSoilCorrectionFactorData = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any,
): FertigationSoilCorrectionFactor | undefined => {
  if (typeof data === 'object' && 'multiplierCorrection' in data) {
    return data;
  }
};
