import { ORDER } from '@common/constants';
import {
  CerealsFixedQuantityCorrectionItem,
  CerealsMultipleCorrectionItem,
  MultipleCorrectionItem,
} from '@common/types';
import {
  CellContainer,
  Circle,
  Level,
} from '@widgets/Polaris/src/components/ParameterLevelCell/ParameterLevelCell.styled';
import { Input, Table } from '@yaradigitallabs/ahua-react';
import React, { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { StyledTable, TableContainer, TableRow } from './SoilCorrectionFactorCollapsible.styled';
import { SoilCorrectionFactorTypes } from './SoilCorrectionFactorCollapsible';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { filterUnitsById } from '@polaris-hooks/index';
import { DEFAULT_INPUT_VALUE } from '../../constants';

type CorrectionItem = CerealsMultipleCorrectionItem | MultipleCorrectionItem;
interface CorrectionFactorTableProps {
  keyPrefix: string;
  correctionType?: SoilCorrectionFactorTypes;
  parameterLevelData: CorrectionItem[] | CerealsFixedQuantityCorrectionItem[];
  onParameterLevelBlur: (paramToUpdateId: string, newMultiplierVal: string) => void;
  onParameterLevelChange: (paramToUpdateId: string, newMultiplierVal: string) => void;
}

const defaultFixedUnit = 'kg/ha';

const CorrectionFactorTable: FC<CorrectionFactorTableProps> = ({
  keyPrefix,
  correctionType,
  parameterLevelData,
  onParameterLevelChange,
  onParameterLevelBlur,
}) => {
  const { selectedCountryUnits } = useAppContext();
  const { t } = useTranslation('polaris', {
    keyPrefix: keyPrefix,
  });

  const fixedUnit = useMemo(() => {
    if (correctionType !== SoilCorrectionFactorTypes.FixedQuantityCorrection) {
      return '';
    }

    const firstItem = parameterLevelData[0];

    if (!firstItem || !('quantityCorrectionUnitId' in firstItem)) {
      return defaultFixedUnit;
    }

    return (
      filterUnitsById(selectedCountryUnits, firstItem.quantityCorrectionUnitId)?.name ||
      defaultFixedUnit
    );
  }, [correctionType, selectedCountryUnits, parameterLevelData]);

  const getHeaderParamType = (): 'Fixed' | 'Multiplier' | undefined => {
    if (!correctionType) return undefined;

    return correctionType === SoilCorrectionFactorTypes.FixedQuantityCorrection
      ? 'Fixed'
      : 'Multiplier';
  };

  const getParamLevelHeaders = (type?: string) => {
    if (!type) {
      return [{ title: 'parameterLevel', css: { width: '50%' } }, { title: 'multiplier' }];
    }

    return [
      { title: 'parameterLevel' },
      { title: 'fertilisationStrategy' },
      { title: `correctionType${type}`, css: { width: 'auto !important' } },
    ];
  };

  const headerType = getHeaderParamType();
  const headers = getParamLevelHeaders(headerType);

  return (
    <TableContainer data-cy='parameter-levels-table-container'>
      <StyledTable>
        <thead>
          <TableRow>
            {headers.map(({ title, css }, i) => (
              <Table.Head css={css} key={title + i}>
                {t(`header.${title}`, {
                  unit: fixedUnit,
                })}
              </Table.Head>
            ))}
          </TableRow>
        </thead>
        <tbody>
          {parameterLevelData &&
            parameterLevelData.map((paramLevel, index) => (
              <Table.Row key={`${index}_${paramLevel?.id}`} className='table-body-row'>
                <Table.Cell>
                  <CellContainer data-cy={`${paramLevel.parameterLevel}-cell`}>
                    <Circle
                      variant={ORDER[paramLevel.parameterLevel]}
                      data-cy={`${paramLevel.parameterLevel}-colored-circle`}
                    />
                    <Level data-cy={`${paramLevel.parameterLevel}-level-text`}>
                      {paramLevel.parameterLevel}
                    </Level>
                  </CellContainer>
                </Table.Cell>
                {paramLevel && 'fertilisationStrategy' in paramLevel && (
                  <Table.Cell>
                    <CellContainer data-cy={`fertilisation-strategy-cell-${index}`}>
                      {paramLevel.fertilisationStrategy}
                    </CellContainer>
                  </Table.Cell>
                )}
                <Table.Cell>
                  <Input
                    style={{ textAlign: 'left' }}
                    size='xs'
                    value={
                      paramLevel
                        ? 'quantityCorrectionValue' in paramLevel
                          ? paramLevel.quantityCorrectionValue ?? DEFAULT_INPUT_VALUE
                          : paramLevel.multiplierValue ?? DEFAULT_INPUT_VALUE
                        : DEFAULT_INPUT_VALUE
                    }
                    tabIndex={index}
                    onChange={({ target: { value } }) => {
                      const valueWithRemovedWhiteSpaces = value.replace(/\s/g, '');
                      onParameterLevelChange(paramLevel.id, valueWithRemovedWhiteSpaces);
                    }}
                    onBlur={({ target: { value } }) => {
                      onParameterLevelBlur(paramLevel.id, value);
                    }}
                    data-cy='correction-input'
                    type='text'
                  />
                </Table.Cell>
              </Table.Row>
            ))}
        </tbody>
      </StyledTable>
    </TableContainer>
  );
};

export default CorrectionFactorTable;
