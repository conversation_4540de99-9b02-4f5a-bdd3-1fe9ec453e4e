import React, { Dispatch, FC, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckBox, Subtitle, Title } from '@yaradigitallabs/ahua-react';
import {
  collapsibleSubtitleStyles,
  NutritionParameterContainer,
} from '@widgets/Polaris/src/components/ConfigurationsAnalysisMethodCollapsible/ConfigurationsAnalysisMethodCollapsible.styled';
import { CheckBoxWrapper, StyledCheckBoxLabel } from './SoilCorrectionFactorCollapsible.styled';
import {
  formatNegativeOrPositiveDecimalsNumber,
  formatValidDecimalsNumber,
  validateParameterNumber,
} from '@widgets/Polaris/utils';
import {
  DemandCalculationModule,
  ModuleNameToAccordionNutrientState,
  FeatureConfigOptions,
  CerealsSoilCorrectionFactor,
  FertigationSoilCorrectionFactor,
} from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { useUpdateDemandCalculationModule } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import CorrectionFactorTable from './CorrectionFactorTable';
import { cloneDeep } from 'lodash';

import {
  HeaderForCollapsible,
  CollapsibleNoPaddingContainer,
  RadioButtonGroup,
  BtnTypes,
  MAX_LIMIT_NUMBER,
  MIN_LIMIT_NUMBER,
} from '../..';
import {
  getCerealsSoilCorrectionFactorData,
  getFertigationSoilCorrectionFactorData,
} from './utils/soilCorrectionFactorCollapsibleUtils';

interface SoilCorrectionFactorCollapsibleProps {
  keyPrefix: string;
  soilCorrectionFactorModule: DemandCalculationModule;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
  configType: FeatureConfigOptions;
}

type SoilCorrectionFactorType = CerealsSoilCorrectionFactor | FertigationSoilCorrectionFactor;
export enum SoilCorrectionFactorTypes {
  MultiplierCorrection = 'multiplierCorrection',
  FixedQuantityCorrection = 'fixedQuantityCorrection',
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isSoilCorrectionFactorType(data: any): data is SoilCorrectionFactorType {
  return data && SoilCorrectionFactorTypes.MultiplierCorrection in data;
}

function isCerealsSoilCorrectionFactor(data: unknown): data is CerealsSoilCorrectionFactor {
  return (
    !!data &&
    typeof data === 'object' &&
    SoilCorrectionFactorTypes.MultiplierCorrection in data &&
    SoilCorrectionFactorTypes.FixedQuantityCorrection in data
  );
}

export const SoilCorrectionFactorCollapsible: FC<SoilCorrectionFactorCollapsibleProps> = ({
  keyPrefix,
  soilCorrectionFactorModule,
  isModuleExpanded,
  setCurrentModulesAccordionState,
  configType,
}) => {
  const tKeyPrefix = `${keyPrefix}.soilCorrectionFactor`;
  const { t } = useTranslation('polaris', { keyPrefix: tKeyPrefix });
  const [soilCorrectionFactorEnabled, setSoilCorrectionFactorEnabled] = useState<boolean>(
    soilCorrectionFactorModule.isEnabled,
  );
  const { CEREAL, FERTIGATION } = FeatureConfigOptions;
  const [hasChange, setHasChange] = useState<boolean>(false);
  const [selectedType, setSelectedType] = useState<SoilCorrectionFactorTypes | undefined>();

  const demandData: SoilCorrectionFactorType | undefined = useMemo(() => {
    const data = soilCorrectionFactorModule.configuration.data;
    if (isSoilCorrectionFactorType(data)) {
      return { ...data };
    }
    return undefined;
  }, [soilCorrectionFactorModule]);

  const [soilCorrectionFactorDataState, setSoilCorrectionFactorDataState] =
    useState<SoilCorrectionFactorType>();

  const {
    methods: { updateDemandCalculationModule },
  } = useAppContext();

  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    configType,
    soilCorrectionFactorModule.id,
  );

  useEffect(() => {
    if (!soilCorrectionFactorModule || !demandData) return;

    setSoilCorrectionFactorEnabled(soilCorrectionFactorModule.isEnabled);

    setSoilCorrectionFactorDataState(demandData);

    if (isCerealsSoilCorrectionFactor(demandData)) {
      setSelectedType(
        demandData.isFixed
          ? SoilCorrectionFactorTypes.FixedQuantityCorrection
          : SoilCorrectionFactorTypes.MultiplierCorrection,
      );
    }
  }, [demandData, soilCorrectionFactorModule]);

  const correctionMethod = useMemo(() => {
    return selectedType === SoilCorrectionFactorTypes.MultiplierCorrection
      ? 'multiplierCorrection'
      : 'fixedQuantityCorrection';
  }, [selectedType]);

  const correctionValueKey = useMemo(() => {
    return selectedType === SoilCorrectionFactorTypes.MultiplierCorrection
      ? 'multiplierValue'
      : 'quantityCorrectionValue';
  }, [selectedType]);

  const defaultRadioBtnsType = useMemo(() => {
    return selectedType === SoilCorrectionFactorTypes.MultiplierCorrection
      ? BtnTypes.LeftType
      : BtnTypes.RightType;
  }, [selectedType]);

  const onParameterLevelChange = (paramToUpdateId: string, newVal: string) => {
    const isValid = validateParameterNumber(newVal) || newVal === '-';
    if (!isValid) return;

    let validatedValue;
    if (selectedType === SoilCorrectionFactorTypes.FixedQuantityCorrection) {
      validatedValue = formatNegativeOrPositiveDecimalsNumber(newVal, 3);
    } else {
      validatedValue = formatValidDecimalsNumber(newVal, 3);
    }

    const isExceedingLimit = Boolean(
      Number(newVal) > MAX_LIMIT_NUMBER || Number(newVal) < MIN_LIMIT_NUMBER,
    );
    if (isExceedingLimit) return;

    if (isCerealsSoilCorrectionFactor(demandData)) {
      setSoilCorrectionFactorDataState((prevState) => {
        if (!prevState || !isCerealsSoilCorrectionFactor(prevState)) {
          prevState = {
            isFixed: false,
            multiplierCorrection: [],
            fixedQuantityCorrection: [],
          };
        }
        return {
          ...prevState,
          isFixed: prevState.isFixed ?? false,
          multiplierCorrection: prevState.multiplierCorrection ?? [],
          fixedQuantityCorrection: prevState.fixedQuantityCorrection ?? [],
          [correctionMethod]: (prevState[correctionMethod] ?? []).map((param) => {
            if (param.id === paramToUpdateId) {
              return { ...param, [correctionValueKey]: validatedValue };
            } else {
              return param;
            }
          }),
        };
      });
    } else {
      setSoilCorrectionFactorDataState((prevState) => {
        if (!prevState) {
          prevState = {
            multiplierCorrection: [],
          };
        }
        return {
          multiplierCorrection: prevState.multiplierCorrection.map((param) => {
            if (param.id === paramToUpdateId) {
              return {
                ...param,
                multiplierValue: validatedValue === '' ? 0 : validatedValue,
              };
            } else {
              return param;
            }
          }),
        };
      });
    }
    setHasChange(true);
  };

  const onParameterLevelBlur = async (paramToUpdateId: string, newVal: string) => {
    if (!hasChange) return;

    let newCorrectionFactor: SoilCorrectionFactorType;
    if (isCerealsSoilCorrectionFactor(demandData)) {
      const cerealsSoilCorrectionFactor = getCerealsSoilCorrectionFactorData(
        soilCorrectionFactorDataState,
      );
      newCorrectionFactor = {
        ...cloneDeep(cerealsSoilCorrectionFactor),
        isFixed: Boolean(cerealsSoilCorrectionFactor?.isFixed),
        multiplierCorrection: cerealsSoilCorrectionFactor?.multiplierCorrection ?? [],
        fixedQuantityCorrection: cerealsSoilCorrectionFactor?.fixedQuantityCorrection ?? [],
        [correctionMethod]: (cerealsSoilCorrectionFactor?.[correctionMethod] ?? []).map((param) =>
          param.id === paramToUpdateId
            ? { ...param, [correctionValueKey]: Number(newVal) || 0 }
            : param,
        ),
      };
    } else {
      const fertigationSoilCorrectionFactor = getFertigationSoilCorrectionFactorData(
        soilCorrectionFactorDataState,
      );
      newCorrectionFactor = {
        multiplierCorrection:
          fertigationSoilCorrectionFactor?.multiplierCorrection.map((param) => {
            if (param.id === paramToUpdateId) {
              return { ...param, multiplierValue: Number(newVal) };
            } else {
              return {
                ...param,
                multiplierValue: Number(param.multiplierValue),
              };
            }
          }) || [],
      };
    }
    const newState: DemandCalculationModule = {
      ...cloneDeep(soilCorrectionFactorModule),
      configuration: { data: newCorrectionFactor },
    };
    await soilCorrectionFactorUpdate(newState);
    setHasChange(false);
  };

  const onCheckboxClick = async () => {
    const newState: DemandCalculationModule = {
      ...soilCorrectionFactorModule,
      isEnabled: !soilCorrectionFactorModule.isEnabled,
    };
    await soilCorrectionFactorUpdate(newState);
    setSoilCorrectionFactorEnabled(!soilCorrectionFactorModule.isEnabled);
  };

  const onCorrectionFactorTypeChanged = async (btnType: BtnTypes) => {
    if (!isCerealsSoilCorrectionFactor(soilCorrectionFactorDataState)) return;

    let value: SoilCorrectionFactorTypes;
    if (btnType === BtnTypes.LeftType) {
      value = SoilCorrectionFactorTypes.MultiplierCorrection;
    } else {
      value = SoilCorrectionFactorTypes.FixedQuantityCorrection;
    }

    const newCorrectionFactor: CerealsSoilCorrectionFactor = {
      ...cloneDeep(soilCorrectionFactorDataState),
      isFixed: value === SoilCorrectionFactorTypes.FixedQuantityCorrection,
      multiplierCorrection: cloneDeep(soilCorrectionFactorDataState?.multiplierCorrection) || [],
      fixedQuantityCorrection:
        cloneDeep(soilCorrectionFactorDataState?.fixedQuantityCorrection) || [],
    };
    const newState: DemandCalculationModule = {
      ...cloneDeep(soilCorrectionFactorModule),
      configuration: { data: newCorrectionFactor },
    };

    await soilCorrectionFactorUpdate(newState);

    if (isCerealsSoilCorrectionFactor(demandData)) {
      setSelectedType(value);
    }
  };

  const soilCorrectionFactorUpdate = async (newState: DemandCalculationModule) => {
    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(newState),
      });
      if (updatedModule) {
        updateDemandCalculationModule(updatedModule);
        if (isSoilCorrectionFactorType(newState.configuration.data)) {
          setSoilCorrectionFactorDataState(newState.configuration.data);
        }
      }
    } catch (error) {
      console.error('Failed to update soil correction factor:', error);
    }
  };

  return (
    <CollapsibleNoPaddingContainer
      data-cy='soil-correction-factor-collapsible'
      open={isModuleExpanded}
      className='collapsible-section'
      header={
        <HeaderForCollapsible
          title={t('title')}
          subtitle={t('subtitle')}
          isStatusEnabled={soilCorrectionFactorEnabled}
        />
      }
      onOpenChange={() =>
        setCurrentModulesAccordionState((prev) => {
          const modules = {
            ...prev.accordionState,
            [soilCorrectionFactorModule.name]: !isModuleExpanded,
          };
          return {
            nutrientId: prev.nutrientId,
            accordionState: modules,
          };
        })
      }
    >
      <CheckBoxWrapper>
        <CheckBox
          data-cy='soil-correction-factor-enable-for-use-checkbox'
          ariaLabel={`Checkbox ${soilCorrectionFactorModule.isEnabled}`}
          checked={soilCorrectionFactorModule.isEnabled}
          onClick={() => onCheckboxClick()}
        />
        <StyledCheckBoxLabel
          data-cy='soil-correction-factor-enable-for-use-checkbox-label'
          size='s'
        >
          {t('checkboxLabel')}
        </StyledCheckBoxLabel>
      </CheckBoxWrapper>
      {configType === CEREAL && (
        <RadioButtonGroup
          title={t('selectMethodTitle')}
          leftLabelText={t('multiplierCorrection')}
          rightLabelText={t('fixedCorrection')}
          defaultType={defaultRadioBtnsType}
          onChange={(value: BtnTypes) => onCorrectionFactorTypeChanged(value)}
        />
      )}
      <NutritionParameterContainer variant='mmm'>
        <Title size='xs'> {t('tableTitle')}</Title>
        <Subtitle css={collapsibleSubtitleStyles} size='xs'>
          {t(
            `tableSubtitle${
              selectedType === SoilCorrectionFactorTypes.MultiplierCorrection
                ? 'Multiplier'
                : 'Fixed'
            }`,
          )}
        </Subtitle>
      </NutritionParameterContainer>

      {soilCorrectionFactorDataState && (
        <CorrectionFactorTable
          keyPrefix={`${tKeyPrefix}.soilCorrectionParamLevelTable`}
          correctionType={selectedType}
          parameterLevelData={
            selectedType === SoilCorrectionFactorTypes.MultiplierCorrection ||
            configType === FERTIGATION
              ? soilCorrectionFactorDataState?.multiplierCorrection
              : isCerealsSoilCorrectionFactor(soilCorrectionFactorDataState)
              ? soilCorrectionFactorDataState.fixedQuantityCorrection
              : []
          }
          onParameterLevelChange={onParameterLevelChange}
          onParameterLevelBlur={onParameterLevelBlur}
        />
      )}
    </CollapsibleNoPaddingContainer>
  );
};
