import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountriesHandler,
  updateCropRegionHandler,
  allUnitsHandler,
  fixedQuantityCorrection,
  multiplierCorrection,
} from '@common/mocks';
import CorrectionFactorTable from '../CorrectionFactorTable';
import { SoilCorrectionFactorTypes } from '../SoilCorrectionFactorCollapsible';

const server = setupServer(updateCropRegionHandler, unitCountriesHandler, allUnitsHandler);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

describe('CorrectionFactorTable', () => {
  it('should render CerealsMultipleCorrectionItems without crashing', async () => {
    const component = render(
      <CorrectionFactorTable
        keyPrefix='correctionFactorTable'
        correctionType={SoilCorrectionFactorTypes.MultiplierCorrection}
        parameterLevelData={multiplierCorrection}
        onParameterLevelChange={jest.fn()}
        onParameterLevelBlur={jest.fn()}
      />,
    );
    expect(screen.getByTestId('parameter-levels-table-container')).toBeInTheDocument();
    expect(screen.getByTestId('LOW-colored-circle')).toBeInTheDocument();
    expect(screen.getByTestId('LOW-level-text')).toBeInTheDocument();
    expect(screen.getByTestId('fertilisation-strategy-cell-0')).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should render CerealsFixedQuantityCorrectionItems without crashing', async () => {
    const component = render(
      <CorrectionFactorTable
        keyPrefix='correctionFactorTable'
        correctionType={SoilCorrectionFactorTypes.FixedQuantityCorrection}
        parameterLevelData={fixedQuantityCorrection}
        onParameterLevelChange={jest.fn()}
        onParameterLevelBlur={jest.fn()}
      />,
    );
    expect(screen.getByTestId('parameter-levels-table-container')).toBeInTheDocument();
    expect(screen.getByTestId('LOW-colored-circle')).toBeInTheDocument();
    expect(screen.getByTestId('LOW-level-text')).toBeInTheDocument();
    expect(screen.getByTestId('fertilisation-strategy-cell-0')).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should update greaterOrEqual input value', async () => {
    render(
      <CorrectionFactorTable
        keyPrefix='correctionFactorTable'
        correctionType={SoilCorrectionFactorTypes.FixedQuantityCorrection}
        parameterLevelData={fixedQuantityCorrection}
        onParameterLevelChange={jest.fn()}
        onParameterLevelBlur={jest.fn()}
      />,
    );

    const correctionInput = screen.getAllByTestId('correction-input') as HTMLInputElement[];

    waitFor(() => {
      fireEvent.change(correctionInput[0], { target: { value: 5 } });
      expect(correctionInput[0].value).toBe(5);

      fireEvent.blur(correctionInput[0]);
      expect(correctionInput[0].value).toBe(5);
    });
  });
});
