import { Label, styled, Table } from '@yaradigitallabs/ahua-react';

export const CheckBoxWrapper = styled('div', {
  padding: '$x6 $x4 $x6 0',
  height: '$x6',
  display: 'flex',
  alignItems: 'center',
});

export const StyledCheckBoxLabel = styled(Label, {
  fontWeight: '$medium',
});

export const TableContainer = styled('div', {
  padding: '0 $x4 $x4 $x4',
});

export const StyledTable = styled(Table, {
  width: '100%',
});

export const TableRow = styled(Table.Row, {
  borderTop: '2px solid $black10 !important',

  th: {
    verticalAlign: 'middle',
  },
});
