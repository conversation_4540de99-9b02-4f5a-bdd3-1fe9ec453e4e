// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MultiplierCorrectionFactorTable should match snapshot 1`] = `
<DocumentFragment>
  <div
    class="c-eNnZw c-bFZoXt c-cXlfGx collapsible-section"
    data-cy="soil-correction-factor-collapsible"
    data-state="open"
  >
    <button
      aria-controls="radix-:r4:"
      aria-expanded="true"
      class="c-cUgXyc"
      data-state="open"
      type="button"
    >
      <div
        class="c-kBfmpw"
      >
        <div>
          <h1
            class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
          >
            title
          </h1>
          <h2
            class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-iPJLV-css"
          >
            subtitle
          </h2>
        </div>
        <div
          class="c-cIigya c-cIigya-eyXUBd-colorType-gray c-cIigya-fYJylb-size-s c-cIigya-iiIClND-css"
          data-cy="is-status-enabled"
        >
          <label
            class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
          >
            statusDisabled
          </label>
        </div>
      </div>
      <div
        class="c-irPLE"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18 15.75l-5-6h-2l-5 6"
          />
        </svg>
      </div>
    </button>
    <div
      class="c-fLVWxk"
      data-state="open"
      id="radix-:r4:"
      style="transition-duration: 0s; animation-name: none;"
    >
      <div
        class="c-iUrglI"
      >
        <div
          class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
        >
          <button
            aria-checked="false"
            aria-label="Checkbox false"
            class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
            data-cy="soil-correction-factor-enable-for-use-checkbox"
            data-state="unchecked"
            role="checkbox"
            type="button"
            value="on"
          />
        </div>
        <label
          class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iqKmYR"
          data-cy="soil-correction-factor-enable-for-use-checkbox-label"
        >
          checkboxLabel
        </label>
      </div>
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
        data-cy="radio-btn-title"
      >
        selectMethodTitle
      </h2>
      <div
        aria-required="false"
        class="c-fixGjY c-jSATwS"
        dir="ltr"
        role="radiogroup"
        style="outline: none;"
        tabindex="0"
      >
        <div
          class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
          data-cy="left-button"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
            for="leftType"
          >
            multiplierCorrection
          </label>
          <div
            class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
          >
            <button
              aria-checked="true"
              class="c-thNAo c-thNAo-bcWJEY-concept-brand"
              data-radix-collection-item=""
              data-state="checked"
              id="leftType"
              role="radio"
              tabindex="-1"
              type="button"
              value="leftType"
            >
              <span
                class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                data-state="checked"
              />
            </button>
          </div>
        </div>
        <div
          class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
          data-cy="right-button"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
            for="RightType"
          >
            fixedCorrection
          </label>
          <div
            class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
          >
            <button
              aria-checked="false"
              class="c-thNAo c-thNAo-bcWJEY-concept-brand"
              data-radix-collection-item=""
              data-state="unchecked"
              id="RightType"
              role="radio"
              tabindex="-1"
              type="button"
              value="RightType"
            />
          </div>
        </div>
      </div>
      <div
        class="c-jdaZYf c-jdaZYf-kFROXp-variant-mmm"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css"
        >
           tableTitle
        </h1>
        <h2
          class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-ijPlcvs-css"
        >
          tableSubtitleMultiplier
        </h2>
      </div>
      <div
        class="c-SsEbC"
        data-cy="parameter-levels-table-container"
      >
        <table
          class="c-kwAGqj c-fGHEql"
        >
          <thead>
            <tr
              class="c-eDGYZe c-bcNRGi"
            >
              <th
                class="c-kxWgPf"
              >
                header.parameterLevel
              </th>
              <th
                class="c-kxWgPf"
              >
                header.fertilisationStrategy
              </th>
              <th
                class="c-kxWgPf c-kxWgPf-iBGLHt-css"
              >
                header.correctionTypeMultiplier
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="VERY_LOW-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                    data-cy="VERY_LOW-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="VERY_LOW-level-text"
                  >
                    VERY_LOW
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-0"
                >
                  Correction
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="0"
                    type="text"
                    value="2.222"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="LOW-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                    data-cy="LOW-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="LOW-level-text"
                  >
                    LOW
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-1"
                >
                  Correction
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="1"
                    type="text"
                    value="1"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="MEDIUM-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                    data-cy="MEDIUM-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="MEDIUM-level-text"
                  >
                    MEDIUM
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-2"
                >
                  Maintenance
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="2"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="HIGH-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                    data-cy="HIGH-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="HIGH-level-text"
                  >
                    HIGH
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-3"
                >
                  Replacement
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="3"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="VERY_HIGH-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-4"
                    data-cy="VERY_HIGH-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="VERY_HIGH-level-text"
                  >
                    VERY_HIGH
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-4"
                >
                  No application
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="4"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MultiplierCorrectionFactorTable should render without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eNnZw c-bFZoXt c-cXlfGx collapsible-section"
        data-cy="soil-correction-factor-collapsible"
        data-state="open"
      >
        <button
          aria-controls="radix-:r0:"
          aria-expanded="true"
          class="c-cUgXyc"
          data-state="open"
          type="button"
        >
          <div
            class="c-kBfmpw"
          >
            <div>
              <h1
                class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
              >
                title
              </h1>
              <h2
                class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-iPJLV-css"
              >
                subtitle
              </h2>
            </div>
            <div
              class="c-cIigya c-cIigya-eyXUBd-colorType-gray c-cIigya-fYJylb-size-s c-cIigya-iiIClND-css"
              data-cy="is-status-enabled"
            >
              <label
                class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
              >
                statusDisabled
              </label>
            </div>
          </div>
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 15.75l-5-6h-2l-5 6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="open"
          id="radix-:r0:"
          style="transition-duration: 0s; animation-name: none;"
        >
          <div
            class="c-iUrglI"
          >
            <div
              class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
            >
              <button
                aria-checked="false"
                aria-label="Checkbox false"
                class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                data-cy="soil-correction-factor-enable-for-use-checkbox"
                data-state="unchecked"
                role="checkbox"
                type="button"
                value="on"
              />
            </div>
            <label
              class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iqKmYR"
              data-cy="soil-correction-factor-enable-for-use-checkbox-label"
            >
              checkboxLabel
            </label>
          </div>
          <h2
            class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
            data-cy="radio-btn-title"
          >
            selectMethodTitle
          </h2>
          <div
            aria-required="false"
            class="c-fixGjY c-jSATwS"
            dir="ltr"
            role="radiogroup"
            style="outline: none;"
            tabindex="0"
          >
            <div
              class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
              data-cy="left-button"
            >
              <label
                class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
                for="leftType"
              >
                multiplierCorrection
              </label>
              <div
                class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
              >
                <button
                  aria-checked="false"
                  class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                  data-radix-collection-item=""
                  data-state="unchecked"
                  id="leftType"
                  role="radio"
                  tabindex="-1"
                  type="button"
                  value="leftType"
                />
              </div>
            </div>
            <div
              class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
              data-cy="right-button"
            >
              <label
                class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
                for="RightType"
              >
                fixedCorrection
              </label>
              <div
                class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
              >
                <button
                  aria-checked="true"
                  class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                  data-radix-collection-item=""
                  data-state="checked"
                  id="RightType"
                  role="radio"
                  tabindex="-1"
                  type="button"
                  value="RightType"
                >
                  <span
                    class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                    data-state="checked"
                  />
                </button>
              </div>
            </div>
          </div>
          <div
            class="c-jdaZYf c-jdaZYf-kFROXp-variant-mmm"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css"
            >
               
              tableTitle
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-ijPlcvs-css"
            >
              tableSubtitleFixed
            </h2>
          </div>
          <div
            class="c-SsEbC"
            data-cy="parameter-levels-table-container"
          >
            <table
              class="c-kwAGqj c-fGHEql"
            >
              <thead>
                <tr
                  class="c-eDGYZe c-bcNRGi"
                >
                  <th
                    class="c-kxWgPf c-kxWgPf-imosJd-css"
                  >
                    header.parameterLevel
                  </th>
                  <th
                    class="c-kxWgPf"
                  >
                    header.multiplier
                  </th>
                </tr>
              </thead>
              <tbody />
            </table>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eNnZw c-bFZoXt c-cXlfGx collapsible-section"
      data-cy="soil-correction-factor-collapsible"
      data-state="open"
    >
      <button
        aria-controls="radix-:r0:"
        aria-expanded="true"
        class="c-cUgXyc"
        data-state="open"
        type="button"
      >
        <div
          class="c-kBfmpw"
        >
          <div>
            <h1
              class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
            >
              title
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-iPJLV-css"
            >
              subtitle
            </h2>
          </div>
          <div
            class="c-cIigya c-cIigya-eyXUBd-colorType-gray c-cIigya-fYJylb-size-s c-cIigya-iiIClND-css"
            data-cy="is-status-enabled"
          >
            <label
              class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
            >
              statusDisabled
            </label>
          </div>
        </div>
        <div
          class="c-irPLE"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18 15.75l-5-6h-2l-5 6"
            />
          </svg>
        </div>
      </button>
      <div
        class="c-fLVWxk"
        data-state="open"
        id="radix-:r0:"
        style="transition-duration: 0s; animation-name: none;"
      >
        <div
          class="c-iUrglI"
        >
          <div
            class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
          >
            <button
              aria-checked="false"
              aria-label="Checkbox false"
              class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
              data-cy="soil-correction-factor-enable-for-use-checkbox"
              data-state="unchecked"
              role="checkbox"
              type="button"
              value="on"
            />
          </div>
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iqKmYR"
            data-cy="soil-correction-factor-enable-for-use-checkbox-label"
          >
            checkboxLabel
          </label>
        </div>
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
          data-cy="radio-btn-title"
        >
          selectMethodTitle
        </h2>
        <div
          aria-required="false"
          class="c-fixGjY c-jSATwS"
          dir="ltr"
          role="radiogroup"
          style="outline: none;"
          tabindex="0"
        >
          <div
            class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
            data-cy="left-button"
          >
            <label
              class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
              for="leftType"
            >
              multiplierCorrection
            </label>
            <div
              class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
            >
              <button
                aria-checked="false"
                class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                data-radix-collection-item=""
                data-state="unchecked"
                id="leftType"
                role="radio"
                tabindex="-1"
                type="button"
                value="leftType"
              />
            </div>
          </div>
          <div
            class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
            data-cy="right-button"
          >
            <label
              class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
              for="RightType"
            >
              fixedCorrection
            </label>
            <div
              class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
            >
              <button
                aria-checked="true"
                class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                data-radix-collection-item=""
                data-state="checked"
                id="RightType"
                role="radio"
                tabindex="-1"
                type="button"
                value="RightType"
              >
                <span
                  class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                  data-state="checked"
                />
              </button>
            </div>
          </div>
        </div>
        <div
          class="c-jdaZYf c-jdaZYf-kFROXp-variant-mmm"
        >
          <h1
            class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css"
          >
             
            tableTitle
          </h1>
          <h2
            class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-ijPlcvs-css"
          >
            tableSubtitleFixed
          </h2>
        </div>
        <div
          class="c-SsEbC"
          data-cy="parameter-levels-table-container"
        >
          <table
            class="c-kwAGqj c-fGHEql"
          >
            <thead>
              <tr
                class="c-eDGYZe c-bcNRGi"
              >
                <th
                  class="c-kxWgPf c-kxWgPf-imosJd-css"
                >
                  header.parameterLevel
                </th>
                <th
                  class="c-kxWgPf"
                >
                  header.multiplier
                </th>
              </tr>
            </thead>
            <tbody />
          </table>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
