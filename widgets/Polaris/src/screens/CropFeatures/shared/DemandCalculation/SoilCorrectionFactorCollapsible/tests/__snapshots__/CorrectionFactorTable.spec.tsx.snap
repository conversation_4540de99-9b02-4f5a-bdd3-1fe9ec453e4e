// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CorrectionFactorTable should render CerealsFixedQuantityCorrectionItems without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-SsEbC"
        data-cy="parameter-levels-table-container"
      >
        <table
          class="c-kwAGqj c-fGHEql"
        >
          <thead>
            <tr
              class="c-eDGYZe c-bcNRGi"
            >
              <th
                class="c-kxWgPf"
              >
                header.parameterLevel
              </th>
              <th
                class="c-kxWgPf"
              >
                header.fertilisationStrategy
              </th>
              <th
                class="c-kxWgPf c-kxWgPf-iBGLHt-css"
              >
                header.correctionTypeFixed
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="VERY_LOW-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                    data-cy="VERY_LOW-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="VERY_LOW-level-text"
                  >
                    VERY_LOW
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-0"
                >
                  Correction
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="0"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="LOW-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                    data-cy="LOW-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="LOW-level-text"
                  >
                    LOW
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-1"
                >
                  Correction
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="1"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="MEDIUM-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                    data-cy="MEDIUM-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="MEDIUM-level-text"
                  >
                    MEDIUM
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-2"
                >
                  Maintenance
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="2"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="HIGH-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                    data-cy="HIGH-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="HIGH-level-text"
                  >
                    HIGH
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-3"
                >
                  Replacement
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="3"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="VERY_HIGH-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-4"
                    data-cy="VERY_HIGH-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="VERY_HIGH-level-text"
                  >
                    VERY_HIGH
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-4"
                >
                  No application
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="4"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-SsEbC"
      data-cy="parameter-levels-table-container"
    >
      <table
        class="c-kwAGqj c-fGHEql"
      >
        <thead>
          <tr
            class="c-eDGYZe c-bcNRGi"
          >
            <th
              class="c-kxWgPf"
            >
              header.parameterLevel
            </th>
            <th
              class="c-kxWgPf"
            >
              header.fertilisationStrategy
            </th>
            <th
              class="c-kxWgPf c-kxWgPf-iBGLHt-css"
            >
              header.correctionTypeFixed
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="VERY_LOW-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                  data-cy="VERY_LOW-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="VERY_LOW-level-text"
                >
                  VERY_LOW
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-0"
              >
                Correction
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="0"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="LOW-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                  data-cy="LOW-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="LOW-level-text"
                >
                  LOW
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-1"
              >
                Correction
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="1"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="MEDIUM-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                  data-cy="MEDIUM-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="MEDIUM-level-text"
                >
                  MEDIUM
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-2"
              >
                Maintenance
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="2"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="HIGH-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                  data-cy="HIGH-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="HIGH-level-text"
                >
                  HIGH
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-3"
              >
                Replacement
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="3"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="VERY_HIGH-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-SmPqU-variant-4"
                  data-cy="VERY_HIGH-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="VERY_HIGH-level-text"
                >
                  VERY_HIGH
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-4"
              >
                No application
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="4"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`CorrectionFactorTable should render CerealsMultipleCorrectionItems without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-SsEbC"
        data-cy="parameter-levels-table-container"
      >
        <table
          class="c-kwAGqj c-fGHEql"
        >
          <thead>
            <tr
              class="c-eDGYZe c-bcNRGi"
            >
              <th
                class="c-kxWgPf"
              >
                header.parameterLevel
              </th>
              <th
                class="c-kxWgPf"
              >
                header.fertilisationStrategy
              </th>
              <th
                class="c-kxWgPf c-kxWgPf-iBGLHt-css"
              >
                header.correctionTypeMultiplier
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="VERY_LOW-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                    data-cy="VERY_LOW-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="VERY_LOW-level-text"
                  >
                    VERY_LOW
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-0"
                >
                  Correction
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="0"
                    type="text"
                    value="2.222"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="LOW-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                    data-cy="LOW-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="LOW-level-text"
                  >
                    LOW
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-1"
                >
                  Correction
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="1"
                    type="text"
                    value="1"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="MEDIUM-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                    data-cy="MEDIUM-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="MEDIUM-level-text"
                  >
                    MEDIUM
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-2"
                >
                  Maintenance
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="2"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="HIGH-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                    data-cy="HIGH-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="HIGH-level-text"
                  >
                    HIGH
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-3"
                >
                  Replacement
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="3"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-body-row"
            >
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="VERY_HIGH-cell"
                >
                  <span
                    class="c-dEkNHX c-dEkNHX-SmPqU-variant-4"
                    data-cy="VERY_HIGH-colored-circle"
                  />
                  <span
                    class="c-AfNyn"
                    data-cy="VERY_HIGH-level-text"
                  >
                    VERY_HIGH
                  </span>
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-UazGY"
                  data-cy="fertilisation-strategy-cell-4"
                >
                  No application
                </div>
              </td>
              <td
                class="c-doquzR"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                    data-cy="correction-input"
                    style="text-align: left;"
                    tabindex="4"
                    type="text"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-SsEbC"
      data-cy="parameter-levels-table-container"
    >
      <table
        class="c-kwAGqj c-fGHEql"
      >
        <thead>
          <tr
            class="c-eDGYZe c-bcNRGi"
          >
            <th
              class="c-kxWgPf"
            >
              header.parameterLevel
            </th>
            <th
              class="c-kxWgPf"
            >
              header.fertilisationStrategy
            </th>
            <th
              class="c-kxWgPf c-kxWgPf-iBGLHt-css"
            >
              header.correctionTypeMultiplier
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="VERY_LOW-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-SmPqU-variant-0"
                  data-cy="VERY_LOW-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="VERY_LOW-level-text"
                >
                  VERY_LOW
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-0"
              >
                Correction
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="0"
                  type="text"
                  value="2.222"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="LOW-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-joyrsX-variant-1"
                  data-cy="LOW-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="LOW-level-text"
                >
                  LOW
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-1"
              >
                Correction
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="1"
                  type="text"
                  value="1"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="MEDIUM-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-eydjY-variant-2"
                  data-cy="MEDIUM-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="MEDIUM-level-text"
                >
                  MEDIUM
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-2"
              >
                Maintenance
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="2"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="HIGH-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-joyrsX-variant-3"
                  data-cy="HIGH-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="HIGH-level-text"
                >
                  HIGH
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-3"
              >
                Replacement
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="3"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe table-body-row"
          >
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="VERY_HIGH-cell"
              >
                <span
                  class="c-dEkNHX c-dEkNHX-SmPqU-variant-4"
                  data-cy="VERY_HIGH-colored-circle"
                />
                <span
                  class="c-AfNyn"
                  data-cy="VERY_HIGH-level-text"
                >
                  VERY_HIGH
                </span>
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-UazGY"
                data-cy="fertilisation-strategy-cell-4"
              >
                No application
              </div>
            </td>
            <td
              class="c-doquzR"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                  data-cy="correction-input"
                  style="text-align: left;"
                  tabindex="4"
                  type="text"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
