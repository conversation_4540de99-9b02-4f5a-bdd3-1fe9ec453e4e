import React from 'react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import FertigationSoilAnalysis from '../../Fertigation/SoilAnalysis/SoilAnalysis';
import SoilAnalysis from '../../CNP/SoilAnalysis/SoilAnalysis';
import CMMMSoilAnalysis from '../../CMMM/SoilAnalysis/SoilAnalysis';

const SoilAnalysisRenderer = () => {
  const { selectedFeature } = useAppContext();

  const componentMapping: Record<string, JSX.Element> = {
    CNP: <SoilAnalysis />,
    CMMM: <CMMMSoilAnalysis />,
    FP: <FertigationSoilAnalysis />,
  };

  return (
    <>
      {selectedFeature && componentMapping[selectedFeature.name] ? (
        componentMapping[selectedFeature.name]
      ) : (
        <div>No Soil Analysis Available</div>
      )}
    </>
  );
};

export default SoilAnalysisRenderer;
