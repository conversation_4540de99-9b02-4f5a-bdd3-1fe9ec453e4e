import {
  Button,
  Chip,
  Combobox,
  Image,
  Label,
  Select,
  styled,
  Table,
  TimelineItem,
} from '@yaradigitallabs/ahua-react';

export const TableWrapper = styled('div', {
  padding: '$x2',
  border: '1px solid $black10',
  borderRadius: '$m',
  position: 'relative',
  backgroundColor: '$white100',
});

export const StyledTable = styled(Table, {
  overflow: 'auto',
  display: 'flex',
  borderCollapse: 'unset',
});

export const TableHead = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  width: '100%',
  alignItems: 'center',
  whiteSpace: 'pre-wrap',
});

export const TBody = styled('tbody', {
  overflow: 'auto',
  border: '1px solid $black10',
  '& tr:first-child td': {
    borderTop: 'none',
  },
});

export const TableCell = styled(Table.Cell, {
  borderTop: '1px solid $black10',
  borderRight: '1px solid $black10',
  backgroundColor: '$white100',
  fontWeight: '$regular',
  fontSize: '$scale3',
  alignItems: 'left',
  padding: '$x2 $x4 $x2 $x3',
  height: '$x8 !important',
  '&:last-child': {
    textAlign: 'left',
    borderRight: 'none',
  },
  '&.hovered-btn button': {
    padding: 0,
  },
  '&.hovered-btn button>svg': {
    display: 'none !important',
    padding: 0,
  },
  '&.hovered-btn:hover button>svg': {
    display: 'flex !important',
    padding: 0,
  },
  '&.hovered-btn [data-state="open"] svg': {
    display: 'flex !important',
    padding: 0,
  },
  '&.hovered-btn [data-state="open"] [data-disabled] svg': {
    stroke: '$black50',
  },
  '&.hovered-btn:hover': {
    border: '1px solid $blue50 !important',
    width: '231px !important',
    minWidth: '231px !important',
    maxWidth: '231px !important',
  },
});

export const TableCellStatic = styled(TableCell, {
  position: 'sticky',
  left: 0,
  zIndex: 1,
  fontWeight: '$semiBold',
});

export const StyledTableCell = styled(TableCell, {
  width: '232px !important',
  minWidth: '232px !important',
  maxWidth: '232px !important',
  boxSizing: 'border-box',
  '&.add-product-cell': {
    height: '92px !important',
    maxHeight: '92px',
    alignContent: 'flex-start',
    paddingBottom: '$x1',

    '&.error': {
      border: '1px solid $red50 !important',
      span: { color: '$red60' },
      svg: {
        color: '$red60',
        stroke: '$red60',
      },
    },
  },
});

export const StyledTableHeaderCell = styled(TableCellStatic, {
  width: '203px !important',
  minWidth: '203px !important',
  maxWidth: '203px !important',
  paddingRight: '$x2',
  lineHeight: '17px',
  boxSizing: 'border-box',
});

export const ChipBody = styled(Chip.Body, {
  color: '$blue60',
});

export const StyledButton = styled('button', {
  display: 'contents',
  background: 'unset',
  border: 'unset',
  padding: 0,
  width: '$x3',
  height: '$x5',
  cursor: 'pointer',
  '& svg': {
    marginLeft: '$x1',
  },
});

export const StyledLabel = styled(Label, {
  height: '13px',
  display: 'block',
  '&&': {
    fontSize: '$scale0',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    alignContent: 'center',
    color: '$black60',
    fontWeight: '$semiBold',
  },
});

export const StyledChip = styled('div', {
  maxWidth: 'fit-content',
  border: '1px solid $black30',
  borderRadius: '32px',
  padding: '0 6px',
  height: '$x5',
  fontWeight: '$semiBold',
  marginBottom: '6px',
  fontSize: '$scale3',
  display: 'flex',
  alignItems: 'center',
});

export const StyledImageWrapper = styled('div', {
  width: '55px',
  '& div': {
    height: '$x3',
    padding: '0 !important',
    display: 'contents',
  },
});

export const StyledImage = styled(Image, {
  height: '$x3',
  width: '51px',
  padding: 0,
  '&&': {
    objectFit: 'inherit',
  },
});

export const ChipWrapperStyled = styled('div', {
  height: '56px',
  maxHeight: '56px',
  boxSizing: 'border-box',
  width: '100%',
  justifyContent: 'flex-start',
  flexWrap: 'wrap',
  overflowY: 'auto',
  overflowX: 'hidden',
  paddingTop: '6px',
  paddingRight: '$x1',
  '&::-webkit-scrollbar': {
    width: '$x1',
  },
  '&::-webkit-scrollbar-track': {
    margin: '$x4 0',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '$black20',
    borderRadius: '$s',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: '$black40',
  },
});

export const AddProductsCellWrapper = styled('div', {
  alignContent: 'center',
});

export const AddProductButton = styled(Button, {
  paddingLeft: '0 !important',
  height: '76px !important',
  span: {
    lineHeight: 'initial',
  },
});
export const AddProductButtonWithChips = styled(Button, {
  padding: '0 !important',
  minHeight: '$x5 !important',
  height: '$x5 !important',
  span: {
    lineHeight: 'initial',
  },
});
export const StyledButtonWrapper = styled('div', {
  paddingBottom: '$x1',
});

export const StyledChipBody = styled(Chip.Body, {
  maxWidth: '$x27',
  width: 'auto !important',
});

export const TimelineItemCounter = styled(TimelineItem.Counter, {
  float: 'right',
});

export const ImageFrame = styled('div', {
  border: '1px solid $black15',
  margin: '0 auto',
  padding: '$xhalf',
  backgroundColor: '$white100',
  width: '100px',
  height: '72.45px',
  borderRadius: '$xs',
});

export const ComboboxComponent = styled(Combobox, {
  maxHeight: '272px',
});

export const StyledSelect = styled(Select, {
  width: '100%',
  '&& div[role="option"]': {
    paddingBlockStart: '$x3 !important',
    paddingBlockEnd: '$x3 !important',
  },
});
