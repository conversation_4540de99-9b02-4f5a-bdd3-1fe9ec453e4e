// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CMMMProductRecommendations Component matches snapshot for soil application type 1`] = `
<DocumentFragment>
  <h2
    class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
    data-cy="radio-btn-title"
  >
    title
  </h2>
  <div
    aria-required="false"
    class="c-fixGjY c-jSATwS"
    dir="ltr"
    role="radiogroup"
    style="outline: none;"
    tabindex="0"
  >
    <div
      class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
      data-cy="product-recommendations-soil-type-button"
    >
      <label
        class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
        for="leftType"
      >
        soilApp
      </label>
      <div
        class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
      >
        <button
          aria-checked="true"
          class="c-thNAo c-thNAo-bcWJEY-concept-brand"
          data-radix-collection-item=""
          data-state="checked"
          id="leftType"
          role="radio"
          tabindex="-1"
          type="button"
          value="leftType"
        >
          <span
            class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
            data-state="checked"
          />
        </button>
      </div>
    </div>
    <div
      class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
      data-cy="product-recommendations-foliar-type-button"
    >
      <label
        class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
        for="RightType"
      >
        foliarApp
      </label>
      <div
        class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
      >
        <button
          aria-checked="false"
          class="c-thNAo c-thNAo-bcWJEY-concept-brand"
          data-radix-collection-item=""
          data-state="unchecked"
          id="RightType"
          role="radio"
          tabindex="-1"
          type="button"
          value="RightType"
        />
      </div>
    </div>
  </div>
  <div
    class="c-hNVtBr"
    data-cy="cmmm-product-recommendations-table"
  >
    <table
      class="c-kwAGqj c-bVvRLL"
    >
      <tbody
        class="c-dMMkOM"
        data-cy="cmmm-product-recommendations-table-body"
      >
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB"
          >
            <div
              class="c-cDbFRZ"
            >
              grStageNo
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              grStageName
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              grStageImg
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              productType
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              recommendedProducts
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              rateDefNutrients
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              maximumAppRate
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              minimumAppRate
            </div>
          </td>
        </tr>
        <tr
          class="c-eDGYZe"
        >
          <td
            class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
          >
            <div
              class="c-cDbFRZ"
            >
              rateRule
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</DocumentFragment>
`;

exports[`CMMMProductRecommendations Component renders without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
        data-cy="radio-btn-title"
      >
        title
      </h2>
      <div
        aria-required="false"
        class="c-fixGjY c-jSATwS"
        dir="ltr"
        role="radiogroup"
        style="outline: none;"
        tabindex="0"
      >
        <div
          class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
          data-cy="product-recommendations-soil-type-button"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
            for="leftType"
          >
            soilApp
          </label>
          <div
            class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
          >
            <button
              aria-checked="true"
              class="c-thNAo c-thNAo-bcWJEY-concept-brand"
              data-radix-collection-item=""
              data-state="checked"
              id="leftType"
              role="radio"
              tabindex="-1"
              type="button"
              value="leftType"
            >
              <span
                class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                data-state="checked"
              />
            </button>
          </div>
        </div>
        <div
          class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
          data-cy="product-recommendations-foliar-type-button"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
            for="RightType"
          >
            foliarApp
          </label>
          <div
            class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
          >
            <button
              aria-checked="false"
              class="c-thNAo c-thNAo-bcWJEY-concept-brand"
              data-radix-collection-item=""
              data-state="unchecked"
              id="RightType"
              role="radio"
              tabindex="-1"
              type="button"
              value="RightType"
            />
          </div>
        </div>
      </div>
      <div
        class="c-hNVtBr"
        data-cy="cmmm-product-recommendations-table"
      >
        <table
          class="c-kwAGqj c-bVvRLL"
        >
          <tbody
            class="c-dMMkOM"
            data-cy="cmmm-product-recommendations-table-body"
          >
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB"
              >
                <div
                  class="c-cDbFRZ"
                >
                  grStageNo
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  grStageName
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  grStageImg
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  productType
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  recommendedProducts
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  rateDefNutrients
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  maximumAppRate
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  minimumAppRate
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe"
            >
              <td
                class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
              >
                <div
                  class="c-cDbFRZ"
                >
                  rateRule
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>,
  "container": <div>
    <h2
      class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
      data-cy="radio-btn-title"
    >
      title
    </h2>
    <div
      aria-required="false"
      class="c-fixGjY c-jSATwS"
      dir="ltr"
      role="radiogroup"
      style="outline: none;"
      tabindex="0"
    >
      <div
        class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
        data-cy="product-recommendations-soil-type-button"
      >
        <label
          class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
          for="leftType"
        >
          soilApp
        </label>
        <div
          class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
        >
          <button
            aria-checked="true"
            class="c-thNAo c-thNAo-bcWJEY-concept-brand"
            data-radix-collection-item=""
            data-state="checked"
            id="leftType"
            role="radio"
            tabindex="-1"
            type="button"
            value="leftType"
          >
            <span
              class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
              data-state="checked"
            />
          </button>
        </div>
      </div>
      <div
        class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
        data-cy="product-recommendations-foliar-type-button"
      >
        <label
          class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
          for="RightType"
        >
          foliarApp
        </label>
        <div
          class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
        >
          <button
            aria-checked="false"
            class="c-thNAo c-thNAo-bcWJEY-concept-brand"
            data-radix-collection-item=""
            data-state="unchecked"
            id="RightType"
            role="radio"
            tabindex="-1"
            type="button"
            value="RightType"
          />
        </div>
      </div>
    </div>
    <div
      class="c-hNVtBr"
      data-cy="cmmm-product-recommendations-table"
    >
      <table
        class="c-kwAGqj c-bVvRLL"
      >
        <tbody
          class="c-dMMkOM"
          data-cy="cmmm-product-recommendations-table-body"
        >
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB"
            >
              <div
                class="c-cDbFRZ"
              >
                grStageNo
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                grStageName
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                grStageImg
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                productType
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                recommendedProducts
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                rateDefNutrients
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                maximumAppRate
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                minimumAppRate
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe"
          >
            <td
              class="c-doquzR c-dCexQS c-ajMBB c-gNKUPu"
            >
              <div
                class="c-cDbFRZ"
              >
                rateRule
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
