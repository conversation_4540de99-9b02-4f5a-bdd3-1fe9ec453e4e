import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import {
  displayFoliarAppsMock,
  displaySoilAppsMock,
  mockAppProviderValue,
  productsRegionMock,
  unitCountriesHandler,
  cmmmProductRecommendationFilterHandler,
  allPartnerTagsHandler,
  productRegionFilterHandler,
} from '@common/mocks';
import { setupServer } from 'msw/node';
import CMMMProductRecommendations from '../ProductRecommendations';
import { ProductRecommendationApplicationTypes } from '@common/types';

const server = setupServer(
  unitCountriesHandler,
  productRegionFilterHandler,
  allPartnerTagsHandler,
  cmmmProductRecommendationFilterHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());
const useUpdateGrowthStageMock = jest.fn();
const useUpdateRecommendedProducts = jest.fn();
const useOpenAddProductRecommendationsPopup = jest.fn();

const mockData = {
  displaySoilApps: displaySoilAppsMock,
  displayFoliarApps: displayFoliarAppsMock,
  productsRegion: productsRegionMock,
  handleOpenAddProductRecommendationsPopup: useOpenAddProductRecommendationsPopup,
  handleUpdateRecommendedProducts: useUpdateRecommendedProducts,
  useUpdateGrowthStage: useUpdateGrowthStageMock,
};
export default function CMMMProductRecommendationsLogic() {
  return mockData;
}

describe('CMMMProductRecommendations Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CMMMProductRecommendations />
      </AppContext.Provider>,
    );

    const tableComponent = component.getByTestId('cmmm-product-recommendations-table');
    const soilRadioBtnComponent = component.getByTestId('product-recommendations-soil-type-button');
    const foliarRadioBtnComponent = component.getByTestId(
      'product-recommendations-foliar-type-button',
    );

    const titleComponent = component.getByTestId('radio-btn-title');

    expect(soilRadioBtnComponent).toBeInTheDocument();
    expect(foliarRadioBtnComponent).toBeInTheDocument();
    expect(tableComponent).toBeInTheDocument();
    expect(titleComponent).toBeInTheDocument();
    expect(component).toMatchSnapshot();
  });

  it('passes correct handlers to ProductRecommendationsTable', () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CMMMProductRecommendations />
      </AppContext.Provider>,
    );

    const tableComponent = getByTestId('cmmm-product-recommendations-table');

    fireEvent.click(tableComponent);

    expect(useUpdateGrowthStageMock).not.toHaveBeenCalled();
  });

  it('sets default selectedAppType to SOIL if not defined', () => {
    const mockAppProviderValueWithoutSelectedAppType = {
      ...mockAppProviderValue,
      selectedAppType: null,
    };

    render(
      <AppContext.Provider value={mockAppProviderValueWithoutSelectedAppType}>
        <CMMMProductRecommendations />
      </AppContext.Provider>,
    );

    expect(
      mockAppProviderValueWithoutSelectedAppType.methods.setSelectedAppType,
    ).toHaveBeenCalledWith(ProductRecommendationApplicationTypes.SOIL);
  });

  it('matches snapshot for soil application type', () => {
    const { asFragment } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CMMMProductRecommendations />
      </AppContext.Provider>,
    );

    expect(asFragment()).toMatchSnapshot();
  });
});
