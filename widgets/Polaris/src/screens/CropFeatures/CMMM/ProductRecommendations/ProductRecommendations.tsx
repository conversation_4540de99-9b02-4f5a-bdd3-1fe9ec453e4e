import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CMMMProductRecommendationsTable } from './ProductRecommendationsTable';
import { ProductRecommendationApplicationTypes } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import CMMMProductRecommendationsLogic from './ProductRecommendationsLogic';
import { AddProductsPopup } from './AddProductsPopup/AddProductsPopup';
import { PRODUCTS_TYPE } from '@common/constants';
import { BtnTypes, RadioButtonGroup } from '../../shared';

const CMMMProductRecommendations = () => {
  const keyPrefix = 'polaris.cmmmDetails.productRecommendations';
  const { t } = useTranslation('polaris', { keyPrefix });

  const {
    selectedAppType,
    methods: { setSelectedAppType },
  } = useAppContext();

  if (!selectedAppType) setSelectedAppType(ProductRecommendationApplicationTypes.SOIL);

  const {
    displaySoilApps,
    displayFoliarApps,
    productsRegion,
    productsFamilyData,
    selectedProducts,
    selectedStage,
    showAddProductDialog,
    setSelectedProducts,
    setShowAddProductDialog,
    useUpdateGrowthStage,
    handleOpenAddProductRecommendationsPopup,
    handleUpdateRecommendedProducts,
  } = CMMMProductRecommendationsLogic();

  const displayedStage = useMemo(() => {
    if (!selectedAppType || !selectedStage) return;

    const usedApps =
      selectedAppType === ProductRecommendationApplicationTypes.SOIL
        ? displaySoilApps
        : displayFoliarApps;

    return usedApps.find(({ id }) => id === selectedStage?.id);
  }, [selectedAppType, selectedStage]);

  const usedProductsRegion = useMemo(() => {
    const filteredProducts =
      selectedAppType === ProductRecommendationApplicationTypes.FOLIAR
        ? productsRegion.filter(({ productTypeId }) => productTypeId === PRODUCTS_TYPE.FOLIAR)
        : productsRegion.filter(({ productTypeId }) => productTypeId !== PRODUCTS_TYPE.FOLIAR);

    return filteredProducts;
  }, [selectedAppType, productsRegion]);

  const onTypeChanged = (btnType: BtnTypes) => {
    if (btnType === BtnTypes.LeftType) {
      setSelectedAppType(ProductRecommendationApplicationTypes.SOIL);
    } else {
      setSelectedAppType(ProductRecommendationApplicationTypes.FOLIAR);
    }
  };

  return (
    <>
      <RadioButtonGroup
        title={t('title')}
        leftLabelText={t('soilApp')}
        rightLabelText={t('foliarApp')}
        leftDataCy='product-recommendations-soil-type-button'
        rightDataCy='product-recommendations-foliar-type-button'
        defaultType={
          selectedAppType === ProductRecommendationApplicationTypes.SOIL
            ? BtnTypes.LeftType
            : BtnTypes.RightType
        }
        onChange={(value: BtnTypes) => onTypeChanged(value)}
      />
      {displaySoilApps && displayFoliarApps && (
        <CMMMProductRecommendationsTable
          displayData={
            selectedAppType === ProductRecommendationApplicationTypes.SOIL
              ? displaySoilApps
              : displayFoliarApps
          }
          productsRegion={productsRegion}
          useUpdateGrowthStage={useUpdateGrowthStage}
          handleOpenAddProductRecommendationsPopup={handleOpenAddProductRecommendationsPopup}
          handleUpdateRecommendedProducts={handleUpdateRecommendedProducts}
        />
      )}
      <AddProductsPopup
        productsRegion={usedProductsRegion}
        productsFamily={productsFamilyData}
        selectedProducts={selectedProducts}
        displayedStage={displayedStage}
        showDialog={showAddProductDialog}
        setSelectedProducts={setSelectedProducts}
        setShowDialog={setShowAddProductDialog}
        handleSave={handleUpdateRecommendedProducts}
      />
    </>
  );
};

export default CMMMProductRecommendations;
