import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AddProducts } from '../AddProducts';
import {
  mockCerealsSoilStage,
  useProductsByRegionResponse,
} from '@widgets/Polaris/src/screens/Home/mock-data/MockData';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockAppProviderValue } from '@common/mocks';
import { CerealsDisplayAppData } from '@common/types';

const mockHandleOpenAddProductRecommendationsPopup = jest.fn;
const mockHandleUpdateRecommendedProducts = jest.fn;

describe('AddProducts Component', () => {
  const mockStage: CerealsDisplayAppData = mockCerealsSoilStage;

  const renderComponent = (stage = mockStage, productsRegion = useProductsByRegionResponse) =>
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AddProducts
          stage={stage}
          productsRegion={productsRegion}
          handleOpenAddProductRecommendationsPopup={mockHandleOpenAddProductRecommendationsPopup}
          handleUpdateRecommendedProducts={mockHandleUpdateRecommendedProducts}
        />
      </AppContext.Provider>,
    );

  it('renders without crashing', () => {
    const { container } = renderComponent();

    const addProductBtn = screen.getByTestId('add-product-button');
    expect(addProductBtn).toBeInTheDocument();

    if (mockStage.selectedProducts.length > 0) {
      const productsCounter = screen.getByTestId(
        `products-counter-${mockStage.selectedProducts.length}`,
      );
      expect(productsCounter).toBeInTheDocument();

      mockStage.selectedProducts.forEach((tag: string) => {
        const product = useProductsByRegionResponse.find((product) => product.id === tag);
        const productName = product?.localizedName ? product.localizedName : product?.name;
        const foundProductName = productName && screen.getByText(productName);
        expect(foundProductName).toBeInTheDocument();
      });

      const productTagCloseButtons = screen.getAllByTestId('product-remove');
      expect(productTagCloseButtons.length).toEqual(mockStage.selectedProducts.length);
    }

    expect(container).toMatchSnapshot();
  });

  it('renders the correct number of tags', () => {
    renderComponent();

    const tags = mockStage.selectedProducts;

    if (tags.length > 0) {
      tags.forEach((tag: string) => {
        const product = useProductsByRegionResponse.find((product) => product.id === tag);
        const productName = product?.localizedName ? product.localizedName : product?.name;
        const chip = productName && screen.getByText(productName);
        expect(chip).toBeInTheDocument();
      });
    } else {
      expect(screen.queryAllByTestId('product')).toHaveLength(0);
    }
  });

  it('renders correctly when no tags are present', () => {
    const emptyStage = { ...mockStage, selectedProducts: [] };

    renderComponent(emptyStage);

    const addProductBtn = screen.getByTestId('add-product-button');
    expect(addProductBtn).toBeInTheDocument();

    expect(screen.queryByTestId('products-counter')).not.toBeInTheDocument();
    expect(screen.queryAllByTestId('product')).toHaveLength(0);
  });

  it('renders the correct product image for each product', () => {
    const products = mockStage.selectedProducts;

    renderComponent();

    products.forEach((id: string) => {
      const product = useProductsByRegionResponse.find((product) => product.id === id);
      const imageUri = product?.productFamily.mediaUri?.[0].value;
      const imageElement = screen.getByRole('img', { name: id });
      expect(imageElement).toHaveAttribute('src', imageUri);
    });
  });

  it('renders correctly when productsRegion is empty', () => {
    const emptyProductsRegion: [] = [];
    renderComponent(mockStage, emptyProductsRegion);

    const addProductBtn = screen.getByTestId('add-product-button');
    expect(addProductBtn).toBeInTheDocument();

    expect(screen.queryByTestId('products-counter')).not.toBeInTheDocument();
    expect(screen.queryAllByTestId('product')).toHaveLength(0);
  });

  it('handles non-array selectedProducts gracefully', () => {
    const invalidStage = { ...mockStage, selectedProducts: [] };
    renderComponent(invalidStage);

    expect(screen.queryByTestId('products-counter')).not.toBeInTheDocument();
    expect(screen.queryAllByTestId('product')).toHaveLength(0);
  });

  it('does not render invalid products from selectedProducts', () => {
    const invalidStage = { ...mockStage, selectedProducts: ['invalid-product-id'] };
    renderComponent(invalidStage);

    expect(screen.queryByTestId('products-counter')).not.toBeInTheDocument();
    expect(screen.queryAllByTestId('product')).toHaveLength(0);
  });

  it('opens the add product popup when no products are selected', () => {
    const mockHandleOpenAddProductRecommendationsPopup = jest.fn((data) =>
      console.log('Popup opened', data),
    );

    const emptyStage = { ...mockStage, selectedProducts: [] };
    renderComponent(emptyStage);
    waitFor(() => {
      const addProductBtn = screen.getByTestId('add-product-button');
      fireEvent.click(addProductBtn);

      expect(mockHandleOpenAddProductRecommendationsPopup).toHaveBeenCalledWith(emptyStage);
    });
  });
});
