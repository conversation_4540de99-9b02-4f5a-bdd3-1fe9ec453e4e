import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AhuaIcon } from '@yaradigitallabs/ahua-react';
import {
  AddProductButton,
  StyledChip,
  ChipWrapperStyled,
  TimelineItemCounter,
  StyledImage,
  StyledButton,
  StyledLabel,
  StyledImageWrapper,
  AddProductButtonWithChips,
  AddProductsCellWrapper,
} from '../ProductRecommendations.styled';
import { ProductRegion, CerealsDisplayAppData } from '@common/types';
import { cloneDeep } from 'lodash';

export interface AddProductProps {
  stage: CerealsDisplayAppData;
  productsRegion: ProductRegion[];
  handleOpenAddProductRecommendationsPopup: (data: CerealsDisplayAppData) => void;
  handleUpdateRecommendedProducts: (data: string[], stage: CerealsDisplayAppData) => void;
}

export const AddProducts: React.FC<AddProductProps> = ({
  stage,
  productsRegion,
  handleOpenAddProductRecommendationsPopup,
  handleUpdateRecommendedProducts,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.productRecommendations.productRecommendationsTable',
  });

  const products = useMemo(() => {
    const selectedProductIds: string[] = stage.selectedProducts ?? [];
    const validProductsRegion: ProductRegion[] = productsRegion ?? [];

    return cloneDeep(validProductsRegion).filter((productRegion: ProductRegion) =>
      selectedProductIds.includes(productRegion.id),
    );
  }, [stage, productsRegion]);

  const handleRemoveProduct = useCallback(
    (value: ProductRegion) => {
      const updatedProducts = products.reduce<string[]>((acc, product) => {
        const isProductToRemove = product.id === value.id;
        if (!isProductToRemove) {
          acc = [...acc, product.id];
        }
        return acc;
      }, []);
      handleUpdateRecommendedProducts(updatedProducts, stage);
    },
    [products],
  );

  return (
    <AddProductsCellWrapper>
      {products?.length > 0 ? (
        <>
          <AddProductButtonWithChips
            colorConcept='inverse'
            iconLeading='Plus'
            size={products?.length > 0 ? 'xs' : 's'}
            id={`add-products-${stage.id}}`}
            data-cy='add-product-button'
            onClick={() => handleOpenAddProductRecommendationsPopup(stage)}
          >
            {t('addProducts')}
          </AddProductButtonWithChips>
          {products?.length > 0 && (
            <>
              <TimelineItemCounter
                mode='dark'
                outline='false'
                value={products?.length}
                data-cy={`products-counter-${products?.length}`}
              />
              <ChipWrapperStyled>
                {products?.map((product) => (
                  <StyledChip key={product.name}>
                    <StyledImageWrapper data-cy='product'>
                      <StyledImage
                        alt={product.id}
                        src={product.productFamily.mediaUri?.[0].value ?? ''}
                      />
                    </StyledImageWrapper>
                    <StyledLabel>{`${
                      product.localizedName ? product.localizedName : product.name
                    }`}</StyledLabel>
                    <StyledButton
                      onClick={() => handleRemoveProduct(product)}
                      data-cy='product-remove'
                    >
                      <AhuaIcon iconSize='x4' colorConcept='neutral' icon='Clear' />
                    </StyledButton>
                  </StyledChip>
                ))}
              </ChipWrapperStyled>
            </>
          )}
        </>
      ) : (
        <AddProductButton
          colorConcept='inverse'
          iconLeading='Plus'
          size={products?.length > 0 ? 'xs' : 's'}
          id={`add-products-${stage.id}}`}
          data-cy='add-product-button'
          onClick={() => handleOpenAddProductRecommendationsPopup(stage)}
        >
          {' '}
          {t('addProducts')}
        </AddProductButton>
      )}
    </AddProductsCellWrapper>
  );
};
