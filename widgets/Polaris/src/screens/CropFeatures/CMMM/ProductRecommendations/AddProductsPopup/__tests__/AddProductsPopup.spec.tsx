import React from 'react';
import { fireEvent, render, waitFor, within } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockAppProviderValue } from '@common/mocks';
import {
  mockCerealsSoilStage,
  useProductsByRegionMultipleResponse,
  useProductsFamilyResponse,
} from '../../../../../Home/mock-data/MockData';
import { AddProductsPopup } from '../AddProductsPopup';
import userEvent from '@testing-library/user-event';
import { AddProductsPopupProps } from '../AddProductsPopup.type';

describe('CMMM Add Products Popup', () => {
  const user = userEvent.setup();
  afterEach(() => {
    jest.clearAllMocks();
  });

  const addProductsPopupProps: AddProductsPopupProps = {
    productsRegion: useProductsByRegionMultipleResponse,
    productsFamily: useProductsFamilyResponse,
    selectedProducts: {},
    displayedStage: mockCerealsSoilStage,
    showDialog: true,
    setSelectedProducts: jest.fn(),
    setShowDialog: jest.fn(),
    handleSave: jest.fn(),
  };
  const renderComponent = (props: AddProductsPopupProps) =>
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AddProductsPopup {...props} />,
      </AppContext.Provider>,
    );

  it('render table and match snapshot', () => {
    const component = renderComponent(addProductsPopupProps);
    expect(component).toMatchSnapshot();
  });

  it('selectedProducts empty, select product', async () => {
    const component = renderComponent(addProductsPopupProps);

    const checkbox = component.getAllByRole('checkbox')[0];

    expect(checkbox).not.toBeChecked();

    await user.click(checkbox);

    expect(checkbox).toBeChecked();
    expect(addProductsPopupProps.setSelectedProducts).toHaveBeenCalledTimes(1);
  });

  it('unselect a selected product', async () => {
    const propsWithSelectedProduct = {
      ...addProductsPopupProps,
      selectedProducts: {
        [`${addProductsPopupProps.displayedStage?.id}`]: [
          addProductsPopupProps.productsRegion[0].id,
        ],
      },
    };
    const component = renderComponent(propsWithSelectedProduct);

    const checkbox = component.getAllByRole('checkbox')[0];

    expect(checkbox).toBeChecked();
    waitFor(() => {
      fireEvent.click(checkbox);
      expect(checkbox).not.toBeChecked();
      expect(addProductsPopupProps.setSelectedProducts).toHaveBeenCalledTimes(1);
    });
  });

  it('render empty table state when there are no products available', async () => {
    const newProps = {
      ...addProductsPopupProps,
      productsRegion: [],
    };
    const component = renderComponent(newProps);

    const emptyStateComp = component.getByTestId('add-products-empty-state');
    expect(emptyStateComp).toBeInTheDocument();
  });

  it('render empty table state when search by criterias', async () => {
    const propsWithSelectedProduct = {
      ...addProductsPopupProps,
      selectedProducts: {
        [`${addProductsPopupProps.displayedStage?.id}`]: [
          addProductsPopupProps.productsRegion[0].id,
        ],
      },
    };
    const component = renderComponent(propsWithSelectedProduct);

    const searchBar = component.getByTestId('add-products-table-search-bar-name');
    expect(searchBar).toBeInTheDocument();

    await user.type(searchBar, 'test test');
    expect(searchBar).toHaveValue('test test');
    await user.keyboard('{Enter}');

    const emptyStateComp = component.getByTestId('add-products-empty-search-state');
    expect(emptyStateComp).toBeInTheDocument();

    const emptyStateIcon = within(emptyStateComp).getAllByTestId('empty-state-info-icon');
    expect(emptyStateIcon[0]).toBeInTheDocument();

    const actBtn = within(emptyStateComp).getByTestId(
      'add-products-empty-search-state-action-button',
    );
    expect(actBtn).toBeInTheDocument();

    expect(component.queryByLabelText('Go to next page')).not.toBeInTheDocument();
    const closeButton = component.getByTestId('add-products-close-btn');
    await userEvent.click(closeButton);
    expect(addProductsPopupProps.setShowDialog).toHaveBeenCalled();
  });

  it('render table pagination when there are more then 5 products available', () => {
    const component = renderComponent(addProductsPopupProps);
    expect(component.getByLabelText('Go to next page')).toBeInTheDocument();
    expect(component.getByLabelText('Go to previous page')).toBeInTheDocument();
    expect(component.getByLabelText('Go to first page')).toBeInTheDocument();
    expect(component.getByLabelText('Go to last page')).toBeInTheDocument();
  });
});
