import { t } from 'i18next';
import { getColumnDefs, validateMaxProductsSelected } from '../addProductsPopupUtils';
import { ColumnDef } from '@tanstack/react-table';
import { ProductRegion } from '@common/types';

describe('addProductsPopupUtils', () => {
  it('getColumnDefs', () => {
    const filterFn1 = jest.fn();
    const filterFn2 = jest.fn();
    const expectedColDefs: ColumnDef<ProductRegion>[] = [
      {
        header: undefined,
        accessorKey: 'checkbox',
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: 'Product family',
        accessorKey: 'productFamilyId',
        enableColumnFilter: true,
        enableSorting: true,
        filterFn: filterFn1,
      },
      {
        header: 'Product name',
        accessorKey: 'name',
        enableSorting: false,
        enableColumnFilter: true,
        filterFn: filterFn2,
      },
      {
        header: 'col4',
        accessorKey: 'n',
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: 'col5',
        accessorKey: 'p',
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: 'col6',
        accessorKey: 'k',
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: 'col7',
        accessorKey: 's',
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: 'col8',
        accessorKey: 'mg',
        enableColumnFilter: false,
        enableSorting: false,
      },
    ];
    const columnDefs = getColumnDefs({
      t,
      keyPrefix: 'key',
      productNameColCustomFilter: filterFn1,
      productFamilyColCustomFilter: filterFn2,
    });

    columnDefs.forEach((column, index) => {
      // @ts-ignore
      expect(column?.accessorKey).toEqual(expectedColDefs[index]?.accessorKey);
      expect(column.enableSorting).toEqual(expectedColDefs[index].enableSorting);
      expect(column.enableColumnFilter).toEqual(expectedColDefs[index].enableColumnFilter);
    });
  });

  it('validateMaxProductsSelected, number is less than max', () => {
    const arrayWithLessThanMaxEntries = new Array(19);
    const validatedNumber = validateMaxProductsSelected(arrayWithLessThanMaxEntries);
    expect(validatedNumber).toEqual(false);
  });
  it('validateMaxProductsSelected, number is more than the max', () => {
    const arrayWithMoreThanMaxEntries = new Array(21);
    const validatedNumber = validateMaxProductsSelected(arrayWithMoreThanMaxEntries);
    expect(validatedNumber).toEqual(true);
  });
});
