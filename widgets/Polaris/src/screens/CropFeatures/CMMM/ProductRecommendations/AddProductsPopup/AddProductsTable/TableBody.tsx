import React, { useCallback } from 'react';
import { Check<PERSON>ox, RadioButton, RadioButtonGroup, Table } from '@yaradigitallabs/ahua-react';
import { flexRender } from '@tanstack/react-table';
import { StyledTableRow } from './AddProductsTable.styled';
import { validateMaxProductsSelected } from '../utils/addProductsPopupUtils';
import { TableBodyProps, RenderConfig } from '../AddProductsPopup.type';
import _ from 'lodash';
import { PRODUCTS_TYPE } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers';

export function TableBody({
  table,
  getProductFamilyUri,
  selectedProducts,
  displayedStage,
  setSelectedProducts,
}: TableBodyProps) {
  const { selectedCountryUnits } = useAppContext();

  const handleAddRemoveProducts = useCallback(
    (value: string) => {
      let updatedProductIds = [];
      let updatedSelectedProducts = { [displayedStage.id]: [value] };

      if (!_.isEmpty(selectedProducts) && selectedProducts[displayedStage.id]) {
        updatedProductIds = [...selectedProducts[displayedStage.id]];
        const alreadySelected = updatedProductIds.includes(value);

        if (alreadySelected) {
          updatedProductIds = updatedProductIds.filter((product) => product !== value);
        } else if (!validateMaxProductsSelected(updatedProductIds)) {
          updatedProductIds = updatedProductIds.concat(value);
        }
        updatedSelectedProducts = {
          ...selectedProducts,
          [displayedStage.id]: updatedProductIds,
        };
      }
      setSelectedProducts(updatedSelectedProducts);
    },
    [selectedProducts, displayedStage],
  );

  const handleDisableCheckbox = useCallback(
    (id: string) => {
      if (!selectedProducts) return;

      return (
        validateMaxProductsSelected(selectedProducts[displayedStage.id]) &&
        !selectedProducts[displayedStage.id].includes(id)
      );
    },
    [selectedProducts, displayedStage],
  );

  const handleSelectSingleProduct = useCallback(
    (value: string) => {
      setSelectedProducts({ [displayedStage.id]: [value] });
    },
    [selectedProducts, displayedStage],
  );

  return (
    <>
      {table.getRowModel().rows.map((row) => {
        return (
          <StyledTableRow key={row.id}>
            {row.getVisibleCells().map((cell) => {
              const unitKeys = ['nUnitId', 'pUnitId', 'kUnitId', 'sUnitId', 'mgUnitId'] as const;

              const unitMap = selectedCountryUnits?.reduce<Record<string, string>>(
                (acc, { id, name }) => {
                  unitKeys.forEach((key) => {
                    if (id === row.original[key]) {
                      acc[key.replace('Id', '')] = name;
                    }
                  });
                  return acc;
                },
                {},
              );

              const renderConfig: RenderConfig = {
                productFamilyId: getProductFamilyUri(cell.getValue<string>()),
                name: row.original.localizedName ? row.original.localizedName : row.original.name,
                n: `${row.original.n} (${unitMap?.nUnit})`,
                p: `${row.original.p} (${unitMap?.pUnit})`,
                k: `${row.original.k} (${unitMap?.kUnit})`,
                s: `${row.original.s} (${unitMap?.sUnit})`,
                mg: `${row.original.mg} (${unitMap?.mgUnit})`,
                checkbox:
                  displayedStage.productType === PRODUCTS_TYPE.FOLIAR ? (
                    <RadioButton
                      key={row.id}
                      id={row.id}
                      concept='success'
                      data-cy={`${row.id}-add-remove-radio-bitton`}
                      checked={selectedProducts?.[displayedStage.id]?.includes(row.original.id)}
                      onClick={() => {
                        handleSelectSingleProduct(row.original.id);
                      }}
                    />
                  ) : (
                    <CheckBox
                      key={row.id}
                      disabled={handleDisableCheckbox(row.original.id)}
                      checked={selectedProducts?.[displayedStage.id]?.includes(row.original.id)}
                      onClick={() => {
                        handleAddRemoveProducts(row.original.id);
                      }}
                    />
                  ),
              };
              return (
                <Table.Cell key={`${row.id}-${cell.id}`} className={''}>
                  <RadioButtonGroup aria-label='label'>
                    {flexRender(renderConfig[cell.column.id], cell.getContext())}
                  </RadioButtonGroup>
                </Table.Cell>
              );
            })}
          </StyledTableRow>
        );
      })}
    </>
  );
}
