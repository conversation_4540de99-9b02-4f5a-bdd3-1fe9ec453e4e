import { CSSProperties } from 'react';
import { styled, Table } from '@yaradigitallabs/ahua-react';

// Table
export const TableWrapperStyled = styled('div', {
  width: '1080px',

  [`& > .table-main > .table`]: {
    height: '460px',
  },

  // Remove pagination absolute position
  [`& div`]: {
    position: 'relative',
  },

  // Table wrapper div
  [`& > div:first-child`]: {
    padding: '$x4',
  },
  ['& thead, & tbody']: {
    verticalAlign: 'middle',
  },

  // Table head rows
  [`& thead > tr`]: {
    verticalAlign: 'middle',
    display: 'inline-block',
    width: '1048px',
  },
  [`& thead > tr:nth-child(1)`]: {
    height: '53px',
    borderTop: '1px solid var(--colors-black10)',
  },
  [`& thead > tr:nth-child(2)`]: {
    borderTop: 'none !important',
    height: '55px',
    [`& th`]: {
      paddingTop: '$x4',
    },
  },

  // Table body rows
  [`& tbody > tr`]: {
    verticalAlign: 'middle',
    display: 'inline-block',
    width: '1048px',
  },
  [`& tbody > tr:nth-child(even)`]: {
    borderTop: 'none !important',
    height: '54px',
  },
  [`& tbody > tr:nth-child(odd)`]: {
    height: '55px',
  },

  // Pagination wrapper
  [`& > div > div`]: {
    padding: '14px 0',
  },

  // Pagination buttons
  [`& > div > div > div > div > div > button`]: {
    padding: '$x2',
  },

  // Pagination input
  [`& > div > div > div > div > div > div > input`]: {
    height: '$x10',
  },
});

export const StyledTableHeaderRow = styled(Table.Row, {
  [`& th, th:last-child`]: {
    boxSizing: 'border-box',
    display: 'inline-block',
    verticalAlign: '-webkit-baseline-middle !important',
    width: '115px',
  },
  [`& th:nth-child(1)`]: {
    width: '69px',
    padding: 0,
  },
  [`& th:nth-child(2)`]: {
    width: '200px',
    [`&:before`]: {
      content: 'none',
    },
  },
  [`& th:nth-child(3)`]: {
    width: '200px',
  },
});

export const StyledTableSearchRow = styled(Table.Row, {
  [`& td:nth-child(1)`]: {
    boxSizing: 'border-box',
    height: '$x14',
    width: '69px',
    padding: 0,
  },
  [`& td:nth-child(n+2):nth-child(-n+3)`]: {
    boxSizing: 'border-box',
    width: '200px',
    padding: '$x2 $x4 $x2 $x3',
  },
});

export const StyledTableSortHead = styled(Table.SortHead, {
  svg: {
    color: '$brand-contrast',
    stroke: '$brand-contrast',
  },
});

export const StyledTableRow = styled(Table.Row, {
  ['& td']: {
    boxSizing: 'border-box',
    verticalAlign: 'middle',
    width: '115px',
    textAlign: 'left !important',
  },
  [`& td:nth-child(1)`]: {
    width: '69px',
    padding: 0,
  },
  [`& td:nth-child(2)`]: {
    width: '200px',
    padding: '14px $x4 18px $x3',
  },
  [`& td:nth-child(3)`]: {
    width: '200px',
    height: '20px',
    padding: '$x4 $x4 $x5 $x3',
  },
});

export const StyledGrowthStageText = styled('th', {
  backgroundColor: '$blue0',
  fontWeight: '$semiBold',
  width: '100% !important',
  height: '$x14',
  textAlign: 'center',
  alignContent: 'center',
});

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: 500,
};
