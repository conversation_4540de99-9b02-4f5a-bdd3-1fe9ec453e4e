.table-row {
  border-bottom: 1px solid var(--colors-border-tertiary-light) !important;

  th:not(:first-child)::before {
    border-left: none;
  }

  th:first-child {
    border-left: none;
  }

  td:first-child {
    border-left: none;
  }

  td {
    background: var(--colors-white100);
  }
}

.splitting-schedule-table {
  th {
    padding: var(--space-x3) !important;
  }

  td {
    padding: 10px var(--space-x3) 10px var(--space-x3);
  }
}

.table-header-with-tooltip {
  display: flex;
  align-items: center;
}

.splitting-rule-cell {
  display: flex;
  align-items: center;

  div {
    width: unset;
  }
}

.icon-button {
  padding: 0;
}

.table-data-label {
  margin-right: var(--space-x2);
}

.table-head-image {
  &>div {
    width: unset;

    &>div {
      padding-bottom: 0 !important;

      &>div {
        position: relative !important;
        text-align: center;
      }
    }
  }
}

.table-input {
  border-color: var(--colors-neutral-lighter);
}

.incorrect-total {
  color: var(--colors-destructive-base);
}