import { Dispatch } from 'react';
import {
  NSplittingScheduleKeys,
  NSplittingRule,
  UpdateSplittingScheduleActions,
  NSplittingScheduleActions,
  SplittingScheduleChangeHandlerArgs,
  WrongRuleInputs,
  NSplittingScheduleSplit,
  WrongSplitInputs,
  SplitWithEmptyCells,
  ValidateSplitFieldsArgs,
  RangeValidity,
  RuleLinkedList,
  NSplittingScheduleData,
  CheckRuleRangeArgs,
  GetIncorrectRuleFieldsBetweenRowsArgs,
  GetIncorrectRuleFieldsOnSameRowArgs,
  CurrentIncorrectFieldsResult,
  NSplittingScheduleModule,
} from '@common/types';
import { NitrogenSplittingRuleErrorCauses, RuleValueRange, SplitValueRange } from './constants';
import { TFunction } from 'i18next';
import { cloneDeep, isString } from 'lodash';

// Needed in order to handle different inputs: data is either NSplittingRule or NSplittingScheduleSplit
/**
 * Function that specifically checks if the given object is of type NSplittingRule
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const isSplittingRule = (obj: any): obj is NSplittingRule => {
  const keys = Object.keys(obj);

  return (
    obj && (keys.includes(NSplittingScheduleKeys.To) || keys.includes(NSplittingScheduleKeys.From))
  );
};

export const splittingScheduleChangeHandler = (
  dispatch: Dispatch<NSplittingScheduleActions>,
  { splittingId, key, value, data }: SplittingScheduleChangeHandlerArgs,
): void => {
  const isRule = isSplittingRule(data);

  if (isRule) {
    switch (key) {
      case NSplittingScheduleKeys.To: {
        if (data.to) {
          data.to.value = value;
        }

        dispatch({
          type: UpdateSplittingScheduleActions.UpdateRule,
          splittingId,
          payload: data,
        });

        break;
      }
      case NSplittingScheduleKeys.From: {
        if (data.from) {
          data.from.value = value;
        }

        dispatch({
          type: UpdateSplittingScheduleActions.UpdateRule,
          splittingId,
          payload: data,
        });

        break;
      }
    }
  } else {
    data.percentage.value = value;

    dispatch({
      type: UpdateSplittingScheduleActions.UpdateSplit,
      splittingId,
      payload: data,
    });
  }
};

/** Check if the value is in given range  */
export const isValueInRange = (
  value: number,
  min: number,
  max: number,
  t: TFunction,
): RangeValidity => {
  const result: RangeValidity = {
    isValid: true,
    message: '',
  };

  if (value > max) {
    result.isValid = false;
    result.message = t('validation.max', {
      value: max,
    });
  } else if (value < min) {
    result.isValid = false;
    result.message = t('validation.min', {
      value: min,
    });
  }

  return result;
};

/** Convert splitting rule list to linked list with previous, current and next element */
export const convertRulesToLinkedList = (
  stateConfigurationData: NSplittingScheduleData[],
): RuleLinkedList[] => {
  const ruleLinkedList: RuleLinkedList[] = [];

  for (let i = 0; i < stateConfigurationData.length; i++) {
    const ruleLinkedListObj: RuleLinkedList = {
      prev: i - 1 < 0 ? null : stateConfigurationData[i - 1],
      curr: stateConfigurationData[i],
      next: i + 1 === stateConfigurationData.length ? null : stateConfigurationData[i + 1],
    };

    ruleLinkedList.push(ruleLinkedListObj);
  }

  return ruleLinkedList;
};

/** Get all error field if rule value is not in range - between Min and Max */
export const getIncorrectRuleRangeFields = ({
  rule,
  key,
  currentIncorrectFields,
  splittingId,
  t,
}: CheckRuleRangeArgs): CurrentIncorrectFieldsResult => {
  let currentIncorrectFieldsCopy = cloneDeep(currentIncorrectFields);

  if (rule[key]) {
    const { isValid, message } = isValueInRange(
      Number(rule[key].value),
      RuleValueRange.Min,
      RuleValueRange.Max,
      t,
    );

    if (!isValid) {
      throw new Error(message, {
        cause: NitrogenSplittingRuleErrorCauses.RuleDiscontinuousRanges,
      });
    }
  }

  // Remove all previous error fields for rule range
  const incorrectFieldToRemove = currentIncorrectFieldsCopy.find(
    (field) =>
      field.id === splittingId &&
      field.key === key &&
      field.cause === NitrogenSplittingRuleErrorCauses.RuleDiscontinuousRanges,
  );
  currentIncorrectFieldsCopy = currentIncorrectFieldsCopy.filter(
    (field) => field !== incorrectFieldToRemove,
  );

  return { currentIncorrectFields: currentIncorrectFieldsCopy };
};

/** Get all error fields if previous "<" and next ">=" rule values are not equal */
export const getIncorrectRuleFieldsBetweenRows = ({
  ruleLinkedList,
  key,
  currentIncorrectFields,
  message,
}: GetIncorrectRuleFieldsBetweenRowsArgs): CurrentIncorrectFieldsResult => {
  let currentIncorrectFieldsCopy = cloneDeep(currentIncorrectFields);
  const otherRangeKey =
    key === NSplittingScheduleKeys.From ? NSplittingScheduleKeys.To : NSplittingScheduleKeys.From;
  let errorFieldsBetweenRows: WrongRuleInputs[] = [];

  for (const rule of ruleLinkedList) {
    switch (key) {
      case NSplittingScheduleKeys.From: {
        if (rule.prev) {
          if (
            Number(rule.curr?.splittingRule[key]?.value) !==
            Number(rule.prev.splittingRule[otherRangeKey]?.value)
          ) {
            const currError: WrongRuleInputs = {
              id: rule.curr.id,
              cause: NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows,
              key,
              isOnSameRow: false,
              message,
            };
            const prevError: WrongRuleInputs = {
              id: rule.prev.id,
              cause: NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows,
              key: otherRangeKey,
              isOnSameRow: false,
              message,
            };

            errorFieldsBetweenRows.push(currError);
            errorFieldsBetweenRows.push(prevError);
          }
        }

        break;
      }
      case NSplittingScheduleKeys.To: {
        if (rule.next) {
          if (
            Number(rule.curr?.splittingRule[key]?.value) !==
            Number(rule.next.splittingRule[otherRangeKey]?.value)
          ) {
            const currError: WrongRuleInputs = {
              id: rule.curr.id,
              cause: NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows,
              key,
              isOnSameRow: false,
              message,
            };
            const nextError: WrongRuleInputs = {
              id: rule.next.id,
              cause: NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows,
              key: otherRangeKey,
              isOnSameRow: false,
              message,
            };

            errorFieldsBetweenRows.push(currError);
            errorFieldsBetweenRows.push(nextError);
          }
        }

        break;
      }
    }
  }

  if (!errorFieldsBetweenRows.length) {
    currentIncorrectFieldsCopy = currentIncorrectFieldsCopy.filter(
      (field) => field.cause !== NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows,
    );
  } else {
    const errorsBetweenRowsCount = currentIncorrectFieldsCopy.filter(
      (field) => field.cause === NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows,
    ).length;

    if (errorFieldsBetweenRows.length > errorsBetweenRowsCount) {
      errorFieldsBetweenRows = errorFieldsBetweenRows.filter(
        (newErrorField) =>
          !currentIncorrectFieldsCopy.find(
            (oldErrorField) =>
              oldErrorField.id === newErrorField.id &&
              oldErrorField.cause === newErrorField.cause &&
              oldErrorField.key === newErrorField.key,
          ),
      );

      currentIncorrectFieldsCopy = [...currentIncorrectFieldsCopy, ...errorFieldsBetweenRows];
    } else {
      currentIncorrectFieldsCopy = [...errorFieldsBetweenRows];
    }
  }

  return { currentIncorrectFields: currentIncorrectFieldsCopy };
};

/** Get all error fields if ">=" and "<" rule range is invalid */
export const getIncorrectRuleFieldsOnSameRow = ({
  ruleLinkedList,
  currentIncorrectFields,
  message,
}: GetIncorrectRuleFieldsOnSameRowArgs): CurrentIncorrectFieldsResult => {
  let currentIncorrectFieldsCopy = cloneDeep(currentIncorrectFields);
  let errorFieldsSameRow: WrongRuleInputs[] = [];

  for (const rule of ruleLinkedList) {
    if (rule.curr.splittingRule.from && rule.curr.splittingRule.to) {
      const fromValue = Number(rule.curr.splittingRule.from.value);
      const toValue = Number(rule.curr.splittingRule.to.value);

      // Check if range "from" - "to" is valid
      if (fromValue >= toValue) {
        const nonConsecutiveFieldFrom: WrongRuleInputs = {
          id: rule.curr.id,
          cause: NitrogenSplittingRuleErrorCauses.NonConsecutiveCurrentRow,
          key: NSplittingScheduleKeys.From,
          isOnSameRow: true,
          message,
        };
        const nonConsecutiveFieldTo: WrongRuleInputs = {
          id: rule.curr.id,
          cause: NitrogenSplittingRuleErrorCauses.NonConsecutiveCurrentRow,
          key: NSplittingScheduleKeys.To,
          isOnSameRow: true,
          message,
        };

        errorFieldsSameRow.push(nonConsecutiveFieldFrom);
        errorFieldsSameRow.push(nonConsecutiveFieldTo);
      }
    }
  }

  if (!currentIncorrectFieldsCopy.length) {
    currentIncorrectFieldsCopy = [...errorFieldsSameRow];
  }

  if (!errorFieldsSameRow.length) {
    currentIncorrectFieldsCopy = currentIncorrectFieldsCopy.filter(
      (field) => field.cause !== NitrogenSplittingRuleErrorCauses.NonConsecutiveCurrentRow,
    );
  } else {
    const errorsOnSameRowCount = currentIncorrectFieldsCopy.filter(
      (field) => field.cause === NitrogenSplittingRuleErrorCauses.NonConsecutiveCurrentRow,
    ).length;

    if (errorFieldsSameRow.length > errorsOnSameRowCount) {
      errorFieldsSameRow = errorFieldsSameRow.filter(
        (newErrorField) =>
          !currentIncorrectFieldsCopy.find(
            (oldErrorField) =>
              oldErrorField.id === newErrorField.id &&
              oldErrorField.cause === newErrorField.cause &&
              oldErrorField.key === newErrorField.key,
          ),
      );

      currentIncorrectFieldsCopy = [...currentIncorrectFieldsCopy, ...errorFieldsSameRow];
    }
  }

  return { currentIncorrectFields: currentIncorrectFieldsCopy };
};

/** Get all error fields for splits  */
export const getInvalidSplitFields = ({
  nitrogenSplittingConfiguration,
  t,
}: ValidateSplitFieldsArgs): WrongSplitInputs[] => {
  let errorFields: WrongSplitInputs[] = [];

  nitrogenSplittingConfiguration.configuration.data.forEach(({ id, splits }) => {
    splits.forEach(({ ordinal, percentage }) => {
      const errorField: WrongSplitInputs = {
        id,
        message: '',
        ordinal: null,
      };
      const { isValid, message } = isValueInRange(
        Number(percentage.value),
        SplitValueRange.Min,
        SplitValueRange.Max,
        t,
      );

      if (!isValid) {
        errorField.message = message;
        errorField.ordinal = ordinal;

        errorFields.push(errorField);
      }
    });
  });

  nitrogenSplittingConfiguration.configuration.data.forEach(({ id, splits }) => {
    const result = getInvalidTotalBasedRows(splits, id, t);

    errorFields = [...errorFields, ...result];
  });

  if (errorFields.length) {
    return errorFields;
  }

  return [];
};

/** Calculate total for all split fields per rule */
export const recalculateTotalPercentage = (
  splits: (NSplittingScheduleSplit | SplitWithEmptyCells)[],
): number => {
  return splits.reduce<number>((acc, split) => {
    acc += Number(split?.percentage?.value ?? 0);

    return Number(acc.toFixed(2));
  }, 0);
};

/** Get invalid rows based on "Total" value calculated from all splits per rule */
const getInvalidTotalBasedRows = (
  splits: (NSplittingScheduleSplit | SplitWithEmptyCells)[],
  splittingId: string,
  t: TFunction,
) => {
  const result = recalculateTotalPercentage(splits);
  const errorFields: WrongSplitInputs[] = [];
  let message = null;

  if (result > SplitValueRange.Max) {
    message = t('validation.totalMax', {
      value: SplitValueRange.Max,
    });
  } else if (result < SplitValueRange.Max) {
    message = t('validation.totalMin', {
      value: SplitValueRange.Max,
    });
  }

  if (message) {
    const errorField: WrongSplitInputs = {
      id: splittingId,
      message: message,
      ordinal: null,
    };

    errorFields.push(errorField);
  }

  return errorFields;
};

export const convertStateValuesToNumber = (
  nitrogenSplittingConfiguration: NSplittingScheduleModule,
): NSplittingScheduleModule => {
  const newState = cloneDeep(nitrogenSplittingConfiguration);

  // Cast string to numbers because decimals have to be handled as string
  newState.configuration.data = newState.configuration.data.map((splitting) => {
    const newSplittingRule = cloneDeep(splitting.splittingRule);

    if (newSplittingRule.from && isString(newSplittingRule.from.value)) {
      newSplittingRule.from.value = Number(newSplittingRule.from.value);
    }

    if (newSplittingRule.to && isString(newSplittingRule.to.value)) {
      newSplittingRule.to.value = Number(newSplittingRule.to.value);
    }

    const newSplits = splitting.splits.map((split) => {
      if (split.percentage.value && isString(split.percentage.value)) {
        const newSplit: NSplittingScheduleSplit = {
          ...split,
          percentage: {
            ...split.percentage,
            value: Number(split.percentage.value),
          },
        };

        return newSplit;
      } else {
        return split;
      }
    });

    return {
      id: splitting.id,
      splits: newSplits,
      stageNumber: splitting.stageNumber,
      splittingRule: newSplittingRule,
    };
  });

  return newState;
};
