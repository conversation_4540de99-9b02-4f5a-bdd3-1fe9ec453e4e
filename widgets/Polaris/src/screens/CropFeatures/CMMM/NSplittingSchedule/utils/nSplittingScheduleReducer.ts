import { cloneDeep } from 'lodash';
import {
  NSplittingScheduleActions,
  NSplittingScheduleModule,
  UpdateSplittingScheduleActions,
} from '@common/types';

export const nSplittingScheduleReducer = (
  state: NSplittingScheduleModule,
  action: NSplittingScheduleActions,
): NSplittingScheduleModule => {
  const stateToUpdate = cloneDeep(state);

  switch (action.type) {
    case UpdateSplittingScheduleActions.UpdateRule: {
      stateToUpdate.configuration.data.forEach((elem) => {
        if (elem.id === action.splittingId) {
          elem.splittingRule = action.payload;
        }
      });

      return stateToUpdate;
    }
    case UpdateSplittingScheduleActions.UpdateSplit: {
      stateToUpdate.configuration.data.forEach((elem) => {
        if (elem.id === action.splittingId) {
          elem.splits.forEach((split) => {
            if (split.growthStageId === action.payload.growthStageId) {
              split = action.payload;
            }
          });
        }
      });

      return stateToUpdate;
    }
    case UpdateSplittingScheduleActions.UpdateState: {
      const newState = cloneDeep(action.payload);

      return newState;
    }
  }
};
