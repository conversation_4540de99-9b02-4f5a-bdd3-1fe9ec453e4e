import {
  splittingSchedule<PERSON>hangeH<PERSON><PERSON>,
  isSplittingRule,
  isValueInRange,
  convertRulesToLinkedList,
  getIncorrectRuleRangeFields,
} from '../splittingUtils';
import {
  NSplittingRule,
  NSplittingScheduleData,
  NSplittingScheduleKeys,
  NSplittingScheduleSplit,
  RangeValidity,
  RuleError,
  SplittingScheduleChangeHandlerArgs,
  UpdateRuleAction,
  UpdateSplitAction,
  UpdateSplittingScheduleActions,
} from '@common/types';
import { NitrogenSplittingRuleErrorCauses, RuleValueRange } from '../constants';
import { FilterType } from '@widgets/Polaris/src/types';
import { getRuleMockWithCustomValue, nSplittingScheduleConfigurationMock } from '@common/mocks';

describe('splittingUtils', () => {
  const splittingRuleMock: NSplittingRule = {
    to: {
      value: 120,
      canEdit: true,
      isApplicable: true,
    },
    from: null,
    toRangeType: FilterType.LT,
    fromRangeType: FilterType.GTE,
    splitRuleDemandUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  };
  const splitMock: NSplittingScheduleSplit = {
    ordinal: 1,
    percentage: {
      value: 100,
      canEdit: true,
      isApplicable: true,
    },
    growthStageId: '2b0e89a2-569f-4f1f-8700-93d280460546',
  };

  describe('isSplittingRule()', () => {
    it(`should return true for Rule object`, () => {
      const result = isSplittingRule(splittingRuleMock);
      const expectedResult = true;

      expect(result).toEqual(expectedResult);
    });

    it(`should return false for non Rule object`, () => {
      const result = isSplittingRule(splitMock);
      const expectedResult = false;

      expect(result).toEqual(expectedResult);
    });
  });

  describe('splittingScheduleChangeHandler()', () => {
    it(`handle changes for LT Rule data`, () => {
      const data: SplittingScheduleChangeHandlerArgs = {
        data: splittingRuleMock,
        value: '123',
        key: NSplittingScheduleKeys.To,
        splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
      };
      const dispatch = jest.fn();

      splittingScheduleChangeHandler(dispatch, data);

      const dispatchInputMock: UpdateRuleAction = {
        type: UpdateSplittingScheduleActions.UpdateRule,
        splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
        payload: splittingRuleMock,
      };

      expect(dispatch).toHaveBeenCalledTimes(1);
      expect(dispatch).toHaveBeenCalledWith(dispatchInputMock);
    });

    it(`handle changes for GTE Rule data`, () => {
      const data: SplittingScheduleChangeHandlerArgs = {
        data: splittingRuleMock,
        value: '456',
        key: NSplittingScheduleKeys.From,
        splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
      };
      const dispatch = jest.fn();

      splittingScheduleChangeHandler(dispatch, data);

      const dispatchInputMock: UpdateRuleAction = {
        type: UpdateSplittingScheduleActions.UpdateRule,
        splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
        payload: splittingRuleMock,
      };

      expect(dispatch).toHaveBeenCalledTimes(1);
      expect(dispatch).toHaveBeenCalledWith(dispatchInputMock);
    });

    it(`handle changes for Split data`, () => {
      const data: SplittingScheduleChangeHandlerArgs = {
        data: splitMock,
        value: '789',
        key: NSplittingScheduleKeys.Split,
        splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
      };
      const dispatch = jest.fn();

      splittingScheduleChangeHandler(dispatch, data);

      const dispatchInputMock: UpdateSplitAction = {
        type: UpdateSplittingScheduleActions.UpdateSplit,
        splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
        payload: splitMock,
      };

      expect(dispatch).toHaveBeenCalledTimes(1);
      expect(dispatch).toHaveBeenCalledWith(dispatchInputMock);
    });
  });

  describe('isValueInRange()', () => {
    it('should return success - true, for valid range', () => {
      const resultMock: RangeValidity = {
        isValid: true,
        message: '',
      };
      const valueMock = 500;
      const translateMock = jest.fn();
      const result = isValueInRange(
        valueMock,
        RuleValueRange.Min,
        RuleValueRange.Max,
        translateMock,
      );

      expect(result.isValid).toBe(resultMock.isValid);
      expect(result.message).toBe(resultMock.message);
      expect(translateMock).toHaveBeenCalledTimes(0);
    });

    it(`should return success - false, for invalid range: lower than Min(${RuleValueRange.Min})`, () => {
      const translationKeyMock = 'validation.min';
      const translationValueMock = {
        value: RuleValueRange.Min,
      };
      const resultMock: RangeValidity = {
        isValid: false,
        message: 'Value should be higher or equal to 1',
      };
      const valueMock = 0;
      const translateMock = jest.fn().mockImplementationOnce(() => resultMock.message);
      const result = isValueInRange(
        valueMock,
        RuleValueRange.Min,
        RuleValueRange.Max,
        translateMock,
      );

      expect(result.isValid).toBe(resultMock.isValid);
      expect(result.message).toBe(resultMock.message);
      expect(translateMock).toHaveBeenCalledTimes(1);
      expect(translateMock).toHaveBeenCalledWith(translationKeyMock, translationValueMock);
    });

    it(`should return success - false, for invalid range: higher than Max(${RuleValueRange.Max})`, () => {
      const translationKeyMock = 'validation.max';
      const translationValueMock = {
        value: RuleValueRange.Max,
      };
      const resultMock: RangeValidity = {
        isValid: false,
        message: 'Value should be lower or equal to 1000',
      };
      const valueMock = 1001;
      const translateMock = jest.fn().mockImplementationOnce(() => resultMock.message);
      const result = isValueInRange(
        valueMock,
        RuleValueRange.Min,
        RuleValueRange.Max,
        translateMock,
      );

      expect(result.isValid).toBe(resultMock.isValid);
      expect(result.message).toBe(resultMock.message);
      expect(translateMock).toHaveBeenCalledTimes(1);
      expect(translateMock).toHaveBeenCalledWith(translationKeyMock, translationValueMock);
    });
  });

  describe('convertRulesToLinkedList()', () => {
    it('should convert correctly a normal list to linked list', () => {
      const listMock: NSplittingScheduleData[] =
        nSplittingScheduleConfigurationMock.configuration.data;
      const result = convertRulesToLinkedList(listMock);

      expect(result.length).toBe(4);
      expect(result[0].prev).toBe(null);
      expect(result[1].curr).toBeDefined();
      expect(result[2].next).toBeDefined();
      expect(result[3].next).toBeNull();
    });
  });

  describe('getIncorrectRuleRangeFields()', () => {
    it('should throw an error trying to get incorrect fields for rule range', () => {
      const translationKeyMock = 'validation.max';
      const messageMock = 'Value should be lower or equal to 1000';
      const fromMock = 1001;
      const toMock = 180;
      const translateMock = jest.fn().mockImplementationOnce(() => messageMock);
      const ruleMock = getRuleMockWithCustomValue(fromMock, toMock);

      try {
        getIncorrectRuleRangeFields({
          rule: ruleMock,
          currentIncorrectFields: [],
          key: NSplittingScheduleKeys.From,
          splittingId: 'cbca3907-b476-4bad-9776-340a313f2ad7',
          t: translateMock,
        });
      } catch (err) {
        const error = err as RuleError;

        expect(error.cause).toBe(NitrogenSplittingRuleErrorCauses.RuleDiscontinuousRanges);
        expect(error.message).toBe(messageMock);
        expect(translateMock).toHaveBeenCalledTimes(1);
        expect(translateMock).toHaveBeenCalledWith(translationKeyMock, {
          value: RuleValueRange.Max,
        });
      }
    });

    it('should not throw an error and get incorrect fields for rule range', () => {
      const fromMock = 100;
      const toMock = 180;
      const translateMock = jest.fn();
      const ruleMock = getRuleMockWithCustomValue(fromMock, toMock);
      const result = getIncorrectRuleRangeFields({
        rule: ruleMock,
        currentIncorrectFields: [],
        key: NSplittingScheduleKeys.To,
        splittingId: 'cbca3907-b476-4bad-9776-340a313f2ad7',
        t: translateMock,
      });

      expect(result.currentIncorrectFields).toHaveLength(0);
      expect(translateMock).toHaveBeenCalledTimes(0);
    });
  });
});
