import { cloneDeep } from 'lodash';
import { nSplittingScheduleReducer } from '..';
import {
  NSplittingRule,
  NSplittingScheduleActions,
  NSplittingScheduleSplit,
  UpdateSplittingScheduleActions,
} from '@common/types';
import { FilterType } from '@widgets/Polaris/src/types';
import { nSplittingScheduleConfigurationMock } from '@common/mocks';

describe('NSplittingScheduleReducer', () => {
  const initialState = cloneDeep(nSplittingScheduleConfigurationMock);

  it(`should handle ${UpdateSplittingScheduleActions.UpdateRule} action`, () => {
    const newNSplittingRule: NSplittingRule = {
      to: {
        value: 110,
        canEdit: true,
        isApplicable: true,
      },
      from: null,
      toRangeType: FilterType.LT,
      fromRangeType: FilterType.GTE,
      splitRuleDemandUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    };
    const action: NSplittingScheduleActions = {
      type: UpdateSplittingScheduleActions.UpdateRule,
      splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
      payload: newNSplittingRule,
    };
    const expectedNewState = cloneDeep(initialState);

    expectedNewState.configuration.data[0].splittingRule = newNSplittingRule;

    const result = nSplittingScheduleReducer(initialState, action);

    expect(result).toEqual(expectedNewState);
  });

  it(`should handle ${UpdateSplittingScheduleActions.UpdateSplit} action`, () => {
    const newSplit: NSplittingScheduleSplit = {
      ordinal: 1,
      percentage: {
        value: 100,
        canEdit: true,
        isApplicable: true,
      },
      growthStageId: '2b0e89a2-569f-4f1f-8700-93d280460546',
    };
    const action: NSplittingScheduleActions = {
      type: UpdateSplittingScheduleActions.UpdateSplit,
      splittingId: '8f58ecb8-abb6-4520-a8fd-1b31030c17c7',
      payload: newSplit,
    };
    const expectedNewState = cloneDeep(initialState);

    expectedNewState.configuration.data[0].splits[0] = newSplit;

    const result = nSplittingScheduleReducer(initialState, action);

    expect(result).toEqual(expectedNewState);
  });
});
