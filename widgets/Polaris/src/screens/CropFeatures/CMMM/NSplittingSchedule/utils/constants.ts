export const MEDIA_URI_KEYS = {
  DEFAULT: 'DEFAULT',
} as const;

export enum RuleValueRange {
  Min = 1,
  Max = 1000,
}

export enum SplitValueRange {
  Min = 1,
  Max = 100,
}

export const MAX_NUMBER_OF_CHARACTERS = 5;

export enum NitrogenSplittingRuleErrorCauses {
  NonConsecutiveBetweenRows = 'NON_CONSECUTIVE_BETWEEN_ROWS',
  NonConsecutiveCurrentRow = 'NON_CONSECUTIVE_CURRENT_ROW',
  NonConsecutive = 'NON_CONSECUTIVE',
  RuleDiscontinuousRanges = 'RULE_DISCONTINUOUS_RANGES',
  SplitDiscontinuousRanges = 'SPLIT_DISCONTINUOUS_RANGES',
  IncorrectTotal = 'INCORRECT_TOTAL',
}
