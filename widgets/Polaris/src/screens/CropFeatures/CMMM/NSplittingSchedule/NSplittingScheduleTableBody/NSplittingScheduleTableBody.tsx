import React from 'react';
import { Label, Table } from '@yaradigitallabs/ahua-react';
import {
  StyledRuleLabel,
  StyledTableCell,
  StyledTableSplitInput,
  StyledTableSplittingRuleInput,
} from '../NSplittingScheduleTable/NSplittingScheduleTable.styled';
import {
  InputChangeEvent,
  InputChangeHandlerArgs,
  NSplittingScheduleModule,
  NSplittingScheduleKeys,
  InputBlurHandlerArgs,
  WrongRuleInputs,
  WrongSplitInputs,
  SplitWithEmptyCells,
  NSplittingScheduleSplit,
} from '@common/types';
import { recalculateTotalPercentage } from '../utils';
import { NitrogenSplittingRuleErrorCauses } from '../utils/constants';

interface NSplittingScheduleTableBodyProps {
  nitrogenSplittingConfiguration: NSplittingScheduleModule;
  incorrectRuleFields: WrongRuleInputs[];
  incorrectSplitFields: WrongSplitInputs[];
  handleValueChange: (args: InputChangeHandlerArgs) => void;
  handleValueUpdate: (args: InputBlurHandlerArgs) => void;
}

export const NSplittingScheduleTableBody: React.FC<NSplittingScheduleTableBodyProps> = ({
  nitrogenSplittingConfiguration,
  incorrectRuleFields,
  incorrectSplitFields,
  handleValueChange,
  handleValueUpdate,
}) => {
  const { data } = nitrogenSplittingConfiguration.configuration;

  return (
    <tbody data-cy='n-splitting-schedule-table-body'>
      {data?.map((splitting) => {
        const numberOfEmptyCells = data[data.length - 1].splits.length - splitting.stageNumber;
        const splitsWithEmptyCells: SplitWithEmptyCells[] = [...Array(numberOfEmptyCells)].map(
          (_, index) => ({
            ordinal: index + splitting.stageNumber + 1,
            growthStageId: '',
            percentage: {
              canEdit: false,
              isApplicable: false,
              value: 0,
            },
            emptyCell: (key: string) => <StyledTableCell key={key}> </StyledTableCell>,
          }),
        );
        const splitsCellsData: (NSplittingScheduleSplit | SplitWithEmptyCells)[] = [
          ...splitting.splits,
          ...splitsWithEmptyCells,
        ];
        const total = recalculateTotalPercentage(splitting.splits);

        return (
          <Table.Row key={`number-of-splits-${splitting.stageNumber}`} className='table-row'>
            <StyledTableCell>
              <div className='splitting-rule-cell'>
                {splitting.stageNumber !== 1 ? (
                  <>
                    <StyledRuleLabel>&gt;=</StyledRuleLabel>

                    <StyledTableSplittingRuleInput
                      size='xs'
                      value={splitting.splittingRule.from?.value ?? ''}
                      variant={
                        incorrectRuleFields.find(
                          ({ id, key, isOnSameRow, cause }) =>
                            id === splitting.id &&
                            key === NSplittingScheduleKeys.From &&
                            (isOnSameRow
                              ? cause ===
                                  NitrogenSplittingRuleErrorCauses.NonConsecutiveCurrentRow ||
                                cause === NitrogenSplittingRuleErrorCauses.RuleDiscontinuousRanges
                              : cause ===
                                NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows),
                        )
                          ? 'error'
                          : 'default'
                      }
                      onChange={(event: InputChangeEvent) =>
                        handleValueChange({
                          splittingId: splitting.id,
                          key: NSplittingScheduleKeys.From,
                          data: splitting.splittingRule,
                          event,
                        })
                      }
                      onBlur={() =>
                        handleValueUpdate({
                          splittingId: splitting.id,
                          key: NSplittingScheduleKeys.From,
                          data: splitting.splittingRule,
                          numberOfSplits: splitting.stageNumber,
                        })
                      }
                      data-cy={`n-splitting-schedule-rule-${splitting.stageNumber}-input-from`}
                    />
                  </>
                ) : null}

                {splitting.stageNumber !== 4 ? (
                  <>
                    <StyledRuleLabel>&lt;</StyledRuleLabel>

                    <StyledTableSplittingRuleInput
                      size='xs'
                      value={splitting.splittingRule.to?.value ?? ''}
                      variant={
                        incorrectRuleFields.find(
                          ({ id, key, isOnSameRow, cause }) =>
                            id === splitting.id &&
                            key === NSplittingScheduleKeys.To &&
                            (isOnSameRow
                              ? cause ===
                                  NitrogenSplittingRuleErrorCauses.NonConsecutiveCurrentRow ||
                                cause === NitrogenSplittingRuleErrorCauses.RuleDiscontinuousRanges
                              : cause ===
                                NitrogenSplittingRuleErrorCauses.NonConsecutiveBetweenRows),
                        )
                          ? 'error'
                          : 'default'
                      }
                      onChange={(event: InputChangeEvent) =>
                        handleValueChange({
                          splittingId: splitting.id,
                          key: NSplittingScheduleKeys.To,
                          data: splitting.splittingRule,
                          event,
                        })
                      }
                      onBlur={() =>
                        handleValueUpdate({
                          splittingId: splitting.id,
                          key: NSplittingScheduleKeys.To,
                          data: splitting.splittingRule,
                          numberOfSplits: splitting.stageNumber,
                        })
                      }
                      data-cy={`n-splitting-schedule-rule-${splitting.stageNumber}-input-to`}
                    />
                  </>
                ) : null}
              </div>
            </StyledTableCell>

            <StyledTableCell
              data-cy={`n-splitting-schedule-number-of-splits-${splitting.stageNumber}`}
            >
              {splitting.stageNumber}
            </StyledTableCell>

            <StyledTableCell
              data-cy={`n-splitting-schedule-total-${splitting.stageNumber}`}
              className={total !== 100 ? 'incorrect-total' : ''}
            >
              {total}
            </StyledTableCell>

            {splitsCellsData
              .sort((currentSplit, nextSplit) => currentSplit.ordinal - nextSplit.ordinal)
              .map((split, index) => {
                return 'emptyCell' in split ? (
                  split.emptyCell(`split-stage-${splitting.stageNumber}-${index}`)
                ) : (
                  <StyledTableCell key={split.growthStageId}>
                    {split.percentage.canEdit ? (
                      <StyledTableSplitInput
                        variant={
                          incorrectSplitFields.find(
                            ({ id, ordinal }) => id === splitting.id && ordinal === split.ordinal,
                          )
                            ? 'error'
                            : 'default'
                        }
                        size='xs'
                        value={split.percentage.value ?? ''}
                        onChange={(event: InputChangeEvent) => {
                          handleValueChange({
                            splittingId: splitting.id,
                            key: NSplittingScheduleKeys.Split,
                            event,
                            data: split,
                          });
                        }}
                        onBlur={() =>
                          handleValueUpdate({
                            splittingId: splitting.id,
                            key: NSplittingScheduleKeys.Split,
                            numberOfSplits: splitting.stageNumber,
                          })
                        }
                        data-cy={`n-splitting-schedule-row-${splitting.stageNumber}-split-${split.ordinal}-input`}
                      />
                    ) : (
                      <Label>{split.percentage.value}</Label>
                    )}
                  </StyledTableCell>
                );
              })}
          </Table.Row>
        );
      })}
    </tbody>
  );
};
