// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NSplittingScheduleTable catching onBlur event after onChange with correct value 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eWFPgW"
        data-cy="n-splitting-schedule-content"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
          data-cy="n-splitting-schedule-sub-title"
        >
          subTitle
        </h2>
        <div
          class="c-gpZOYa"
        >
          <table
            class="c-kwAGqj splitting-schedule-table"
            data-cy="n-splitting-schedule-table"
          >
            <thead
              data-cy="n-splitting-schedule-table-header"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.numberHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  1
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  2
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  3
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  4
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.nameHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Tillering"
                >
                  Tillering
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Stem-elongation"
                >
                  Stem elongation
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flag-leaf"
                >
                  Flag leaf
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flowering"
                >
                  Flowering
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.imageHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Tillering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Tillering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Stem elongation"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Stem-elongation"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flag leaf"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flag-leaf"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flowering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flowering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-kKsAIr"
                >
                  rule.nameHead
                  <br />
                  <label
                    class="c-dYJjti"
                  >
                    rule.subNameHead
                  </label>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  numberOfSplits
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  <div
                    class="table-header-with-tooltip"
                  >
                    <label
                      class="table-data-label"
                    >
                      Total (%)
                    </label>
                    <button
                      class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                      data-state="closed"
                      type="button"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
              </tr>
            </thead>
            <tbody
              data-cy="n-splitting-schedule-table-body"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-1-input-to"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-1"
                >
                  1
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-1"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-1-split-1-input"
                      value="100"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-from"
                        value="9"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-to"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-2"
                >
                  2
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-2"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-1-input"
                      value="60"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-2-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-from"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-to"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-3"
                >
                  3
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-3"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-3-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-4-input-from"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-4"
                >
                  4
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-4"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-3-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-4-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eWFPgW"
      data-cy="n-splitting-schedule-content"
    >
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
        data-cy="n-splitting-schedule-sub-title"
      >
        subTitle
      </h2>
      <div
        class="c-gpZOYa"
      >
        <table
          class="c-kwAGqj splitting-schedule-table"
          data-cy="n-splitting-schedule-table"
        >
          <thead
            data-cy="n-splitting-schedule-table-header"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.numberHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                1
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                2
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                3
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                4
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.nameHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Tillering"
              >
                Tillering
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Stem-elongation"
              >
                Stem elongation
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flag-leaf"
              >
                Flag leaf
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flowering"
              >
                Flowering
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.imageHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Tillering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Tillering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Stem elongation"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Stem-elongation"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flag leaf"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flag-leaf"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flowering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flowering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-kKsAIr"
              >
                rule.nameHead
                <br />
                <label
                  class="c-dYJjti"
                >
                  rule.subNameHead
                </label>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                numberOfSplits
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                <div
                  class="table-header-with-tooltip"
                >
                  <label
                    class="table-data-label"
                  >
                    Total (%)
                  </label>
                  <button
                    class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                    data-state="closed"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </button>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
            </tr>
          </thead>
          <tbody
            data-cy="n-splitting-schedule-table-body"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-1-input-to"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-1"
              >
                1
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-1"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-1-split-1-input"
                    value="100"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-from"
                      value="9"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-to"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-2"
              >
                2
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-2"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-1-input"
                    value="60"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-2-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-from"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-to"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-3"
              >
                3
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-3"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-3-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-4-input-from"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-4"
              >
                4
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-4"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-3-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-4-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`NSplittingScheduleTable catching onBlur event after onChange with incorrect value 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eWFPgW"
        data-cy="n-splitting-schedule-content"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
          data-cy="n-splitting-schedule-sub-title"
        >
          subTitle
        </h2>
        <div
          class="c-gpZOYa"
        >
          <table
            class="c-kwAGqj splitting-schedule-table"
            data-cy="n-splitting-schedule-table"
          >
            <thead
              data-cy="n-splitting-schedule-table-header"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.numberHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  1
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  2
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  3
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  4
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.nameHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Tillering"
                >
                  Tillering
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Stem-elongation"
                >
                  Stem elongation
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flag-leaf"
                >
                  Flag leaf
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flowering"
                >
                  Flowering
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.imageHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Tillering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Tillering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Stem elongation"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Stem-elongation"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flag leaf"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flag-leaf"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flowering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flowering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-kKsAIr"
                >
                  rule.nameHead
                  <br />
                  <label
                    class="c-dYJjti"
                  >
                    rule.subNameHead
                  </label>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  numberOfSplits
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  <div
                    class="table-header-with-tooltip"
                  >
                    <label
                      class="table-data-label"
                    >
                      Total (%)
                    </label>
                    <button
                      class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                      data-state="closed"
                      type="button"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
              </tr>
            </thead>
            <tbody
              data-cy="n-splitting-schedule-table-body"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-1-input-to"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-1"
                >
                  1
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-1"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-1-split-1-input"
                      value="100"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-from"
                        value="0"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-to"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-2"
                >
                  2
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-2"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-1-input"
                      value="60"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-2-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-from"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-to"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-3"
                >
                  3
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-3"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-3-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-4-input-from"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-4"
                >
                  4
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-4"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-3-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-4-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eWFPgW"
      data-cy="n-splitting-schedule-content"
    >
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
        data-cy="n-splitting-schedule-sub-title"
      >
        subTitle
      </h2>
      <div
        class="c-gpZOYa"
      >
        <table
          class="c-kwAGqj splitting-schedule-table"
          data-cy="n-splitting-schedule-table"
        >
          <thead
            data-cy="n-splitting-schedule-table-header"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.numberHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                1
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                2
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                3
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                4
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.nameHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Tillering"
              >
                Tillering
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Stem-elongation"
              >
                Stem elongation
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flag-leaf"
              >
                Flag leaf
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flowering"
              >
                Flowering
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.imageHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Tillering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Tillering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Stem elongation"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Stem-elongation"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flag leaf"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flag-leaf"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flowering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flowering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-kKsAIr"
              >
                rule.nameHead
                <br />
                <label
                  class="c-dYJjti"
                >
                  rule.subNameHead
                </label>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                numberOfSplits
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                <div
                  class="table-header-with-tooltip"
                >
                  <label
                    class="table-data-label"
                  >
                    Total (%)
                  </label>
                  <button
                    class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                    data-state="closed"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </button>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
            </tr>
          </thead>
          <tbody
            data-cy="n-splitting-schedule-table-body"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-1-input-to"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-1"
              >
                1
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-1"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-1-split-1-input"
                    value="100"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-from"
                      value="0"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-to"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-2"
              >
                2
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-2"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-1-input"
                    value="60"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-2-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-from"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-to"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-3"
              >
                3
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-3"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-3-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-4-input-from"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-4"
              >
                4
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-4"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-3-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-4-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`NSplittingScheduleTable catching onBlur event with correct input and an incorrect value in any of the other fields 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eWFPgW"
        data-cy="n-splitting-schedule-content"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
          data-cy="n-splitting-schedule-sub-title"
        >
          subTitle
        </h2>
        <div
          class="c-gpZOYa"
        >
          <table
            class="c-kwAGqj splitting-schedule-table"
            data-cy="n-splitting-schedule-table"
          >
            <thead
              data-cy="n-splitting-schedule-table-header"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.numberHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  1
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  2
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  3
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  4
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.nameHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Tillering"
                >
                  Tillering
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Stem-elongation"
                >
                  Stem elongation
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flag-leaf"
                >
                  Flag leaf
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flowering"
                >
                  Flowering
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.imageHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Tillering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Tillering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Stem elongation"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Stem-elongation"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flag leaf"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flag-leaf"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flowering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flowering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-kKsAIr"
                >
                  rule.nameHead
                  <br />
                  <label
                    class="c-dYJjti"
                  >
                    rule.subNameHead
                  </label>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  numberOfSplits
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  <div
                    class="table-header-with-tooltip"
                  >
                    <label
                      class="table-data-label"
                    >
                      Total (%)
                    </label>
                    <button
                      class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                      data-state="closed"
                      type="button"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
              </tr>
            </thead>
            <tbody
              data-cy="n-splitting-schedule-table-body"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-1-input-to"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-1"
                >
                  1
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-1"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-1-split-1-input"
                      value="100"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-from"
                        value="9"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-to"
                        value="9"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-2"
                >
                  2
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-2"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-1-input"
                      value="60"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-2-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-from"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-to"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-3"
                >
                  3
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-3"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-3-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-4-input-from"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-4"
                >
                  4
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-4"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-3-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-4-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eWFPgW"
      data-cy="n-splitting-schedule-content"
    >
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
        data-cy="n-splitting-schedule-sub-title"
      >
        subTitle
      </h2>
      <div
        class="c-gpZOYa"
      >
        <table
          class="c-kwAGqj splitting-schedule-table"
          data-cy="n-splitting-schedule-table"
        >
          <thead
            data-cy="n-splitting-schedule-table-header"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.numberHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                1
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                2
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                3
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                4
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.nameHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Tillering"
              >
                Tillering
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Stem-elongation"
              >
                Stem elongation
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flag-leaf"
              >
                Flag leaf
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flowering"
              >
                Flowering
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.imageHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Tillering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Tillering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Stem elongation"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Stem-elongation"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flag leaf"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flag-leaf"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flowering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flowering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-kKsAIr"
              >
                rule.nameHead
                <br />
                <label
                  class="c-dYJjti"
                >
                  rule.subNameHead
                </label>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                numberOfSplits
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                <div
                  class="table-header-with-tooltip"
                >
                  <label
                    class="table-data-label"
                  >
                    Total (%)
                  </label>
                  <button
                    class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                    data-state="closed"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </button>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
            </tr>
          </thead>
          <tbody
            data-cy="n-splitting-schedule-table-body"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-1-input-to"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-1"
              >
                1
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-1"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-1-split-1-input"
                    value="100"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-from"
                      value="9"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-to"
                      value="9"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-2"
              >
                2
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-2"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-1-input"
                    value="60"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-2-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-honwvX-variant-error c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-gimtZx-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-from"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-to"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-3"
              >
                3
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-3"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-3-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-4-input-from"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-4"
              >
                4
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-4"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-3-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-4-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`NSplittingScheduleTable catching onChange event with correct input 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eWFPgW"
        data-cy="n-splitting-schedule-content"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
          data-cy="n-splitting-schedule-sub-title"
        >
          subTitle
        </h2>
        <div
          class="c-gpZOYa"
        >
          <table
            class="c-kwAGqj splitting-schedule-table"
            data-cy="n-splitting-schedule-table"
          >
            <thead
              data-cy="n-splitting-schedule-table-header"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.numberHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  1
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  2
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  3
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  4
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.nameHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Tillering"
                >
                  Tillering
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Stem-elongation"
                >
                  Stem elongation
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flag-leaf"
                >
                  Flag leaf
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flowering"
                >
                  Flowering
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.imageHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Tillering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Tillering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Stem elongation"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Stem-elongation"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flag leaf"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flag-leaf"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flowering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flowering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-kKsAIr"
                >
                  rule.nameHead
                  <br />
                  <label
                    class="c-dYJjti"
                  >
                    rule.subNameHead
                  </label>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  numberOfSplits
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  <div
                    class="table-header-with-tooltip"
                  >
                    <label
                      class="table-data-label"
                    >
                      Total (%)
                    </label>
                    <button
                      class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                      data-state="closed"
                      type="button"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
              </tr>
            </thead>
            <tbody
              data-cy="n-splitting-schedule-table-body"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-1-input-to"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-1"
                >
                  1
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-1"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-1-split-1-input"
                      value="100"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-from"
                        value="1201"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-to"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-2"
                >
                  2
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-2"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-1-input"
                      value="60"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-2-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-from"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-to"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-3"
                >
                  3
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-3"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-3-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-4-input-from"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-4"
                >
                  4
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-4"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-3-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-4-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eWFPgW"
      data-cy="n-splitting-schedule-content"
    >
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
        data-cy="n-splitting-schedule-sub-title"
      >
        subTitle
      </h2>
      <div
        class="c-gpZOYa"
      >
        <table
          class="c-kwAGqj splitting-schedule-table"
          data-cy="n-splitting-schedule-table"
        >
          <thead
            data-cy="n-splitting-schedule-table-header"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.numberHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                1
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                2
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                3
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                4
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.nameHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Tillering"
              >
                Tillering
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Stem-elongation"
              >
                Stem elongation
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flag-leaf"
              >
                Flag leaf
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flowering"
              >
                Flowering
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.imageHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Tillering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Tillering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Stem elongation"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Stem-elongation"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flag leaf"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flag-leaf"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flowering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flowering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-kKsAIr"
              >
                rule.nameHead
                <br />
                <label
                  class="c-dYJjti"
                >
                  rule.subNameHead
                </label>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                numberOfSplits
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                <div
                  class="table-header-with-tooltip"
                >
                  <label
                    class="table-data-label"
                  >
                    Total (%)
                  </label>
                  <button
                    class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                    data-state="closed"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </button>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
            </tr>
          </thead>
          <tbody
            data-cy="n-splitting-schedule-table-body"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-1-input-to"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-1"
              >
                1
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-1"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-1-split-1-input"
                    value="100"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-from"
                      value="1201"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-to"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-2"
              >
                2
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-2"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-1-input"
                    value="60"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-2-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-from"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-to"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-3"
              >
                3
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-3"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-3-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-4-input-from"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-4"
              >
                4
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-4"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-3-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-4-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`NSplittingScheduleTable catching onChange event with incorrect input 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eWFPgW"
        data-cy="n-splitting-schedule-content"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
          data-cy="n-splitting-schedule-sub-title"
        >
          subTitle
        </h2>
        <div
          class="c-gpZOYa"
        >
          <table
            class="c-kwAGqj splitting-schedule-table"
            data-cy="n-splitting-schedule-table"
          >
            <thead
              data-cy="n-splitting-schedule-table-header"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.numberHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  1
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  2
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  3
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  4
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.nameHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Tillering"
                >
                  Tillering
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Stem-elongation"
                >
                  Stem elongation
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flag-leaf"
                >
                  Flag leaf
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flowering"
                >
                  Flowering
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.imageHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Tillering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Tillering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Stem elongation"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Stem-elongation"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flag leaf"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flag-leaf"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flowering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flowering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-kKsAIr"
                >
                  rule.nameHead
                  <br />
                  <label
                    class="c-dYJjti"
                  >
                    rule.subNameHead
                  </label>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  numberOfSplits
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  <div
                    class="table-header-with-tooltip"
                  >
                    <label
                      class="table-data-label"
                    >
                      Total (%)
                    </label>
                    <button
                      class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                      data-state="closed"
                      type="button"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
              </tr>
            </thead>
            <tbody
              data-cy="n-splitting-schedule-table-body"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-1-input-to"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-1"
                >
                  1
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-1"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-1-split-1-input"
                      value="100"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-from"
                        value="1201"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-to"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-2"
                >
                  2
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-2"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-1-input"
                      value="60"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-2-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-from"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-to"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-3"
                >
                  3
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-3"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-3-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-4-input-from"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-4"
                >
                  4
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-4"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-3-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-4-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eWFPgW"
      data-cy="n-splitting-schedule-content"
    >
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
        data-cy="n-splitting-schedule-sub-title"
      >
        subTitle
      </h2>
      <div
        class="c-gpZOYa"
      >
        <table
          class="c-kwAGqj splitting-schedule-table"
          data-cy="n-splitting-schedule-table"
        >
          <thead
            data-cy="n-splitting-schedule-table-header"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.numberHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                1
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                2
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                3
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                4
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.nameHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Tillering"
              >
                Tillering
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Stem-elongation"
              >
                Stem elongation
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flag-leaf"
              >
                Flag leaf
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flowering"
              >
                Flowering
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.imageHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Tillering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Tillering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Stem elongation"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Stem-elongation"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flag leaf"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flag-leaf"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flowering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flowering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-kKsAIr"
              >
                rule.nameHead
                <br />
                <label
                  class="c-dYJjti"
                >
                  rule.subNameHead
                </label>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                numberOfSplits
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                <div
                  class="table-header-with-tooltip"
                >
                  <label
                    class="table-data-label"
                  >
                    Total (%)
                  </label>
                  <button
                    class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                    data-state="closed"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </button>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
            </tr>
          </thead>
          <tbody
            data-cy="n-splitting-schedule-table-body"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-1-input-to"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-1"
              >
                1
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-1"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-1-split-1-input"
                    value="100"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-from"
                      value="1201"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-to"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-2"
              >
                2
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-2"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-1-input"
                    value="60"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-2-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-from"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-to"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-3"
              >
                3
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-3"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-3-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-4-input-from"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-4"
              >
                4
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-4"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-3-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-4-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`NSplittingScheduleTable renders without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-eWFPgW"
        data-cy="n-splitting-schedule-content"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
          data-cy="n-splitting-schedule-sub-title"
        >
          subTitle
        </h2>
        <div
          class="c-gpZOYa"
        >
          <table
            class="c-kwAGqj splitting-schedule-table"
            data-cy="n-splitting-schedule-table"
          >
            <thead
              data-cy="n-splitting-schedule-table-header"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.numberHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  1
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  2
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  3
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  4
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.nameHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Tillering"
                >
                  Tillering
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Stem-elongation"
                >
                  Stem elongation
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flag-leaf"
                >
                  Flag leaf
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                  data-cy="growth-stage-name-Flowering"
                >
                  Flowering
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-fnskwG"
                  colspan="3"
                >
                  growthStage.imageHead
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Tillering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Tillering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Stem elongation"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Stem-elongation"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flag leaf"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flag-leaf"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG table-head-image"
                >
                  <div
                    class="c-PJLV c-PJLV-iDHMfl-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 100%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          alt="Flowering"
                          class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                          data-cy="growth-stage-image-Flowering"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                        />
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <th
                  class="c-kxWgPf c-kKsAIr"
                >
                  rule.nameHead
                  <br />
                  <label
                    class="c-dYJjti"
                  >
                    rule.subNameHead
                  </label>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  numberOfSplits
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  <div
                    class="table-header-with-tooltip"
                  >
                    <label
                      class="table-data-label"
                    >
                      Total (%)
                    </label>
                    <button
                      class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                      data-state="closed"
                      type="button"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
                <th
                  class="c-kxWgPf c-fnskwG"
                >
                  split
                </th>
              </tr>
            </thead>
            <tbody
              data-cy="n-splitting-schedule-table-body"
            >
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-1-input-to"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-1"
                >
                  1
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-1"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-1-split-1-input"
                      value="100"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-from"
                        value="120"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-2-input-to"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-2"
                >
                  2
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-2"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-1-input"
                      value="60"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-2-split-2-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-from"
                        value="180"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                    <label
                      class="c-ivQRjQ"
                    >
                      &lt;
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-3-input-to"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-3"
                >
                  3
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-3"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-3-split-3-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                   
                </td>
              </tr>
              <tr
                class="c-eDGYZe table-row"
              >
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="splitting-rule-cell"
                  >
                    <label
                      class="c-ivQRjQ"
                    >
                      &gt;=
                    </label>
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                        data-cy="n-splitting-schedule-rule-4-input-from"
                        value="230"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-number-of-splits-4"
                >
                  4
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                  data-cy="n-splitting-schedule-total-4"
                >
                  100
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-1-input"
                      value="40"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-2-input"
                      value="30"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-3-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR c-kszBpO"
                >
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                      data-cy="n-splitting-schedule-row-4-split-4-input"
                      value="15"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-eWFPgW"
      data-cy="n-splitting-schedule-content"
    >
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css c-jEqnPD"
        data-cy="n-splitting-schedule-sub-title"
      >
        subTitle
      </h2>
      <div
        class="c-gpZOYa"
      >
        <table
          class="c-kwAGqj splitting-schedule-table"
          data-cy="n-splitting-schedule-table"
        >
          <thead
            data-cy="n-splitting-schedule-table-header"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.numberHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                1
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                2
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                3
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                4
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.nameHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Tillering"
              >
                Tillering
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Stem-elongation"
              >
                Stem elongation
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flag-leaf"
              >
                Flag leaf
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
                data-cy="growth-stage-name-Flowering"
              >
                Flowering
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-fnskwG"
                colspan="3"
              >
                growthStage.imageHead
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Tillering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Tillering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/14.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Stem elongation"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Stem-elongation"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flag leaf"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flag-leaf"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/30.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG table-head-image"
              >
                <div
                  class="c-PJLV c-PJLV-iDHMfl-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 100%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        alt="Flowering"
                        class="c-hinyfY c-hinyfY-ikyJlj-css c-jBjSYi"
                        data-cy="growth-stage-image-Flowering"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/bbch_cereals/59.svg"
                      />
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <th
                class="c-kxWgPf c-kKsAIr"
              >
                rule.nameHead
                <br />
                <label
                  class="c-dYJjti"
                >
                  rule.subNameHead
                </label>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                numberOfSplits
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                <div
                  class="table-header-with-tooltip"
                >
                  <label
                    class="table-data-label"
                  >
                    Total (%)
                  </label>
                  <button
                    class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost icon-button"
                    data-state="closed"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                      />
                    </svg>
                  </button>
                </div>
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
              <th
                class="c-kxWgPf c-fnskwG"
              >
                split
              </th>
            </tr>
          </thead>
          <tbody
            data-cy="n-splitting-schedule-table-body"
          >
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-1-input-to"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-1"
              >
                1
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-1"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-1-split-1-input"
                    value="100"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-from"
                      value="120"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-2-input-to"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-2"
              >
                2
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-2"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-1-input"
                    value="60"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-2-split-2-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-from"
                      value="180"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                  <label
                    class="c-ivQRjQ"
                  >
                    &lt;
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-3-input-to"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-3"
              >
                3
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-3"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-3-split-3-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                 
              </td>
            </tr>
            <tr
              class="c-eDGYZe table-row"
            >
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="splitting-rule-cell"
                >
                  <label
                    class="c-ivQRjQ"
                  >
                    &gt;=
                  </label>
                  <div
                    class="c-gJoajD c-gJoajD-ifGHEql-css"
                  >
                    <input
                      class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-lltvtV"
                      data-cy="n-splitting-schedule-rule-4-input-from"
                      value="230"
                    />
                    <span
                      class="c-fcBbhr"
                    />
                  </div>
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-number-of-splits-4"
              >
                4
              </td>
              <td
                class="c-doquzR c-kszBpO"
                data-cy="n-splitting-schedule-total-4"
              >
                100
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-1-input"
                    value="40"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-2-input"
                    value="30"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-3-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
              <td
                class="c-doquzR c-kszBpO"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv c-ceXbhU"
                    data-cy="n-splitting-schedule-row-4-split-4-input"
                    value="15"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
