import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { NSplittingScheduleTable } from '../NSplittingScheduleTable';
import userEvent from '@testing-library/user-event';
import { setupServer } from 'msw/node';
import { mockGrowthScaleStages } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';
import {
  nSplittingScheduleConfigurationMock,
  updateNSplittingScheduleConfiugrationHandler,
} from '@common/mocks';

const server = setupServer(updateNSplittingScheduleConfiugrationHandler);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

const mockSetDisplaySnackbar = jest.fn();

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

jest.mock('@libs/snackbar-context/snackbar-context', () => ({
  ...jest.requireActual('@libs/snackbar-context/snackbar-context'),
  useSnackbar: () => ({
    setDisplaySnackbar: mockSetDisplaySnackbar,
  }),
}));

describe('NSplittingScheduleTable', () => {
  afterEach(() => {
    mockSetDisplaySnackbar.mockClear();
  });

  it('renders without crashing', async () => {
    const component = render(
      <NSplittingScheduleTable
        growthScaleStages={mockGrowthScaleStages}
        nSplittingConfiguration={nSplittingScheduleConfigurationMock}
      />,
    );

    expect(component).toMatchSnapshot();
  });

  it('catching onChange event with correct input', async () => {
    const component = render(
      <NSplittingScheduleTable
        growthScaleStages={mockGrowthScaleStages}
        nSplittingConfiguration={nSplittingScheduleConfigurationMock}
      />,
    );
    const twoSplitsRuleFromInput = component.getByTestId(
      'n-splitting-schedule-rule-2-input-from',
    ) as HTMLInputElement;
    const userInputMock = '1';
    const newInputValueMock = '1201';

    await userEvent.type(twoSplitsRuleFromInput, userInputMock);

    const inputForCheck = component.getByDisplayValue(newInputValueMock) as HTMLInputElement;

    expect(component).toMatchSnapshot();
    expect(inputForCheck).toBeInTheDocument();
    expect(inputForCheck.value).toBe(newInputValueMock);
    expect(mockSetDisplaySnackbar).toHaveBeenCalledTimes(0);
  });

  it('catching onChange event with incorrect input', async () => {
    const component = render(
      <NSplittingScheduleTable
        growthScaleStages={mockGrowthScaleStages}
        nSplittingConfiguration={nSplittingScheduleConfigurationMock}
      />,
    );
    const twoSplitsRuleFromInput = component.getByTestId('n-splitting-schedule-rule-2-input-from');
    const userInputMock = 'a';
    const newInputValueMock = '120';

    await userEvent.type(twoSplitsRuleFromInput, userInputMock);

    const inputForCheck = component.getByDisplayValue(newInputValueMock) as HTMLInputElement;

    expect(component).toMatchSnapshot();
    expect(inputForCheck).toBeInTheDocument();
    expect(inputForCheck.value).toBe(newInputValueMock);
    expect(mockSetDisplaySnackbar).toHaveBeenCalledTimes(0);
  });

  it('catching onBlur event after onChange with incorrect value', async () => {
    const component = render(
      <NSplittingScheduleTable
        growthScaleStages={mockGrowthScaleStages}
        nSplittingConfiguration={nSplittingScheduleConfigurationMock}
      />,
    );
    const twoSplitsRuleFromInput = component.getByTestId(
      'n-splitting-schedule-rule-2-input-from',
    ) as HTMLInputElement;
    const userInputMock = '0';
    const newInputValueMock = '0';

    await waitFor(() => {
      fireEvent.change(twoSplitsRuleFromInput, { target: { value: userInputMock } });
      fireEvent.blur(twoSplitsRuleFromInput);
    });

    const inputForCheck = component.getByDisplayValue(newInputValueMock) as HTMLInputElement;
    const setDisplaySnackBarArgsMock = {
      title: 'validation.min',
      colorConcept: 'destructiveLight',
      duration: 3000,
      icon: 'Close',
      open: true,
      placement: 'bottomRight',
      useClose: true,
    };

    expect(component).toMatchSnapshot();
    expect(inputForCheck).toBeInTheDocument();
    expect(inputForCheck.value).toBe(newInputValueMock);
    expect(mockSetDisplaySnackbar).toHaveBeenCalledTimes(1);
    expect(mockSetDisplaySnackbar).toHaveBeenCalledWith(setDisplaySnackBarArgsMock);
  });

  it('catching onBlur event after onChange with correct value', async () => {
    const component = render(
      <NSplittingScheduleTable
        growthScaleStages={mockGrowthScaleStages}
        nSplittingConfiguration={nSplittingScheduleConfigurationMock}
      />,
    );
    const twoSplitsRuleFromInput = component.getByTestId(
      'n-splitting-schedule-rule-2-input-from',
    ) as HTMLInputElement;
    const userInputMock = '9';
    const newInputValueMock = '9';

    await waitFor(() => {
      fireEvent.change(twoSplitsRuleFromInput, { target: { value: userInputMock } });
      fireEvent.blur(twoSplitsRuleFromInput);
    });

    const inputForCheck = component.getByDisplayValue(newInputValueMock) as HTMLInputElement;

    expect(component).toMatchSnapshot();
    expect(inputForCheck).toBeInTheDocument();
    expect(inputForCheck.value).toBe(newInputValueMock);
    expect(mockSetDisplaySnackbar).toHaveBeenCalledTimes(1);
  });

  it('catching onBlur event with correct input and an incorrect value in any of the other fields', async () => {
    const component = render(
      <NSplittingScheduleTable
        growthScaleStages={mockGrowthScaleStages}
        nSplittingConfiguration={nSplittingScheduleConfigurationMock}
      />,
    );
    const twoSplitsRuleFromInput = component.getByTestId(
      'n-splitting-schedule-rule-2-input-from',
    ) as HTMLInputElement;
    const twoSplitsRuleToInput = component.getByTestId(
      'n-splitting-schedule-rule-2-input-to',
    ) as HTMLInputElement;
    const userInputMock = '9';
    const newInputValueMock = '9';

    await waitFor(() => {
      fireEvent.change(twoSplitsRuleFromInput, { target: { value: userInputMock } });
      fireEvent.change(twoSplitsRuleToInput, { target: { value: userInputMock } });
      fireEvent.blur(twoSplitsRuleFromInput);
      fireEvent.blur(twoSplitsRuleToInput);
    });

    const inputsForCheck = component.getAllByDisplayValue(newInputValueMock) as HTMLInputElement[];

    expect(component).toMatchSnapshot();
    expect(inputsForCheck.length).toBe(2);
    expect(inputsForCheck[0]).toBeInTheDocument();
    expect(inputsForCheck[1]).toBeInTheDocument();
    expect(inputsForCheck[0].value).toBe(newInputValueMock);
    expect(inputsForCheck[1].value).toBe(newInputValueMock);
    expect(mockSetDisplaySnackbar).toHaveBeenCalledTimes(1);
  });
});
