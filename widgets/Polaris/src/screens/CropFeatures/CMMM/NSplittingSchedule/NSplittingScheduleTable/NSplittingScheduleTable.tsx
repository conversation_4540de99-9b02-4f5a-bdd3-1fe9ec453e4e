import { Table } from '@yaradigitallabs/ahua-react';
import React, { useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep, isEqual } from 'lodash';
import {
  StyledDiv,
  StyledSubtitle,
  NSplittingScheduleTableContainer,
} from './NSplittingScheduleTable.styled';
import {
  nSplittingScheduleReducer,
  splittingScheduleChangeHandler,
  getInvalidSplitFields,
  convertRulesToLinkedList,
  getIncorrectRuleRangeFields,
  getIncorrectRuleFieldsBetweenRows,
  getIncorrectRuleFieldsOnSameRow,
  convertStateValuesToNumber,
} from '../utils';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import {
  GrowthScaleStage,
  NSplittingScheduleModule,
  InputChangeHandlerArgs,
  InputBlurHandlerArgs,
  WrongRuleInputs,
  RuleError,
  WrongSplitInputs,
  NSplittingScheduleKeys,
  ValidateRuleHandlerArgs,
  UpdateSplittingScheduleActions,
} from '@common/types';
import { NSplittingScheduleTableHeader } from '../NSplittingScheduleTableHeader';
import { NSplittingScheduleTableBody } from '../NSplittingScheduleTableBody';
import { useUpdateNSplittingScheduleConfiguration } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import { MAX_NUMBER_OF_CHARACTERS, NitrogenSplittingRuleErrorCauses } from '../utils/constants';
import { getRuleError } from '@widgets/Polaris/utils/error/errorUtils';

interface NSplittingScheduleProps {
  nSplittingConfiguration: NSplittingScheduleModule;
  growthScaleStages: GrowthScaleStage[];
}

export const NSplittingScheduleTable: React.FC<NSplittingScheduleProps> = ({
  nSplittingConfiguration,
  growthScaleStages,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.planConfiguration.nSplittingSchedule',
  });
  const { setDisplaySnackbar } = useSnackbar();

  const [nitrogenSplittingConfiguration, dispatch] = useReducer(
    nSplittingScheduleReducer,
    nSplittingConfiguration,
  );
  // "nitrogenSplittingConfiguration" is checked against "state" for differences
  // to avoid sending redundant requests
  const [state, setState] = useState(convertStateValuesToNumber(nitrogenSplittingConfiguration));
  const [incorrectRuleFields, setIncorrectRuleFields] = useState<WrongRuleInputs[]>([]);
  const [incorrectSplitFields, setIncorrectSplitFields] = useState<WrongSplitInputs[]>([]);
  const [rulesValidity, setRulesValidity] = useState<boolean>(true);
  const [splitsValidity, setSplitsValidity] = useState<boolean>(true);

  const { trigger: updateNSplittingScheduleConfiguration } =
    useUpdateNSplittingScheduleConfiguration(nSplittingConfiguration?.id);

  const handleValueChange = ({ data, event, key, splittingId }: InputChangeHandlerArgs): void => {
    const isValid = validateParameterNumber(event.target.value);

    if (!isValid) {
      return;
    }

    if (
      event.target.value.length &&
      event.target.value.split('.')[0].length > MAX_NUMBER_OF_CHARACTERS
    ) {
      return;
    }

    const formattedValue = formatValidDecimalsNumber(event.target.value, 2);
    const validValue = formattedValue.length ? formattedValue : '0';

    splittingScheduleChangeHandler(dispatch, {
      data: data,
      value: validValue,
      key: key,
      splittingId: splittingId,
    });
  };

  const validateRuleSplitting = ({ data, key, splittingId }: ValidateRuleHandlerArgs): boolean => {
    const {
      configuration: { data: stateConfigurationData },
    } = nitrogenSplittingConfiguration;
    const ruleLinkedList = convertRulesToLinkedList(stateConfigurationData);
    const changedField = ruleLinkedList.find((rule) => rule.curr.id === splittingId);

    if (!changedField) {
      return false;
    }

    let incorrectRuleFieldsCopy = cloneDeep(incorrectRuleFields);

    try {
      const { currentIncorrectFields: rangeFieldErrors } = getIncorrectRuleRangeFields({
        currentIncorrectFields: incorrectRuleFieldsCopy,
        rule: data,
        splittingId,
        key,
        t,
      });

      incorrectRuleFieldsCopy = rangeFieldErrors;

      const message = `${t('validation.discontinuousRanges')}`;
      const { currentIncorrectFields: betweenRowsFieldErrors } = getIncorrectRuleFieldsBetweenRows({
        ruleLinkedList,
        key,
        currentIncorrectFields: incorrectRuleFieldsCopy,
        message,
      });

      incorrectRuleFieldsCopy = betweenRowsFieldErrors;

      const { currentIncorrectFields: sameRowFieldErrors } = getIncorrectRuleFieldsOnSameRow({
        ruleLinkedList,
        currentIncorrectFields: incorrectRuleFieldsCopy,
        message,
      });

      incorrectRuleFieldsCopy = sameRowFieldErrors;

      if (incorrectRuleFieldsCopy.length) {
        throw new Error(message, {
          cause: NitrogenSplittingRuleErrorCauses.NonConsecutive,
        });
      }

      setIncorrectRuleFields([]);

      return true;
    } catch (err) {
      const error: RuleError | undefined = getRuleError(err);
      let shouldShowErrorMessage = true;
      switch (error?.cause) {
        case NitrogenSplittingRuleErrorCauses.RuleDiscontinuousRanges: {
          // Logic to show the message only once per new incorrect range field
          shouldShowErrorMessage = !incorrectRuleFields.find(
            (field) =>
              field.id === splittingId && field.key === key && field.message === error?.message,
          );

          if (shouldShowErrorMessage) {
            const errorField = incorrectRuleFields.find(
              (field) =>
                field.id === splittingId && field.key === key && field.cause === error?.cause,
            );
            const incorrectRuleRangeFieldsFiltered = incorrectRuleFields.filter(
              (field) => field !== errorField,
            );
            const newError: WrongRuleInputs = {
              id: splittingId,
              key,
              message: error?.message,
              isOnSameRow: true,
              cause: error?.cause,
            };

            setIncorrectRuleFields([...incorrectRuleRangeFieldsFiltered, newError]);
          }

          break;
        }
        case NitrogenSplittingRuleErrorCauses.NonConsecutive: {
          // Logic to show the message only once per new incorrect field
          const isErrorShown = incorrectRuleFields.find(
            (field) =>
              field.id === changedField.curr.id &&
              field.key === key &&
              field.message === error?.message,
          );
          const newErrorFields = incorrectRuleFieldsCopy.filter(
            (resultErrorField) =>
              !incorrectRuleFields.find(
                (field) =>
                  field.id === resultErrorField.id &&
                  field.key === resultErrorField.key &&
                  field.cause === resultErrorField.cause &&
                  field.isOnSameRow === resultErrorField.isOnSameRow &&
                  field.message === resultErrorField.message,
              ),
          );

          shouldShowErrorMessage = !isErrorShown || (isErrorShown && !!newErrorFields.length);

          setIncorrectRuleFields(incorrectRuleFieldsCopy);

          break;
        }
      }

      if (shouldShowErrorMessage) {
        setDisplaySnackbar({
          title: error?.message,
          colorConcept: 'destructiveLight',
          icon: 'Close',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
          useClose: true,
        });
      }
      return false;
    }
  };

  const validateNitrogenSplitting = (): boolean => {
    try {
      const errorFieldsResult = getInvalidSplitFields({ nitrogenSplittingConfiguration, t });
      const newErrorFields = errorFieldsResult.filter(
        (resultErrorField) =>
          !incorrectSplitFields.find(
            (field) =>
              field.id === resultErrorField.id &&
              field.ordinal === resultErrorField.ordinal &&
              field.message === resultErrorField.message,
          ),
      );

      setIncorrectSplitFields(errorFieldsResult);

      if (newErrorFields.length) {
        throw new Error(newErrorFields[0].message);
      }

      if (errorFieldsResult.length) {
        return false;
      }
    } catch (err) {
      const error: RuleError | undefined = getRuleError(err);
      setDisplaySnackbar({
        title: error?.message || '',
        colorConcept: 'destructiveLight',
        icon: 'Close',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
        useClose: true,
      });
      return false;
    }

    return true;
  };

  const handleValueUpdate = async ({
    key,
    data,
    splittingId,
  }: InputBlurHandlerArgs): Promise<void> => {
    let areSplitsValid = splitsValidity;
    let areRulesValid = rulesValidity;

    if (key === NSplittingScheduleKeys.Split) {
      areSplitsValid = validateNitrogenSplitting();

      setSplitsValidity(areSplitsValid);
    } else {
      areRulesValid = validateRuleSplitting({ data, key, splittingId });

      setRulesValidity(areRulesValid);
    }

    if (!areSplitsValid || !areRulesValid) {
      return;
    }

    const newNitrogenSplittingConfiguration = convertStateValuesToNumber(
      nitrogenSplittingConfiguration,
    );
    const isStateEqual = isEqual(newNitrogenSplittingConfiguration, state);

    if (!isStateEqual) {
      await updateNSplittingScheduleConfiguration({
        method: METHOD.PUT,
        body: JSON.stringify(newNitrogenSplittingConfiguration),
      });
    }

    dispatch({
      payload: newNitrogenSplittingConfiguration,
      type: UpdateSplittingScheduleActions.UpdateState,
      splittingId,
    });
    setState(newNitrogenSplittingConfiguration);
  };

  return (
    <NSplittingScheduleTableContainer data-cy='n-splitting-schedule-content'>
      <StyledSubtitle size='s' data-cy='n-splitting-schedule-sub-title'>
        {t(`subTitle`)}
      </StyledSubtitle>

      <StyledDiv>
        <Table className='splitting-schedule-table' data-cy='n-splitting-schedule-table'>
          <NSplittingScheduleTableHeader
            growthScaleStages={growthScaleStages}
            stageNumbers={nitrogenSplittingConfiguration.configuration.data.map(
              (splittingSchedule) => splittingSchedule.stageNumber,
            )}
          />

          <NSplittingScheduleTableBody
            nitrogenSplittingConfiguration={nitrogenSplittingConfiguration}
            handleValueChange={handleValueChange}
            handleValueUpdate={handleValueUpdate}
            incorrectRuleFields={incorrectRuleFields}
            incorrectSplitFields={incorrectSplitFields}
          />
        </Table>
      </StyledDiv>
    </NSplittingScheduleTableContainer>
  );
};
