import { Image, Input, Subtitle, Table, styled } from '@yaradigitallabs/ahua-react';

export const NSplittingScheduleTableContainer = styled('div', {
  paddingLeft: 0,
  position: 'relative',
});

export const StyledSubtitle = styled(Subtitle, {
  marginBottom: '14px',
  fontWeight: '$bold',
});

export const StyledTableHead = styled(Table.Head, {
  borderLeft: '1px solid $border-tertiary-light',
  fontWeight: '$bold',
  verticalAlign: 'middle',
  whiteSpace: 'break-spaces',
});

export const StyledImage = styled(Image, {
  width: '90px',
  height: '78px',
});

export const StyledTableRow = styled(Table.Row, {
  fontWeight: '$bold',
});

export const StyledTableCell = styled(Table.Cell, {
  borderLeft: '1px solid $border-tertiary-light',
  textAlign: 'left !important',
  fontWeight: '$semiBold',
});

export const StyledMultiLineHead = styled(Table.Head, {
  lineHeight: '17.5px',
  fontWeight: '$bold',
});

export const StyledTableSubhead = styled('label', {
  fontWeight: '$regular',
});

export const StyledDiv = styled('div', {
  padding: '$x2 0px',
  border: '1px solid $border-tertiary-light',
  borderRadius: '$m',
  display: 'inline-block',
  paddingBottom: '55px',
});

export const StyledTableSplittingRuleInput = styled(Input, {
  fontSize: '$scale3',
  fontWeight: '$semiBold',
  maxWidth: '76px',
  marginRight: '33px',
});

export const StyledRuleLabel = styled('label', {
  width: '15px',
  marginRight: '$x4',
  textAlign: 'left',
});

export const StyledTableSplitInput = styled(Input, {
  fontSize: '$scale3',
  fontWeight: '$semiBold',
  width: '119px',
  textAlign: 'left',
});
