import React, { useEffect, useMemo } from 'react';
import { NSplittingScheduleTable } from './NSplittingScheduleTable';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import './styles.scss';
import { useAppContext } from '@widgets/Polaris/src/providers';
import {
  useFetchNSplittingScheduleConfigurations,
  useFetchGrowthScaleStages,
} from '@polaris-hooks/index';
import { METHOD } from '@common/constants';

const NSplittingSchedule = () => {
  const { cropRegion } = useAppContext();

  const nSplittingScheduleFilters = useMemo(() => {
    if (!cropRegion?.id) {
      return;
    }
    return [
      {
        key: 'cropRegionId',
        value: cropRegion.id,
        type: FilterType.EQ,
      },
    ];
  }, [cropRegion]);

  const { nSplittingConfigurations } =
    useFetchNSplittingScheduleConfigurations(nSplittingScheduleFilters);

  const { data: growthScaleStages, trigger: fetchGrowthScaleStages } = useFetchGrowthScaleStages();

  const growthScaleFilters: GenericFilter[] | undefined = useMemo(() => {
    const growthScaleStageIds = nSplittingConfigurations?.[0]?.configuration.data[
      nSplittingConfigurations?.[0]?.configuration.data.length - 1
    ].splits.map((split) => split.growthStageId);

    if (!growthScaleStageIds) {
      return;
    }

    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: growthScaleStageIds.join(','),
      },
    ];
  }, [nSplittingConfigurations]);

  const propertyToSortBy = 'ordinal';
  useEffect(() => {
    if (growthScaleFilters) {
      fetchGrowthScaleStages({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: growthScaleFilters,
          sorting: [
            {
              column: propertyToSortBy,
            },
          ],
        }),
      });
    }
  }, [growthScaleFilters]);

  return (
    nSplittingConfigurations &&
    growthScaleStages && (
      <NSplittingScheduleTable
        nSplittingConfiguration={nSplittingConfigurations[0]}
        growthScaleStages={growthScaleStages.entities}
      />
    )
  );
};

export default NSplittingSchedule;
