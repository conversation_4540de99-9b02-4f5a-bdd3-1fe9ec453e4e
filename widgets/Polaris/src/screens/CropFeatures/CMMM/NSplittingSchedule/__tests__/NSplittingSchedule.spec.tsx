import React from 'react';
import { act, render, screen } from '@testing-library/react';
import NSplittingSchedule from '../NSplittingSchedule';
import * as useGrowthScaleStages from '@polaris-hooks/growthScale/useGrowthScaleService/useGrowthScaleService';
import * as useNSplitting from '@polaris-hooks/masterMindMap/useNSplitting/useNSplitting';
import { setupServer } from 'msw/node';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import {
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  growthScaleStagesHandler,
  cerealNSplittingScheduleConfigurationHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { NavbarProvider } from '@libs/nav-context';

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  growthScaleStagesHandler,
  cerealNSplittingScheduleConfigurationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('NSplittingSchedule', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('renders without crashing', async () => {
    await act(async () => {
      const component = render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <NSplittingSchedule />
          </NavbarProvider>
        </AppContext.Provider>,
      );

      expect(component).toMatchSnapshot();
    });

    expect(screen.getByTestId('n-splitting-schedule-content')).toBeInTheDocument();
    expect(screen.getByTestId('n-splitting-schedule-sub-title')).toBeInTheDocument();
    expect(screen.getByTestId('n-splitting-schedule-table')).toBeInTheDocument();
    expect(screen.getByTestId('n-splitting-schedule-table-header')).toBeInTheDocument();
    expect(screen.getByTestId('n-splitting-schedule-table-body')).toBeInTheDocument();
  });

  it('does not render if cropRegion is missing', async () => {
    jest
      .spyOn(useNSplitting, 'useFetchNSplittingScheduleConfigurations')
      .mockImplementation(() => ({
        nSplittingConfigurations: undefined,
        error: undefined,
        trigger: jest.fn(),
        isMutating: false,
      }));
    jest.spyOn(useGrowthScaleStages, 'useFetchGrowthScaleStages').mockImplementation(() => ({
      data: undefined,
      error: undefined,
      trigger: jest.fn(),
      isMutating: false,
    }));
    await act(async () => {
      render(
        <AppContext.Provider value={{ ...mockAppProviderValue, cropRegion: null }}>
          <NavbarProvider>
            <NSplittingSchedule />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });
    expect(screen.queryByTestId('n-splitting-schedule-content')).not.toBeInTheDocument();
    expect(screen.queryByTestId('n-splitting-schedule-sub-title')).not.toBeInTheDocument();
    expect(screen.queryByTestId('n-splitting-schedule-table')).not.toBeInTheDocument();
    expect(screen.queryByTestId('n-splitting-schedule-table-header')).not.toBeInTheDocument();
    expect(screen.queryByTestId('n-splitting-schedule-table-body')).not.toBeInTheDocument();
  });
});
