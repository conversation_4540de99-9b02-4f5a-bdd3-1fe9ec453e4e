import React from 'react';
import { Icon<PERSON>utton, Table, Tooltip } from '@yaradigitallabs/ahua-react';
import {
  StyledImage,
  StyledMultiLineHead,
  StyledTableHead,
  StyledTableSubhead,
} from '../NSplittingScheduleTable/NSplittingScheduleTable.styled';
import { MEDIA_URI_KEYS } from '../utils/constants';
import { useTranslation } from 'react-i18next';
import { GrowthScaleStage } from '@common/types';

interface NSplittingScheduleTableHeaderProps {
  growthScaleStages: GrowthScaleStage[];
  stageNumbers: number[];
}

export const NSplittingScheduleTableHeader: React.FC<NSplittingScheduleTableHeaderProps> = ({
  growthScaleStages,
  stageNumbers,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.planConfiguration.nSplittingSchedule.headers',
  });

  return (
    <thead data-cy='n-splitting-schedule-table-header'>
      <Table.Row className='table-row'>
        <StyledTableHead colSpan={3}>{t('growthStage.numberHead')}</StyledTableHead>

        {stageNumbers.map((number) => (
          <StyledTableHead key={`growthStageNo${number}`}>{number}</StyledTableHead>
        ))}
      </Table.Row>

      <Table.Row className='table-row'>
        <StyledTableHead colSpan={3}>{t('growthStage.nameHead')}</StyledTableHead>

        {growthScaleStages.map((growthScaleStage) => (
          <StyledTableHead
            key={growthScaleStage.id}
            data-cy={`growth-stage-name-${growthScaleStage.name.replaceAll(' ', '-')}`}
          >
            {growthScaleStage.name}
          </StyledTableHead>
        ))}
      </Table.Row>

      <Table.Row className='table-row'>
        <StyledTableHead colSpan={3}>{t('growthStage.imageHead')}</StyledTableHead>

        {growthScaleStages.map((growthScaleStage) => (
          <StyledTableHead key={growthScaleStage.id} className='table-head-image'>
            <StyledImage
              src={
                growthScaleStage.mediaUri?.find((elem) => elem.key === MEDIA_URI_KEYS.DEFAULT)
                  ?.value ?? ''
              }
              alt={growthScaleStage.name}
              fit='contain'
              data-cy={`growth-stage-image-${growthScaleStage.name.replaceAll(' ', '-')}`}
            />
          </StyledTableHead>
        ))}
      </Table.Row>

      <Table.Row className='table-row'>
        <StyledMultiLineHead>
          <>{t('rule.nameHead')}</>

          <br />

          <StyledTableSubhead>{t('rule.subNameHead')}</StyledTableSubhead>
        </StyledMultiLineHead>

        <StyledTableHead>
          <>{t('numberOfSplits')}</>
        </StyledTableHead>

        <StyledTableHead>
          <div className='table-header-with-tooltip'>
            <label className='table-data-label'>Total (%)</label>

            <Tooltip
              concept={'inverse'}
              text='Must equal 100'
              tipVisibility={true}
              position={'top'}
            >
              <IconButton className='icon-button' icon='Info' colorConcept='neutral' />
            </Tooltip>
          </div>
        </StyledTableHead>

        {stageNumbers.map((number) => (
          <StyledTableHead key={`splitNo${number}`}>
            {t('split', {
              number: number,
            })}
          </StyledTableHead>
        ))}
      </Table.Row>
    </thead>
  );
};
