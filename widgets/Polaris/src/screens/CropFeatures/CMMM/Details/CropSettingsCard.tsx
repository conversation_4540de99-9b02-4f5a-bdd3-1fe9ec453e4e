import React from 'react';
import { CropSettingsCMMMCardLogic } from './CropSettingsCardLogic';
import { useTranslation } from 'react-i18next';
import { CropRegion, GrowthScale } from '@common/types';
import { CropSettingsCard } from '../../shared/CropSettingsCard';

interface CerealsCropSettingsCardProps {
  cropRegion: CropRegion | null;
  growthScalesData: GrowthScale[] | undefined;
  setIsEditCropSettingOpened: React.Dispatch<boolean>;
  isEditingDisabled: boolean;
}

const CerealsCropSettingsCard: React.FC<CerealsCropSettingsCardProps> = ({
  cropRegion,
  growthScalesData,
  setIsEditCropSettingOpened,
  isEditingDisabled,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.cropSettings'
  });

  const {
    cerealsCropSettingsConfig,
    filteredPartnerTags,
  } = CropSettingsCMMMCardLogic({ cropRegion, growthScalesData });

  return (
    <CropSettingsCard
      cropSettings={cerealsCropSettingsConfig}
      cropRegion={cropRegion}
      setIsEditCropSettingOpened={setIsEditCropSettingOpened}
      isEditingDisabled={isEditingDisabled}
      filteredPartnerTags={filteredPartnerTags}
      titles={{
        head: t(`title`),
        editButton: t(`editButton`),
        partner: t(`partnersTitle`),
      }}
      texts={{
        headActionTooltip: t('editTooltipMessage'),
        partnerCaption: t(`partnersCaption`),
      }}
    />
  );
};

export default CerealsCropSettingsCard;
