/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { fireEvent, render, waitFor, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  features<PERSON>and<PERSON>,
  cropRegionsHandler,
  cnpPartnersHandler,
  planValidationHandler,
  updatedPlanValidationsHandler,
  growthScalesHandler,
  yieldSolidUnitsHandler,
  updateCropRegionHandler,
  canModifyCropRegionGrowthScaleHandler,
  unitCountriesHandler,
  allUnitsHandler,
  allPartnerTagsHandler,
  mockCMMMAppProviderValue,
  mockAppProviderValue,
  snackbarInitialStateMock,
  testCropRegionWithParameters,
  mockMMMValidation,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { BrowserRouter as Router, MemoryRouter, Routes, Route } from 'react-router-dom';
import { NavbarProvider } from '@libs/nav-context';
import CMMMDetails from '../Details';
import {
  ConfigurationType,
  CropNutritionPlans,
  MMMValidation,
  ValidationStatus,
} from '@common/types';
import { mmmValidations } from '../../../../Home/mock-data/MockData';
import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';
import * as useMMMValidations from '@widgets/Polaris/src/hooks';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
  planValidationHandler,
  updatedPlanValidationsHandler,
  growthScalesHandler,
  yieldSolidUnitsHandler,
  updateCropRegionHandler,
  canModifyCropRegionGrowthScaleHandler,
  unitCountriesHandler,
  allUnitsHandler,
  allPartnerTagsHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('@polaris-hooks/masterMindMap/useMMMValidations/useMMMValidations', () => ({
  useFetchMMMValidations: jest.fn(),
  useUpdateMMMValidation: jest.fn(() => ({
    trigger: jest.fn(),
  })),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('CMMM Homepage', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  const mockSetDisplaySnackbar = jest.fn();

  it('should initialize the context with default values and render the CMMM Details page', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();

    expect(component.getByTestId('mmm-details-content')).toBeInTheDocument();
    expect(component.getByTestId('crop-settings-card')).toBeInTheDocument();
    expect(component.getByTestId('mmm-configuration-card')).toBeInTheDocument();
  });

  it('should allow user to navigate to the CMMM configuration screen', async () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockCMMMAppProviderValue,
          cropRegion: testCropRegionWithParameters,
        }}
      >
        <NavbarProvider>
          <AppContext.Consumer>
            {(_context) => (
              <MemoryRouter initialEntries={['/cereals-mmm']}>
                <Routes>
                  <Route path='cereals-mmm' element={<CMMMDetails />} />
                  <Route
                    path={`/crop-features/cereals-mmm/crop-region/${testCropRegionWithParameters.id}/cereals-mmm/edit-configuration`}
                    element={
                      <div>
                        <h1>{CropNutritionPlans.CMMM}</h1>
                      </div>
                    }
                  />
                </Routes>
              </MemoryRouter>
            )}
          </AppContext.Consumer>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const configureButton = component.getByTestId('configuration-card-test-harness-button');
    expect(configureButton).toBeInTheDocument();
    fireEvent.click(configureButton);
    waitFor(() => {
      expect(component.getByText(CropNutritionPlans.CMMM)).toBeInTheDocument();
    });
  });

  it('should display Default yield with numeric value when it is available', () => {
    const defaultYieldValue = 42;

    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component.getByTestId('crop-settings-card')).toBeInTheDocument();

    const yieldSelect = component.getByTestId('default-yield-select');
    expect(yieldSelect).toBeInTheDocument();

    const yieldValueLabel = yieldSelect.querySelector('label:last-of-type');
    expect(yieldValueLabel?.innerHTML).toEqual(defaultYieldValue.toString());
  });

  it('should show EditCropSetting popup after clicking on edit button', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const editButton = component.getByTestId('crop-settings-edit-button');
    expect(editButton).toBeInTheDocument();
    fireEvent.click(editButton);
    waitFor(() => {
      expect(component.findByTestId('cmmm-edit-popup-container')).toBeInTheDocument();
    });
  });

  it('should show tooltip when hovering over disabled button in the CMMM card', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const disabledButton = component.getByTestId('configuration-card-test-harness-button');
    fireEvent.mouseMove(disabledButton);
    expect(disabledButton).toBeInTheDocument();
    waitFor(() => {
      expect(component.getByTestId('configuration-card-test-harness-button-tooltip')).toBeInTheDocument();
    });
  });

  it('should show checked validation switch in the CMMM card when validationStatus of a plan validation is validated', () => {
    (useMMMValidations.useFetchMMMValidations as jest.Mock).mockReturnValue(
      mmmValidations.entities,
    );

    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const validationSwitch = screen.getByTestId('soil-validation-switch-btn');
    expect(validationSwitch.getAttribute('data-state')).toBe('checked');
  });

  it('should show unchecked validation switch in the CMMM card when validationStatus of a plan validation is Failed', () => {
    (useMMMValidations.useFetchMMMValidations as jest.Mock).mockReturnValue(
      mmmValidations.entities,
    );
    const mockAppProviderValueAdjusted = {
      ...mockAppProviderValue,
      selectedMMMValidation: {
        ...mockMMMValidation,
        validationStatus: ValidationStatus.FAILED,
      },
    };

    render(
      <AppContext.Provider value={mockAppProviderValueAdjusted}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const validationSwitch = screen.getByTestId('soil-validation-switch-btn');
    expect(validationSwitch.getAttribute('data-state')).toBe('unchecked');
  });

  it('should show unchecked validation switch in the CMMM card when validationStatus is NOT set', () => {
    const planValidationsWithUncheckedStatus: MMMValidation = {
      id: '59a3a562-a4c7-4084-9dc2-5fe3e43361b2',
      countryId: '9473c999-f14c-4a2f-9f95-531aa9a1cf4e',
      cropRegionId: '2963d361-a089-4bd8-ba0b-ffd019799587',
      validationStatus: ValidationStatus.NOT_SET,
      configurationType: ConfigurationType.Cereal,
      created: '2024-02-08T19:54:51.221Z',
      modified: '2024-02-08T19:54:51.221Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    };

    jest
      .spyOn(useMMMValidations, 'useFetchMMMValidations')
      .mockImplementation(() => [planValidationsWithUncheckedStatus]);

    const mockAppProviderValueAdjusted = {
      ...mockAppProviderValue,
      selectedMMMValidation: {
        ...mockMMMValidation,
        validationStatus: ValidationStatus.INVALIDATED,
      },
    };
    render(
      <AppContext.Provider value={mockAppProviderValueAdjusted}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CMMMDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const validationSwitch = screen.getByTestId('soil-validation-switch-btn');
    expect(validationSwitch.getAttribute('data-state')).toBe('unchecked');
  });

  it('should switch state when CMMM plan validate switch is clicked', async () => {
    const mockAppProviderValueAdjusted = {
      ...mockAppProviderValue,
      selectedMMMValidation: {
        ...mockMMMValidation,
        validationStatus: ValidationStatus.NOT_SET,
      },
    };
    const component = render(
      <AppContext.Provider value={mockAppProviderValueAdjusted}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {(_context) => (
                <SnackbarContext.Provider
                  value={{
                    displaySnackbar: snackbarInitialStateMock,
                    setDisplaySnackbar: mockSetDisplaySnackbar,
                  }}
                >
                  <CMMMDetails />
                </SnackbarContext.Provider>
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const switchBtn = component.getByTestId('soil-validation-switch-btn');
    expect(switchBtn).toHaveAttribute('data-state', 'unchecked');
    fireEvent.click(switchBtn);
    waitFor(() => {
      expect(switchBtn).toHaveAttribute('data-state', 'checked');
    });
  });
});
