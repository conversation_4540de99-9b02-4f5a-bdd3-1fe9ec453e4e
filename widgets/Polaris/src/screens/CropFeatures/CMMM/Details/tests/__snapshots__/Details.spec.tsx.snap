// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CMMM Homepage should initialize the context with default values and render the CMMM Details page 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-hAzCDl"
        data-cy="mmm-details-content"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ijZBIvl-css"
          data-cy="mmm-details-title"
        >
          polaris.cmmmDetails.title
        </h1>
        <a
          class="c-wzBoY"
          data-cy="crop-settings-card"
        >
          <div
            class="card-header"
          >
            <div
              class="c-jCarvd c-jCarvd-ikfQhrm-css"
            >
              <div
                class="c-cVKzzi"
              >
                <div
                  class="c-cXFqtJ"
                >
                  <h1
                    class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                  >
                    title
                  </h1>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                />
              </div>
              <div
                class="c-hcUxto"
              >
                <div
                  data-state="closed"
                >
                  <button
                    class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-sXzsQ-cv c-kkaoVB"
                    data-cy="crop-settings-edit-button"
                    disabled=""
                    title="editButton"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                      />
                    </svg>
                    <span
                      class="c-iepcqn"
                    >
                      editButton
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
            data-cy="crop-settings-card-body"
          >
            <div
              data-cy="growth-scale-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cmmmDetails.cropSettings.growthScale
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r1:"
                    aria-expanded="false"
                    aria-label="Growth scale"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 4.5H6a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-11a3 3 0 0 0-3-3h-3m-6 0h6m-6 0v-2m0 2V6m6-1.5v-2m0 2V6m-3.557 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="yield-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cmmmDetails.cropSettings.yieldUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r2:"
                    aria-expanded="false"
                    aria-label="Yield unit"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="demand-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cmmmDetails.cropSettings.recommendationUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r3:"
                    aria-expanded="false"
                    aria-label="Default target yield unit"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M14.405 17.29a6.83 6.83 0 0 1-4.786 1.564 6.88 6.88 0 0 1-4.152-1.765M15 11.583a5.346 5.346 0 0 1-2.007 3.141 4.88 4.88 0 0 1-3.485.947 4.987 4.987 0 0 1-3.203-1.725 5.423 5.423 0 0 1-1.174-2.363m5.933-.026c-.012.143-.05.282-.115.409a1.05 1.05 0 0 1-.258.328.96.96 0 0 1-1.147.086 1.03 1.03 0 0 1-.3-.286 1.087 1.087 0 0 1-.178-.813m1.022-7.82S7.647 5.936 7.063 6.624c-.585.689-1.444 1.807-1.616 2.115-.172.307-.312 1.324-.344 1.876C5.07 11.197 5 14.293 5 15.05c0 .757.172 2.131.24 2.922.07.79.344 2.097.551 2.474.207.377.55.55.413.756-.138.206-.582.165-.55.481.032.316.412.317.55.317h11.448s.473.004.618-.093c.206-.137.138-.326-.069-.429a1.263 1.263 0 0 1-.378-.343c.217-.246.412-.51.582-.79.24-.413.447-1.376.582-2.475.135-1.1.068-3.747.068-4.985 0-1.238 0-2.029-.068-2.785a4.983 4.983 0 0 0-.275-1.306c-.378-1.272-4.538-5.226-4.538-5.226m-1.262-1.395c-.86-.413-1.528.02-1.87.206-.326.176-1.224.893-1.224.893l.955.64s.31 1.169.413 1.41c.212.493.549.791 1.017.791.469 0 .7-.379.906-.791.206-.413.48-1.376.48-1.376l.819-.632s-.635-.732-1.496-1.141z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="default-yield-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cmmmDetails.cropSettings.defaultYield
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    42
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r4:"
                    aria-expanded="false"
                    aria-label="Nutrient removal unit"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-iTKOFX-orientation-vertical c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-kdEBiP"
          >
            <label
              class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-ibPNjhd-css partners-title"
              data-cy="crop-setting-partners-title"
            >
              partnersTitle
            </label>
            <div
              class="c-bfPkPS c-bfPkPS-iczWuCV-css stack"
            />
          </div>
        </a>
        <a
          class="c-wzBoY c-wzBoY-ikclSel-css"
          data-cy="mmm-configuration-card"
        >
          <div
            class="c-hPgkjm"
          >
            <div
              class="c-iQKjxI"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-kVDIWi"
              >
                title
              </h1>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iiffMpN-css c-fIVxPx"
                data-state="closed"
              >
                polaris.sharedText.validatedStatus
              </p>
            </div>
            <div
              class="validate-switch"
            >
              <div
                class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-icOKMlW-css"
                >
                  switchLabel
                </label>
                <button
                  aria-checked="true"
                  class="c-dyBzGm c-dyBzGm-iiQcApQ-css"
                  data-cy="soil-validation-switch-btn"
                  data-state="checked"
                  role="switch"
                  type="button"
                  value="on"
                >
                  <span
                    class="c-bILIWL"
                    data-state="checked"
                  />
                </button>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical plan-details-configure-nav-content"
          >
            <div
              class="c-hPExTj c-hPExTj-ibUIiAd-css"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
              >
                content.title
              </h1>
              <p
                class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
              >
                content.description
              </p>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
          >
            <div
              data-state="closed"
              style="flex: 1 1 0%;"
            >
              <button
                class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv"
                data-cy="configuration-card-test-harness-button"
                disabled=""
              >
                <span
                  class="c-iepcqn"
                >
                  buttons.test
                </span>
              </button>
            </div>
            <div
              data-state="closed"
              style="flex: 1 1 0%;"
            >
              <button
                class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-bETQVM-variant-primary c-hRrCwb-evtOnb-cv"
                data-cy="configuration-card-configure-button"
                disabled=""
              >
                <span
                  class="c-iepcqn"
                >
                  buttons.configure
                </span>
              </button>
            </div>
          </div>
        </a>
        <div
          class="crop-settings-edit-popup-root"
        />
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-hAzCDl"
      data-cy="mmm-details-content"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ijZBIvl-css"
        data-cy="mmm-details-title"
      >
        polaris.cmmmDetails.title
      </h1>
      <a
        class="c-wzBoY"
        data-cy="crop-settings-card"
      >
        <div
          class="card-header"
        >
          <div
            class="c-jCarvd c-jCarvd-ikfQhrm-css"
          >
            <div
              class="c-cVKzzi"
            >
              <div
                class="c-cXFqtJ"
              >
                <h1
                  class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                >
                  title
                </h1>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
              />
            </div>
            <div
              class="c-hcUxto"
            >
              <div
                data-state="closed"
              >
                <button
                  class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-sXzsQ-cv c-kkaoVB"
                  data-cy="crop-settings-edit-button"
                  disabled=""
                  title="editButton"
                  type="button"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                    />
                  </svg>
                  <span
                    class="c-iepcqn"
                  >
                    editButton
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
          data-cy="crop-settings-card-body"
        >
          <div
            data-cy="growth-scale-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.cmmmDetails.cropSettings.growthScale
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r1:"
                  aria-expanded="false"
                  aria-label="Growth scale"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9 4.5H6a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-11a3 3 0 0 0-3-3h-3m-6 0h6m-6 0v-2m0 2V6m6-1.5v-2m0 2V6m-3.557 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="yield-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.cmmmDetails.cropSettings.yieldUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r2:"
                  aria-expanded="false"
                  aria-label="Yield unit"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="demand-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.cmmmDetails.cropSettings.recommendationUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r3:"
                  aria-expanded="false"
                  aria-label="Default target yield unit"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.405 17.29a6.83 6.83 0 0 1-4.786 1.564 6.88 6.88 0 0 1-4.152-1.765M15 11.583a5.346 5.346 0 0 1-2.007 3.141 4.88 4.88 0 0 1-3.485.947 4.987 4.987 0 0 1-3.203-1.725 5.423 5.423 0 0 1-1.174-2.363m5.933-.026c-.012.143-.05.282-.115.409a1.05 1.05 0 0 1-.258.328.96.96 0 0 1-1.147.086 1.03 1.03 0 0 1-.3-.286 1.087 1.087 0 0 1-.178-.813m1.022-7.82S7.647 5.936 7.063 6.624c-.585.689-1.444 1.807-1.616 2.115-.172.307-.312 1.324-.344 1.876C5.07 11.197 5 14.293 5 15.05c0 .757.172 2.131.24 2.922.07.79.344 2.097.551 2.474.207.377.55.55.413.756-.138.206-.582.165-.55.481.032.316.412.317.55.317h11.448s.473.004.618-.093c.206-.137.138-.326-.069-.429a1.263 1.263 0 0 1-.378-.343c.217-.246.412-.51.582-.79.24-.413.447-1.376.582-2.475.135-1.1.068-3.747.068-4.985 0-1.238 0-2.029-.068-2.785a4.983 4.983 0 0 0-.275-1.306c-.378-1.272-4.538-5.226-4.538-5.226m-1.262-1.395c-.86-.413-1.528.02-1.87.206-.326.176-1.224.893-1.224.893l.955.64s.31 1.169.413 1.41c.212.493.549.791 1.017.791.469 0 .7-.379.906-.791.206-.413.48-1.376.48-1.376l.819-.632s-.635-.732-1.496-1.141z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="default-yield-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.cmmmDetails.cropSettings.defaultYield
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  42
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r4:"
                  aria-expanded="false"
                  aria-label="Nutrient removal unit"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-cvAFBk c-cvAFBk-iTKOFX-orientation-vertical c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-kdEBiP"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-ibPNjhd-css partners-title"
            data-cy="crop-setting-partners-title"
          >
            partnersTitle
          </label>
          <div
            class="c-bfPkPS c-bfPkPS-iczWuCV-css stack"
          />
        </div>
      </a>
      <a
        class="c-wzBoY c-wzBoY-ikclSel-css"
        data-cy="mmm-configuration-card"
      >
        <div
          class="c-hPgkjm"
        >
          <div
            class="c-iQKjxI"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-kVDIWi"
            >
              title
            </h1>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iiffMpN-css c-fIVxPx"
              data-state="closed"
            >
              polaris.sharedText.validatedStatus
            </p>
          </div>
          <div
            class="validate-switch"
          >
            <div
              class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
            >
              <label
                class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-icOKMlW-css"
              >
                switchLabel
              </label>
              <button
                aria-checked="true"
                class="c-dyBzGm c-dyBzGm-iiQcApQ-css"
                data-cy="soil-validation-switch-btn"
                data-state="checked"
                role="switch"
                type="button"
                value="on"
              >
                <span
                  class="c-bILIWL"
                  data-state="checked"
                />
              </button>
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical plan-details-configure-nav-content"
        >
          <div
            class="c-hPExTj c-hPExTj-ibUIiAd-css"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
            >
              content.title
            </h1>
            <p
              class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
            >
              content.description
            </p>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
        >
          <div
            data-state="closed"
            style="flex: 1 1 0%;"
          >
            <button
              class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv"
              data-cy="configuration-card-test-harness-button"
              disabled=""
            >
              <span
                class="c-iepcqn"
              >
                buttons.test
              </span>
            </button>
          </div>
          <div
            data-state="closed"
            style="flex: 1 1 0%;"
          >
            <button
              class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-bETQVM-variant-primary c-hRrCwb-evtOnb-cv"
              data-cy="configuration-card-configure-button"
              disabled=""
            >
              <span
                class="c-iepcqn"
              >
                buttons.configure
              </span>
            </button>
          </div>
        </div>
      </a>
      <div
        class="crop-settings-edit-popup-root"
      />
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
