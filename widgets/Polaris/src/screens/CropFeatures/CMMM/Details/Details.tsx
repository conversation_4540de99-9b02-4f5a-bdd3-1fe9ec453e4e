import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { filterUnitsByTag, useGetGrowthScales } from '@polaris-hooks/index';
import { ConfigurationType, ValidationStatus } from '@common/types';
import { ROUTES } from '@src/routes';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { UNIT_TAGS, PLAN_NAME_PARAM } from '@common/constants';
import Details from '../../shared/Details/Details';
import ConfigurationCard from '../../shared/ConfigurationCard/ConfigurationCard';
import { EditCropSettings } from '@widgets/Polaris/src/components';
import CerealsCropSettingsCard from './CropSettingsCard';
import '../../../../../styles/Polaris/screens/NPDetails/_page-details-configure-nav.scss';

const CMMMDetails = () => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.cerealsMMM',
  });

  const [isEditCropSettingOpened, setIsEditCropSettingOpened] = useState(false);

  const {
    selectedFeature,
    selectedCropDescription,
    selectedCrop,
    selectedRegion,
    selectedCountry,
    selectedCountryUnits,
    cropRegion,
    selectedMMMValidation,
    methods: { setCropRegion },
  } = useAppContext();

  const { data: growthScalesData } = useGetGrowthScales();

  const isMMMValidated = selectedMMMValidation?.validationStatus === ValidationStatus.VALIDATED;
  const filteredYieldUnits = filterUnitsByTag(selectedCountryUnits, UNIT_TAGS.CMMM_YIELD_UNIT);
  const filteredDemandUnits = filterUnitsByTag(selectedCountryUnits, UNIT_TAGS.CMMM_DEMAND_UNIT);

  return (
    <Details route={ROUTES.cerealsMasterMindMap} featureDetails={'cmmmDetails'}>
      <CerealsCropSettingsCard
        cropRegion={cropRegion}
        growthScalesData={growthScalesData}
        isEditingDisabled={isMMMValidated}
        setIsEditCropSettingOpened={setIsEditCropSettingOpened}
      />

      <ConfigurationCard
        titles={{
          header: t(`title`),
          content: t(`content.title`),
        }}
        tooltips={{
          testHarnessButton: t('buttons.testTooltipMessage'),
          configureButton: t('buttons.configureTooltipMessage'),
        }}
        labels={{
          testHarnessAction: t(`buttons.test`),
          configureButton: t(`buttons.configure`),
        }}
        showConfigureButtonTooltip={isMMMValidated}
        contentDescription={t(`content.description`)}
        featureTranslationKeyPrefix={'cmmmDetails.cerealsMMM.validationSwitch'}
        configurationType={ConfigurationType.Cereal}
        isConfigureButtonDisable={isMMMValidated}
        featurePlanConfigurationRoute={ROUTES.cerealsMMMConfiguration}
        featurePlanName={PLAN_NAME_PARAM.CMMM}
      />

      {cropRegion && (
        <EditCropSettings
          dataPrefix='cmmm'
          selectedFeature={selectedFeature}
          selectedCropRegion={cropRegion}
          selectedCrop={selectedCrop}
          selectedCountry={selectedCountry}
          selectedRegion={selectedRegion}
          setCropRegion={setCropRegion}
          selectedCropDescription={selectedCropDescription}
          isEditCropSettingOpened={isEditCropSettingOpened}
          setIsEditCropSettingOpened={setIsEditCropSettingOpened}
          yieldUnitsData={filteredYieldUnits}
          demandUnitsData={filteredDemandUnits}
          growthScalesData={growthScalesData}
          growthScalesChangeDisabled={isMMMValidated}
        />
      )}
    </Details>
  );
};

export default CMMMDetails;
