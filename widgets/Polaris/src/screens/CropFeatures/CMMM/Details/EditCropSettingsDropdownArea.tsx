import { Input, Select } from '@yaradigitallabs/ahua-react';
import React, { ChangeEventHandler } from 'react';
import { useTranslation } from 'react-i18next';

import { SelectWrapper } from '@widgets/Polaris/src/components/Select/SelectWrapper';
import { CanModifyGrowthScaleResponse, GrowthScale, BaseUnit } from '@common/types';

type SingleUnitSelection =
  | {
      value: string;
      text: string;
    }
  | undefined;

interface CMMMCropSettingsDropdown {
  selectedYieldUnit: BaseUnit | null;
  growthScale: GrowthScale | null;
  selectedUnitSolid: BaseUnit | null;
  yieldAndSolidsUnit: SingleUnitSelection[] | undefined;
  growthScales: SingleUnitSelection[] | undefined;
  demandUnits: SingleUnitSelection[] | undefined;
  onYieldUnitChange: (value: string) => void;
  onDefaultYieldChange: ChangeEventHandler<HTMLInputElement>;
  onGrowthScaleChange: (value: string) => void;
  ondUnitSolidChange: (value: string) => void;
  defaultYield: number | null;
  canModifyResponse: CanModifyGrowthScaleResponse;
  growthScalesChangeDisabled: boolean | undefined;
}

const CMMMEditCropSettingsDropdownArea: React.FC<CMMMCropSettingsDropdown> = ({
  selectedYieldUnit,
  selectedUnitSolid,
  growthScale,
  demandUnits,
  yieldAndSolidsUnit,
  growthScales,
  onYieldUnitChange,
  onGrowthScaleChange,
  defaultYield,
  onDefaultYieldChange,
  ondUnitSolidChange,
  canModifyResponse,
  growthScalesChangeDisabled,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <SelectWrapper dataCy='growth-scale-dropdown'>
        <Select
          ariaLabel={t(`polaris.cmmmDetails.cropSettings.growthScaleLabel`)}
          cover={!canModifyResponse.success || growthScalesChangeDisabled ? 'fill' : 'outline'}
          css={{ width: '100%' }}
          value={
            growthScale && canModifyResponse.success && !growthScalesChangeDisabled
              ? growthScale?.id
              : null
          }
          items={growthScales || []}
          placeholder={
            !canModifyResponse.success || growthScalesChangeDisabled
              ? growthScale?.name
              : t(`polaris.cmmmDetails.cropSettings.growthScaleLabel`)
          }
          position='popper'
          size='n'
          helper-text={
            !canModifyResponse.success || growthScalesChangeDisabled
              ? t(`polaris.cmmmDetails.cropSettings.growthScaleHelperText`)
              : null
          }
          disabled={!canModifyResponse.success || growthScalesChangeDisabled}
          onChange={onGrowthScaleChange}
          label={t(`polaris.cmmmDetails.cropSettings.growthScaleLabel`)}
        />
      </SelectWrapper>

      <SelectWrapper dataCy='yield-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.cmmmDetails.cropSettings.yieldUnit`)}
          css={{ width: '100%' }}
          value={selectedYieldUnit ? selectedYieldUnit?.id : null}
          items={yieldAndSolidsUnit || []}
          placeholder={t(`polaris.cmmmDetails.cropSettings.yieldUnit`)}
          position='popper'
          size='n'
          onChange={onYieldUnitChange}
          label={t(`polaris.cmmmDetails.cropSettings.yieldUnit`)}
        />
      </SelectWrapper>

      <SelectWrapper dataCy='recommendation-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.cmmmDetails.cropSettings.recommendationUnit`)}
          css={{ width: '100%' }}
          value={selectedUnitSolid ? selectedUnitSolid?.id : null}
          items={demandUnits || []}
          placeholder={t(`polaris.cmmmDetails.cropSettings.recommendationUnit`)}
          position='popper'
          size='n'
          onChange={ondUnitSolidChange}
          label={t(`polaris.cmmmDetails.cropSettings.recommendationUnit`)}
        />
      </SelectWrapper>

      <SelectWrapper dataCy='default-yield-input'>
        <Input
          size='n'
          type='number'
          value={defaultYield || defaultYield === 0 ? defaultYield : ''}
          onKeyDown={(e) =>
            ['e', 'E', '-', '+', 'ArrowUp', 'ArrowDown'].includes(e.key) && e.preventDefault()
          }
          onChange={onDefaultYieldChange}
          label={t(`polaris.cmmmDetails.cropSettings.defaultYieldLabel`, {
            yieldUnit: selectedYieldUnit?.name || 'N/A',
          })}
          aria-label={t(`polaris.cmmmDetails.cropSettings.defaultYieldLabel`, {
            yieldUnit: selectedYieldUnit?.name || 'N/A',
          })}
        />
      </SelectWrapper>
    </>
  );
};

export default CMMMEditCropSettingsDropdownArea;
