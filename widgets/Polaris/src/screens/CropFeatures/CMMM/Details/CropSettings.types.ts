
import { ConfigurationType } from '@common/types';
import { CropSettingsCardConfigurationData, CropSettingsCardConfigurationInitial } from '../../shared/CropSettingsCard';

export type CropSettingCardTitleArgs = {
	head: string;
	editButton: string;
	partner: string;
};

export type CropSettingCardTextArgs = {
	headActionTooltip: string;
	partnerCaption: string;
};

export enum CerealsSettingsFieldKeys {
	GrowthScale = 'growthScale',
	YieldUnit = 'yieldUnit',
	RecommendationUnit = 'recommendationUnit',
	DefaultYield = 'defaultYield',
}

type CerealsCropSettingsCardConfigurationName = {
	name: CerealsSettingsFieldKeys;
};

export type CerealsCropSettingsCardInitial = CerealsCropSettingsCardConfigurationName & CropSettingsCardConfigurationInitial;

export type CerealsCropSettingsCardData = CerealsCropSettingsCardConfigurationName & CropSettingsCardConfigurationData;

export type CerealsCropSettingsCardConfig = CerealsCropSettingsCardInitial & CerealsCropSettingsCardData;

export type CerealsCropSettingsCard = {
	configurationType: ConfigurationType.Cereal;
	config: CerealsCropSettingsCardConfig[][];
};
