import { CropSettingsCardLogicProps, CropSettingsCardPrep } from '../../shared/CropSettingsCard';
import { useMemo } from 'react';
import { AhuaIconProps } from '@yaradigitallabs/ahua-react';
import {
  CerealsCropSettingsCard,
  CerealsCropSettingsCardConfig,
  CerealsCropSettingsCardData,
  CerealsSettingsFieldKeys,
} from './CropSettings.types';
import { cerealsCropSettingsConfigInitial } from './CropSettings.constants';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { ConfigurationType } from '@common/types';
import { filterUnitsById, useGetFilteredPartnersData } from '@widgets/Polaris/src/hooks';
import { ADDITIONAL_PROP_NAME } from '@common/constants';

/**
 * Hook to get crop settings units for Cereal MMM homepage
 * @param {CropRegion | null} cropRegion - The crop region object for the selected country.
 * @returns {Object} Object containing Cereal MMM crop settings units
 */
export const CropSettingsCMMMCardLogic = ({
  cropRegion,
  growthScalesData,
}: CropSettingsCardLogicProps) => {
  const { selectedCountryUnits } = useAppContext();
  const { getItems, getValue } = CropSettingsCardPrep();

  const filteredPartnerTags = useGetFilteredPartnersData(cropRegion);

  const defaultYield = useMemo(() => {
    return cropRegion?.additionalProperties?.find(
      ({ Name }) => Name === ADDITIONAL_PROP_NAME.DEFAULT_YIELD,
    );
  }, [cropRegion?.additionalProperties]);

  const hasDefaultYieldValue = useMemo(
    () =>
      Boolean(defaultYield) && !!(defaultYield?.DefaultValue || defaultYield?.DefaultValue === 0),
    [cropRegion?.additionalProperties],
  );

  const growthScale = useMemo(
    () => growthScalesData && growthScalesData.find(({ id }) => id === cropRegion?.growthScaleId),
    [growthScalesData, cropRegion],
  );

  const yieldUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, cropRegion?.yieldBaseUnitId),
    [selectedCountryUnits, cropRegion],
  );

  const recommendationUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, cropRegion?.demandBaseUnitId),
    [selectedCountryUnits, cropRegion],
  );

  const growthScaleItems = useMemo(() => {
    return getItems(growthScale, 'CalendarDate');
  }, [growthScale]);

  const growthScaleValue = useMemo(() => {
    return getValue(growthScale);
  }, [growthScale]);

  const yieldUnitItems = useMemo(() => {
    return getItems(yieldUnit, 'Crop');
  }, [yieldUnit]);

  const yieldUnitValue = useMemo(() => {
    return getValue(yieldUnit);
  }, [yieldUnit]);

  const recommendationItems = useMemo(() => {
    return getItems(recommendationUnit, 'Fertillizer');
  }, [recommendationUnit]);

  const recommendationValue = useMemo(() => {
    return getValue(recommendationUnit);
  }, [recommendationUnit]);

  const defaultYieldValueItems = useMemo((): {
    icon: AhuaIconProps['icon'];
    value: string;
    text: string;
  }[] => {
    const cropIconName: AhuaIconProps['icon'] = 'Crop';
    const item = !hasDefaultYieldValue
      ? {
        icon: cropIconName,
        value: 'error',
        text: '-',
      }
      : {
        icon: cropIconName,
        value: String(defaultYield?.DefaultValue),
        text: String(defaultYield?.DefaultValue),
      };
    return [item];
  }, [defaultYield, hasDefaultYieldValue]);

  const defaultYieldValue = useMemo(() => {
    return !hasDefaultYieldValue ? 'error' : String(defaultYield?.DefaultValue);
  }, [defaultYield, hasDefaultYieldValue]);

  // Each row is represented as an array of elements
  const cerealsCropSettingsConfigData: CerealsCropSettingsCardData[][] = [
    [
      {
        name: CerealsSettingsFieldKeys.GrowthScale,
        data: { items: growthScaleItems, value: growthScaleValue },
      },
      {
        name: CerealsSettingsFieldKeys.YieldUnit,
        data: { items: yieldUnitItems, value: yieldUnitValue },
      },
      {
        name: CerealsSettingsFieldKeys.RecommendationUnit,
        data: { items: recommendationItems, value: recommendationValue },
      },
      {
        name: CerealsSettingsFieldKeys.DefaultYield,
        data: { items: defaultYieldValueItems, value: defaultYieldValue },
        labelAddition: { key: 'yieldUnit', value: yieldUnit?.name || 'N/A' },
      },
    ],
  ];

  const structuredConfig: CerealsCropSettingsCardConfig[][] = cerealsCropSettingsConfigData.map(
    (configRow) => {
      const structuredConfigData = configRow.reduce<CerealsCropSettingsCardConfig[]>(
        (acc, curr) => {
          const initialData = cerealsCropSettingsConfigInitial.find(
            ({ name }) => name === curr.name,
          );

          if (initialData) {
            const row: CerealsCropSettingsCardConfig = {
              ...curr,
              ...initialData,
              ...configRow,
            };
            const newConfigData = [...acc, row];

            return newConfigData;
          }

          return acc;
        },
        [],
      );

      return structuredConfigData;
    },
  );

  const cerealsCropSettingsConfig: CerealsCropSettingsCard = {
    configurationType: ConfigurationType.Cereal,
    config: structuredConfig,
  };

  return {
    cerealsCropSettingsConfig,
    filteredPartnerTags,
  };
};
