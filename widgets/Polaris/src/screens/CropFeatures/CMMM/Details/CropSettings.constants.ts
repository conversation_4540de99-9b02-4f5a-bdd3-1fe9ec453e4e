import { CerealsCropSettingsCardInitial, CerealsSettingsFieldKeys } from './CropSettings.types';

export const cerealsCropSettingsConfigInitial: CerealsCropSettingsCardInitial[] = [
  {
    name: CerealsSettingsFieldKeys.GrowthScale,
    label: 'polaris.cmmmDetails.cropSettings.growthScale',
    ariaLabel: 'Growth scale',
    dataCy: 'growth-scale',
  },
  {
    name: CerealsSettingsFieldKeys.YieldUnit,
    label: 'polaris.cmmmDetails.cropSettings.yieldUnit',
    ariaLabel: 'Yield unit',
    dataCy: 'yield-unit',
  },
  {
    name: CerealsSettingsFieldKeys.RecommendationUnit,
    label: 'polaris.cmmmDetails.cropSettings.recommendationUnit',
    ariaLabel: 'Default target yield unit',
    dataCy: 'demand-unit',
  },
  {
    name: CerealsSettingsFieldKeys.DefaultYield,
    label: 'polaris.cmmmDetails.cropSettings.defaultYield',
    ariaLabel: 'Nutrient removal unit',
    dataCy: 'default-yield',
  },
];
