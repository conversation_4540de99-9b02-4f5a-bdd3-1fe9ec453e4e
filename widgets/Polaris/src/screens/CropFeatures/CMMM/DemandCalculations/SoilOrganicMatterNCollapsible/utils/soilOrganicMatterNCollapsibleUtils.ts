import { CerealsSoilOrganicMatter } from '@common/types';

export const getCerealsSoilOrganicMatterData = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any,
): CerealsSoilOrganicMatter | undefined => {
  if (
    typeof data === 'object' &&
    'somThreshold' in data &&
    'somThresholdUnitId' in data &&
    'deduct' in data &&
    'deductUnitId' in data &&
    'somEvery' in data &&
    'somEveryUnitId' in data &&
    'somDefault' in data &&
    'somDefaultUnitId' in data
  ) {
    return data;
  }
};
