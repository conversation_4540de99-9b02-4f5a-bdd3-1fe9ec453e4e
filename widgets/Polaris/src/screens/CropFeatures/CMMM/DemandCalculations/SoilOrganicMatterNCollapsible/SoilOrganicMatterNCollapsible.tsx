import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import { Subtitle, Input, CheckBox } from '@yaradigitallabs/ahua-react';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { useUpdateDemandCalculationModule } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import {
  CerealsSoilOrganicMatter,
  DemandCalculationModule,
  FeatureConfigOptions,
  ModuleNameToAccordionNutrientState,
} from '@common/types';
import { Separator } from '@yaradigitallabs/ahua-react';
import {
  InputContainer,
  InputsBlock,
  CheckBoxWrapper,
  StyledCheckBoxLabel,
  InputSOM,
  InputSOMContainer,
  StyledCaption,
} from '../DemandCalculations.styled';
import {
  HeaderForCollapsible,
  CollapsibleNoPaddingContainer,
  CerealsSomInputs,
  CerealsSomUnits,
  MAX_LIMIT_NUMBER,
  SomUnitsState,
  MAX_PERCENT_VALUE,
  MIN_PERCENT_VALUE,
  SOM_INPUTS,
  DEFAULT_INPUT_VALUE,
  DEFAULT_NUMBER_VALUE,
} from '../../../shared';
import { getCerealsSoilOrganicMatterData } from './utils/soilOrganicMatterNCollapsibleUtils';

interface SoilOrganicMatterNCollapsibleProps {
  soilOrganicMatterModule: DemandCalculationModule;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const SoilOrganicMatterNCollapsible = ({
  soilOrganicMatterModule,
  isModuleExpanded,
  setCurrentModulesAccordionState,
}: SoilOrganicMatterNCollapsibleProps) => {
  const keyPrefix = 'polaris.cmmmDetails.demandCalculations.soilOrganicMatterN';
  const { t } = useTranslation('polaris', {
    keyPrefix,
  });

  const {
    selectedCountryUnits,
    methods: { updateDemandCalculationModule },
  } = useAppContext();
  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    FeatureConfigOptions.CEREAL,
    soilOrganicMatterModule.id,
  );
  const { ThresholdUnitId, DeductUnitId, EveryUnitId, DefaultUnitId } = CerealsSomUnits;
  const {
    SomThreshold,
    SomThresholdUnitId,
    Deduct,
    SomDeductUnitId,
    SomEvery,
    SomEveryUnitId,
    SomDefault,
    SomDefaultUnitId,
  } = CerealsSomInputs;
  const configurationData: CerealsSoilOrganicMatter | undefined = useMemo(
    () => getCerealsSoilOrganicMatterData(soilOrganicMatterModule.configuration.data),
    [soilOrganicMatterModule?.configuration?.data],
  );

  const [somValues, setSomValues] = useState<Record<CerealsSomInputs, number | string>>({
    [SomThreshold]: configurationData?.somThreshold || DEFAULT_NUMBER_VALUE,
    [Deduct]: configurationData?.deduct || DEFAULT_NUMBER_VALUE,
    [SomEvery]: configurationData?.somEvery || DEFAULT_NUMBER_VALUE,
    [SomDefault]: configurationData?.somDefault || DEFAULT_NUMBER_VALUE,
    [SomThresholdUnitId]: configurationData?.somThresholdUnitId || ThresholdUnitId,
    [SomDeductUnitId]: configurationData?.deductUnitId || DeductUnitId,
    [SomEveryUnitId]: configurationData?.somEveryUnitId || EveryUnitId,
    [SomDefaultUnitId]: configurationData?.somDefaultUnitId || DefaultUnitId,
  });

  const [somUnits, setSomUnits] = useState<SomUnitsState>(SOM_INPUTS);

  const [somModuleEnabled, setSomModuleEnabled] = useState<boolean>(
    soilOrganicMatterModule.isEnabled,
  );

  useEffect(() => {
    if (!soilOrganicMatterModule) return;

    setSomModuleEnabled(soilOrganicMatterModule.isEnabled);
  }, [soilOrganicMatterModule]);

  useEffect(() => {
    if (!configurationData || !selectedCountryUnits) return;

    const updatedSomValues: Record<CerealsSomInputs, string | number> = {
      [SomThreshold]: configurationData.somThreshold || DEFAULT_NUMBER_VALUE,
      [Deduct]: configurationData.deduct || DEFAULT_NUMBER_VALUE,
      [SomEvery]: configurationData.somEvery || DEFAULT_NUMBER_VALUE,
      [SomDefault]: configurationData.somDefault || DEFAULT_NUMBER_VALUE,
      [SomThresholdUnitId]: configurationData.somThresholdUnitId || ThresholdUnitId,
      [SomDeductUnitId]: configurationData.deductUnitId || DeductUnitId,
      [SomEveryUnitId]: configurationData.somEveryUnitId || EveryUnitId,
      [SomDefaultUnitId]: configurationData.somDefaultUnitId || DefaultUnitId,
    };

    const unitKeys = [
      { key: 'somThresholdUnitName', id: SomThresholdUnitId },
      { key: 'deductUnitName', id: SomDeductUnitId },
      { key: 'somEveryUnitName', id: SomEveryUnitId },
      { key: 'somDefaultUnitName', id: SomDefaultUnitId },
    ];

    const updatedSomUnits: SomUnitsState = unitKeys.reduce(
      (acc, { key, id }) => {
        const unitName = selectedCountryUnits.find(
          (unit) => unit.id === configurationData[id] || unit.id === id,
        )?.name;
        return { ...acc, [key]: unitName || '' };
      },
      {
        somThresholdUnitName: '',
        deductUnitName: '',
        somEveryUnitName: '',
        somDefaultUnitName: '',
      },
    );

    setSomValues((prev) =>
      JSON.stringify(prev) === JSON.stringify(updatedSomValues) ? prev : updatedSomValues,
    );

    setSomUnits((prev) =>
      JSON.stringify(prev) === JSON.stringify(updatedSomUnits) ? prev : updatedSomUnits,
    );
  }, [configurationData, selectedCountryUnits]);

  const handleChangeValues = (value: string, name: CerealsSomInputs) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;

    const validatedValue = formatValidDecimalsNumber(value, 3);
    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;

    if (
      name !== CerealsSomInputs.Deduct &&
      value !== '' &&
      (Number(validatedValue) < Number(MIN_PERCENT_VALUE) ||
        Number(validatedValue) > Number(MAX_PERCENT_VALUE) ||
        isNaN(Number(validatedValue)))
    )
      return;

    setSomValues((prev) => ({
      ...prev,
      [name]: validatedValue === '' ? DEFAULT_INPUT_VALUE : validatedValue,
    }));
  };

  const handleUpdateValues = async (prop: CerealsSomInputs) => {
    const currentValue = somValues[prop].toString();

    const isValid = validateParameterNumber(currentValue);
    if (!isValid) return;

    const validatedValue = formatValidDecimalsNumber(currentValue, 3);

    const sanitizedValue = isNaN(Number(validatedValue))
      ? DEFAULT_NUMBER_VALUE
      : Number(validatedValue);

    if (configurationData?.[prop] === sanitizedValue) return;

    const newSoilOrganicMatterModule = cloneDeep(soilOrganicMatterModule);
    const newconfigurationData = {
      ...newSoilOrganicMatterModule.configuration.data,
      [prop]: sanitizedValue,
    };
    newSoilOrganicMatterModule.configuration.data = newconfigurationData;

    try {
      const updatedNDemandModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(newSoilOrganicMatterModule),
      });
      updatedNDemandModule && updateDemandCalculationModule(newSoilOrganicMatterModule);
    } catch (error) {
      console.error('Failed to update Soil Organic Matter N module:', error);
    }
  };

  const limitInputCharacters = (e: React.KeyboardEvent<HTMLInputElement>) => {
    ['ArrowUp', 'ArrowDown'].includes(e.key) && e.preventDefault();
  };

  const onCheckboxClick = async () => {
    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify({
          ...soilOrganicMatterModule,
          isEnabled: !soilOrganicMatterModule.isEnabled,
        }),
      });

      if (updatedModule) {
        updateDemandCalculationModule(updatedModule);
        setSomModuleEnabled(!soilOrganicMatterModule.isEnabled);
      }
    } catch (error) {
      console.error('Failed to update plan validation:', error);
    }
  };

  return (
    <CollapsibleNoPaddingContainer
      open={isModuleExpanded}
      className='collapsible-section'
      data-cy='cereals-soil-organic-matter-n-collapsible'
      header={
        <HeaderForCollapsible
          title={t('title')}
          subtitle={t('subtitle')}
          dataCy='som-status'
          isStatusEnabled={somModuleEnabled}
        />
      }
      onOpenChange={() =>
        setCurrentModulesAccordionState((prev) => {
          const modules = {
            ...prev.accordionState,
            [soilOrganicMatterModule.name]: !isModuleExpanded,
          };
          return {
            nutrientId: prev.nutrientId,
            accordionState: modules,
          };
        })
      }
    >
      {' '}
      <CheckBoxWrapper>
        <CheckBox
          data-cy='som-enable-for-use-checkbox'
          ariaLabel={`Checkbox ${somModuleEnabled}`}
          checked={somModuleEnabled}
          onClick={() => onCheckboxClick()}
        />
        <StyledCheckBoxLabel data-cy='som-enable-for-use-checkbox-label' size='n'>
          {t('checkboxLabel')}
        </StyledCheckBoxLabel>
      </CheckBoxWrapper>
      <InputsBlock css={{ padding: '0 $x4 $x6 $x4' }}>
        <Subtitle size='s' css={{ display: 'flex', gap: '$x2', alignItems: 'center' }}>
          {t('somExpressionTitle')}
        </Subtitle>
        <InputSOMContainer>
          <StyledCaption>{t('ifSomText')}</StyledCaption>
          <InputSOM
            size='s'
            label={t('inputs.labelSom', {
              unit: somUnits.somThresholdUnitName,
            })}
            data-cy='cereals-som-threshold-input'
            value={somValues[SomThreshold]}
            name={SomThreshold}
            aria-label={SomThreshold}
            onChange={({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
              handleChangeValues(value.replace(/\s/g, ''), SomThreshold);
            }}
            onBlur={() => {
              handleUpdateValues(SomThreshold);
            }}
            onKeyDown={limitInputCharacters}
          />
          <StyledCaption>{t('thenSomText')}</StyledCaption>
          <InputSOM
            size='s'
            label={somUnits.deductUnitName}
            data-cy='cereals-som-deduct-input'
            value={somValues[Deduct]}
            name={Deduct}
            aria-label={Deduct}
            onChange={({ target: { value } }) =>
              handleChangeValues(value.replace(/\s/g, ''), Deduct)
            }
            onBlur={() => handleUpdateValues(Deduct)}
            onKeyDown={limitInputCharacters}
          />{' '}
          <StyledCaption>{t('forSomText')}</StyledCaption>
          <InputSOM
            size='s'
            label={t('inputs.labelSom', {
              unit: somUnits.somEveryUnitName,
            })}
            name={SomEvery}
            aria-label={SomEvery}
            data-cy='cereals-som-every-input'
            value={somValues[SomEvery]}
            onChange={({ target: { value } }) =>
              handleChangeValues(value.replace(/\s/g, ''), SomEvery)
            }
            onBlur={() => handleUpdateValues(SomEvery)}
            onKeyDown={limitInputCharacters}
          />
        </InputSOMContainer>
      </InputsBlock>
      <Separator />
      <InputsBlock css={{ padding: '$x1 $x4 $x6 $x4' }}>
        <Subtitle size='s' css={{ display: 'flex', gap: '$x2', alignItems: 'center' }}>
          {t('defaultSomSubtitle')}
        </Subtitle>
        <InputContainer>
          <Input
            size='s'
            label={t('inputs.labelDefaultSom', {
              unit: somUnits.somDefaultUnitName,
            })}
            name={SomDefault}
            aria-label={SomDefault}
            data-cy='cereals-default-som-input'
            value={somValues[SomDefault]}
            onChange={({ target: { value } }) =>
              handleChangeValues(value.replace(/\s/g, ''), SomDefault)
            }
            onBlur={() => handleUpdateValues(SomDefault)}
            onKeyDown={limitInputCharacters}
          />
        </InputContainer>
      </InputsBlock>
    </CollapsibleNoPaddingContainer>
  );
};
