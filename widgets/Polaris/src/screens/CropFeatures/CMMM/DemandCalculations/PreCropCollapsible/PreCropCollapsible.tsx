import React, { Di<PERSON><PERSON>, FC, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckBox, Select, Separator, Subtitle, Title } from '@yaradigitallabs/ahua-react';

import {
  CheckBoxWrapper,
  SelectDefaultSection,
  StyledButton,
  StyledCheckBoxLabel,
} from './PreCropCollapsible.styled';
import {
  DemandCalculationModule,
  ModuleNameToAccordionNutrientState,
  FeatureConfigOptions,
  CerealsPreCrop,
  PreCrop,
} from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { METHOD } from '@common/constants';
import { cloneDeep } from 'lodash';
import PreCropTable from './PreCropTable';
import { AddPreCropDialog } from './AddPreCropDialog';
import {
  useFetchPreCrops,
  useUpdateDemandCalculationModule,
  filterUnitsById,
} from '@polaris-hooks/index';
import { v4 as uuidv4 } from 'uuid';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  BtnTypes,
  RadioButtonGroup,
  HeaderForCollapsible,
  CollapsibleNoPaddingContainer,
  HeaderSection,
  AddPreCropConfig,
  CerealsPreCropItem,
  DEFAULT_PRE_CROP_RESIDUAL_UNIT,
  DEFAULT_PRE_CROP_RESIDUAL_YIELD_UNIT,
  DEFAULT_PRE_CROP_YIELD_UNIT,
  PreCropArrayKeys,
  PreCropTypes,
} from '../../../shared';

interface PreCropCollapsibleProps {
  preCropModule: DemandCalculationModule;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const PreCropCollapsible: FC<PreCropCollapsibleProps> = ({
  preCropModule,
  isModuleExpanded,
  setCurrentModulesAccordionState,
}) => {
  const keyPrefix = 'polaris.cmmmDetails.demandCalculations.preCrop';
  const { t } = useTranslation('polaris', {
    keyPrefix,
  });

  const { setDisplaySnackbar } = useSnackbar();
  const displaySnackbarMessage = () => {
    setDisplaySnackbar({
      title: t('successDeleteMessage'),
      colorConcept: 'successLight',
      icon: 'Check',
      placement: 'bottomRight',
      duration: 3000,
      open: true,
    });
  };

  const {
    selectedCountryUnits,
    methods: { updateDemandCalculationModule },
  } = useAppContext();

  const allPreCrops = useFetchPreCrops([], true);

  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    FeatureConfigOptions.CEREAL,
    preCropModule.id,
  );

  const [preCropEnabled, setPreCropEnabled] = useState<boolean>(preCropModule.isEnabled);
  const [hasChange, setHasChange] = useState<boolean>(false);
  const [preCropType, setPreCropType] = useState<PreCropTypes>();
  const [preCropDataState, setPreCropDataState] = useState<CerealsPreCrop>({
    isResidual: false,
    residuals: [],
    residualsWithYield: [],
    residualDefaultPreCropId: '',
    residualYieldDefaultPreCropId: '',
  });

  const [openAddPreCropDialog, setOpenAddPreCropDialog] = useState<boolean>(false);
  const [selectFirstPage, setSelectFirstPage] = useState<boolean>(false);

  const preCropKeyArray = useMemo(() => {
    return preCropType === PreCropTypes.PreCropResidual
      ? PreCropArrayKeys.Residuals
      : PreCropArrayKeys.ResidualsWithYield;
  }, [preCropType]);

  const isCerealsPreCrop = (data: unknown): data is CerealsPreCrop => {
    return (
      typeof data === 'object' &&
      data !== null &&
      'residuals' in data &&
      'residualsWithYield' in data
    );
  };

  const preCropData = useMemo(() => preCropModule.configuration.data ?? {}, [preCropModule]);

  const { residuals, residualsWithYield } = isCerealsPreCrop(preCropData)
    ? preCropData
    : { residuals: [], residualsWithYield: [] };

  const filterAvailablePreCrops = (allPreCrops: PreCrop[], excludedIds: string[]) => {
    if (!excludedIds.length) return allPreCrops;
    return allPreCrops.filter((item) => !excludedIds.includes(item.id));
  };

  const [availableForResidualPreCrops, availableForYieldPreCrops] = useMemo(() => {
    if (!allPreCrops) return [[], []];

    const residualCropIds = residuals.map((entry) => entry.cropId);
    const residualWithYieldCropIds = residualsWithYield.map((entry) => entry.cropId);

    const availableForResidual = filterAvailablePreCrops(allPreCrops, residualCropIds);
    const availableForYield = filterAvailablePreCrops(allPreCrops, residualWithYieldCropIds);

    return [availableForResidual, availableForYield];
  }, [allPreCrops, residuals, residualsWithYield]);

  const [residualUnitName, residualForYieldUnitName, yieldUnitName] = useMemo(() => {
    if (!selectedCountryUnits?.length) return [DEFAULT_PRE_CROP_RESIDUAL_UNIT, ''];

    const findUnitName = (unitId?: string) => filterUnitsById(selectedCountryUnits, unitId)?.name;

    const firstResidual = residuals[0];
    const residualUnitName = firstResidual
      ? findUnitName(firstResidual.residualUnitId) || DEFAULT_PRE_CROP_RESIDUAL_UNIT
      : DEFAULT_PRE_CROP_RESIDUAL_UNIT;

    const firstYield = residualsWithYield[0];
    const yieldUnitName =
      preCropType === PreCropTypes.PreCropYield
        ? findUnitName(firstYield?.preCropYieldUnitId) || DEFAULT_PRE_CROP_YIELD_UNIT
        : '';

    const residualForYieldUnitName =
      preCropType === PreCropTypes.PreCropYield
        ? findUnitName(firstYield?.residualUnitId) || DEFAULT_PRE_CROP_RESIDUAL_YIELD_UNIT
        : '';
    return [residualUnitName, residualForYieldUnitName, yieldUnitName];
  }, [preCropType, selectedCountryUnits, residuals, residualsWithYield]);

  const preCropDefaultOptionsArray = useMemo(() => {
    if (!allPreCrops) return [];

    const currentArray =
      preCropType === PreCropTypes.PreCropResidual ? residuals : residualsWithYield;

    if (!currentArray.length) return [];

    const cropIds = currentArray.map((item) => item.cropId);

    const sortedPreCrops = allPreCrops
      .filter((item) => cropIds.includes(item.id))
      .sort((a, b) => cropIds.indexOf(a.id) - cropIds.indexOf(b.id));

    return sortedPreCrops.map((el) => ({
      value: el.id,
      text: el.name,
    }));
  }, [allPreCrops, residuals, residualsWithYield]);

  useEffect(() => {
    if (!preCropModule || !preCropData || !isCerealsPreCrop(preCropData)) return;

    setPreCropEnabled(preCropModule.isEnabled);

    setPreCropDataState(preCropData);

    setPreCropType(
      preCropData.isResidual ? PreCropTypes.PreCropResidual : PreCropTypes.PreCropYield,
    );
  }, [preCropData, preCropModule]);

  const onPreCropValueChange = (paramToUpdateId: string, newVal: string, valueKey: string) => {
    setPreCropDataState((prevState) => {
      return {
        ...prevState,
        [preCropKeyArray]: (prevState[preCropKeyArray] ?? []).map((param) =>
          param.id === paramToUpdateId ? { ...param, [valueKey]: newVal } : param,
        ),
      };
    });

    setHasChange(true);
  };

  const onPreCropValueBlur = async (paramToUpdateId: string, newVal: string, valueKey: string) => {
    if (!hasChange) return;

    const { [preCropKeyArray]: preCropArray = [] } = preCropDataState || {};

    const newPreCropState = {
      ...cloneDeep(preCropDataState),
      [preCropKeyArray]: preCropArray.map((param) =>
        param.id === paramToUpdateId ? { ...param, [valueKey]: Number(newVal) || 0 } : param,
      ),
    };

    const newState: DemandCalculationModule = {
      ...cloneDeep(preCropModule),
      configuration: { data: newPreCropState },
    };
    await preCropUpdate(newState);
    setHasChange(false);
  };

  const onCheckboxClick = async () => {
    const newState = {
      ...preCropModule,
      isEnabled: !preCropModule.isEnabled,
    };
    await preCropUpdate(newState);
    setPreCropEnabled(!preCropModule.isEnabled);
  };

  const onPreCropTypeChanged = async (value: BtnTypes) => {
    const newPreCrop: CerealsPreCrop = {
      ...cloneDeep(preCropDataState ?? {}),
      isResidual: value === BtnTypes.LeftType,
    };

    const newState: DemandCalculationModule = {
      ...cloneDeep(preCropModule),
      configuration: { data: newPreCrop },
    };

    await preCropUpdate(newState);
    setPreCropType(
      value === BtnTypes.LeftType ? PreCropTypes.PreCropResidual : PreCropTypes.PreCropYield,
    );
    setSelectFirstPage(true);
  };

  const preCropUpdate = async (newState: DemandCalculationModule) => {
    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(newState),
      });

      if (updatedModule && isCerealsPreCrop(newState.configuration.data)) {
        updateDemandCalculationModule(updatedModule);
        setPreCropDataState(newState.configuration.data);
      }
    } catch (error) {
      console.error('Failed to update Pre Crop N module:', error);
    }
  };

  const onPreCropDelete = async (preCropToDelete: CerealsPreCropItem) => {
    if (!preCropDataState) return;

    const newPreCropData: CerealsPreCrop = {
      ...cloneDeep(preCropDataState),
      [preCropKeyArray]:
        preCropDataState[preCropKeyArray]?.filter(({ id }) => id !== preCropToDelete.id) || [],
    };

    const newState: DemandCalculationModule = {
      ...cloneDeep(preCropModule),
      configuration: { data: newPreCropData },
    };

    await preCropUpdate(newState);
    displaySnackbarMessage();
  };

  const handleAddPreCrop = async (preCrop: AddPreCropConfig) => {
    const newPreCropState: CerealsPreCrop = {
      ...preCropDataState,
      residuals: preCropDataState?.residuals ?? [],
      residualsWithYield: preCropDataState?.residualsWithYield ?? [],
      isResidual: preCropDataState?.isResidual ?? false,
      residualDefaultPreCropId: preCropDataState?.residualDefaultPreCropId ?? '',
      residualYieldDefaultPreCropId: preCropDataState?.residualYieldDefaultPreCropId ?? '',
    };

    if (preCropType === PreCropTypes.PreCropResidual) {
      newPreCropState.residuals = [
        {
          id: uuidv4(),
          cropId: preCrop.id,
          residualValue: Number(preCrop.residualValue) || 0,
          residualUnitId: newPreCropState.residuals[0].residualUnitId,
        },
        ...(newPreCropState.residuals ?? []),
      ];
    } else {
      newPreCropState.residualsWithYield = [
        {
          id: uuidv4(),
          cropId: preCrop.id,
          residualValue: Number(preCrop.residualValue) || 0,
          residualUnitId: newPreCropState.residualsWithYield[0].residualUnitId,
          preCropYield: Number(preCrop.yieldValue) || 0,
          preCropYieldUnitId: newPreCropState.residualsWithYield[0].preCropYieldUnitId,
        },
        ...(newPreCropState.residualsWithYield ?? []),
      ];
    }

    const newState: DemandCalculationModule = {
      ...cloneDeep(preCropModule),
      configuration: { data: newPreCropState },
    };

    await preCropUpdate(newState);
    setOpenAddPreCropDialog(false);
    setSelectFirstPage(true);
  };

  const handleChangeDefaultPreCrop = async (preCropId: string) => {
    const { residualDefaultPreCropId = '', residualYieldDefaultPreCropId = '' } =
      preCropDataState || {};

    const newPreCrop: CerealsPreCrop = {
      ...cloneDeep(preCropDataState),
      residualDefaultPreCropId:
        preCropType === PreCropTypes.PreCropResidual ? preCropId : residualDefaultPreCropId,
      residualYieldDefaultPreCropId:
        preCropType !== PreCropTypes.PreCropResidual ? preCropId : residualYieldDefaultPreCropId,
    };

    const newState: DemandCalculationModule = {
      ...cloneDeep(preCropModule),
      configuration: { data: newPreCrop },
    };

    await preCropUpdate(newState);
  };

  return (
    <>
      <AddPreCropDialog
        isOpen={openAddPreCropDialog}
        onClose={() => setOpenAddPreCropDialog(false)}
        preCrops={
          preCropType === PreCropTypes.PreCropResidual
            ? availableForResidualPreCrops
            : availableForYieldPreCrops
        }
        residualUnitName={
          preCropType === PreCropTypes.PreCropResidual ? residualUnitName : residualForYieldUnitName
        }
        yieldUnitName={yieldUnitName}
        preCropType={preCropType || PreCropTypes.PreCropResidual}
        keyPrefix={`${keyPrefix}.addPreCropDialog`}
        onAddPreCrop={handleAddPreCrop}
      />
      <CollapsibleNoPaddingContainer
        data-cy='cereals-pre-crop-n-collapsible'
        open={isModuleExpanded}
        className='collapsible-section'
        header={
          <HeaderForCollapsible
            title={t('title')}
            subtitle={t('subtitle')}
            dataCy='is-enable-pre-crop-n-status'
            isStatusEnabled={preCropEnabled}
          />
        }
        onOpenChange={() =>
          setCurrentModulesAccordionState((prev) => {
            const modules = {
              ...prev.accordionState,
              [preCropModule.name]: !isModuleExpanded,
            };
            return {
              nutrientId: prev.nutrientId,
              accordionState: modules,
            };
          })
        }
      >
        <CheckBoxWrapper>
          <CheckBox
            data-cy='pre-crop-n-enable-for-use-checkbox'
            ariaLabel={`Checkbox ${preCropModule.isEnabled}`}
            checked={preCropModule.isEnabled}
            onClick={() => onCheckboxClick()}
          />
          <StyledCheckBoxLabel data-cy='pre-crop-n-enable-for-use-checkbox-label' size='n'>
            {t('checkboxLabel')}
          </StyledCheckBoxLabel>
        </CheckBoxWrapper>
        <RadioButtonGroup
          title={t('selectMethodTitle')}
          leftLabelText={t('preCropResidual')}
          rightLabelText={t('preCropYield')}
          defaultType={
            preCropType === PreCropTypes.PreCropResidual ? BtnTypes.LeftType : BtnTypes.RightType
          }
          onChange={(value: BtnTypes) => onPreCropTypeChanged(value)}
        />
        <HeaderSection>
          <Title size='xs' css={{ padding: '$x4', alignSelf: 'center' }}>
            {t(`tableTitle${preCropType === PreCropTypes.PreCropResidual ? 'Residual' : ''}`)}
          </Title>
          <StyledButton
            size='s'
            onClick={() => setOpenAddPreCropDialog(true)}
            data-cy='add-pre-crop-btn'
          >
            {t('addPreCropButton')}
          </StyledButton>
        </HeaderSection>

        {residualUnitName && preCropDataState && allPreCrops && preCropType && (
          <PreCropTable
            keyPrefix={`${keyPrefix}.preCropTable`}
            preCropType={preCropType}
            allPreCrops={allPreCrops}
            preCropDataState={preCropDataState}
            yieldUnitName={yieldUnitName}
            residualUnitName={
              preCropType === PreCropTypes.PreCropResidual
                ? residualUnitName
                : residualForYieldUnitName
            }
            selectFirstPage={selectFirstPage}
            setSelectFirstPage={setSelectFirstPage}
            onPreCropValueChange={onPreCropValueChange}
            onPreCropValueBlur={onPreCropValueBlur}
            onPreCropDelete={onPreCropDelete}
          />
        )}
        <Separator />
        <SelectDefaultSection data-cy='default-pre-crop-select'>
          <Subtitle size='s' css={{ paddingBottom: '$x2' }}>
            {t('setDefaultTitle')}
          </Subtitle>
          <Select
            ariaLabel='Select default Pre Crop'
            cover='outline'
            items={preCropDefaultOptionsArray || []}
            position='popper'
            size='s'
            label={t('setDefaultLabel')}
            onChange={(value) => handleChangeDefaultPreCrop(value)}
            value={
              preCropType === PreCropTypes.PreCropResidual
                ? preCropDataState?.residualDefaultPreCropId
                : preCropDataState?.residualYieldDefaultPreCropId
            }
            css={{
              maxWidth: '328px',
              width: '328px',
              label: {
                overflow: 'unset',
              },
            }}
            onFocus={null}
            onBlur={null}
          />
        </SelectDefaultSection>
      </CollapsibleNoPaddingContainer>
    </>
  );
};
