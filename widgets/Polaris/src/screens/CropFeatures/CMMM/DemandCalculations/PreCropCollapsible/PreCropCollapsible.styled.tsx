import { CSSProperties } from '@stitches/react';
import { AdvancedDialog, Button, Label, styled, Table } from '@yaradigitallabs/ahua-react';
const EMPTY_STATE_HEIGHT = 290;

export const CheckBoxWrapper = styled('div', {
  padding: '$x6 $x4 $x6 0',
  height: '$x6',
  display: 'flex',
  alignItems: 'center',
});

export const StyledCheckBoxLabel = styled(Label, {
  fontWeight: '$medium',
});

export const TableContainer = styled('div', {
  // height: '400px',

  '&> div': {
    position: 'relative',
    paddingTop: 'unset',
    height: '400px',
  },
  '& > div > div': {
    paddingTop: '$x3',
  },
});

export const StyledTable = styled(Table, {
  width: '100%',
});

export const TableRow = styled(Table.Row, {
  borderTop: '2px solid $black10 !important',
  'th:first-child': {
    width: '35%',
  },
  th: {
    verticalAlign: 'middle',
    maxWidth: '40%',
  },
  'th:last-child': {
    width: '10%',
    textAlign: 'center',
  },
});

export const AddPreCropButton = styled(Button, {});
export const SelectDefaultSection = styled('div', {
  padding: '$x6 $x4',
});

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: EMPTY_STATE_HEIGHT,
};

export const InputContainer = styled('div', {
  display: 'flex',
  gap: '$x4',
  label: {
    overflow: 'unset',
  },
});

export const AdvancedDialogMiddle = styled(AdvancedDialog.Middle, {
  gap: '$x7',
  position: 'relative',
  p: {
    position: 'absolute',
    bottom: '85px',
  },
});

export const StyledButton = styled(Button, {
  marginRight: '$x4',
  span: {
    overflow: 'unset',
  },
});
