// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PreCropCollapsible component should match snapshot 1`] = `
<DocumentFragment>
  <div
    class="c-eNnZw c-bFZoXt c-cXlfGx collapsible-section"
    data-cy="cereals-pre-crop-n-collapsible"
    data-state="open"
  >
    <button
      aria-controls="radix-:rb:"
      aria-expanded="true"
      class="c-cUgXyc"
      data-state="open"
      type="button"
    >
      <div
        class="c-kBfmpw"
      >
        <div>
          <h1
            class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
          >
            title
          </h1>
          <h2
            class="c-iAVmsd c-iAVmsd-fADHcj-size-xs c-iAVmsd-iPJLV-css"
          >
            subtitle
          </h2>
        </div>
        <div
          class="c-cIigya c-cIigya-eyXUBd-colorType-gray c-cIigya-fYJylb-size-s c-cIigya-iiIClND-css"
          data-cy="is-enable-pre-crop-n-status"
        >
          <label
            class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
          >
            statusDisabled
          </label>
        </div>
      </div>
      <div
        class="c-irPLE"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18 15.75l-5-6h-2l-5 6"
          />
        </svg>
      </div>
    </button>
    <div
      class="c-fLVWxk"
      data-state="open"
      id="radix-:rb:"
      style="transition-duration: 0s; animation-name: none;"
    >
      <div
        class="c-iUrglI"
      >
        <div
          class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
        >
          <button
            aria-checked="false"
            aria-label="Checkbox false"
            class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
            data-cy="pre-crop-n-enable-for-use-checkbox"
            data-state="unchecked"
            role="checkbox"
            type="button"
            value="on"
          />
        </div>
        <label
          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-iqKmYR"
          data-cy="pre-crop-n-enable-for-use-checkbox-label"
        >
          checkboxLabel
        </label>
      </div>
      <h2
        class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-ihgGvzz-css"
        data-cy="radio-btn-title"
      >
        selectMethodTitle
      </h2>
      <div
        aria-required="false"
        class="c-fixGjY c-jSATwS"
        dir="ltr"
        role="radiogroup"
        style="outline: none;"
        tabindex="0"
      >
        <div
          class="c-gHhZvU c-gHhZvU-gWlYHv-checked-true"
          data-cy="left-button"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
            for="leftType"
          >
            preCropResidual
          </label>
          <div
            class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
          >
            <button
              aria-checked="true"
              class="c-thNAo c-thNAo-bcWJEY-concept-brand"
              data-radix-collection-item=""
              data-state="checked"
              id="leftType"
              role="radio"
              tabindex="-1"
              type="button"
              value="leftType"
            >
              <span
                class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                data-state="checked"
              />
            </button>
          </div>
        </div>
        <div
          class="c-gHhZvU c-gHhZvU-iCnFdU-checked-false"
          data-cy="right-button"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css"
            for="RightType"
          >
            preCropYield
          </label>
          <div
            class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
          >
            <button
              aria-checked="false"
              class="c-thNAo c-thNAo-bcWJEY-concept-brand"
              data-radix-collection-item=""
              data-state="unchecked"
              id="RightType"
              role="radio"
              tabindex="-1"
              type="button"
              value="RightType"
            />
          </div>
        </div>
      </div>
      <div
        class="c-kBfmpw"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-ihHnzIu-css"
        >
          tableTitleResidual
        </h1>
        <button
          class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary c-daAWGm"
          data-cy="add-pre-crop-btn"
        >
          <span
            class="c-iepcqn"
          >
            addPreCropButton
          </span>
        </button>
      </div>
      <div
        class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
      />
      <div
        class="c-deDiYc"
        data-cy="default-pre-crop-select"
      >
        <h2
          class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-igrDnkr-css"
        >
          setDefaultTitle
        </h2>
        <div
          class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ijzXTqc-css"
        >
          <div
            class="c-kFLrJl"
          >
            <label
              class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-ubosY-textType-labelAsPlaceholder"
            >
              setDefaultLabel
            </label>
            <label
              class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-hDbASB-selectSize-s"
            />
            <button
              aria-autocomplete="none"
              aria-controls="radix-:rf:"
              aria-expanded="false"
              aria-label="Select default Pre Crop"
              class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
              data-state="closed"
              dir="ltr"
              role="combobox"
              tabindex="0"
              type="button"
            >
              <span
                style="pointer-events: none;"
              >
                <div
                  class="c-fSebPZ"
                />
              </span>
              <svg
                aria-hidden="true"
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 9.75l-5 6h-2l-5-6"
                />
              </svg>
            </button>
          </div>
          <p
            class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
