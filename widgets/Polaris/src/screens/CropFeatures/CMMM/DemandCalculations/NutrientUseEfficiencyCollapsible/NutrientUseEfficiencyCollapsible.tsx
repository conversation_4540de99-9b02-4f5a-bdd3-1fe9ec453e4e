import React, { Dispatch, FC, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Input, Subtitle } from '@yaradigitallabs/ahua-react';
import {
  DemandCalculationModule,
  ModuleNameToAccordionNutrientState,
  FeatureConfigOptions,
  CerealsUseEfficiency,
  Nutrient,
} from '@common/types';
import { useUpdateDemandCalculationModule, filterUnitsById } from '@polaris-hooks/index';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { METHOD } from '@common/constants';

import { InputContainer, InputsBlock } from '../DemandCalculations.styled';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';

import {
  CollapsibleContainer,
  HeaderForCollapsible,
  MAX_PERCENT_VALUE,
  MIN_PERCENT_VALUE,
} from '../../../shared';

interface NutrientUseEfficiencyCollapsibleProps {
  selectedNutrient: Nutrient;
  useEfficiencyModule: DemandCalculationModule;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const NutrientUseEfficiencyCollapsible: FC<NutrientUseEfficiencyCollapsibleProps> = ({
  selectedNutrient,
  useEfficiencyModule,
  isModuleExpanded,
  setCurrentModulesAccordionState,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.demandCalculations.nutrientUseEfficiency',
  });

  const {
    selectedCountryUnits,
    methods: { updateDemandCalculationModule },
  } = useAppContext();

  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    FeatureConfigOptions.CEREAL,
    useEfficiencyModule.id || '',
  );

  const configurationData: CerealsUseEfficiency | undefined = useMemo(() => {
    const data = useEfficiencyModule.configuration?.data;
    if (isCerealsUseEfficiency(data)) {
      return {
        ...data,
      };
    }
    return undefined;
  }, [useEfficiencyModule]);

  const elementalName: string = useMemo(() => {
    return selectedNutrient.elementalName;
  }, [selectedNutrient]);

  const [nueValue, setNueValue] = useState<string>();

  useEffect(() => {
    configurationData?.useEfficiency && setNueValue(configurationData.useEfficiency.toString());
  }, [configurationData]);

  const nueUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, configurationData?.useEfficiencyUnitId),
    [selectedCountryUnits, configurationData],
  );
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function isCerealsUseEfficiency(data: any): data is CerealsUseEfficiency {
    return data && typeof data.useEfficiency !== undefined;
  }

  const handleValueUpdate = async (value: string) => {
    const nueValue = Number(value) === 0 ? MIN_PERCENT_VALUE : value;

    if (
      isCerealsUseEfficiency(useEfficiencyModule.configuration.data) &&
      useEfficiencyModule.configuration.data.useEfficiency === Number(nueValue)
    ) {
      return setNueValue(nueValue);
    }

    useEfficiencyModule.configuration.data = {
      ...useEfficiencyModule.configuration.data,
      useEfficiency: Number(nueValue),
    };

    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(useEfficiencyModule),
      });
      updatedModule && updateDemandCalculationModule(updatedModule);
    } catch (error) {
      console.error('Failed to update Nutrient Use Efficiency:', error);
    }
  };

  const getValidatedValue = (value: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;
    const validatedValue = formatValidDecimalsNumber(value, 3);
    if (
      value !== '' &&
      (Number(validatedValue) < Number(MIN_PERCENT_VALUE) ||
        Number(validatedValue) > Number(MAX_PERCENT_VALUE) ||
        isNaN(Number(validatedValue)))
    )
      return;

    return validatedValue.toString();
  };

  return (
    <CollapsibleContainer
      open={isModuleExpanded}
      data-cy='cereals-nutrient-use-efficiency-collapsible'
      header={
        <HeaderForCollapsible
          title={t('headerTitle', {
            nutrient: elementalName,
          })}
          subtitle={t('headerSubtitle', {
            nutrient: elementalName,
          })}
        />
      }
      className='collapsible-section'
      onOpenChange={() =>
        setCurrentModulesAccordionState((prev) => {
          const modules = {
            ...prev.accordionState,
            [useEfficiencyModule.name]: !isModuleExpanded,
          };
          return {
            nutrientId: prev.nutrientId,
            accordionState: modules,
          };
        })
      }
    >
      <InputsBlock>
        <Subtitle size='s' css={{ paddingTop: '$x1 !important' }}>
          {t('enterFields')}
        </Subtitle>
        <InputContainer>
          {' '}
          <Input
            value={nueValue ?? MIN_PERCENT_VALUE}
            label={t('inputLabelNUE', {
              nutrient: elementalName,
              unit: nueUnit?.name ?? '%',
            })}
            onChange={({ target: { value } }) => {
              const validatedValue = getValidatedValue(value);
              if (validatedValue !== undefined) {
                setNueValue(validatedValue);
              }
            }}
            onBlur={({ target: { value } }) => {
              handleValueUpdate(value);
            }}
            data-cy='cereals-use-efficiency-input'
            size='s'
          />
        </InputContainer>
      </InputsBlock>
    </CollapsibleContainer>
  );
};
