import { Caption, Input, Label, Select, styled } from '@yaradigitallabs/ahua-react';

export const CMMMDemandCalculationsWrapper = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  gap: '$x4',
  width: '100%',
});

export const StyledSelect = styled(Select, {
  width: '420px',
  margin: '$x4 0 $x4',
  label: {
    overflow: 'unset',
  },
  p: {
    paddingTop: '$x1',
  },
});

export const InputsBlock = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  gap: '$x2',

  '& h2': {
    paddingTop: '$x6',
  },
});

export const InputContainer = styled('div', {
  display: 'flex',
  gap: '$x4',
  '& > div': {
    maxWidth: '328px',
    '& label': {
      overflow: 'unset !important',
    },
  },
});

export const InputSOMContainer = styled(InputContainer, {
  gap: '$x2',
  paddingTop: '$x1',
  '& > div': {
    width: 'auto',
  },
});

export const StyledCaption = styled(Caption, {
  alignSelf: 'center',
});

export const CheckBoxWrapper = styled('div', {
  padding: '$x6 $x4 $x1 0',
  height: '$x6',
  display: 'flex',
  alignItems: 'center',
  gap: '$x1',
});

export const StyledCheckBoxLabel = styled(Label, {
  fontWeight: '$medium',
});

export const HeaderSection = styled('div', {
  display: 'flex',
  width: '100%',
  justifyContent: 'space-between',
});

export const InputSOM = styled(Input, {
  width: '114px',
  height: '56px',
});
