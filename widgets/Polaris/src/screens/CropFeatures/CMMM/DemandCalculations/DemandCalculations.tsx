import React from 'react';
import { useTranslation } from 'react-i18next';
import { NutrientsStaticList } from '@widgets/Polaris/src/components';
import { CMMMDemandCalculationsWrapper, StyledSelect } from './DemandCalculations.styled';
import {
  CropNutrientDemandCollapsible,
  Equation,
  SoilCorrectionFactorCollapsible,
} from '../../shared';
import CMMMDemandCalculationsLogic from './DemandCalculationLogic';
import { SoilOrganicMatterNCollapsible } from './SoilOrganicMatterNCollapsible';
import { SoilMineralCollapsible } from './SoilMineralCollapsible';
import { NutrientUseEfficiencyCollapsible } from './NutrientUseEfficiencyCollapsible';
import {
  FertigationModuleNameToAccordionState,
  CerealsDemandCalculationModule,
  CerealsModuleNameToAccordionState,
  FeatureConfigOptions,
} from '@common/types';
import { PreCropCollapsible } from './PreCropCollapsible';

function isCerealsAccordionState(
  state: FertigationModuleNameToAccordionState | CerealsModuleNameToAccordionState,
): state is CerealsModuleNameToAccordionState {
  return Object.values(CerealsDemandCalculationModule).some((key) => key in state);
}

const CMMMDemandCalculations = () => {
  const keyPrefix = 'polaris.cmmmDetails.demandCalculations';
  const { t } = useTranslation('polaris', {
    keyPrefix,
  });
  const {
    selectedNutrient,
    nutrientsData,
    filteredUnitsByNutrientForms,
    cropNutrientDemandUnits,
    currentNutrientDemandModule,
    currentSoilOrganicMatterNModule,
    currentSoilMineralModule,
    currentUseEfficiencyModule,
    currentSoilCorrectionFactorModule,
    currentPreCropModule,
    currentModulesAccordionState,
    setCurrentModulesAccordionState,
  } = CMMMDemandCalculationsLogic();

  const isModuleExpanded = (tab: keyof CerealsModuleNameToAccordionState) => {
    const accordionState = currentModulesAccordionState.accordionState;

    if (isCerealsAccordionState(accordionState)) {
      return accordionState[tab] ?? false;
    }

    return false;
  };

  return (
    <CMMMDemandCalculationsWrapper data-cy='cereals-demand-calculations-wrapper'>
      {nutrientsData && (
        <NutrientsStaticList
          selectedNutrient={selectedNutrient}
          nutrientParams={nutrientsData}
          title={t('nutrientList.title')}
          emptyStateText={t('nutrientList.emptyStateText')}
        />
      )}
      <div data-cy='cereals-demand-calculations-nutrient-forms'>
        <StyledSelect
          ariaLabel={t('nutrientForm.areaLabel')}
          cover='outline'
          css={{ width: '420px', margin: '$x3 0 $x4' }}
          helper-text={t('nutrientForm.description')}
          items={nutrientsData.map((n) => {
            const unit = filteredUnitsByNutrientForms?.find((u) => {
              const tags = u.tags.split(',');
              return tags.includes(n.elementalName);
            });

            return {
              text: unit?.name ?? '',
              value: n.id,
            };
          })}
          disabled
          label={t('nutrientForm.title')}
          position='popper'
          size='s'
          variant='default'
          value={selectedNutrient?.id}
          loading={nutrientsData.length === 0}
          onFocus={null}
          onBlur={null}
        />
      </div>
      {selectedNutrient && (
        <Equation
          title={t('mainEquation.title')}
          description={t('mainEquation.description', {
            nutrient: selectedNutrient.elementalName,
          })}
          equation={t(`mainEquation.equation${selectedNutrient.elementalName}`)}
          dataCy='cereals-demand-calculations-main-equation'
        />
      )}
      {currentNutrientDemandModule && selectedNutrient && (
        <CropNutrientDemandCollapsible
          units={cropNutrientDemandUnits}
          nutrientDemandModule={currentNutrientDemandModule}
          selectedNutrient={selectedNutrient}
          isModuleExpanded={isModuleExpanded(CerealsDemandCalculationModule.CropDemand)}
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
          configType={FeatureConfigOptions.CEREAL}
        />
      )}
      {currentUseEfficiencyModule && selectedNutrient && (
        <NutrientUseEfficiencyCollapsible
          selectedNutrient={selectedNutrient}
          useEfficiencyModule={currentUseEfficiencyModule}
          isModuleExpanded={isModuleExpanded(CerealsDemandCalculationModule.UseEfficiency)}
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
        />
      )}
      {currentSoilOrganicMatterNModule && selectedNutrient && (
        <SoilOrganicMatterNCollapsible
          soilOrganicMatterModule={currentSoilOrganicMatterNModule}
          isModuleExpanded={isModuleExpanded(CerealsDemandCalculationModule.SoilOrganicMatter)}
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
        />
      )}
      {currentSoilMineralModule && selectedNutrient && (
        <SoilMineralCollapsible
          soilMineralModule={currentSoilMineralModule}
          selectedNutrient={selectedNutrient}
          isModuleExpanded={isModuleExpanded(CerealsDemandCalculationModule.SoilMineral)}
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
        />
      )}
      {currentSoilCorrectionFactorModule && (
        <SoilCorrectionFactorCollapsible
          keyPrefix={keyPrefix}
          soilCorrectionFactorModule={currentSoilCorrectionFactorModule}
          isModuleExpanded={isModuleExpanded(CerealsDemandCalculationModule.SoilCorrectionFactor)}
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
          configType={FeatureConfigOptions.CEREAL}
        />
      )}
      {currentPreCropModule && (
        <PreCropCollapsible
          preCropModule={currentPreCropModule}
          isModuleExpanded={isModuleExpanded(CerealsDemandCalculationModule.PreCrop)}
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
        />
      )}
    </CMMMDemandCalculationsWrapper>
  );
};

export default CMMMDemandCalculations;
