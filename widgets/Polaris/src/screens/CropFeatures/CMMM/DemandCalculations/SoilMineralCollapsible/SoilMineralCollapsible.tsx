import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Subtitle, Input, CheckBox } from '@yaradigitallabs/ahua-react';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { filterUnitsById, useUpdateDemandCalculationModule } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import {
  CerealsSoilMineral,
  DemandCalculationModule,
  FeatureConfigOptions,
  ModuleNameToAccordionNutrientState,
  Nutrient,
} from '@common/types';
import {
  InputContainer,
  InputsBlock,
  CheckBoxWrapper,
  StyledCheckBoxLabel,
} from '../DemandCalculations.styled';

import {
  HeaderForCollapsible,
  CollapsibleNoPaddingContainer,
  MAX_LIMIT_NUMBER,
  ELEMENT_N,
  DEFAULT_INPUT_VALUE,
} from '../../../shared';

interface SoilMineralCollapsibleProps {
  soilMineralModule: DemandCalculationModule;
  selectedNutrient: Nutrient;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const SoilMineralCollapsible = ({
  soilMineralModule,
  selectedNutrient,
  isModuleExpanded,
  setCurrentModulesAccordionState,
}: SoilMineralCollapsibleProps) => {
  const keyPrefix = 'polaris.cmmmDetails.demandCalculations.soilMineral';
  const { t } = useTranslation('polaris', {
    keyPrefix,
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isCerealsSoilMineral = (data: any): data is CerealsSoilMineral => {
    return data && typeof data.soilMineral !== undefined;
  };

  const {
    selectedCountryUnits,
    methods: { updateDemandCalculationModule },
  } = useAppContext();
  const elementalName: string = useMemo(() => {
    return selectedNutrient.elementalName;
  }, [selectedNutrient]);

  const configurationData: CerealsSoilMineral | undefined = useMemo(() => {
    const data = soilMineralModule.configuration?.data;
    if (isCerealsSoilMineral(data)) {
      return {
        ...data,
      };
    }
    return undefined;
  }, [soilMineralModule?.configuration?.data]);

  const [soilMineralValue, setSoilMineralValue] = useState<string>();
  const [isEditable, setIsEditable] = useState<boolean>();
  const soilMineralUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, configurationData?.soilMineralUnitId),
    [selectedCountryUnits, configurationData],
  );
  const [soilMineralModuleEnabled, setSoilMineralModuleEnabled] = useState<boolean>(
    soilMineralModule.isEnabled,
  );

  useEffect(() => {
    if (!soilMineralModule) return;

    setSoilMineralModuleEnabled(soilMineralModule.isEnabled);
  }, [soilMineralModule]);

  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    FeatureConfigOptions.CEREAL,
    soilMineralModule.id || '',
  );
  useEffect(() => {
    configurationData?.defaultSoilMineral &&
      setSoilMineralValue(configurationData.defaultSoilMineral.toString());
    configurationData?.isEditable && setIsEditable(configurationData.isEditable);
  }, [configurationData]);

  const getValidatedValue = (value: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;

    const validatedValue = formatValidDecimalsNumber(value, 3);
    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;

    return validatedValue.toString();
  };

  const handleValueUpdate = async (value: string) => {
    const newValue = Number(value) === 0 ? DEFAULT_INPUT_VALUE : value;

    if (
      isCerealsSoilMineral(soilMineralModule.configuration.data) &&
      soilMineralModule.configuration.data.defaultSoilMineral === Number(newValue)
    ) {
      return setSoilMineralValue(newValue);
    }

    soilMineralModule.configuration.data = {
      ...soilMineralModule.configuration.data,
      defaultSoilMineral: Number(newValue),
    };

    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(soilMineralModule),
      });
      updatedModule && updateDemandCalculationModule(updatedModule);
    } catch (error) {
      console.error('Failed to update Soil mineral', error);
    }
  };

  const limitInputCharacters = (e: React.KeyboardEvent<HTMLInputElement>) => {
    ['ArrowUp', 'ArrowDown'].includes(e.key) && e.preventDefault();
  };

  const onCheckboxClick = async () => {
    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify({
          ...soilMineralModule,
          isEnabled: !soilMineralModule.isEnabled,
        }),
      });

      if (updatedModule) {
        updateDemandCalculationModule(updatedModule);
        setSoilMineralModuleEnabled(!soilMineralModule.isEnabled);
      }
    } catch (error) {
      console.error('Failed to update Soil mineral', error);
    }
  };

  const onIsEditCheckboxClick = async (value: boolean) => {
    soilMineralModule.configuration.data = {
      ...soilMineralModule.configuration.data,
      isEditable: value,
    };
    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(soilMineralModule),
      });
      updatedModule && updateDemandCalculationModule(updatedModule);
      setIsEditable(value);
    } catch (error) {
      console.error('Failed to update isEditable in soil mineral', error);
    }
  };

  return (
    <CollapsibleNoPaddingContainer
      open={isModuleExpanded}
      className='collapsible-section'
      data-cy='cereals-soil-mineral-collapsible'
      header={
        <HeaderForCollapsible
          title={t('title', {
            nutrient: elementalName,
          })}
          subtitle={t('subtitle')}
          dataCy='is-enable-soil-mineral-status'
          isStatusEnabled={soilMineralModuleEnabled}
        />
      }
      onOpenChange={() =>
        setCurrentModulesAccordionState((prev) => {
          const modules = {
            ...prev.accordionState,
            [soilMineralModule.name]: !isModuleExpanded,
          };
          return {
            nutrientId: prev.nutrientId,
            accordionState: modules,
          };
        })
      }
    >
      <CheckBoxWrapper>
        <CheckBox
          data-cy='soil-mineral-enable-for-use-checkbox'
          ariaLabel={`Checkbox ${soilMineralModule.isEnabled}`}
          checked={soilMineralModule.isEnabled}
          onClick={() => onCheckboxClick()}
        />
        <StyledCheckBoxLabel data-cy='soil-mineral-enable-for-use-checkbox-label' size='n'>
          {t('checkboxLabel')}
        </StyledCheckBoxLabel>
      </CheckBoxWrapper>
      <InputsBlock css={{ padding: '0 $x4 $x6 $x4' }}>
        <Subtitle size='s' css={{ display: 'flex', gap: '$x2', alignItems: 'center' }}>
          {elementalName === ELEMENT_N
            ? t('inputSoilMineralTitle', { nutrient: elementalName })
            : t('defaultSoilMineralTitle')}
        </Subtitle>
        <InputContainer>
          <Input
            size='s'
            label={t(`${elementalName === ELEMENT_N ? 'input' : 'default'}SoilMineralLabel`, {
              nutrient: elementalName,
              unit: soilMineralUnit?.name,
            })}
            data-cy='cereals-soil-mineral-input'
            value={soilMineralValue ?? DEFAULT_INPUT_VALUE}
            onChange={({ target: { value } }) => {
              const validatedValue = getValidatedValue(value);
              if (validatedValue !== undefined) {
                setSoilMineralValue(validatedValue);
              }
            }}
            onBlur={({ target: { value } }) => {
              handleValueUpdate(value);
            }}
            onKeyDown={limitInputCharacters}
          />
          <CheckBoxWrapper
            css={{
              border: '1px solid $black40',
              borderRadius: '$m',
              padding: '$x3 0 $x3 $x3',
            }}
          >
            <StyledCheckBoxLabel data-cy='soil-mineral-is-editable-checkbox-label' size='n'>
              {t('isEditableLabel')}
            </StyledCheckBoxLabel>
            <CheckBox
              data-cy='soil-mineral-is-editable-checkbox'
              ariaLabel={`Checkbox ${isEditable}`}
              checked={isEditable}
              onClick={() => onIsEditCheckboxClick(!isEditable)}
            />
          </CheckBoxWrapper>
        </InputContainer>
      </InputsBlock>
    </CollapsibleNoPaddingContainer>
  );
};
