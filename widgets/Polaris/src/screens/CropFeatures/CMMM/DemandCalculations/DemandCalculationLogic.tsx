import { useEffect, useMemo, useRef, useState } from 'react';
import {
  CMMM_DEMAND_CALCULATION_NUTRIENTS,
  CMMM_DEMAND_CALCULATION_NUTRIENT_FORMS,
  CEREALS_MODULES_TO_STATES_INITIAL_MAP,
  CMMM_PLAN_CONFIGURATION_TABS,
  CerealsCropNutrientDemandUnits,
} from '../../shared';

import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import { useFetchDemandCalculations, useFetchNutrientsFromPolaris } from '@polaris-hooks/index';

import {
  DemandCalculationModule,
  CerealsDemandCalculationModule,
  FeatureConfigOptions,
} from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { isEqual, sortBy } from 'lodash';

const CMMMDemandCalculationsLogic = () => {
  const {
    selectedFeatureNutrients,
    selectedPlanConfigTab,
    selectedCountry,
    selectedCountryUnits,
    cropRegion,
    demandCalculations,
    demandCalculationAccordion,
    methods: { setDemandCalculations, setDemandCalculationAccordion },
  } = useAppContext();

  const nutrientsData = useFetchNutrientsFromPolaris([
    {
      key: 'id',
      value: CMMM_DEMAND_CALCULATION_NUTRIENTS.join(','),
      type: FilterType.IN,
    },
  ]);

  const nutrientID = useMemo(
    () => selectedFeatureNutrients?.[selectedPlanConfigTab ?? ''],
    [selectedPlanConfigTab, selectedFeatureNutrients],
  );

  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      CMMM_DEMAND_CALCULATION_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);

  const nutrientsRef = useRef(sortedNutrients);
  if (!isEqual(nutrientsRef.current, sortedNutrients)) {
    nutrientsRef.current = sortedNutrients;
  }

  const selectedNutrient = useMemo(
    () => sortedNutrients && sortedNutrients.find(({ id }) => id === nutrientID),
    [nutrientsRef.current, nutrientID],
  );

  const filteredUnitsByNutrientForms = useMemo(() => {
    if (selectedCountryUnits) {
      return selectedCountryUnits.filter((unit) =>
        CMMM_DEMAND_CALCULATION_NUTRIENT_FORMS.includes(unit.id),
      );
    }
  }, [selectedCountryUnits]);

  const demandCalculationFilter: GenericFilter[] | undefined = useMemo(() => {
    if (!selectedCountry?.id || !cropRegion?.id) return;
    return [
      {
        key: 'countryId',
        value: selectedCountry.id,
        type: FilterType.EQ,
      },
      {
        key: 'cropRegionId',
        value: cropRegion.id,
        type: FilterType.EQ,
      },
    ];
  }, [selectedCountry?.id, cropRegion?.id]);

  const demandCalculationData = useFetchDemandCalculations(
    FeatureConfigOptions.CEREAL,
    demandCalculationFilter,
    Boolean(selectedCountry?.id && cropRegion?.id),
  );

  const [currentNutrientDemandModule, setCurrentNutrientDemandModule] =
    useState<DemandCalculationModule | null>();

  const [currentUseEfficiencyModule, setCurrentUseEfficiencyModule] =
    useState<DemandCalculationModule | null>();

  const [currentSoilOrganicMatterNModule, setCurrentSoilOrganicMatterNModule] =
    useState<DemandCalculationModule | null>();

  const [currentSoilMineralModule, setCurrentSoilMineralModule] =
    useState<DemandCalculationModule | null>();

  const [currentSoilCorrectionFactorModule, setCurrentSoilCorrectionFactorModule] =
    useState<DemandCalculationModule | null>();
  const [currentPreCropModule, setCurrentPreCropModule] =
    useState<DemandCalculationModule | null>();

  const [currentModulesAccordionState, setCurrentModulesAccordionState] = useState(
    demandCalculationAccordion ?? {
      nutrientId: selectedNutrient?.id || '',
      accordionState: CEREALS_MODULES_TO_STATES_INITIAL_MAP,
    },
  );

  useEffect(() => {
    if (!demandCalculationData) return;
    setDemandCalculations(demandCalculationData);
  }, [demandCalculationData]);

  // Whenever nutrient changes, we reset local accordion state to default
  useEffect(() => {
    const noNutrient =
      !selectedNutrient ||
      !selectedNutrient?.id ||
      !selectedFeatureNutrients ||
      !selectedFeatureNutrients?.[CMMM_PLAN_CONFIGURATION_TABS.DEMAND_CALC];
    if (noNutrient) return;

    const demandCalcFeatureNutrientId =
      selectedFeatureNutrients[CMMM_PLAN_CONFIGURATION_TABS.DEMAND_CALC];

    // The state should not reset when user gets back from a different config tab.
    // It could happen that the nutrient has not updated yet (because it comes from a child component)
    const shouldNotResetState =
      selectedNutrient.id !== demandCalcFeatureNutrientId ||
      currentModulesAccordionState.nutrientId === demandCalcFeatureNutrientId;
    if (shouldNotResetState) return;

    const newState = {
      nutrientId: selectedNutrient?.id || '',
      accordionState: CEREALS_MODULES_TO_STATES_INITIAL_MAP,
    };
    setCurrentModulesAccordionState(newState);
  }, [selectedNutrient?.id]);

  // Setting the global state when a collapsible expands or closes
  useEffect(() => {
    selectedNutrient &&
      selectedNutrient.id &&
      setDemandCalculationAccordion(currentModulesAccordionState);
  }, [currentModulesAccordionState]);

  useEffect(() => {
    if (!demandCalculations || !selectedNutrient) return;

    const selectedDemandCalculation = demandCalculations.find(
      (demandCalculation) => demandCalculation.nutrientId === selectedNutrient.id,
    );

    // when there is no configuration for a newly selected nutrient,
    // the modules are set to null to avoid showing the modules for the previously selected nutrient
    if (!selectedDemandCalculation) {
      setCurrentNutrientDemandModule(null);
      setCurrentUseEfficiencyModule(null);
      setCurrentSoilOrganicMatterNModule(null);
      setCurrentSoilMineralModule(null);
      setCurrentSoilCorrectionFactorModule(null);
      setCurrentPreCropModule(null);
      return;
    }
    const moduleMap = selectedDemandCalculation.demandCalculationModules.reduce<
      Record<string, DemandCalculationModule>
    >((acc, module) => {
      acc[module.name] = module;
      return acc;
    }, {});

    setCurrentNutrientDemandModule(moduleMap[CerealsDemandCalculationModule.CropDemand] || null);
    setCurrentUseEfficiencyModule(moduleMap[CerealsDemandCalculationModule.UseEfficiency] || null);
    setCurrentSoilOrganicMatterNModule(
      moduleMap[CerealsDemandCalculationModule.SoilOrganicMatter] || null,
    );
    setCurrentSoilMineralModule(moduleMap[CerealsDemandCalculationModule.SoilMineral] || null);
    setCurrentSoilCorrectionFactorModule(
      moduleMap[CerealsDemandCalculationModule.SoilCorrectionFactor] || null,
    );
    setCurrentPreCropModule(moduleMap[CerealsDemandCalculationModule.PreCrop] || null);
  }, [selectedNutrient, demandCalculations]);

  const cropNutrientDemandUnits = useMemo(() => {
    if (selectedCountryUnits) {
      return selectedCountryUnits.filter(
        ({ id }) =>
          CerealsCropNutrientDemandUnits.NutrientRemovalId.includes(id) ||
          CerealsCropNutrientDemandUnits.NutrientUptakeId.includes(id),
      );
    }
  }, [selectedCountryUnits]);

  return {
    selectedNutrient,
    nutrientsData: sortedNutrients,
    filteredUnitsByNutrientForms,
    currentNutrientDemandModule,
    currentUseEfficiencyModule,
    currentSoilOrganicMatterNModule,
    currentSoilMineralModule,
    currentSoilCorrectionFactorModule,
    currentPreCropModule,
    cropNutrientDemandUnits,
    currentModulesAccordionState,
    setCurrentModulesAccordionState,
  };
};

export default CMMMDemandCalculationsLogic;
