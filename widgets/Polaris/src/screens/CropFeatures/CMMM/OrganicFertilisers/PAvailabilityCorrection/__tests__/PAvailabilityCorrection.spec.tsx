import React from 'react';
import { cloneDeep } from 'lodash';
import { setupServer } from 'msw/node';
import {
  updateCerealOrganicFertiliserPAvailabilityCorrectionConfiugrationHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { NavbarProvider } from '@libs/nav-context';
import { act, fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { MAX_LIMIT_NUMBER } from '../../../../shared/constants';
import PAvailabilityCorrection from '../PAvailabilityCorrection';
import { OrganicFertiliserConfigurationNames } from '@common/constants';
import { FilterType } from '@widgets/Polaris/src/types';
import * as useOrganicFertiliser from '@polaris-hooks/masterMindMap/useOrganicFertiliser/useOrganicFertiliser';

const server = setupServer(
  updateCerealOrganicFertiliserPAvailabilityCorrectionConfiugrationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('P availability correction', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('match snapshot', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <PAvailabilityCorrection
            organicFertiliserConfigurationsData={
              mockAppProviderValue.organicFertiliserConfigurations
            }
          />
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });

  it('render a table with 2 rows, a caption and an input per row when there is a valid p availability correction configuration', async () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <PAvailabilityCorrection
            organicFertiliserConfigurationsData={
              mockAppProviderValue.organicFertiliserConfigurations
            }
          />
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(screen.getByTestId('p-availability-correction-collapsible')).toBeInTheDocument();
    expect(screen.getByTestId('p-availability-correction-header-title')).toBeInTheDocument();

    const pAvailabilityCorrectionSubtitle = screen.getByTestId(
      'p-availability-correction-subtitle',
    );
    expect(pAvailabilityCorrectionSubtitle).toBeInTheDocument();
    expect(pAvailabilityCorrectionSubtitle).toHaveTextContent('table.title');

    const headerRow = screen.getByTestId('p-availability-correction-table-header-row');
    expect(headerRow).toBeInTheDocument();

    const numberOfRows = screen.getAllByTestId(/p-availability-correction-[<|>=\w]+-row/);
    const numberOfInputs = screen.getAllByTestId(/p-availability-correction-[<|>=\w]+-input/);
    const numberOfCaptions = screen.getAllByTestId(/p-availability-correction-[<|>=\w]+-caption/);
    expect(numberOfRows.length).toBe(2);
    expect(numberOfInputs.length).toBe(2);
    expect(numberOfCaptions.length).toBe(2);
  });

  it('render 2 rows, a caption and an input per row when there is additional invalid data', async () => {
    await act(async () => {
      const clonedProviderValue = cloneDeep(mockAppProviderValue);
      const newPAvailabilityCorrectionConfiguration =
        clonedProviderValue.organicFertiliserConfigurations.find(
          (configuration) =>
            configuration.name === OrganicFertiliserConfigurationNames.PAvailabilityCorrection,
        );
      if (newPAvailabilityCorrectionConfiguration) {
        newPAvailabilityCorrectionConfiguration.configuration.data.medium = [
          ...newPAvailabilityCorrectionConfiguration.configuration.data.medium,
          {
            id: '1cd72815-cc03-491b-b2b2-1e305791974a',
            to: null,
            from: FilterType.GT,
            name: 'test',
            ordinal: 3,
            correctionPercent: {
              value: 10,
              canEdit: true,
              isApplicable: true,
            },
          },
        ];

        clonedProviderValue.organicFertiliserConfigurations[2] =
          newPAvailabilityCorrectionConfiguration;
      }

      render(
        <AppContext.Provider value={clonedProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                clonedProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const numberOfRows = screen.getAllByTestId(/p-availability-correction-[<|>=\w]+-row/);
    const numberOfInputs = screen.getAllByTestId(/p-availability-correction-[<|>=\w]+-input/);
    const numberOfCaptions = screen.getAllByTestId(/p-availability-correction-[<|>=\w]+-caption/);
    expect(numberOfRows.length).toBe(2);
    expect(numberOfInputs.length).toBe(2);
    expect(numberOfCaptions.length).toBe(2);
  });

  it('not rendering any rows, captions or inputs when data is invalid', async () => {
    await act(async () => {
      const invalidDataProviderValue = {
        ...mockAppProviderValue,
        organicFertiliserConfigurations: [
          {
            ...mockAppProviderValue.organicFertiliserConfigurations[2],
            configuration: {
              data: {
                medium: [
                  {
                    id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                    ordinal: 1,
                    correctionPercent: {
                      canEdit: true,
                      isApplicable: true,
                      value: 0,
                    },
                    name: 'test',
                    from: FilterType.GT,
                    to: null,
                  },
                ],
              },
            },
          },
        ],
      };

      render(
        <AppContext.Provider value={invalidDataProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                invalidDataProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const rows = screen.queryByTestId(/p-availability-correction-[<|>=\w]+-row/);
    const inputs = screen.queryByTestId(/p-availability-correction-[<|>=\w]+-input/);
    const captions = screen.queryByTestId(/p-availability-correction-[<|>=\w]+-caption/);
    expect(rows).not.toBeInTheDocument();
    expect(inputs).not.toBeInTheDocument();
    expect(captions).not.toBeInTheDocument();
  });
  it('invalid value', async () => {
    await act(async () => {
      const invalidDataProviderValue = {
        ...mockAppProviderValue,
        organicFertiliserConfigurations: [
          {
            ...mockAppProviderValue.organicFertiliserConfigurations[2],
            configuration: {
              data: {
                medium: [
                  {
                    id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
                    ordinal: 1,
                    correctionPercent: {
                      canEdit: true,
                      isApplicable: true,
                      value: 'abc',
                    },
                    name: '<Medium',
                    from: FilterType.GT,
                    to: null,
                  },
                ],
              },
            },
          },
        ],
      };

      render(
        <AppContext.Provider value={invalidDataProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                invalidDataProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const lessThanMediumInput = screen.getByTestId('p-availability-correction-<Medium-input');

    expect(lessThanMediumInput).toBeInTheDocument();
    expect(lessThanMediumInput).toHaveValue('abc');
    fireEvent.change(lessThanMediumInput, { target: { value: 'bbb' } });
    fireEvent.blur(lessThanMediumInput);
    expect(lessThanMediumInput).toHaveValue('abc');
  });

  it('updates to input are correct and reflected', async () => {
    const promise = Promise.resolve();
    mockAppProviderValue.methods.updateOrganicFertiliserConfigurations = jest.fn(() => promise);
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                mockAppProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const lessThanMediumInput = screen.getByTestId('p-availability-correction-<Medium-input');

    expect(lessThanMediumInput).toBeInTheDocument();
    expect(lessThanMediumInput).toHaveValue('0');
    fireEvent.change(lessThanMediumInput, { target: { value: '9' } });
    fireEvent.blur(lessThanMediumInput);
    expect(lessThanMediumInput).toHaveValue('9');

    // Removes warning about not using act
    await act(() => promise);
  });

  it('updates to input are invalid type', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                mockAppProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const lessThanMediumInput = screen.getByTestId('p-availability-correction-<Medium-input');

    expect(lessThanMediumInput).toBeInTheDocument();
    expect(lessThanMediumInput).toHaveValue('0');
    fireEvent.change(lessThanMediumInput, { target: { value: 'asd' } });
    fireEvent.blur(lessThanMediumInput);
    expect(lessThanMediumInput).toHaveValue('0');
  });

  it('updates to input exceed max or min limit', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                mockAppProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const numberExceedingMaxLimit = MAX_LIMIT_NUMBER + 1;
    const lessThanMediumInput = screen.getByTestId('p-availability-correction-<Medium-input');

    expect(lessThanMediumInput).toBeInTheDocument();
    expect(lessThanMediumInput).toHaveValue('0');
    fireEvent.change(lessThanMediumInput, {
      target: { value: numberExceedingMaxLimit },
    });
    fireEvent.blur(lessThanMediumInput);
    expect(lessThanMediumInput).toHaveValue('0');
  });

  it('render empty table state when there are no configurations', async () => {
    const mockAppProviderValueWithoutConfigurations = {
      ...mockAppProviderValue,
      organicFertiliserConfigurations: [],
    };
    render(
      <AppContext.Provider value={mockAppProviderValueWithoutConfigurations}>
        <NavbarProvider>
          <PAvailabilityCorrection
            organicFertiliserConfigurationsData={
              mockAppProviderValueWithoutConfigurations.organicFertiliserConfigurations
            }
          />
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const emptyStateComp = screen.getByTestId('p-availability-correction-empty-state');
    expect(emptyStateComp).toBeInTheDocument();

    const emptyStateIcon = within(emptyStateComp).getAllByTestId('empty-state-info-icon');
    expect(emptyStateIcon[0]).toBeInTheDocument();
  });

  it('update returns an error', async () => {
    const callbackError = jest.fn();
    console.error = callbackError;

    await act(async () => {
      jest
        .spyOn(useOrganicFertiliser, 'useUpdateOrganicFertiliserConfiguration')
        .mockImplementation(() => ({
          data: undefined,
          trigger: () => {
            throw Error();
          },
          error: undefined,
          isMutating: false,
        }));
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <PAvailabilityCorrection
              organicFertiliserConfigurationsData={
                mockAppProviderValue.organicFertiliserConfigurations
              }
            />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const lessThanMediumInput = screen.getByTestId('p-availability-correction-<Medium-input');

    expect(lessThanMediumInput).toBeInTheDocument();
    expect(lessThanMediumInput).toHaveValue('0');
    fireEvent.change(lessThanMediumInput, { target: { value: '1' } });
    fireEvent.blur(lessThanMediumInput);

    await waitFor(() => {
      expect(callbackError).toHaveBeenCalled();
    });
  });
});
