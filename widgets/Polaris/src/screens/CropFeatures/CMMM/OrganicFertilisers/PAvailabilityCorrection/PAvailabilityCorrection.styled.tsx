import { styled, Table, Tooltip } from '@yaradigitallabs/ahua-react';
import { CSSProperties } from 'react';

export const CollapsibleContainer = styled('div', {
  paddingTop: '$x6',
});

export const TableContainer = styled('div', {
  padding: '0 $x4 $x6 $x4',
  justifyItems: 'center',
  '& table': {
    width: '100%',
  },
});

export const SubtitleContainer = styled('div', {
  display: 'flex',
  padding: '23px 0 6px $x4',
  alignItems: 'center',
  '& h2': {
    fontSize: '$scale3',
    paddingRight: '$x1',
    height: '$x5',
  },
  '& svg': {
    color: '$black50',
  },
});

export const StyledTooltip = styled(Tooltip, {
  '& > div': {
    paddingLeft: 0,
  },
});

export const TableHeaderRow = styled(Table.Row, {
  borderTop: '2px solid $black10',
  '& th': {
    width: '50%',
    verticalAlign: 'middle',
  },
  '& th > p': {
    fontWeight: '$semiBold',
  },
});

export const TableBodyRow = styled(Table.Row, {
  '& td:nth-child(2)': {
    padding: '7px $x4 7px $x3 !important',
  },
});

const EMPTY_STATE_HEIGHT = 169;

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: EMPTY_STATE_HEIGHT,
};
