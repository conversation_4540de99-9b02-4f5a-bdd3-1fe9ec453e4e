import { TFunction } from 'i18next';

export interface TableConfigProps {
  t: TFunction;
  keyPrefix: string;
}

export enum Inputs {
  LessThanMedium = '<Medium',
  GreaterThanOrEqualToMedium = '>=Medium',
}

export const getKeyFromEnum = (value: string) => {
  switch (value) {
    case Inputs.LessThanMedium:
      return Inputs.LessThanMedium;
    case Inputs.GreaterThanOrEqualToMedium:
      return Inputs.GreaterThanOrEqualToMedium;
    default:
      break;
  }
};
