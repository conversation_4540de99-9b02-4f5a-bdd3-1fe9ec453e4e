import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import { AhuaIcon, Caption, Input, Subtitle, Table } from '@yaradigitallabs/ahua-react';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';
import CollapsibleSectionHeaderTitle from '@widgets/Polaris/src/components/Collapsible/components/CollapsibleSectionHeaderTitle/CollapsibleSectionHeaderTitle';
import CollapsibleSection from '@widgets/Polaris/src/components/Collapsible/Collapsible';
import { useUpdateOrganicFertiliserConfiguration } from '@polaris-hooks/index';
import {
  OrganicFertiliserConfigurationResponse,
  PAvailabilityCorrectionFactor,
} from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  METHOD,
  OrganicFertiliserConfigurationNames,
  THREE_DECIMAL_PLACES_LIMIT,
} from '@common/constants';
import {
  formatNegativeOrPositiveDecimalsNumber,
  formatValidNegativeNumber,
  validateParameterNumber,
} from '@widgets/Polaris/utils';
import { MAX_LIMIT_NUMBER, MIN_LIMIT_NUMBER } from '../../../shared/constants';
import {
  CollapsibleContainer,
  TableContainer,
  EmptyStateAdditionalStyles,
  SubtitleContainer,
  TableHeaderRow,
  TableBodyRow,
  StyledTooltip,
} from './PAvailabilityCorrection.styled';
import { getKeyFromEnum, Inputs } from './utils/PAvailabilityCorrectionUtils';

const tPrefix = 'polaris.cmmmDetails.planConfiguration.organicFertilisers.pAvailabilityCorrection';

interface PAvailabilityCorrectionProps {
  organicFertiliserConfigurationsData: OrganicFertiliserConfigurationResponse;
}

export const PAvailabilityCorrection = ({
  organicFertiliserConfigurationsData,
}: PAvailabilityCorrectionProps) => {
  const { t } = useTranslation('polaris', { keyPrefix: tPrefix });

  const { LessThanMedium, GreaterThanOrEqualToMedium } = Inputs;

  const {
    methods: { updateOrganicFertiliserConfigurations },
  } = useAppContext();

  const pAvailabilityCorrectionConfigurationData = useMemo(() => {
    if (!organicFertiliserConfigurationsData) {
      return;
    }

    const clonedConfigurations = cloneDeep(organicFertiliserConfigurationsData);
    const pAvailabilityCorrectionConfiguration = clonedConfigurations.find(
      (configuration) =>
        configuration.name === OrganicFertiliserConfigurationNames.PAvailabilityCorrection,
    );

    if (pAvailabilityCorrectionConfiguration) {
      return pAvailabilityCorrectionConfiguration;
    }
  }, [organicFertiliserConfigurationsData]);

  const { trigger: updatePAvailabilityCorrectionConfiguration } =
    useUpdateOrganicFertiliserConfiguration(pAvailabilityCorrectionConfigurationData?.id);

  const [correctionPercentValues, setCorrectionPercentValues] = useState<
    Record<Inputs, number | string>
  >({
    [LessThanMedium]:
      pAvailabilityCorrectionConfigurationData?.configuration.data.medium?.[0]?.correctionPercent
        .value ?? 0,
    [GreaterThanOrEqualToMedium]:
      pAvailabilityCorrectionConfigurationData?.configuration.data.medium?.[1]?.correctionPercent
        .value ?? 0,
  });

  useEffect(() => {
    if (!pAvailabilityCorrectionConfigurationData?.configuration.data.medium) {
      return;
    }

    setCorrectionPercentValues({
      [LessThanMedium]:
        pAvailabilityCorrectionConfigurationData?.configuration.data.medium?.[0]?.correctionPercent
          .value ?? 0,
      [GreaterThanOrEqualToMedium]:
        pAvailabilityCorrectionConfigurationData?.configuration.data.medium?.[1]?.correctionPercent
          .value ?? 0,
    });
  }, [pAvailabilityCorrectionConfigurationData]);

  const handleChangeValues = (value: string, name: string) => {
    const typedName = getKeyFromEnum(name);
    if (!typedName) {
      return;
    }

    const formattedNegativeValue = formatValidNegativeNumber(value);

    const isValid =
      validateParameterNumber(formattedNegativeValue) || formattedNegativeValue === '-';
    if (!isValid) {
      return;
    }

    const validatedValue = formatNegativeOrPositiveDecimalsNumber(
      formattedNegativeValue,
      THREE_DECIMAL_PLACES_LIMIT,
    );

    const isExceedingMaxLimit = Boolean(Number(validatedValue) > MAX_LIMIT_NUMBER);
    const isExceedingMinLimit = Boolean(Number(validatedValue) < MIN_LIMIT_NUMBER);
    if (isExceedingMaxLimit || isExceedingMinLimit) {
      return;
    }

    setCorrectionPercentValues((prev: Record<Inputs, number | string>) => ({
      ...prev,
      [name]: validatedValue,
    }));
  };

  const handleUpdateValues = async (name: string) => {
    if (!pAvailabilityCorrectionConfigurationData) {
      return;
    }

    const typedName = getKeyFromEnum(name);
    if (!typedName) {
      return;
    }

    const otherName =
      typedName === Inputs.LessThanMedium
        ? Inputs.GreaterThanOrEqualToMedium
        : Inputs.LessThanMedium;

    const currentValue = correctionPercentValues[typedName].toString();

    const sanitizedCurrentValue = isNaN(Number(currentValue)) ? '0' : currentValue;

    const isValid = validateParameterNumber(sanitizedCurrentValue);

    if (!isValid) {
      return;
    }

    const validatedValue = Number(
      formatNegativeOrPositiveDecimalsNumber(sanitizedCurrentValue, THREE_DECIMAL_PLACES_LIMIT),
    );

    const clonedPAvailabilityCorrectionConfigurationData = cloneDeep(
      pAvailabilityCorrectionConfigurationData,
    );

    const currentlyEditedItemInData =
      clonedPAvailabilityCorrectionConfigurationData.configuration.data.medium.find(
        (data: PAvailabilityCorrectionFactor) => data.name === name,
      );

    if (
      !currentlyEditedItemInData ||
      (validatedValue !== 0 && currentlyEditedItemInData.correctionPercent.value === validatedValue)
    ) {
      return;
    }

    currentlyEditedItemInData.correctionPercent.value = validatedValue;

    const otherItemInData =
      clonedPAvailabilityCorrectionConfigurationData.configuration.data.medium.find(
        (data) => data.name === otherName,
      );
    const otherInputValue = correctionPercentValues[otherName].toString();
    const sanitizedOtherInputValue = isNaN(Number(otherInputValue)) ? 0 : Number(otherInputValue);

    //set correct other input value in case previous value update request hasn't returned yet
    if (otherItemInData) {
      otherItemInData.correctionPercent.value = sanitizedOtherInputValue;
    }

    try {
      const result = await updatePAvailabilityCorrectionConfiguration({
        method: METHOD.PUT,
        body: JSON.stringify(clonedPAvailabilityCorrectionConfigurationData),
      });

      if (result) {
        updateOrganicFertiliserConfigurations(result);
      }
    } catch (error) {
      console.error('Failed to update Organic Fertiliser Configuration:', error);
    }
  };

  const headers = [t('table.col1'), t('table.col2')];

  return (
    <CollapsibleContainer>
      <CollapsibleSection
        defaultOpen={true}
        showCardContent={false}
        dataCY='p-availability-correction-collapsible'
        headerTitle={
          <CollapsibleSectionHeaderTitle
            tHeaderTitle={`${tPrefix}.headerTitle`}
            tHeaderSubtitle={`${tPrefix}.headerSubtitle`}
            dataCy='p-availability-correction'
          />
        }
      >
        <SubtitleContainer>
          <Subtitle data-cy='p-availability-correction-subtitle'>{t('table.title')}</Subtitle>
          <StyledTooltip
            data-cy='p-availability-correction-tooltip'
            concept='inverse'
            maxWidth={211}
            minWidth={211}
            position='right'
            text={t('table.tooltip')}
          >
            <AhuaIcon icon='Info' colorConcept='neutral' iconSize='x4' />
          </StyledTooltip>
        </SubtitleContainer>
        <TableContainer>
          <Table>
            <thead>
              <TableHeaderRow data-cy='p-availability-correction-table-header-row'>
                {headers.map((header, i) => (
                  <Table.Head key={header + i}>
                    <Caption>{header}</Caption>
                  </Table.Head>
                ))}
              </TableHeaderRow>
            </thead>
            <tbody>
              {pAvailabilityCorrectionConfigurationData &&
                pAvailabilityCorrectionConfigurationData.configuration.data.medium.map(
                  ({ id, name }: PAvailabilityCorrectionFactor, i: number) => {
                    const keyFromEnum = getKeyFromEnum(name);
                    if (!keyFromEnum) {
                      return;
                    }
                    return (
                      <TableBodyRow key={id + i} data-cy={`p-availability-correction-${name}-row`}>
                        <Table.Cell>
                          <Caption data-cy={`p-availability-correction-${name}-caption`}>
                            {name}
                          </Caption>
                        </Table.Cell>
                        <Table.Cell>
                          <Input
                            style={{ textAlign: 'left' }}
                            size='xs'
                            name={keyFromEnum}
                            id={id}
                            aria-label={name}
                            data-cy={`p-availability-correction-${name}-input`}
                            value={correctionPercentValues[keyFromEnum]}
                            onChange={({ target: { value, name } }) =>
                              handleChangeValues(value.replace(/\s/g, ''), name)
                            }
                            onBlur={({ target: { name } }) => handleUpdateValues(name)}
                          />
                        </Table.Cell>
                      </TableBodyRow>
                    );
                  },
                )}
            </tbody>
          </Table>

          {(!pAvailabilityCorrectionConfigurationData ||
            pAvailabilityCorrectionConfigurationData.configuration.data.medium.length === 0) && (
            <EmptyStateComponent
              dataCy='p-availability-correction-empty-state'
              styles={{ additionalStyles: EmptyStateAdditionalStyles }}
              message={t('emptyStateText')}
            />
          )}
        </TableContainer>
      </CollapsibleSection>
    </CollapsibleContainer>
  );
};

export default PAvailabilityCorrection;
