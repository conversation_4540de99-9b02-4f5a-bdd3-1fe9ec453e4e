// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`P availability correction match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-cdvoUX"
      >
        <div
          class="c-eNnZw collapsible-section"
          data-cy="p-availability-correction-collapsible"
          data-state="open"
        >
          <button
            aria-controls="radix-:r0:"
            aria-expanded="true"
            class="c-cUgXyc"
            data-state="open"
            type="button"
          >
            <div>
              <h1
                class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
                data-cy="p-availability-correction-header-title"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.pAvailabilityCorrection.headerTitle
              </h1>
              <h2
                class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-kvYgSl"
                data-cy="p-availability-correction-header-subtitle"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.pAvailabilityCorrection.headerSubtitle
              </h2>
            </div>
            <div
              class="c-irPLE"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 15.75l-5-6h-2l-5 6"
                />
              </svg>
            </div>
          </button>
          <div
            class="c-fLVWxk"
            data-state="open"
            id="radix-:r0:"
            style="transition-duration: 0s; animation-name: none;"
          >
            <div
              class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
            />
            <div
              class="c-fvcLlo"
            >
              <h2
                class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css"
                data-cy="p-availability-correction-subtitle"
              >
                table.title
              </h2>
              <svg
                class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                data-state="closed"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                />
              </svg>
            </div>
            <div
              class="c-knVzzd"
            >
              <table
                class="c-kwAGqj"
              >
                <thead>
                  <tr
                    class="c-eDGYZe c-FbBEV"
                    data-cy="p-availability-correction-table-header-row"
                  >
                    <th
                      class="c-kxWgPf"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      >
                        table.col1
                      </p>
                    </th>
                    <th
                      class="c-kxWgPf"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      >
                        table.col2
                      </p>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    class="c-eDGYZe c-iNbPbA"
                    data-cy="p-availability-correction-<Medium-row"
                  >
                    <td
                      class="c-doquzR"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                        data-cy="p-availability-correction-<Medium-caption"
                      >
                        &lt;Medium
                      </p>
                    </td>
                    <td
                      class="c-doquzR"
                    >
                      <div
                        class="c-gJoajD c-gJoajD-ifGHEql-css"
                      >
                        <input
                          aria-label="<Medium"
                          class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                          data-cy="p-availability-correction-<Medium-input"
                          id="3fa85f64-5717-4562-b3fc-2c963f66afa6"
                          name="<Medium"
                          style="text-align: left;"
                          value="0"
                        />
                        <span
                          class="c-fcBbhr"
                        />
                      </div>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe c-iNbPbA"
                    data-cy="p-availability-correction->=Medium-row"
                  >
                    <td
                      class="c-doquzR"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                        data-cy="p-availability-correction->=Medium-caption"
                      >
                        &gt;=Medium
                      </p>
                    </td>
                    <td
                      class="c-doquzR"
                    >
                      <div
                        class="c-gJoajD c-gJoajD-ifGHEql-css"
                      >
                        <input
                          aria-label=">=Medium"
                          class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                          data-cy="p-availability-correction->=Medium-input"
                          id="3fa85f64-5717-4562-b3fc-2c963f66afa6"
                          name=">=Medium"
                          style="text-align: left;"
                          value="2"
                        />
                        <span
                          class="c-fcBbhr"
                        />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-cdvoUX"
    >
      <div
        class="c-eNnZw collapsible-section"
        data-cy="p-availability-correction-collapsible"
        data-state="open"
      >
        <button
          aria-controls="radix-:r0:"
          aria-expanded="true"
          class="c-cUgXyc"
          data-state="open"
          type="button"
        >
          <div>
            <h1
              class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
              data-cy="p-availability-correction-header-title"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.pAvailabilityCorrection.headerTitle
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-kvYgSl"
              data-cy="p-availability-correction-header-subtitle"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.pAvailabilityCorrection.headerSubtitle
            </h2>
          </div>
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 15.75l-5-6h-2l-5 6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="open"
          id="radix-:r0:"
          style="transition-duration: 0s; animation-name: none;"
        >
          <div
            class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
          />
          <div
            class="c-fvcLlo"
          >
            <h2
              class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css"
              data-cy="p-availability-correction-subtitle"
            >
              table.title
            </h2>
            <svg
              class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
              data-state="closed"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
              />
            </svg>
          </div>
          <div
            class="c-knVzzd"
          >
            <table
              class="c-kwAGqj"
            >
              <thead>
                <tr
                  class="c-eDGYZe c-FbBEV"
                  data-cy="p-availability-correction-table-header-row"
                >
                  <th
                    class="c-kxWgPf"
                  >
                    <p
                      class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                    >
                      table.col1
                    </p>
                  </th>
                  <th
                    class="c-kxWgPf"
                  >
                    <p
                      class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                    >
                      table.col2
                    </p>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="c-eDGYZe c-iNbPbA"
                  data-cy="p-availability-correction-<Medium-row"
                >
                  <td
                    class="c-doquzR"
                  >
                    <p
                      class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      data-cy="p-availability-correction-<Medium-caption"
                    >
                      &lt;Medium
                    </p>
                  </td>
                  <td
                    class="c-doquzR"
                  >
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        aria-label="<Medium"
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                        data-cy="p-availability-correction-<Medium-input"
                        id="3fa85f64-5717-4562-b3fc-2c963f66afa6"
                        name="<Medium"
                        style="text-align: left;"
                        value="0"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe c-iNbPbA"
                  data-cy="p-availability-correction->=Medium-row"
                >
                  <td
                    class="c-doquzR"
                  >
                    <p
                      class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      data-cy="p-availability-correction->=Medium-caption"
                    >
                      &gt;=Medium
                    </p>
                  </td>
                  <td
                    class="c-doquzR"
                  >
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        aria-label=">=Medium"
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-eztJEB-cv c-exiTqG-bTMGXY-cv"
                        data-cy="p-availability-correction->=Medium-input"
                        id="3fa85f64-5717-4562-b3fc-2c963f66afa6"
                        name=">=Medium"
                        style="text-align: left;"
                        value="2"
                      />
                      <span
                        class="c-fcBbhr"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
