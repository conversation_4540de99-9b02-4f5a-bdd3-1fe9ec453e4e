import React from 'react';
import { BaseUnit, ProductRegion } from '@common/types';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { Caption } from '@yaradigitallabs/ahua-react';
import { Cell, Row, flexRender } from '@tanstack/react-table';
import './table.styles.scss';
import { TableCellStyled, TableRowStyled } from './ProductsAvailableTable.styled';
import { NOT_AVAILABLE } from '@common/constants';
import { tableNutrientNames } from '../constants';
import { isUnitIdKey } from '../utils/productsAvailableUtils';

export type TableBodyProps = {
  table: TanstackTable<ProductRegion>;
  getProductFamilyUri: (productFamilyId: string) => string | React.JSX.Element | null;
  units: BaseUnit[];
};

export function TableBody({ table, getProductFamilyUri, units }: TableBodyProps) {
  const getNutrientColumnValue = (cell: Cell<ProductRegion, unknown>, row: Row<ProductRegion>) => {
    const nutrientForm = units.find((unit) => {
      const key = `${cell.column.id}UnitId`;

      if (isUnitIdKey(key)) {
        const unitId = row.original[key];

        return unit.id === unitId;
      }
    });
    const value = `${cell.renderValue()} (${nutrientForm?.name ?? NOT_AVAILABLE})`;

    return value;
  };

  return (
    <>
      {table.getRowModel().rows.map((row) => {
        return (
          <TableRowStyled key={row.id}>
            {row.getVisibleCells().map((cell) => {
              const includedInTableNutrientNames =
                isUnitIdKey(cell.column.id) && tableNutrientNames.includes(cell.column.id);
              return (
                <TableCellStyled
                  key={`${row.id}-${cell.id}`}
                  className={`${
                    cell.column.id === 'productFamilyId' && 'products-available-pr-family-p'
                  }`}
                >
                  <Caption>
                    {flexRender(
                      cell.column.id === 'productFamilyId'
                        ? getProductFamilyUri(cell.getValue<string>())
                        : includedInTableNutrientNames
                        ? getNutrientColumnValue(cell, row)
                        : cell.column.columnDef.cell,
                      cell.getContext(),
                    )}
                  </Caption>
                </TableCellStyled>
              );
            })}
          </TableRowStyled>
        );
      })}
    </>
  );
}
