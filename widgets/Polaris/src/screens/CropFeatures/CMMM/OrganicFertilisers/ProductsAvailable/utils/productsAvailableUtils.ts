import { ProductRegion } from '@common/types';
import { ColumnDef } from '@tanstack/react-table';
import { TFunction } from 'i18next';

export interface TableConfigProps {
  t: TFunction;
  keyPrefix: string;
  productFamilyColCustomFilter: (productFamilyId: string, filterValue: string) => boolean;
}

export function getColumnDefs({
  t,
  keyPrefix,
  productFamilyColCustomFilter,
}: TableConfigProps): ColumnDef<ProductRegion>[] {
  return [
    {
      header: t(`col1`, { keyPrefix }),
      accessorKey: 'productFamilyId',
      enableColumnFilter: true,
      enableSorting: false,
      filterFn: (row, columnId, filterValue) =>
        productFamilyColCustomFilter(row.getValue(columnId), filterValue),
    },
    {
      header: t(`col2`, { keyPrefix }),
      accessorKey: 'name',
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      header: t(`col3`, { keyPrefix }),
      accessor<PERSON>ey: 'density',
      enableColumnFilter: false,
      enableSorting: false,
    },
    {
      header: t(`col4`, { keyPrefix }),
      accessorKey: 'n',
      enableColumnFilter: false,
      enableSorting: false,
    },
    {
      header: t(`col5`, { keyPrefix }),
      accessorKey: 'p',
      enableColumnFilter: false,
      enableSorting: false,
    },
    {
      header: t(`col6`, { keyPrefix }),
      accessorKey: 'k',
      enableColumnFilter: false,
      enableSorting: false,
    },
    {
      header: t(`col7`, { keyPrefix }),
      accessorKey: 's',
      enableColumnFilter: false,
      enableSorting: false,
    },
    {
      header: t(`col8`, { keyPrefix }),
      accessorKey: 'mg',
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];
}

export const isUnitIdKey = (value: string) =>
  value === 'nUnitId' ||
  value === 'pUnitId' ||
  value === 'kUnitId' ||
  value === 'mgUnitId' ||
  value === 'sUnitId' ||
  value === 'caUnitId' ||
  value === 'bUnitId' ||
  value === 'znUnitId' ||
  value === 'mnUnitId' ||
  value === 'cuUnitId' ||
  value === 'feUnitId' ||
  value === 'moUnitId' ||
  value === 'naUnitId' ||
  value === 'seUnitId' ||
  value === 'coUnitId';
