import { Subtitle, styled } from '@yaradigitallabs/ahua-react';
import { CSSProperties } from 'react';

const EMPTY_STATE_HEIGHT = 272;

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: EMPTY_STATE_HEIGHT,
};

export const ImagePreview = styled('img', {
  width: '89px',
  height: '24px',
});

export const NoteSubtitle = styled(Subtitle, {
  lineHeight: 'var(--lineHeights-scale6)',
  padding: '$x6 $x4',
});
