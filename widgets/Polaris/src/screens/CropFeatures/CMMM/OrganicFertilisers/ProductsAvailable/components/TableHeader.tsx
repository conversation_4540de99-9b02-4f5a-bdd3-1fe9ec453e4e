import React, { Fragment } from 'react';
import { ProductRegion } from '@common/types';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { Label, Table } from '@yaradigitallabs/ahua-react';
import {
  NutrientContentBox,
  TableSortHeadStyled,
  TableCellStyled,
  TableRowStyled,
  PreHeaderRowStick,
} from './ProductsAvailableTable.styled';
import { flexRender } from '@tanstack/react-table';
import { Searchbar } from '@widgets/Polaris/src/components/Searchbar/Searchbar';
import { useTranslation } from 'react-i18next';

export type TableHeaderProps = {
  table: TanstackTable<ProductRegion>;
  resetSearch: boolean;
  autoResetPageIndexRef: React.MutableRefObject<boolean>;
  tPrefix: string;
};

export function TableHeader({
  table,
  resetSearch,
  autoResetPageIndexRef,
  tPrefix,
}: TableHeaderProps): JSX.Element {
  const { t } = useTranslation();

  return (
    <>
      {table.getHeaderGroups().map((headerGroup, index) => {
        const key = headerGroup.headers[index].id;
        return (
          <Fragment key={key}>
            <PreHeaderRowStick>
              <Table.Cell colSpan={3} key={`blank`}>
                {null}
              </Table.Cell>
              <NutrientContentBox colSpan={headerGroup.headers.length - 3} key={`nutrient-content`}>
                <Label size={'s'}>{t(`${tPrefix}.nutrientContentCell`)}</Label>
              </NutrientContentBox>
            </PreHeaderRowStick>

            <TableRowStyled>
              {headerGroup.headers.map((header) => {
                return (
                  <TableSortHeadStyled
                    direction={header.column.getIsSorted()}
                    active={Boolean(header.column.getIsSorted())}
                    onClick={header.column.getToggleSortingHandler()}
                    key={`${header.id}-${header.column.columnDef.header}`}
                    color={'primary'}
                    colSpan={header.colSpan}
                  >
                    <Label size={'n'}>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </Label>
                  </TableSortHeadStyled>
                );
              })}
            </TableRowStyled>

            <TableRowStyled>
              {headerGroup.headers.map((header) => {
                return (
                  <TableCellStyled key={`${header.id}-${headerGroup.id}`}>
                    {header.column.getCanFilter() ? (
                      <Searchbar
                        column={header.column}
                        resetSearch={resetSearch}
                        autoResetPageIndexRef={autoResetPageIndexRef}
                      />
                    ) : null}
                  </TableCellStyled>
                );
              })}
            </TableRowStyled>
          </Fragment>
        );
      })}
    </>
  );
}
