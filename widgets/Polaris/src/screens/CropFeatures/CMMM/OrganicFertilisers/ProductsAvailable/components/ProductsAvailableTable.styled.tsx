import { styled, Table } from '@yaradigitallabs/ahua-react';

const sharedStickyStyles = {
  [`& th, & td`]: {
    position: 'sticky',
  },
  [`& th:nth-child(1), & td:nth-child(1)`]: {
    left: 0,
    zIndex: 1,
  },
  [`& th:nth-child(2), & td:nth-child(2)`]: {
    left: '212px',
    zIndex: 1,
  },
  [`& th:nth-child(3), & td:nth-child(3)`]: {
    left: '424px',
    zIndex: 1,
  },
  // Removing left border from the nutrient headers
  [`& th:nth-child(n+4)::before`]: {
    display: 'none',
  },
};

// Table
export const TableWrapperStyled = styled('div', {
  // Remove pagination absolute position
  [`& div`]: {
    position: 'relative',
  },

  // Columns sticky styles
  [`& table`]: {
    overflow: 'auto',
    display: 'block',
  },

  // Table border
  [`& table > thead > tr:first-child`]: {
    borderTop: '1px solid var(--colors-gray10) !important',
    borderBottom: '0px !important',
  },
  [`& table > thead > tr:last-child`]: {
    borderTop: '2px solid var(--colors-gray10) !important',
    borderBottom: '1px solid var(--colors-gray10) !important',
  },
  [`& table > tbody > tr:last-child`]: {
    borderBottom: '1px solid var(--colors-gray10) !important',
  },
  [`& table > thead > tr > *:nth-child(n+4):not(:last-child), & table > tbody > tr > *:nth-child(n+4):not(:last-child)`]:
    {
      borderLeft: '1px solid var(--colors-gray10) !important',
      borderRight: '1px solid var(--colors-gray10) !important',
    },

  // Rows colors
  [`& table > thead > tr > *, & table > tbody > tr:nth-child(odd) > *`]: {
    backgroundColor: 'var(--colors-white100)',
  },
  [`& table > tbody > tr:nth-child(even) > *`]: {
    backgroundColor: 'var(--colors-gray0)',
  },

  // Make 2nd header on first row and header 4+ or cell 4+ on
  // other rows to expand with the page width
  [`& table > thead > tr:first-child > td:nth-child(2),
    & table > thead > tr:nth-child(2) > th:nth-child(n+4),
    & table > thead > tr:nth-child(3) > td:nth-child(n+4),
    & table > tbody > tr > td:nth-child(n+4)`]: {
    width: '20%',
  },
});

// Pre header row
export const PreHeaderRowStick = styled(Table.Row, {
  [`& tr`]: {
    position: 'sticky',
    zIndex: 1,
    left: 0,
  },
  [`& td:nth-child(1)`]: {
    position: 'sticky',
    zIndex: 1,
    left: 0,
  },
});

// Table headers
export const TableSortHeadStyled = styled(Table.SortHead, {
  ...sharedStickyStyles,
  svg: {
    color: '$brand-contrast',
    stroke: '$brand-contrast',
  },
});

// Table rows and cells
export const NutrientContentBox = styled(Table.Cell, {
  backgroundColor: 'var(--colors-brand-lightest) !important',
  padding: '$x2 0 $x2 $x3',
});

export const TableRowStyled = styled(Table.Row, {
  ...sharedStickyStyles,
  [`& tr`]: {
    position: 'sticky',
    top: 0,
  },
  [`& th:nth-child(-n+2)`]: {
    minWidth: '200px',
    width: '200px',
  },
  [`& th:nth-child(3)`]: {
    minWidth: '74px',
    width: '74px',
  },
  [`& th:nth-child(n+4)`]: {
    minWidth: 'var(--space-x29)',
    width: 'var(--space-x29)',
  },
});

export const TableCellStyled = styled(Table.Cell, {
  ...sharedStickyStyles,
  padding: '7px $x3',
  variants: {
    hasEllipsis: {
      true: {
        maxWidth: '98px',
        ['& p']: {
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      },
    },
  },
});
