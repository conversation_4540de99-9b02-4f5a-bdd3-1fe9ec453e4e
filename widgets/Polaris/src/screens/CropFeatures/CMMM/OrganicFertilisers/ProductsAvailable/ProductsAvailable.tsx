import React, { FC, useMemo } from 'react';
import CollapsibleSection from '@widgets/Polaris/src/components/Collapsible/Collapsible';
import CollapsibleSectionHeaderTitle from '@widgets/Polaris/src/components/Collapsible/components/CollapsibleSectionHeaderTitle/CollapsibleSectionHeaderTitle';
import { useTranslation } from 'react-i18next';
import { useFetchOrganicProductRegions, useFetchProductFamilyDetails } from '@polaris-hooks/index';
import { Country, Feature } from '@common/types';
import ProductsAvailableTable from './components/ProductsAvailableTable';
import { NoteSubtitle } from './ProductsAvailable.styled';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import { PRODUCTS_TYPE } from '@common/constants';

interface ProductsAvailableProps {
  selectedCountry: Country | null;
  selectedFeature: Feature | null;
}
const tPrefix = 'polaris.cmmmDetails.planConfiguration.organicFertilisers.productsAvailable';

const ProductsAvailable: FC<ProductsAvailableProps> = ({ selectedCountry, selectedFeature }) => {
  const { t } = useTranslation();

  const organicProductsFilter: GenericFilter[] | undefined = useMemo(() => {
    if (!selectedCountry?.id || !selectedFeature?.id) return;
    return [
      {
        key: 'countryId',
        value: selectedCountry.id,
        type: FilterType.EQ,
      },
      {
        key: 'productTypeId',
        value: `${PRODUCTS_TYPE.ORGANIC_LIQUID},${PRODUCTS_TYPE.ORGANIC_SOLID}`,
        type: FilterType.IN,
      },
      {
        key: 'tagsConfiguration.featureTags.id',
        value: selectedFeature.id,
        type: FilterType.EQ,
      },
    ];
  }, [selectedCountry, selectedFeature]);

  const organicProductsAvailable =
    organicProductsFilter && useFetchOrganicProductRegions(organicProductsFilter);
  const organicProductsAvailableFamily = useFetchProductFamilyDetails(organicProductsAvailable);

  return (
    <CollapsibleSection
      dataCY={'products-available-collapsible-section-component'}
      defaultOpen={true}
      showCardContent={false}
      headerTitle={
        <CollapsibleSectionHeaderTitle
          tHeaderTitle={`${tPrefix}.headerTitle`}
          tHeaderSubtitle={`${tPrefix}.headerSubtitle`}
          dataCy='products-available'
        />
      }
    >
      <NoteSubtitle size='s' data-cy={'products-available-section-note'}>
        {t(`${tPrefix}.noteText`)}
      </NoteSubtitle>

      <ProductsAvailableTable
        tPrefix={`${tPrefix}.table`}
        productsList={organicProductsAvailable}
        productFamilies={organicProductsAvailableFamily}
      />
    </CollapsibleSection>
  );
};

export default ProductsAvailable;
