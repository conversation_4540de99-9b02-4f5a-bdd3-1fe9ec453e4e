import React, { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTableConfig } from '@polaris-hooks/index';
import { ProductFamily, ProductRegion } from '@common/types';
import TableSection from '@widgets/Polaris/src/components/Table/Table';
import { getColumnDefs } from '@widgets/Polaris/src/screens/CropFeatures/CMMM/OrganicFertilisers/ProductsAvailable/utils/productsAvailableUtils';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';
import { TableHeader, TableBody } from '.';
import { TableWrapperStyled } from './ProductsAvailableTable.styled';
import {
  EmptyStateAdditionalStyles,
  ImagePreview,
} from '@widgets/Polaris/src/screens/CropFeatures/CMMM/OrganicFertilisers/ProductsAvailable/ProductsAvailable.styled';
import { useAppContext } from '@widgets/Polaris/src/providers';

interface ProductsAvailableTableProps {
  tPrefix: string;
  productsList: ProductRegion[] | undefined;
  productFamilies: ProductFamily[];
}

const ProductsAvailableTable: FC<ProductsAvailableTableProps> = ({
  tPrefix,
  productsList,
  productFamilies,
}) => {
  const { selectedCountryUnits } = useAppContext();
  const { t } = useTranslation();

  const [resetSearch, setResetSearch] = useState<boolean>(false);

  const productFamilyColCustomFilter = (productFamilyId: string, filterValue: string) => {
    const productFamily = productFamilies.find((x) => x.id == productFamilyId);
    return productFamily?.name.toLowerCase().includes(filterValue.toLowerCase()) ?? false;
  };

  const { table, paginationState, autoResetPageIndexRef } = useTableConfig<ProductRegion>(
    productsList || [],
    () => getColumnDefs({ t, keyPrefix: tPrefix, productFamilyColCustomFilter }),
    { pageIndex: 0, pageSize: 5 },
  );

  const getProductFamilyUri = (productFamilyId: string) => {
    const productFamily = productFamilies.find((x) => x.id == productFamilyId);

    if (productFamily) {
      const uri =
        productFamily.mediaUri && productFamily.mediaUri.find((item) => item.key == 'DEFAULT');

      return uri ? <ImagePreview src={uri.value} title={productFamily.name} /> : productFamily.name;
    }

    return null;
  };

  return (
    <TableWrapperStyled>
      <TableSection
        showPagination={table.getRowCount() ? table.getRowCount() > 5 : false}
        tableHeader={
          <TableHeader
            table={table}
            resetSearch={resetSearch}
            autoResetPageIndexRef={autoResetPageIndexRef}
            tPrefix={tPrefix}
          />
        }
        tableBody={
          <TableBody
            table={table}
            getProductFamilyUri={getProductFamilyUri}
            units={selectedCountryUnits || []}
          />
        }
        pageCount={table.getPageCount()}
        pageIndex={paginationState.pageIndex}
        canNextPage={table.getCanNextPage()}
        canPreviousPage={table.getCanPreviousPage()}
        onNextPage={() => table.nextPage()}
        onPrevPage={() => table.previousPage()}
        onLastPage={() => table.lastPage()}
        onFirstPage={() => table.firstPage()}
        variant={'dynamic'}
      />

      {table.getFilteredRowModel().rows.length === 0 && (
        <EmptyStateComponent
          dataCy='product-available-empty-search-state'
          styles={{ additionalStyles: EmptyStateAdditionalStyles }}
          message={t(`polaris.common.emptySearchState.text`)}
          searchTerm={` ${table
            .getState()
            .columnFilters.map((filter) => filter.value)
            .join(' ')}`}
          actionText={t(`polaris.common.emptySearchState.button`)}
          onActionClick={() => {
            setResetSearch(!resetSearch);
            table.resetColumnFilters(true);
          }}
        />
      )}
    </TableWrapperStyled>
  );
};

export default ProductsAvailableTable;
