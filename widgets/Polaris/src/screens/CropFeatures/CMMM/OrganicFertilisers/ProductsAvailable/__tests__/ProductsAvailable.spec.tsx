import React from 'react';
import { act, render, screen, within } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import {
  productRegionHandler,
  productFamiliesHandler,
  featureResponse,
  mockAppProviderValue,
} from '@common/mocks';
import * as useProductRegion from '@polaris-hooks/product/useProductRegion/useProductRegion';
import {
  countryResponse,
  useProductsByRegionEmptyResponse,
  useProductsByRegionMultipleResponse,
  useProductsByRegionResponse,
} from '../../../../../Home/mock-data/MockData';
import ProductsAvailable from '../ProductsAvailable';

const server = setupServer(productRegionHandler, productFamiliesHandler);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('Products Available', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('render table when there are products available', async () => {
    jest
      .spyOn(useProductRegion, 'useFetchOrganicProductRegions')
      .mockImplementation(() => useProductsByRegionResponse);

    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>
                {() => (
                  <ProductsAvailable
                    data-cy={'products-available-section-component'}
                    selectedCountry={countryResponse[0]}
                    selectedFeature={featureResponse[0]}
                  />
                )}
              </AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(screen.getByTestId('table-section')).toBeInTheDocument();
    expect(screen.queryByTestId('product-available-empty-search-state')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Go to next page')).not.toBeInTheDocument();
  });

  it('render empty table state when there are no products available', async () => {
    jest
      .spyOn(useProductRegion, 'useFetchOrganicProductRegions')
      .mockImplementation(() => useProductsByRegionEmptyResponse);

    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>
                {() => (
                  <ProductsAvailable
                    data-cy={'products-available-section-component'}
                    selectedCountry={countryResponse[0]}
                    selectedFeature={featureResponse[0]}
                  />
                )}
              </AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const emptyStateComp = screen.getByTestId('product-available-empty-search-state');
    expect(emptyStateComp).toBeInTheDocument();

    const emptyStateIcon = within(emptyStateComp).getAllByTestId('empty-state-info-icon');
    expect(emptyStateIcon[0]).toBeInTheDocument();

    const actBtn = within(emptyStateComp).getByTestId(
      'product-available-empty-search-state-action-button',
    );
    expect(actBtn).toBeInTheDocument();

    expect(screen.queryByLabelText('Go to next page')).not.toBeInTheDocument();
  });

  it('render table pagination when there are more then 5 products available', async () => {
    jest
      .spyOn(useProductRegion, 'useFetchOrganicProductRegions')
      .mockImplementation(() => useProductsByRegionMultipleResponse);

    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>
                {() => (
                  <ProductsAvailable
                    data-cy={'products-available-section-component'}
                    selectedCountry={countryResponse[0]}
                    selectedFeature={featureResponse[0]}
                  />
                )}
              </AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(screen.getByLabelText('Go to next page')).toBeInTheDocument();
    expect(screen.getByLabelText('Go to previous page')).toBeInTheDocument();
    expect(screen.getByLabelText('Go to first page')).toBeInTheDocument();
    expect(screen.getByLabelText('Go to last page')).toBeInTheDocument();
  });
});
