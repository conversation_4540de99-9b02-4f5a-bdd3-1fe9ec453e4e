import React from 'react';
import { cloneDeep } from 'lodash';
import { setupServer } from 'msw/node';
import {
  updateCerealOrganicFertiliserNAvailabilityCorrectionConfiugrationHandler,
  getSoilTypes<PERSON>and<PERSON>,
  getSpreader<PERSON>eth<PERSON><PERSON><PERSON><PERSON>,
  mockAppProviderValue,
} from '@common/mocks';
import { NavbarProvider } from '@libs/nav-context';
import { act, fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { MAX_LIMIT_NUMBER } from '../../../../shared/constants';
import NAvailabilityCorrection from '../NAvailabilityCorrection';
import { OrganicFertiliserConfigurationNames } from '@common/constants';
import * as useOrganicFertiliser from '@polaris-hooks/masterMindMap/useOrganicFertiliser/useOrganicFertiliser';
import { NAvailabilityCorrectionKeys } from '@common/types';

const server = setupServer(
  getSoilTypesHandler,
  getSpreaderMethodsHandler,
  updateCerealOrganicFertiliserNAvailabilityCorrectionConfiugrationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('N availability correction', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('match snapshot', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <NAvailabilityCorrection />
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });

  it('render a table with 3 rows and 8 inputs when there is a valid n availability correction configuration', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(screen.getByTestId('n-availability-correction-collapsible')).toBeInTheDocument();
    expect(screen.getByTestId('n-availability-correction-header-title')).toBeInTheDocument();

    const nAvailabilityCorrectionSubtitle = screen.getByTestId(
      'n-availability-correction-subtitle',
    );
    expect(nAvailabilityCorrectionSubtitle).toBeInTheDocument();
    expect(nAvailabilityCorrectionSubtitle).toHaveTextContent('table.title');

    const headerRow = screen.getByTestId('n-availability-correction-table-header-row');
    expect(headerRow).toBeInTheDocument();

    const numberOfRows = screen.getAllByTestId(/n-availability-correction-\w+-row/);
    const numberOfFirstCells = screen.getAllByTestId(/n-availability-correction-\w+-first-cell/);
    const numberOfSecondCells = screen.getAllByTestId(/n-availability-correction-\w+-second-cell/);
    const numberOfInputs = screen.getAllByTestId(/n-availability-correction-\w+-\d+-input/);
    expect(numberOfRows.length).toBe(3);
    expect(numberOfFirstCells.length).toBe(3);
    expect(numberOfSecondCells.length).toBe(3);
    expect(numberOfInputs.length).toBe(8);
  });

  it('render 3 inputs for soil type in the 3rd column, even if there is additional invalid data', async () => {
    await act(async () => {
      const clonedProviderValue = cloneDeep(mockAppProviderValue);
      const newNAvailabilityCorrectionConfiguration =
        clonedProviderValue.organicFertiliserConfigurations.find(
          (configuration) =>
            configuration.name === OrganicFertiliserConfigurationNames.NAvailabilityCorrection,
        );
      if (newNAvailabilityCorrectionConfiguration) {
        newNAvailabilityCorrectionConfiguration.configuration.data[
          NAvailabilityCorrectionKeys.SoilCorrectionFactors
        ] = [
          ...newNAvailabilityCorrectionConfiguration.configuration.data[
            NAvailabilityCorrectionKeys.SoilCorrectionFactors
          ],
          {
            id: 'test',
            ordinal: 3,
            soilTypeIds: [],
            correctionPercent: {
              value: 10,
              canEdit: true,
              isApplicable: true,
            },
          },
        ];
      }

      render(
        <AppContext.Provider value={clonedProviderValue}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const numberOfRows = screen.getAllByTestId(/n-availability-correction-\w+-row/);
    const numberOfInputs = screen.getAllByTestId(/n-availability-correction-\w+-\d+-input/);

    expect(numberOfRows.length).toBe(3);
    expect(numberOfInputs.length).toBe(8);
  });

  it('invalid value', async () => {
    await act(async () => {
      const clonedProviderValueWithInvalidData = cloneDeep(mockAppProviderValue);
      const newNAvailabilityCorrectionConfiguration =
        clonedProviderValueWithInvalidData.organicFertiliserConfigurations.find(
          (configuration) =>
            configuration.name === OrganicFertiliserConfigurationNames.NAvailabilityCorrection,
        );
      if (newNAvailabilityCorrectionConfiguration) {
        newNAvailabilityCorrectionConfiguration.configuration.data[
          NAvailabilityCorrectionKeys.SoilCorrectionFactors
        ] = [
          {
            id: '76c76c41-6431-4718-b69c-fbf97fff0f79',
            ordinal: 1,
            soilTypeIds: [
              '723fdf91-b86f-4bd1-9662-4d2aafd5c440',
              'f914cfe4-cd91-4b05-8c39-d071a1330bf3',
              '39751711-0c1e-4223-b81e-e5250f970310',
              '7afe2f77-46e1-45d7-9c2b-5fe7c1f16a01',
            ],
            correctionPercent: {
              value: 'abc',
              canEdit: true,
              isApplicable: true,
            },
          },
        ];
      }
      render(
        <AppContext.Provider value={clonedProviderValueWithInvalidData}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const soilCorrectionFirstInput = screen.getByTestId(
      'n-availability-correction-soilCorrectionFactors-0-input',
    );

    expect(soilCorrectionFirstInput).toBeInTheDocument();
    expect(soilCorrectionFirstInput).toHaveValue('abc');
    fireEvent.change(soilCorrectionFirstInput, { target: { value: 'bbb' } });
    fireEvent.blur(soilCorrectionFirstInput);
    expect(soilCorrectionFirstInput).toHaveValue('0');
  });

  it('updates to input are correct and reflected', async () => {
    const promise = Promise.resolve();
    mockAppProviderValue.methods.updateOrganicFertiliserConfigurations = jest.fn(() => promise);
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const techniqueSplashPlateInput = screen.getByTestId(
      'n-availability-correction-techniqueCorrectionFactors-0-input',
    );

    expect(techniqueSplashPlateInput).toBeInTheDocument();
    expect(techniqueSplashPlateInput).toHaveValue('-5');
    fireEvent.change(techniqueSplashPlateInput, { target: { value: '9' } });
    fireEvent.blur(techniqueSplashPlateInput);
    expect(techniqueSplashPlateInput).toHaveValue('9');

    // Removes warning about not using act
    await act(() => promise);
  });

  it('updates to input are invalid type', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const techniqueSplashPlateInput = screen.getByTestId(
      'n-availability-correction-techniqueCorrectionFactors-0-input',
    );

    expect(techniqueSplashPlateInput).toBeInTheDocument();
    expect(techniqueSplashPlateInput).toHaveValue('-5');
    fireEvent.change(techniqueSplashPlateInput, { target: { value: 'asd' } });
    fireEvent.blur(techniqueSplashPlateInput);
    expect(techniqueSplashPlateInput).toHaveValue('-5');
  });

  it('updates to input exceed max or min limit', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const numberExceedingMaxLimit = MAX_LIMIT_NUMBER + 1;
    const techniqueSplashPlateInput = screen.getByTestId(
      'n-availability-correction-techniqueCorrectionFactors-0-input',
    );

    expect(techniqueSplashPlateInput).toBeInTheDocument();
    expect(techniqueSplashPlateInput).toHaveValue('-5');
    fireEvent.change(techniqueSplashPlateInput, {
      target: { value: numberExceedingMaxLimit },
    });
    fireEvent.blur(techniqueSplashPlateInput);
    expect(techniqueSplashPlateInput).toHaveValue('-5');
  });

  it('render empty table state when there are no configurations', async () => {
    const mockAppProviderValueWithoutConfigurations = {
      ...mockAppProviderValue,
      organicFertiliserConfigurations: [],
    };
    render(
      <AppContext.Provider value={mockAppProviderValueWithoutConfigurations}>
        <NavbarProvider>
          <NAvailabilityCorrection />
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const emptyStateComp = screen.getByTestId('n-availability-correction-empty-state');
    expect(emptyStateComp).toBeInTheDocument();

    const emptyStateIcon = within(emptyStateComp).getAllByTestId('empty-state-info-icon');
    expect(emptyStateIcon[0]).toBeInTheDocument();
  });

  it('update returns an error', async () => {
    const callbackError = jest.fn();
    console.error = callbackError;

    await act(async () => {
      jest
        .spyOn(useOrganicFertiliser, 'useUpdateOrganicFertiliserConfiguration')
        .mockImplementation(() => ({
          data: undefined,
          trigger: () => {
            throw Error();
          },
          error: undefined,
          isMutating: false,
        }));
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <NAvailabilityCorrection />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const techniqueSplashPlateInput = screen.getByTestId(
      'n-availability-correction-techniqueCorrectionFactors-0-input',
    );

    expect(techniqueSplashPlateInput).toBeInTheDocument();
    expect(techniqueSplashPlateInput).toHaveValue('-5');
    fireEvent.change(techniqueSplashPlateInput, { target: { value: '1' } });
    fireEvent.blur(techniqueSplashPlateInput);

    await waitFor(() => {
      expect(callbackError).toHaveBeenCalled();
    });
  });
});
