import { TFunction } from 'react-i18next';
import {
  UpdateNAvailabilityCorrectionsActions,
  NAvailabilityCorrectionKeys,
  OrganicFertiliserNAvailabilityCorrectionsConfiguration,
  NAvailabilityCorrectionsState,
  SpreaderMethod,
  SoilType,
  NAvailabilityCorrectionFactors,
  NAvailabilityCorrectionActions,
} from '@common/types';
import {
  formatNegativeOrPositiveDecimalsNumber,
  validateParameterNumber,
} from '@widgets/Polaris/utils';
import { THREE_DECIMAL_PLACES_LIMIT } from '@common/constants';
import { cloneDeep } from 'lodash';
import { Dispatch } from 'react';

export const getKeyFromEnum = (value: string): NAvailabilityCorrectionKeys | undefined => {
  switch (value) {
    case NAvailabilityCorrectionKeys.TechniqueCorrectionFactors:
      return NAvailabilityCorrectionKeys.TechniqueCorrectionFactors;
    case NAvailabilityCorrectionKeys.SoilCorrectionFactors:
      return NAvailabilityCorrectionKeys.SoilCorrectionFactors;
    case NAvailabilityCorrectionKeys.DryMatterContentCorrectionFactors:
      return NAvailabilityCorrectionKeys.DryMatterContentCorrectionFactors;
    default:
      break;
  }
};

export const getTranslatedCell = (value: string, tPrefix: string, t: TFunction) => {
  switch (value) {
    case NAvailabilityCorrectionKeys.TechniqueCorrectionFactors:
      return t(`${tPrefix}.table.techniqueCell`);
    case NAvailabilityCorrectionKeys.SoilCorrectionFactors:
      return t(`${tPrefix}.table.soilCell`);
    case NAvailabilityCorrectionKeys.DryMatterContentCorrectionFactors:
      return t(`${tPrefix}.table.dryMatterCell`);
    default:
      break;
  }
};

export const getActionTypeFromEnum = (
  value: string,
): UpdateNAvailabilityCorrectionsActions | undefined => {
  switch (value) {
    case NAvailabilityCorrectionKeys.TechniqueCorrectionFactors:
      return UpdateNAvailabilityCorrectionsActions.UpdateTechnique;
    case NAvailabilityCorrectionKeys.SoilCorrectionFactors:
      return UpdateNAvailabilityCorrectionsActions.UpdateSoil;
    case NAvailabilityCorrectionKeys.DryMatterContentCorrectionFactors:
      return UpdateNAvailabilityCorrectionsActions.UpdateDryMatter;
    default:
      break;
  }
};
export const orderedKeys = [
  NAvailabilityCorrectionKeys.TechniqueCorrectionFactors,
  NAvailabilityCorrectionKeys.SoilCorrectionFactors,
  NAvailabilityCorrectionKeys.DryMatterContentCorrectionFactors,
];

export const nAvailabilityCorrectionsInitialState: NAvailabilityCorrectionsState = {
  soilCorrectionFactors: {},
  techniqueCorrectionFactors: {},
  dryMatterContentCorrectionFactors: {},
};

export const populateState = (
  nAvailabilityCorrectionsConfigurationData: OrganicFertiliserNAvailabilityCorrectionsConfiguration,
  dispatch: Dispatch<NAvailabilityCorrectionActions>,
) => {
  const { soilCorrectionFactors, techniqueCorrectionFactors, dryMatterContentCorrectionFactors } =
    nAvailabilityCorrectionsConfigurationData.configuration.data;

  for (const soilCorrectionFactor of soilCorrectionFactors) {
    dispatch({
      type: UpdateNAvailabilityCorrectionsActions.UpdateSoil,
      payload: {
        id: soilCorrectionFactor.id,
        value: String(soilCorrectionFactor.correctionPercent.value),
      },
    });
  }
  for (const techniqueCorrectionFactor of techniqueCorrectionFactors) {
    dispatch({
      type: UpdateNAvailabilityCorrectionsActions.UpdateTechnique,
      payload: {
        id: techniqueCorrectionFactor.id,
        value: String(techniqueCorrectionFactor.correctionPercent.value),
      },
    });
  }
  for (const dryMatterContentCorrectionFactor of dryMatterContentCorrectionFactors) {
    dispatch({
      type: UpdateNAvailabilityCorrectionsActions.UpdateDryMatter,
      payload: {
        id: dryMatterContentCorrectionFactor.id,
        value: String(dryMatterContentCorrectionFactor.correctionPercent.value),
      },
    });
  }
};

export const getValidatedStateValues = (
  state: NAvailabilityCorrectionsState,
): NAvailabilityCorrectionsState => {
  const validatedState: NAvailabilityCorrectionsState = {
    soilCorrectionFactors: {},
    techniqueCorrectionFactors: {},
    dryMatterContentCorrectionFactors: {},
  };

  for (const key of orderedKeys) {
    for (const idKey in state[key]) {
      const value = state[key][idKey];
      const sanitizedValue = isNaN(Number(value)) ? '0' : value;
      const isValid = validateParameterNumber(sanitizedValue);
      if (!isValid) {
        return validatedState;
      }
      const validatedValue = formatNegativeOrPositiveDecimalsNumber(
        sanitizedValue,
        THREE_DECIMAL_PLACES_LIMIT,
      );

      if (Number(validatedValue) !== 0 && validatedState[key][idKey] === validatedValue) {
        return validatedState;
      }
      validatedState[key][idKey] = validatedValue;
    }
  }
  return validatedState;
};

export const updateConfigurationWithValidatedStateValues = (
  nAvailabilityCorrectionConfigurationData: OrganicFertiliserNAvailabilityCorrectionsConfiguration,
  state: NAvailabilityCorrectionsState,
) => {
  const validatedValues = getValidatedStateValues(cloneDeep(state));
  for (const key in nAvailabilityCorrectionConfigurationData.configuration.data) {
    const typedKey = getKeyFromEnum(key);
    if (!typedKey) {
      return;
    }

    for (const factor of nAvailabilityCorrectionConfigurationData.configuration.data[typedKey]) {
      factor.correctionPercent.value = Number(validatedValues[typedKey][factor.id]);
    }
  }
  return nAvailabilityCorrectionConfigurationData;
};

export const dryMatterContentCorrectionFactorNames = ['<3', '3> and <=7', '>=7'];

export const validateFactor = (
  factor: NAvailabilityCorrectionFactors,
  soilTypes: SoilType[],
  spreaderMethods: SpreaderMethod[],
) => {
  if ('soilTypeIds' in factor) {
    return factor.soilTypeIds.some((soilTypeId) =>
      soilTypes.some((soilType) => soilType.id === soilTypeId),
    );
  } else if ('spreaderMethodId' in factor) {
    return spreaderMethods.some((spreaderMethod) => spreaderMethod.id === factor.spreaderMethodId);
  } else if ('name' in factor) {
    return dryMatterContentCorrectionFactorNames.includes(factor.name);
  }
};
