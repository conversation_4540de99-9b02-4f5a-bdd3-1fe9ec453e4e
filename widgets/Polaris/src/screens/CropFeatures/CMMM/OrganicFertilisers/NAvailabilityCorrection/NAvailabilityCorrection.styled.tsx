import { styled, Subtitle, Table, Tooltip } from '@yaradigitallabs/ahua-react';
import { CSSProperties } from 'react';

export const CollapsibleContainer = styled('div', {
  paddingTop: '$x6',
});

export const TableContainer = styled('div', {
  padding: '0 $x4 $x6 $x4',
  justifyItems: 'center',

  '& table': {
    width: '100%',
  },

  '& tr': {
    backgroundColor: 'unset !important',
  },
});

export const StyledSubtitle = styled(Subtitle, {
  fontSize: '$scale3 !important',
  padding: '$x6 0 $x2 $x4',
});

export const StyledTooltip = styled(Tooltip, {
  variants: {
    variant: {
      hidden: {
        display: 'none',
      },
      default: {},
    },
  },
});

export const TableHeaderRow = styled(Table.Row, {
  borderTop: '2px solid $black10',

  '& th': {
    padding: '0 !important',
    verticalAlign: 'middle',
  },

  '& th > p': {
    padding: '19px 9px 19px $x3 !important',
    height: '18px',
    fontWeight: '$semiBold',
    textBox: 'auto',
  },

  '& th:first-child': {
    width: '190px',

    '& p': {
      width: '169px',
    },
  },

  '& th:nth-child(3)': {
    width: '310px',
  },
});

export const TableBodyRow = styled(Table.Row, {
  '& td': {
    padding: 'unset',
  },

  '& p': {
    paddingLeft: '$x3',
  },

  '& > td:first-child': {
    borderRight: '1px solid $black15',
  },

  '& > td:nth-child(2)': {
    display: 'flex',
    flexDirection: 'column',

    '& > div': {
      height: '55px',
      alignContent: 'center',

      '& > p': {
        // truncate if break to line 4 happens, still supported
        display: '-webkit-box',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': 3,
        overflow: 'hidden',

        textBox: 'auto',
        lineHeight: '$scale4',
      },
    },

    '& > div:not(:last-child)': {
      borderBottom: '1px solid $black15',
    },
  },

  '& td:nth-child(3)': {
    '& > div': {
      height: '55px',
      display: 'flex',

      '& > div': {
        width: '282px',
        padding: '$x2 $x4 $x2 $x3',
      },
    },

    '& > div:not(:last-child)': {
      borderBottom: '1px solid $black15',
    },
  },
});

const EMPTY_STATE_HEIGHT = 169;

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: EMPTY_STATE_HEIGHT,
};
