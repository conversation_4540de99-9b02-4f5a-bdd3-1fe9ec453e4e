import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Caption, Input, Table } from '@yaradigitallabs/ahua-react';
import {
  OrganicFertiliserNAvailabilityCorrectionsConfiguration,
  NAvailabilityCorrectionKeys,
  SpreaderMethod,
  SoilType,
  NAvailabilityCorrectionsState,
} from '@common/types';
import {
  getTranslatedCell,
  orderedKeys,
  validateFactor,
} from '../utils/NAvailabilityCorrectionUtils';
import { StyledTooltip, TableBodyRow } from '../NAvailabilityCorrection.styled';

interface TableBodyProps {
  tPrefix: string;
  state: NAvailabilityCorrectionsState;
  nAvailabilityCorrectionConfigurationData: OrganicFertiliserNAvailabilityCorrectionsConfiguration;
  spreaderMethods: SpreaderMethod[];
  soilTypes: SoilType[];
  handleChangeValues: (newValue: string, id: string, key: string) => void;
  handleUpdateValues: (key: string, id: string) => void;
}

export const TableBody = ({
  tPrefix,
  state,
  nAvailabilityCorrectionConfigurationData,
  spreaderMethods,
  soilTypes,
  handleChangeValues,
  handleUpdateValues,
}: TableBodyProps) => {
  const { t } = useTranslation('polaris');
  const [showTooltip, setShowTooltip] = useState<boolean>(false);

  const {
    configuration: {
      data: {
        soilCorrectionFactors,
        techniqueCorrectionFactors,
        dryMatterContentCorrectionFactors,
      },
    },
  } = nAvailabilityCorrectionConfigurationData;

  const getSecondCellContent = useCallback(
    (key: NAvailabilityCorrectionKeys) => {
      if (!spreaderMethods && !soilTypes) {
        return;
      }
      switch (key) {
        case NAvailabilityCorrectionKeys.TechniqueCorrectionFactors:
          return (
            <>
              {techniqueCorrectionFactors.map(({ id, spreaderMethodId }) => (
                <div key={id}>
                  <Caption>
                    {t(spreaderMethods.find(({ id }) => id === spreaderMethodId)?.name || '')}
                  </Caption>
                </div>
              ))}
            </>
          );
        case NAvailabilityCorrectionKeys.SoilCorrectionFactors:
          return (
            <>
              {soilCorrectionFactors.map(({ id, soilTypeIds }) => {
                const matchingSoilTypeNames = soilTypeIds
                  .map((soilTypeId: string) => soilTypes.find(({ id }) => id === soilTypeId)?.name)
                  .join(', ');
                return (
                  <div key={id}>
                    <StyledTooltip
                      variant={showTooltip ? 'default' : 'hidden'}
                      data-cy='n-availability-correction-tooltip'
                      concept='inverse'
                      maxWidth={255}
                      minWidth={255}
                      position='top'
                      text={matchingSoilTypeNames}
                    >
                      <Caption>{matchingSoilTypeNames}</Caption>
                    </StyledTooltip>
                  </div>
                );
              })}
            </>
          );
        case NAvailabilityCorrectionKeys.DryMatterContentCorrectionFactors:
          return (
            <>
              {dryMatterContentCorrectionFactors.map(({ id, name }) => (
                <div key={id}>
                  <Caption>{name}</Caption>
                </div>
              ))}
            </>
          );
        default:
          break;
      }
    },
    [spreaderMethods, soilTypes, showTooltip],
  );

  const soilTypeCellTruncatePoint = 976;
  useEffect(() => {
    //Show tooltip when soil type second column cell has been truncated
    const showTooltipHandler = () => setShowTooltip(window.innerWidth <= soilTypeCellTruncatePoint);
    window.addEventListener('resize', showTooltipHandler);
    return () => window.removeEventListener('resize', showTooltipHandler);
  }, []);

  return (
    <tbody>
      {nAvailabilityCorrectionConfigurationData &&
        orderedKeys.map((key, i: number) => {
          return (
            <TableBodyRow key={key + i} data-cy={`n-availability-correction-${key}-row`}>
              <Table.Cell data-cy={`n-availability-correction-${key}-first-cell`}>
                <Caption>{getTranslatedCell(key, tPrefix, t)}</Caption>
              </Table.Cell>
              <Table.Cell data-cy={`n-availability-correction-${key}-second-cell`}>
                {getSecondCellContent(key)}
              </Table.Cell>
              <Table.Cell>
                {nAvailabilityCorrectionConfigurationData.configuration.data[key].map(
                  (factor, index) => {
                    const { id } = factor;
                    const isValid = validateFactor(factor, soilTypes, spreaderMethods);
                    if (!isValid) {
                      return;
                    }

                    return (
                      <div key={id}>
                        <Input
                          style={{ textAlign: 'left' }}
                          size='xs'
                          name={key}
                          id={id}
                          aria-label={key}
                          data-cy={`n-availability-correction-${key}-${index}-input`}
                          value={state[key][id]}
                          onChange={({ target: { value, name } }) =>
                            handleChangeValues(value.replace(/\s/g, ''), id, name)
                          }
                          onBlur={({ target: { name } }) => handleUpdateValues(name, id)}
                        />
                      </div>
                    );
                  },
                )}
              </Table.Cell>
            </TableBodyRow>
          );
        })}
    </tbody>
  );
};
