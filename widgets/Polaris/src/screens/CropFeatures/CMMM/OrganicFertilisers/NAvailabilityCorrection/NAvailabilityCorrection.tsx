import React, { useEffect, useMemo, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import { Caption, Table } from '@yaradigitallabs/ahua-react';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';
import CollapsibleSectionHeaderTitle from '@widgets/Polaris/src/components/Collapsible/components/CollapsibleSectionHeaderTitle/CollapsibleSectionHeaderTitle';
import CollapsibleSection from '@widgets/Polaris/src/components/Collapsible/Collapsible';
import { SoilType, SpreaderMethod } from '@common/types';
import {
  useGetSpreaderMethods,
  useGetSoilTypes,
  useUpdateOrganicFertiliserConfiguration,
} from '@polaris-hooks/index';
import { FilterType } from '@widgets/Polaris/src/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  METHOD,
  OrganicFertiliserConfigurationNames,
  THREE_DECIMAL_PLACES_LIMIT,
} from '@common/constants';
import {
  formatNegativeOrPositiveDecimalsNumber,
  formatValidNegativeNumber,
  validateParameterNumber,
} from '@widgets/Polaris/utils';
import { MAX_LIMIT_NUMBER, MIN_LIMIT_NUMBER } from '../../../shared/constants';
import {
  CollapsibleContainer,
  TableContainer,
  EmptyStateAdditionalStyles,
  TableHeaderRow,
  StyledSubtitle,
} from './NAvailabilityCorrection.styled';
import { nAvailabilityCorrectionReducer } from './utils/NAvailabilityCorrectionReducer';
import { TableBody } from './components/TableBody';
import {
  getActionTypeFromEnum,
  getKeyFromEnum,
  populateState,
  nAvailabilityCorrectionsInitialState,
  updateConfigurationWithValidatedStateValues,
} from './utils/NAvailabilityCorrectionUtils';

const tPrefix = 'polaris.cmmmDetails.planConfiguration.organicFertilisers.nAvailabilityCorrection';

export const NAvailabilityCorrection = () => {
  const { t } = useTranslation('polaris');

  const {
    organicFertiliserConfigurations,
    methods: { updateOrganicFertiliserConfigurations },
  } = useAppContext();

  const [soilTypesData, setSoilTypesData] = useState<SoilType[]>();
  const [spreaderMethodsData, setSpreaderMethodsData] = useState<SpreaderMethod[]>();

  const { trigger: fetchSoilTypes } = useGetSoilTypes();
  const { trigger: fetchSpreaderMethods } = useGetSpreaderMethods();

  const nAvailabilityCorrectionConfigurationData = useMemo(() => {
    if (!organicFertiliserConfigurations) {
      return;
    }

    const clonedConfigurations = cloneDeep(organicFertiliserConfigurations);
    const nAvailabilityCorrectionConfiguration = clonedConfigurations.find(
      (configuration) =>
        configuration.name === OrganicFertiliserConfigurationNames.NAvailabilityCorrection,
    );

    if (!nAvailabilityCorrectionConfiguration) {
      return;
    }
    return nAvailabilityCorrectionConfiguration;
  }, [organicFertiliserConfigurations]);

  const [state, dispatch] = useReducer(
    nAvailabilityCorrectionReducer,
    nAvailabilityCorrectionsInitialState,
  );

  const getSoilTypes = async () => {
    const soilTypesFilters = [
      {
        key: 'id',
        value:
          nAvailabilityCorrectionConfigurationData?.configuration.data.soilCorrectionFactors
            .map(({ soilTypeIds }) => soilTypeIds.join(','))
            .join(',') || '',
        type: FilterType.IN,
      },
    ];
    try {
      const result = await fetchSoilTypes({
        method: METHOD.POST,
        body: JSON.stringify({ filter: soilTypesFilters }),
      });
      result && setSoilTypesData(result.entities);
    } catch (error) {
      console.error('Error fetching soil types data:', error);
    }
  };

  const getSpreaderMethods = async () => {
    const spreaderMethodsFilters = [
      {
        key: 'id',
        value:
          nAvailabilityCorrectionConfigurationData?.configuration.data.techniqueCorrectionFactors
            .map(({ spreaderMethodId }) => spreaderMethodId)
            .join(',') || '',
        type: FilterType.IN,
      },
    ];
    try {
      const result = await fetchSpreaderMethods({
        method: METHOD.POST,
        body: JSON.stringify({ filter: spreaderMethodsFilters }),
      });
      result && setSpreaderMethodsData(result.entities);
    } catch (error) {
      console.error('Error fetching spreader methods data:', error);
    }
  };

  useEffect(() => {
    if (!nAvailabilityCorrectionConfigurationData) {
      return;
    }
    getSoilTypes();
    getSpreaderMethods();
    populateState(nAvailabilityCorrectionConfigurationData, dispatch);
  }, [nAvailabilityCorrectionConfigurationData]);

  const { trigger: updateNAvailabilityCorrectionConfiguration } =
    useUpdateOrganicFertiliserConfiguration(nAvailabilityCorrectionConfigurationData?.id);

  const handleChangeValues = (newValue: string, id: string, key: string) => {
    const formattedNegativeValue = formatValidNegativeNumber(newValue);

    const isValid =
      validateParameterNumber(formattedNegativeValue) || formattedNegativeValue === '-';
    if (!isValid) {
      return;
    }

    const validatedValue = formatNegativeOrPositiveDecimalsNumber(
      formattedNegativeValue,
      THREE_DECIMAL_PLACES_LIMIT,
    );

    const isExceedingMaxLimit = Boolean(Number(validatedValue) > MAX_LIMIT_NUMBER);
    const isExceedingMinLimit = Boolean(Number(validatedValue) < MIN_LIMIT_NUMBER);
    if (isExceedingMaxLimit || isExceedingMinLimit) {
      return;
    }

    const actionType = getActionTypeFromEnum(key);
    if (!actionType) {
      return;
    }
    dispatch({
      type: actionType,
      payload: {
        value: validatedValue,
        id,
      },
    });
  };

  const handleUpdateValues = async (key: string, id: string) => {
    if (!nAvailabilityCorrectionConfigurationData) {
      return;
    }

    const typedKey = getKeyFromEnum(key);
    if (!typedKey) {
      return;
    }

    const currentValue = state[typedKey][id];

    const sanitizedCurrentValue = isNaN(Number(currentValue)) ? '0' : currentValue;

    const isValid = validateParameterNumber(sanitizedCurrentValue);

    if (!isValid) {
      return;
    }

    const validatedValue = Number(
      formatNegativeOrPositiveDecimalsNumber(sanitizedCurrentValue, THREE_DECIMAL_PLACES_LIMIT),
    );

    const clonedNAvailabilityCorrectionConfigurationData = cloneDeep(
      nAvailabilityCorrectionConfigurationData,
    );

    const currentlyEditedItemInData =
      clonedNAvailabilityCorrectionConfigurationData.configuration.data[typedKey].find(
        (factor) => factor.id === id,
      );

    if (
      !currentlyEditedItemInData ||
      (validatedValue !== 0 && currentlyEditedItemInData.correctionPercent.value === validatedValue)
    ) {
      return;
    }

    currentlyEditedItemInData.correctionPercent.value = validatedValue;

    const actionType = getActionTypeFromEnum(typedKey);
    if (actionType) {
      dispatch({
        type: actionType,
        payload: {
          value: String(validatedValue),
          id,
        },
      });
    }

    const updatedConfiguration = updateConfigurationWithValidatedStateValues(
      clonedNAvailabilityCorrectionConfigurationData,
      state,
    );

    try {
      const result = await updateNAvailabilityCorrectionConfiguration({
        method: METHOD.PUT,
        body: JSON.stringify(updatedConfiguration),
      });

      if (result) {
        updateOrganicFertiliserConfigurations(result);
      }
    } catch (error) {
      console.error('Failed to update Organic Fertiliser Configuration:', error);
    }
  };

  const headers = [
    t(`${tPrefix}.table.col1`),
    t(`${tPrefix}.table.col2`),
    t(`${tPrefix}.table.col3`),
  ];

  const noCorrectionFactorData =
    !nAvailabilityCorrectionConfigurationData?.configuration.data.techniqueCorrectionFactors
      .length ||
    !nAvailabilityCorrectionConfigurationData?.configuration.data.soilCorrectionFactors.length ||
    !nAvailabilityCorrectionConfigurationData?.configuration.data.dryMatterContentCorrectionFactors
      .length;

  return (
    <CollapsibleContainer>
      <CollapsibleSection
        defaultOpen={true}
        showCardContent={false}
        dataCY='n-availability-correction-collapsible'
        headerTitle={
          <CollapsibleSectionHeaderTitle
            tHeaderTitle={`${tPrefix}.headerTitle`}
            tHeaderSubtitle={`${tPrefix}.headerSubtitle`}
            dataCy='n-availability-correction'
          />
        }
      >
        <StyledSubtitle data-cy='n-availability-correction-subtitle'>
          {t(`${tPrefix}.table.title`)}
        </StyledSubtitle>
        <TableContainer>
          <Table>
            <thead>
              <TableHeaderRow data-cy='n-availability-correction-table-header-row'>
                {headers.map((header, i) => (
                  <Table.Head key={header + i}>
                    <Caption>{header}</Caption>
                  </Table.Head>
                ))}
              </TableHeaderRow>
            </thead>
            {nAvailabilityCorrectionConfigurationData &&
              !noCorrectionFactorData &&
              spreaderMethodsData &&
              soilTypesData &&
              state && (
                <TableBody
                  tPrefix={tPrefix}
                  state={state}
                  nAvailabilityCorrectionConfigurationData={
                    nAvailabilityCorrectionConfigurationData
                  }
                  spreaderMethods={spreaderMethodsData}
                  soilTypes={soilTypesData}
                  handleChangeValues={handleChangeValues}
                  handleUpdateValues={handleUpdateValues}
                />
              )}
          </Table>

          {(!nAvailabilityCorrectionConfigurationData ||
            !nAvailabilityCorrectionConfigurationData?.configuration.data ||
            !soilTypesData ||
            !spreaderMethodsData ||
            noCorrectionFactorData) && (
            <EmptyStateComponent
              dataCy='n-availability-correction-empty-state'
              styles={{ additionalStyles: EmptyStateAdditionalStyles }}
              message={t(`${tPrefix}.emptyStateText`)}
            />
          )}
        </TableContainer>
      </CollapsibleSection>
    </CollapsibleContainer>
  );
};

export default NAvailabilityCorrection;
