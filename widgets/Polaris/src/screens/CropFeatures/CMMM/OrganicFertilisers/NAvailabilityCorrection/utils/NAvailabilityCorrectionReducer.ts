import { cloneDeep } from 'lodash';
import {
  UpdateNAvailabilityCorrectionsActions,
  NAvailabilityCorrectionActions,
  NAvailabilityCorrectionsState,
} from '@common/types';

export const nAvailabilityCorrectionReducer = (
  state: NAvailabilityCorrectionsState,
  action: NAvailabilityCorrectionActions,
): NAvailabilityCorrectionsState => {
  const stateToUpdate = cloneDeep(state);
  const { id, value } = action.payload;

  switch (action.type) {
    case UpdateNAvailabilityCorrectionsActions.UpdateTechnique: {
      stateToUpdate.techniqueCorrectionFactors[id] = value;
      return stateToUpdate;
    }
    case UpdateNAvailabilityCorrectionsActions.UpdateSoil: {
      stateToUpdate.soilCorrectionFactors[id] = value;
      return stateToUpdate;
    }
    case UpdateNAvailabilityCorrectionsActions.UpdateDryMatter: {
      stateToUpdate.dryMatterContentCorrectionFactors[id] = value;
      return stateToUpdate;
    }
  }
};
