import React, { FC, useEffect, useMemo } from 'react';
import ProductsAvailable from './ProductsAvailable/ProductsAvailable';
import MarketRegulations from './MarketRegulations/MarketRegulations';
import SoilTypes from './SoilTypes/SoilTypes';
import NAvailabilityCorrection from './NAvailabilityCorrection/NAvailabilityCorrection';
import PAvailabilityCorrection from './PAvailabilityCorrection/PAvailabilityCorrection';
import { Country } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { FilterType } from '@widgets/Polaris/src/types';
import { useFetchOrganicFertiliserConfigurations } from '@polaris-hooks/index';

interface OrganicFertiliserProps {
  selectedCountry: Country | null;
}

const OrganicFertilisers: FC<OrganicFertiliserProps> = ({ selectedCountry }) => {
  const {
    cropRegion,
    selectedFeature,
    organicFertiliserConfigurations,
    methods: { setOrganicFertiliserConfigurations },
  } = useAppContext();

  const soilTypeConfigurationFilters = useMemo(() => {
    if (!cropRegion?.id) {
      return;
    }

    return [
      {
        key: 'cropRegionId',
        value: cropRegion.id,
        type: FilterType.EQ,
      },
    ];
  }, [cropRegion]);

  const { organicFertiliserConfigurations: organicFertiliserConfigurationsData } =
    useFetchOrganicFertiliserConfigurations(soilTypeConfigurationFilters);

  useEffect(() => {
    if (!organicFertiliserConfigurationsData) {
      return;
    }
    setOrganicFertiliserConfigurations(organicFertiliserConfigurationsData);
  }, [organicFertiliserConfigurationsData]);

  return (
    <>
      <ProductsAvailable
        data-cy={'products-available-section-component'}
        selectedCountry={selectedCountry}
        selectedFeature={selectedFeature}
      />
      <MarketRegulations />
      <SoilTypes />
      <NAvailabilityCorrection />
      {organicFertiliserConfigurations && (
        <PAvailabilityCorrection
          organicFertiliserConfigurationsData={organicFertiliserConfigurations}
        />
      )}
    </>
  );
};

export default OrganicFertilisers;
