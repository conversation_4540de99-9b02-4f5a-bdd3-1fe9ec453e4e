import React from 'react';
import { render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import {
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  productRegionHandler,
  cerealOrganicFertiliserConfigurationHandler,
  mockAppProviderValue,
} from '@common/mocks';
import * as useProductRegion from '@polaris-hooks/product/useProductRegion/useProductRegion';
import { countryResponse, useProductsByRegionResponse } from '../../../../Home/mock-data/MockData';
import OrganicFertilisers from '../OrganicFertilisers';

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  productRegionHandler,
  cerealOrganicFertiliserConfigurationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('OrganicFertilisers', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders organic fertiliser without an issue', () => {
    jest
      .spyOn(useProductRegion, 'useFetchOrganicProductRegions')
      .mockImplementation(() => useProductsByRegionResponse);

    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => <OrganicFertilisers selectedCountry={countryResponse[0]} />}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
    expect(
      component.getByTestId('products-available-collapsible-section-component'),
    ).toBeInTheDocument();
    expect(component.getByTestId('products-available-header-title')).toBeInTheDocument();
    expect(component.getByTestId('products-available-section-note')).toBeInTheDocument();

    expect(component.getByTestId('soil-types-collapsible')).toBeInTheDocument();
  });
});
