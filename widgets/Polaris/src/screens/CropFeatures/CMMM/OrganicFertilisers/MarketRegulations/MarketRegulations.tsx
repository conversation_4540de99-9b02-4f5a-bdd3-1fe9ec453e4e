import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CollapsibleSectionHeaderTitle from '@widgets/Polaris/src/components/Collapsible/components/CollapsibleSectionHeaderTitle/CollapsibleSectionHeaderTitle';
import CollapsibleSection from '@widgets/Polaris/src/components/Collapsible/Collapsible';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { CollapsibleContainer, InputsContainer, InputWithLabel } from './MarketRegulations.styled';
import { Input, Subtitle } from '@yaradigitallabs/ahua-react';
import { filterUnitsById, useUpdateOrganicFertiliserConfiguration } from '@polaris-hooks/index';
import { MARKET_REGULATION_UNIT_INDEX_TO_IDS, MAX_LIMIT_NUMBER } from '../../../shared/constants';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import { cloneDeep } from 'lodash';
import {
  METHOD,
  OrganicFertiliserConfigurationNames,
  THREE_DECIMAL_PLACES_LIMIT,
} from '@common/constants';
import { getKeyFromEnum, Inputs, Nutrients } from './utils/marketRegulationsUtils';

const tPrefix = 'polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations';

export const SoilTypes = () => {
  const { t } = useTranslation('polaris');

  const {
    selectedCountryUnits,
    organicFertiliserConfigurations,
    methods: { updateOrganicFertiliserConfigurations },
  } = useAppContext();

  const marketRegulationsConfigurationData = useMemo(() => {
    if (!organicFertiliserConfigurations) {
      return;
    }

    const clonedConfigurations = cloneDeep(organicFertiliserConfigurations);
    const marketRegulationConfiguration = clonedConfigurations.find(
      (configuration) =>
        configuration.name === OrganicFertiliserConfigurationNames.MarketRegulations,
    );

    if (!marketRegulationConfiguration) {
      return;
    }

    return marketRegulationConfiguration;
  }, [organicFertiliserConfigurations]);

  const { trigger: updateMarketRegulationsConfiguration } = useUpdateOrganicFertiliserConfiguration(
    marketRegulationsConfigurationData?.id,
  );

  const [applicationLimitValues, setApplicationLimitValues] = useState<
    Record<Inputs, number | string>
  >({
    [Inputs.applicationLimits]:
      marketRegulationsConfigurationData?.configuration.data?.applicationLimits
        ?.maxApplicationValue || 0,
    [Inputs.nApplicationLimits]:
      marketRegulationsConfigurationData?.configuration.data?.nApplicationLimits
        ?.maxApplicationValue || 0,
    [Inputs.pApplicationLimits]:
      marketRegulationsConfigurationData?.configuration.data?.pApplicationLimits
        ?.maxApplicationValue || 0,
  });

  useEffect(() => {
    setApplicationLimitValues({
      [Inputs.applicationLimits]:
        marketRegulationsConfigurationData?.configuration.data?.applicationLimits
          ?.maxApplicationValue || 0,
      [Inputs.nApplicationLimits]:
        marketRegulationsConfigurationData?.configuration.data?.nApplicationLimits
          ?.maxApplicationValue || 0,
      [Inputs.pApplicationLimits]:
        marketRegulationsConfigurationData?.configuration.data?.pApplicationLimits
          ?.maxApplicationValue || 0,
    });
  }, [marketRegulationsConfigurationData]);

  // Logic for using hardcoded units for MVP, to be changed later when dynamic units are to be implemented
  const getUnitName = useCallback(
    (index: number) => {
      if (!selectedCountryUnits) {
        return;
      }

      return filterUnitsById(selectedCountryUnits, MARKET_REGULATION_UNIT_INDEX_TO_IDS[index])
        ?.name;
    },
    [selectedCountryUnits],
  );

  const handleChangeValues = (value: string, name: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) {
      return;
    }
    const validatedValue = formatValidDecimalsNumber(value, THREE_DECIMAL_PLACES_LIMIT);

    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) {
      return;
    }
    setApplicationLimitValues((prev) => ({
      ...prev,
      [name]: validatedValue === '' ? '0' : validatedValue,
    }));
  };

  const handleUpdateValues = async (value: string) => {
    if (!marketRegulationsConfigurationData) {
      return;
    }

    const key = getKeyFromEnum(value);
    if (!key) {
      return;
    }

    const currentValue = applicationLimitValues[key].toString();
    const isValid = validateParameterNumber(currentValue);
    if (!isValid) {
      return;
    }

    const validatedValue = Number(
      formatValidDecimalsNumber(currentValue, THREE_DECIMAL_PLACES_LIMIT),
    );

    const sanitizedValue = isNaN(validatedValue) ? 0 : validatedValue;
    if (applicationLimitValues[key] === sanitizedValue) {
      return;
    }

    const newConfiguration = cloneDeep(marketRegulationsConfigurationData);
    newConfiguration.configuration.data = {
      ...newConfiguration?.configuration?.data,
      [key]: {
        maxApplicationValue: sanitizedValue,
        maxApplicationUnitId: newConfiguration.configuration.data[key].maxApplicationUnitId,
      },
    };

    try {
      const result = await updateMarketRegulationsConfiguration({
        method: METHOD.PUT,
        body: JSON.stringify(newConfiguration),
      });

      if (result) {
        updateOrganicFertiliserConfigurations(newConfiguration);
      }
    } catch (error) {
      console.error('Failed to update Organic Fertiliser Configuration:', error);
    }
  };

  return (
    <CollapsibleContainer>
      <CollapsibleSection
        defaultOpen={true}
        showCardContent={false}
        dataCY='market-regulations-collapsible'
        headerTitle={
          <CollapsibleSectionHeaderTitle
            tHeaderTitle={`${tPrefix}.headerTitle`}
            tHeaderSubtitle={`${tPrefix}.headerSubtitle`}
            dataCy='market-regulations'
          />
        }
      >
        <InputsContainer>
          {marketRegulationsConfigurationData &&
            Object.keys(marketRegulationsConfigurationData.configuration.data).map((key, i) => {
              const unitName = getUnitName(i);
              const keyFromEnum = getKeyFromEnum(key);
              if (!keyFromEnum) {
                return;
              }
              return (
                <InputWithLabel key={key}>
                  <Subtitle size='s' data-cy={`market-regulations-${key}-subtitle`}>
                    {t(`${tPrefix}.applicationLimitsTitle`, {
                      name: Nutrients[keyFromEnum],
                    })}
                  </Subtitle>

                  <Input
                    size='s'
                    label={t(`${tPrefix}.inputs.label`, {
                      name: Nutrients[keyFromEnum],
                      unit: unitName,
                    })}
                    name={key}
                    aria-label={key}
                    data-cy={`market-regulations-${key}-input`}
                    value={applicationLimitValues[keyFromEnum]}
                    onChange={({ target: { value, name } }) => {
                      handleChangeValues(value.replace(/\s/g, ''), name);
                    }}
                    onBlur={({ target: { name } }) => handleUpdateValues(name)}
                  />
                </InputWithLabel>
              );
            })}
        </InputsContainer>
      </CollapsibleSection>
    </CollapsibleContainer>
  );
};

export default SoilTypes;
