import { styled } from '@yaradigitallabs/ahua-react';

export const CollapsibleContainer = styled('div', {
  paddingTop: '$x6',
});

export const InputsContainer = styled('div', {
  display: 'flex',
  flexDirection: 'row',
  flexWrap: 'wrap',
  gap: '$x4',
  padding: '$x6 0 $x6 $x4',

  '& div:first-child': {
    flex: '0 1 100%',
  },

  '& > div:last-child': {
    paddingLeft: '$x2',
  },
});

export const InputWithLabel = styled('div', {
  width: '400px',
  display: 'flex',
  flexDirection: 'column',
  flexWrap: 'wrap',

  '& div': {
    width: '400px',
  },

  '& h2': {
    width: '400px',
    paddingBottom: '$x2',
  },

  '& > div > input': {
    height: '$x12',
    paddingTop: '$x5',
    paddingBottom: '6px',
  },
  '& div > label': {
    paddingTop: '7px',
  },
});
