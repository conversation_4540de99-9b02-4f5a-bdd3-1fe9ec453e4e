import React from 'react';
import {
  getAllUnitsHandler,
  updateCerealOrganicFertiliserMarketRegulationConfiugrationHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { NavbarProvider } from '@libs/nav-context';
import { act, fireEvent, render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { MAX_LIMIT_NUMBER } from '../../../../shared/constants';
import MarketRegulations from '../MarketRegulations';

const server = setupServer(
  getAllUnitsHandler,
  updateCerealOrganicFertiliserMarketRegulationConfiugrationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('Market Regulations', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('match snapshot', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <MarketRegulations />
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });

  it('render 3 inputs and 4 subtitles when there is a valid market regulations configuration', async () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <MarketRegulations />
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(screen.getByTestId('market-regulations-collapsible')).toBeInTheDocument();
    expect(screen.getByTestId('market-regulations-header-title')).toBeInTheDocument();

    const applicationLimitsSubtitle = screen.getByTestId(
      'market-regulations-applicationLimits-subtitle',
    );
    expect(applicationLimitsSubtitle).toBeInTheDocument();
    expect(applicationLimitsSubtitle).toHaveTextContent(
      'polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle',
    );

    const numberOfInputs = screen.getAllByTestId(/market-regulations-[\w]+-input/);
    const numberOfSubtitles = screen.getAllByTestId(/market-regulations-[\w]+-subtitle/);
    expect(numberOfInputs.length).toBe(3);
    expect(numberOfSubtitles.length).toBe(4);
  });

  it('render 3 inputs and 4 subtitles when there is additional invalid data', async () => {
    await act(async () => {
      const { organicFertiliserConfigurations } = mockAppProviderValue;
      organicFertiliserConfigurations[1].configuration.data = {
        ...organicFertiliserConfigurations[1].configuration.data,
        test: {
          maxApplicationValue: 1,
          maxApplicationUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
        },
      };

      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <MarketRegulations />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const inputs = screen.getAllByTestId(/market-regulations-[\w]+-input/);
    const subtitles = screen.getAllByTestId(/market-regulations-[\w]+-subtitle/);
    expect(inputs.length).toBe(3);
    expect(subtitles.length).toBe(4);
  });
  it('not rendering any inputs when data is invalid', async () => {
    await act(async () => {
      const invalidDataProviderValue = {
        ...mockAppProviderValue,
        organicFertiliserConfigurations: [
          {
            ...mockAppProviderValue.organicFertiliserConfigurations[1],
            configuration: {
              data: {
                test: {
                  maxApplicationValue: 1,
                  maxApplicationUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
                },
              },
            },
          },
        ],
      };

      render(
        <AppContext.Provider value={invalidDataProviderValue}>
          <NavbarProvider>
            <MarketRegulations />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const inputs = screen.queryByTestId(/market-regulations-[\w]+-input/);
    expect(inputs).not.toBeInTheDocument();
  });

  it('updates to input are correct and reflected', async () => {
    const promise = Promise.resolve();
    mockAppProviderValue.methods.updateOrganicFertiliserConfigurations = jest.fn(() => promise);
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <MarketRegulations />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const applicationLimitsInput = screen.getByTestId('market-regulations-applicationLimits-input');

    expect(applicationLimitsInput).toBeInTheDocument();
    expect(applicationLimitsInput).toHaveValue('133.789');
    fireEvent.change(applicationLimitsInput, { target: { value: '9' } });
    fireEvent.blur(applicationLimitsInput);
    expect(applicationLimitsInput).toHaveValue('9');

    // Removes warning about not using act
    await act(() => promise);
  });

  it('updates to input are invalid type', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <MarketRegulations />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const applicationLimitsInput = screen.getByTestId('market-regulations-applicationLimits-input');

    expect(applicationLimitsInput).toBeInTheDocument();
    expect(applicationLimitsInput).toHaveValue('133.789');
    fireEvent.change(applicationLimitsInput, { target: { value: 'asd' } });
    fireEvent.blur(applicationLimitsInput);
    expect(applicationLimitsInput).toHaveValue('133.789');
  });

  it('updates to input exceed max limit', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <MarketRegulations />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const numberExceedingMaxLimit = MAX_LIMIT_NUMBER + 1;
    const applicationLimitsInput = screen.getByTestId('market-regulations-applicationLimits-input');

    expect(applicationLimitsInput).toBeInTheDocument();
    expect(applicationLimitsInput).toHaveValue('133.789');
    fireEvent.change(applicationLimitsInput, {
      target: { value: numberExceedingMaxLimit },
    });
    fireEvent.blur(applicationLimitsInput);
    expect(applicationLimitsInput).toHaveValue('133.789');
  });
});
