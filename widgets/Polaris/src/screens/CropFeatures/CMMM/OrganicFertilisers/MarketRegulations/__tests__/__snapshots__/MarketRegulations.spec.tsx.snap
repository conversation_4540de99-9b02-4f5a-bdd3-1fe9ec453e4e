// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Market Regulations match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-cdvoUX"
      >
        <div
          class="c-eNnZw collapsible-section"
          data-cy="market-regulations-collapsible"
          data-state="open"
        >
          <button
            aria-controls="radix-:r0:"
            aria-expanded="true"
            class="c-cUgXyc"
            data-state="open"
            type="button"
          >
            <div>
              <h1
                class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
                data-cy="market-regulations-header-title"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.headerTitle
              </h1>
              <h2
                class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-kvYgSl"
                data-cy="market-regulations-header-subtitle"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.headerSubtitle
              </h2>
            </div>
            <div
              class="c-irPLE"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 15.75l-5-6h-2l-5 6"
                />
              </svg>
            </div>
          </button>
          <div
            class="c-fLVWxk"
            data-state="open"
            id="radix-:r0:"
            style="transition-duration: 0s; animation-name: none;"
          >
            <div
              class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
            />
            <div
              class="c-bLIgMS"
            >
              <div
                class="c-eumrnG"
              >
                <h2
                  class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css"
                  data-cy="market-regulations-applicationLimits-subtitle"
                >
                  polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle
                </h2>
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    aria-label="applicationLimits"
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                    data-cy="market-regulations-applicationLimits-input"
                    name="applicationLimits"
                    placeholder="polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label"
                    value="133.789"
                  />
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                  >
                    polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label
                  </label>
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </div>
              <div
                class="c-eumrnG"
              >
                <h2
                  class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css"
                  data-cy="market-regulations-nApplicationLimits-subtitle"
                >
                  polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle
                </h2>
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    aria-label="nApplicationLimits"
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                    data-cy="market-regulations-nApplicationLimits-input"
                    name="nApplicationLimits"
                    placeholder="polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label"
                    value="15"
                  />
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                  >
                    polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label
                  </label>
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </div>
              <div
                class="c-eumrnG"
              >
                <h2
                  class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css"
                  data-cy="market-regulations-pApplicationLimits-subtitle"
                >
                  polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle
                </h2>
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    aria-label="pApplicationLimits"
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                    data-cy="market-regulations-pApplicationLimits-input"
                    name="pApplicationLimits"
                    placeholder="polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label"
                    value="42.069"
                  />
                  <label
                    class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                  >
                    polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label
                  </label>
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-cdvoUX"
    >
      <div
        class="c-eNnZw collapsible-section"
        data-cy="market-regulations-collapsible"
        data-state="open"
      >
        <button
          aria-controls="radix-:r0:"
          aria-expanded="true"
          class="c-cUgXyc"
          data-state="open"
          type="button"
        >
          <div>
            <h1
              class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
              data-cy="market-regulations-header-title"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.headerTitle
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-kvYgSl"
              data-cy="market-regulations-header-subtitle"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.headerSubtitle
            </h2>
          </div>
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 15.75l-5-6h-2l-5 6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="open"
          id="radix-:r0:"
          style="transition-duration: 0s; animation-name: none;"
        >
          <div
            class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
          />
          <div
            class="c-bLIgMS"
          >
            <div
              class="c-eumrnG"
            >
              <h2
                class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css"
                data-cy="market-regulations-applicationLimits-subtitle"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle
              </h2>
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  aria-label="applicationLimits"
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                  data-cy="market-regulations-applicationLimits-input"
                  name="applicationLimits"
                  placeholder="polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label"
                  value="133.789"
                />
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                >
                  polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label
                </label>
                <span
                  class="c-fcBbhr"
                />
              </div>
            </div>
            <div
              class="c-eumrnG"
            >
              <h2
                class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css"
                data-cy="market-regulations-nApplicationLimits-subtitle"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle
              </h2>
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  aria-label="nApplicationLimits"
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                  data-cy="market-regulations-nApplicationLimits-input"
                  name="nApplicationLimits"
                  placeholder="polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label"
                  value="15"
                />
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                >
                  polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label
                </label>
                <span
                  class="c-fcBbhr"
                />
              </div>
            </div>
            <div
              class="c-eumrnG"
            >
              <h2
                class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iPJLV-css"
                data-cy="market-regulations-pApplicationLimits-subtitle"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.applicationLimitsTitle
              </h2>
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  aria-label="pApplicationLimits"
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv"
                  data-cy="market-regulations-pApplicationLimits-input"
                  name="pApplicationLimits"
                  placeholder="polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label"
                  value="42.069"
                />
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                >
                  polaris.cmmmDetails.planConfiguration.organicFertilisers.marketRegulations.inputs.label
                </label>
                <span
                  class="c-fcBbhr"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
