export enum Inputs {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  applicationLimits = 'applicationLimits',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  nApplicationLimits = 'nApplicationLimits',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  pApplicationLimits = 'pApplicationLimits',
}

export enum Nutrients {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  applicationLimits = '',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  nApplicationLimits = 'N',
  // eslint-disable-next-line @typescript-eslint/naming-convention
  pApplicationLimits = 'P',
}

export const getKeyFromEnum = (value: string) => {
  switch (value) {
    case Inputs.applicationLimits:
      return Inputs.applicationLimits;
    case Inputs.nApplicationLimits:
      return Inputs.nApplicationLimits;
    case Inputs.pApplicationLimits:
      return Inputs.pApplicationLimits;
    default:
      break;
  }
};
