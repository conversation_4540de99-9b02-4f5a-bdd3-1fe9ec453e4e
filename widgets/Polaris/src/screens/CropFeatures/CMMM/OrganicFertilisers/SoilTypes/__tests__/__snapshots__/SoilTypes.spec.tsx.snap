// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Soil Types match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-qHTDg"
      >
        <div
          class="c-eNnZw collapsible-section"
          data-cy="soil-types-collapsible"
          data-state="open"
        >
          <button
            aria-controls="radix-:r0:"
            aria-expanded="true"
            class="c-cUgXyc"
            data-state="open"
            type="button"
          >
            <div>
              <h1
                class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
                data-cy="soil-types-header-title"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.headerTitle
              </h1>
              <h2
                class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-kvYgSl"
                data-cy="soil-types-header-subtitle"
              >
                polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.headerSubtitle
              </h2>
            </div>
            <div
              class="c-irPLE"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 15.75l-5-6h-2l-5 6"
                />
              </svg>
            </div>
          </button>
          <div
            class="c-fLVWxk"
            data-state="open"
            id="radix-:r0:"
            style="transition-duration: 0s; animation-name: none;"
          >
            <div
              class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
            />
            <h2
              class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-cHDQPg"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.description
            </h2>
            <div
              class="c-bRJTUo"
            >
              <div
                class="c-jqtSSh c-jqtSSh-hihSaP-variant-dynamic table-bottom-padding"
                data-cy="table-section"
              >
                <table
                  class="c-kwAGqj table"
                >
                  <thead>
                    <tr
                      class="c-eDGYZe c-hMviFu"
                      data-cy="soil-types-table-header-row"
                    >
                      <th
                        class="c-kxWgPf"
                      >
                        <p
                          class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                        >
                          table.col1
                        </p>
                      </th>
                      <th
                        class="c-kxWgPf"
                      >
                        <p
                          class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                        >
                          table.col2
                        </p>
                      </th>
                      <th
                        class="c-kxWgPf"
                      >
                        <p
                          class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                        >
                          table.col3
                        </p>
                      </th>
                    </tr>
                  </thead>
                  <tbody />
                </table>
              </div>
              <div
                class="c-htRpEE"
                data-cy="soil-types-empty-state"
                style="height: 336px;"
              >
                <div
                  class="c-ghqIYf"
                >
                  <div
                    class="c-jBEZvR c-jBEZvR-iTKOFX-orientation-vertical"
                  >
                    <div
                      class="c-jxdTCW"
                    >
                      <div
                        class="c-eHLrry c-eHLrry-KMkko-size-n c-eHLrry-kxdaWf-cv"
                        data-cy="empty-state-info-icon"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-gmtoDF-iconSize-x8 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
                          data-cy="empty-state-info-icon"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div
                      class="c-htcaFI c-htcaFI-gjdJOs-orientation-vertical"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-dDOYgV-size-l c-gIhYmC-iPJLV-css c-PJLV c-PJLV-gjdJOs-orientation-vertical c-ebEscy"
                        data-cy="empty-screen-text"
                      >
                        polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.emptyStateText
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-qHTDg"
    >
      <div
        class="c-eNnZw collapsible-section"
        data-cy="soil-types-collapsible"
        data-state="open"
      >
        <button
          aria-controls="radix-:r0:"
          aria-expanded="true"
          class="c-cUgXyc"
          data-state="open"
          type="button"
        >
          <div>
            <h1
              class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
              data-cy="soil-types-header-title"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.headerTitle
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-kvYgSl"
              data-cy="soil-types-header-subtitle"
            >
              polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.headerSubtitle
            </h2>
          </div>
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 15.75l-5-6h-2l-5 6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="open"
          id="radix-:r0:"
          style="transition-duration: 0s; animation-name: none;"
        >
          <div
            class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
          />
          <h2
            class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css c-cHDQPg"
          >
            polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.description
          </h2>
          <div
            class="c-bRJTUo"
          >
            <div
              class="c-jqtSSh c-jqtSSh-hihSaP-variant-dynamic table-bottom-padding"
              data-cy="table-section"
            >
              <table
                class="c-kwAGqj table"
              >
                <thead>
                  <tr
                    class="c-eDGYZe c-hMviFu"
                    data-cy="soil-types-table-header-row"
                  >
                    <th
                      class="c-kxWgPf"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      >
                        table.col1
                      </p>
                    </th>
                    <th
                      class="c-kxWgPf"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      >
                        table.col2
                      </p>
                    </th>
                    <th
                      class="c-kxWgPf"
                    >
                      <p
                        class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
                      >
                        table.col3
                      </p>
                    </th>
                  </tr>
                </thead>
                <tbody />
              </table>
            </div>
            <div
              class="c-htRpEE"
              data-cy="soil-types-empty-state"
              style="height: 336px;"
            >
              <div
                class="c-ghqIYf"
              >
                <div
                  class="c-jBEZvR c-jBEZvR-iTKOFX-orientation-vertical"
                >
                  <div
                    class="c-jxdTCW"
                  >
                    <div
                      class="c-eHLrry c-eHLrry-KMkko-size-n c-eHLrry-kxdaWf-cv"
                      data-cy="empty-state-info-icon"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-gmtoDF-iconSize-x8 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
                        data-cy="empty-state-info-icon"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div
                    class="c-htcaFI c-htcaFI-gjdJOs-orientation-vertical"
                  >
                    <p
                      class="c-gIhYmC c-gIhYmC-dDOYgV-size-l c-gIhYmC-iPJLV-css c-PJLV c-PJLV-gjdJOs-orientation-vertical c-ebEscy"
                      data-cy="empty-screen-text"
                    >
                      polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes.emptyStateText
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
