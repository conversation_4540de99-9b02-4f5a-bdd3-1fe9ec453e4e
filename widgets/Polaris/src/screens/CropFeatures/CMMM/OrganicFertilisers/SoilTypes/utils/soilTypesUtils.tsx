import { OrganicFertiliserSoilType } from '@common/types';
import { ColumnDef } from '@tanstack/react-table';
import { TFunction } from 'i18next';

export interface TableConfigProps {
  t: TFunction;
  keyPrefix: string;
}

export const getColumnDefs = ({
  t,
  keyPrefix,
}: TableConfigProps): ColumnDef<OrganicFertiliserSoilType>[] => {
  return [
    {
      header: t(`table.col1`, { keyPrefix }),
      accessorKey: 'isDefault',
      enableColumnFilter: true,
      enableSorting: false,
    },
    {
      header: t(`table.col2`, { keyPrefix }),
      accessorKey: 'soilTypeId',
      enableSorting: true,
      enableColumnFilter: true,
    },
    {
      header: t(`table.col3`, { keyPrefix }),
      accessorKey: 'isVisibleToUser',
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];
};
