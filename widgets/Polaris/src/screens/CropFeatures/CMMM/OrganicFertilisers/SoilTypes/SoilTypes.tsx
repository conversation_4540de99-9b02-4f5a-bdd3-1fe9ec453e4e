import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import CollapsibleSectionHeaderTitle from '@widgets/Polaris/src/components/Collapsible/components/CollapsibleSectionHeaderTitle/CollapsibleSectionHeaderTitle';
import CollapsibleSection from '@widgets/Polaris/src/components/Collapsible/Collapsible';
import {
  OrganicFertiliserSoilType,
  OrganicFertiliserSoilTypesConfiguration,
  SoilType,
} from '@common/types';
import { useGetSoilTypes } from '@polaris-hooks/index';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { FilterType } from '@widgets/Polaris/src/types';
import { METHOD, OrganicFertiliserConfigurationNames } from '@common/constants';
import { CollapsibleContainer, Description } from './SoilTypes.styled';
import SoilTypesTable from './components/SoilTypesTable';

const tPrefix = 'polaris.cmmmDetails.planConfiguration.organicFertilisers.soilTypes';

const NOT_APPLICABLE = '2827983a-e483-4ba6-b855-b9036a8e8675';

export const SoilTypes = () => {
  const { t } = useTranslation('polaris');
  const [soilTypeConfigurationData, setSoilTypeConfigurationData] =
    useState<OrganicFertiliserSoilTypesConfiguration>();
  const [soilTypesData, setSoilTypesData] = useState<SoilType[]>();

  const { organicFertiliserConfigurations } = useAppContext();

  const { trigger: fetchSoilTypes } = useGetSoilTypes();
  const getSoilTypes = async (configurationData: OrganicFertiliserSoilTypesConfiguration) => {
    if (!configurationData) {
      return;
    }

    const soilTypeFilters = [
      {
        key: 'id',
        value: configurationData.configuration.data.soilTypes
          .map(({ soilTypeId }) => soilTypeId)
          .join(','),
        type: FilterType.IN,
      },
    ];
    try {
      const result = await fetchSoilTypes({
        method: METHOD.POST,
        body: JSON.stringify({ filter: soilTypeFilters }),
      });
      result && setSoilTypesData(result.entities);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    if (!organicFertiliserConfigurations) {
      return;
    }

    const clonedConfigurations = cloneDeep(organicFertiliserConfigurations);

    const soilTypesConfiguration = clonedConfigurations.find(
      (configuration) => configuration.name === OrganicFertiliserConfigurationNames.SoilTypes,
    );

    if (soilTypesConfiguration) {
      soilTypesConfiguration.configuration.data.soilTypes.sort(
        (a: OrganicFertiliserSoilType, _) => {
          return a.soilTypeId === NOT_APPLICABLE ? -1 : 0;
        },
      );

      setSoilTypeConfigurationData(soilTypesConfiguration);
    }
  }, [organicFertiliserConfigurations?.length]);

  useEffect(() => {
    if (!soilTypeConfigurationData) {
      return;
    }
    getSoilTypes(soilTypeConfigurationData);
  }, [soilTypeConfigurationData]);

  return (
    <CollapsibleContainer>
      <CollapsibleSection
        defaultOpen={true}
        showCardContent={false}
        dataCY='soil-types-collapsible'
        headerTitle={
          <CollapsibleSectionHeaderTitle
            tHeaderTitle={`${tPrefix}.headerTitle`}
            tHeaderSubtitle={`${tPrefix}.headerSubtitle`}
            dataCy='soil-types'
          />
        }
      >
        <Description>{t(`${tPrefix}.description`)}</Description>

        <SoilTypesTable
          tPrefix={tPrefix}
          soilTypeConfigurationData={soilTypeConfigurationData}
          setSoilTypeConfigurationData={setSoilTypeConfigurationData}
          soilTypesData={soilTypesData}
        />
      </CollapsibleSection>
    </CollapsibleContainer>
  );
};

export default SoilTypes;
