import { RadioButtonGroup, Table, styled } from '@yaradigitallabs/ahua-react';
import { CSSProperties } from 'react';

export const TableContainer = styled('div', {
  paddingRight: '$x4',

  // Remove pagination absolute position
  '& div': {
    position: 'relative',
    paddingTop: 'unset',
  },

  // Pagination outer div
  '& > div > div': {
    paddingTop: '$x3',
  },
});

export const TableCell = styled(Table.Cell, {
  border: '1px solid $black10',
  backgroundColor: '$white100',
  fontWeight: '$semiBold',
  alignItems: 'left',
  padding: '$x2 $x4 $x2 $x3',
  '&:last-child': {
    textAlign: 'left',
  },
});

export const StyledHeaderRow = styled(Table.Row, {
  borderTop: '2px solid $black10',
  '& > th': {
    alignContent: 'center',
    lineHeight: '$scale4',
    p: {
      fontWeight: '$semiBold',
    },
  },
  '& > th:nth-child(1)': {
    width: '50px',
    padding: '10px 9px 10px $x3 !important',
  },
  '& > th:nth-child(3)': {
    width: '205px',
  },
  '& > th:not(:first-child)': {
    padding: '19px 9px 19px $x3 !important',
  },
  '& th:first-child > p': {
    height: '34px',
  },
  '& th:not(:first-child) > p': {
    height: '16px',
  },
});

export const StyledBodyRow = styled(Table.Row, {
  '& > td': {
    backgroundColor: 'unset',
    border: 'unset',
  },
  '& > td:nth-child(1)': {
    padding: '$x4 $x8 $x4 $x4 !important',
    '& > div': {
      height: '$x5',
      width: '$x6',
    },
  },
  '& > td:nth-child(2)': {
    padding: '18px $x4 18px $x3 !important',
    '& > p': {
      height: '18px',
    },
  },
  '& > td:nth-child(3)': {
    padding: '$x4 0 $x4 $x3 !important',
    justifyItems: 'baseline',
    '& > div': {
      height: '22px',
      width: '138px',
    },
  },
});

export const StyledRadioButtonGroup = styled(RadioButtonGroup, {
  // parent div of the radio button
  '& div': {
    width: '$x6',
  },
});

const EMPTY_STATE_HEIGHT = 336;

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: EMPTY_STATE_HEIGHT,
};
