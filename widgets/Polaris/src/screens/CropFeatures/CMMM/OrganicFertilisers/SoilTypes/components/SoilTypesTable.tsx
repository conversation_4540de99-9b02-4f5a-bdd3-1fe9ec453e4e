import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { cloneDeep } from 'lodash';
import { Caption, Table } from '@yaradigitallabs/ahua-react';
import { flexRender } from '@tanstack/react-table';
import { useTableConfig, useUpdateOrganicFertiliserConfiguration } from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import {
  OrganicFertiliserSoilType,
  OrganicFertiliserSoilTypesConfiguration,
  SoilType,
} from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';
import TableSection from '@widgets/Polaris/src/components/Table/Table';
import { getColumnDefs } from '../utils/soilTypesUtils';
import TableBody from './TableBody';
import {
  StyledHeaderRow,
  TableContainer,
  EmptyStateAdditionalStyles,
} from './SoilTypesTable.styled';

interface SoilTypesTableProps {
  tPrefix: string;
  soilTypeConfigurationData: OrganicFertiliserSoilTypesConfiguration | undefined;
  setSoilTypeConfigurationData: React.Dispatch<OrganicFertiliserSoilTypesConfiguration>;
  soilTypesData: SoilType[] | undefined;
}

const SoilTypesTable: FC<SoilTypesTableProps> = ({
  tPrefix,
  soilTypeConfigurationData,
  setSoilTypeConfigurationData,
  soilTypesData,
}) => {
  const { t } = useTranslation();

  const {
    methods: { updateOrganicFertiliserConfigurations },
  } = useAppContext();

  const { trigger: updateSoilTypeConfiguration } = useUpdateOrganicFertiliserConfiguration(
    soilTypeConfigurationData?.id,
  );

  const { table, paginationState, autoResetPageIndexRef } =
    useTableConfig<OrganicFertiliserSoilType>(
      soilTypeConfigurationData?.configuration.data.soilTypes || [],
      () => getColumnDefs({ t, keyPrefix: tPrefix }),
      { pageIndex: 0, pageSize: 5 },
    );

  // Set the current of autoResetPageIndexRef to prevent
  // page refreshing back to first page on update
  autoResetPageIndexRef.current = false;

  const handleRadioButtonUpdate = async (data: OrganicFertiliserSoilType) => {
    if (!soilTypeConfigurationData) {
      return;
    }

    const clonedSoilTypeConfigurations = cloneDeep(soilTypeConfigurationData);
    const {
      configuration: {
        data: { soilTypes },
      },
    } = clonedSoilTypeConfigurations;

    const currentDefaultSoilType = soilTypes.find((soilType) => soilType.isDefault);
    const newDefaultSoilType = soilTypes.find(
      (soilType) => soilType.soilTypeId === data?.soilTypeId,
    );

    if (!currentDefaultSoilType || !newDefaultSoilType) {
      return;
    }

    currentDefaultSoilType.isDefault = false;
    newDefaultSoilType.isDefault = true;

    try {
      const result = await updateSoilTypeConfiguration({
        method: METHOD.PUT,
        body: JSON.stringify(clonedSoilTypeConfigurations),
      });

      if (result) {
        setSoilTypeConfigurationData(clonedSoilTypeConfigurations);
        updateOrganicFertiliserConfigurations(clonedSoilTypeConfigurations);
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  const toggleSwitch = async (data: OrganicFertiliserSoilType) => {
    if (!soilTypeConfigurationData) {
      return;
    }

    const clonedSoilTypeConfigurations = cloneDeep(soilTypeConfigurationData);
    const {
      configuration: {
        data: { soilTypes },
      },
    } = clonedSoilTypeConfigurations;

    const soilTypeToUpdate = soilTypes.find((soilType) => soilType.soilTypeId === data?.soilTypeId);

    if (!soilTypeToUpdate) {
      return;
    }

    soilTypeToUpdate.isVisibleToUser = !soilTypeToUpdate?.isVisibleToUser;

    try {
      const result = await updateSoilTypeConfiguration({
        method: METHOD.PUT,
        body: JSON.stringify(clonedSoilTypeConfigurations),
      });
      if (result) {
        setSoilTypeConfigurationData(clonedSoilTypeConfigurations);
        updateOrganicFertiliserConfigurations(clonedSoilTypeConfigurations);
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  return (
    <TableContainer>
      <TableSection
        showPagination={!!soilTypesData && table.getFilteredRowModel().rows?.length !== 0}
        tableHeader={
          <StyledHeaderRow data-cy='soil-types-table-header-row'>
            {table.getHeaderGroups().map((headerGroup) =>
              headerGroup.headers.map((header) => {
                return (
                  <Table.Head key={header.id}>
                    <Caption>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </Caption>
                  </Table.Head>
                );
              }),
            )}
          </StyledHeaderRow>
        }
        tableBody={
          soilTypesData && (
            <TableBody
              table={table}
              soilTypesData={soilTypesData}
              handleRadioButtonUpdate={handleRadioButtonUpdate}
              toggleSwitch={toggleSwitch}
              tPrefix={tPrefix}
            />
          )
        }
        pageCount={table.getPageCount()}
        pageIndex={paginationState.pageIndex}
        canNextPage={table.getCanNextPage()}
        canPreviousPage={table.getCanPreviousPage()}
        onNextPage={() => table.nextPage()}
        onPrevPage={() => table.previousPage()}
        onLastPage={() => table.lastPage()}
        onFirstPage={() => table.firstPage()}
        variant={'dynamic'}
      />

      {(!soilTypesData || table.getFilteredRowModel().rows?.length === 0) && (
        <EmptyStateComponent
          dataCy='soil-types-empty-state'
          styles={{ additionalStyles: EmptyStateAdditionalStyles }}
          message={t(`${tPrefix}.emptyStateText`)}
        />
      )}
    </TableContainer>
  );
};

export default SoilTypesTable;
