import React from 'react';
import { act, fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import {
  cerealOrganicFertiliserConfigurationHandler,
  updateCerealOrganicFertiliserSoilTypeConfiugrationHandler,
  getSoilTypesHandler,
  mockAppProviderValue,
} from '@common/mocks';
import SoilTypes from '../SoilTypes';

const server = setupServer(
  getSoilTypesHandler,
  cerealOrganicFertiliserConfigurationHandler,
  updateCerealOrganicFertiliserSoilTypeConfiugrationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('Soil Types', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('match snapshot', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <SoilTypes />
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });

  it('render table when there are soil types and configurations', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <SoilTypes />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(screen.getByTestId('table-section')).toBeInTheDocument();
    expect(screen.getByTestId('soil-types-collapsible')).toBeInTheDocument();
    expect(screen.getByTestId('soil-types-header-title')).toBeInTheDocument();
    expect(screen.getByTestId('soil-types-header-subtitle')).toBeInTheDocument();
    expect(screen.getByTestId('soil-types-table-header-row')).toBeInTheDocument();
    expect(screen.queryByTestId('soil-types-empty-state')).not.toBeInTheDocument();

    const rows = screen.getAllByTestId('soil-types-table-body-row');
    const textCells = screen.getAllByTestId('soil-types-id-text');
    const radioButtons = screen.getAllByTestId('soil-types-is-default-radio-button');
    const switchButtons = screen.getAllByTestId('soil-types-action-switch-button');
    expect(rows[0]).toBeInTheDocument();
    expect(rows.length).toBe(5);
    expect(textCells[0]).toBeInTheDocument();
    expect(textCells.length).toBe(5);
    expect(screen.getByText('Sandy loam')).toBeInTheDocument();

    expect(radioButtons[0]).toBeInTheDocument();
    expect(radioButtons.length).toBe(5);
    expect(switchButtons[0]).toBeInTheDocument();
    expect(switchButtons.length).toBe(5);
  });

  it('render table and page navigation works', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <SoilTypes />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const firstPageButton = screen.getByLabelText('Go to first page');
    const lastPageButton = screen.getByLabelText('Go to last page');
    const nextPageButton = screen.getByLabelText('Go to next page');
    const previousPageButton = screen.getByLabelText('Go to previous page');
    const firstPageRows = screen.getAllByTestId('soil-types-table-body-row');

    expect(firstPageButton).toBeInTheDocument();
    expect(lastPageButton).toBeInTheDocument();
    expect(previousPageButton).toBeInTheDocument();
    expect(nextPageButton).toBeInTheDocument();
    expect(firstPageRows.length).toBe(5);

    fireEvent.click(nextPageButton);
    expect(screen.getAllByTestId('soil-types-table-body-row').length).toBe(1);

    fireEvent.click(previousPageButton);
    expect(screen.getAllByTestId('soil-types-table-body-row').length).toBe(5);

    fireEvent.click(lastPageButton);
    expect(screen.getAllByTestId('soil-types-table-body-row').length).toBe(1);
    fireEvent.click(firstPageButton);
    expect(screen.getAllByTestId('soil-types-table-body-row').length).toBe(5);
  });

  it('render empty table state when there are no configurations', async () => {
    const mockAppProviderValueWithoutConfigurations = {
      ...mockAppProviderValue,
      organicFertiliserConfigurations: [],
    };
    render(
      <AppContext.Provider value={mockAppProviderValueWithoutConfigurations}>
        <NavbarProvider>
          <Router>
            <SoilTypes />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const emptyStateComp = screen.getByTestId('soil-types-empty-state');
    expect(emptyStateComp).toBeInTheDocument();

    const emptyStateIcon = within(emptyStateComp).getAllByTestId('empty-state-info-icon');
    expect(emptyStateIcon[0]).toBeInTheDocument();

    expect(screen.queryByLabelText('Go to next page')).not.toBeInTheDocument();
  });

  it('update radio button on configuration are reflected', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <SoilTypes />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    let radioButtons = screen.getAllByTestId('soil-types-is-default-radio-button');
    expect(radioButtons[1]).toBeInTheDocument();
    expect(radioButtons[0]).toBeChecked();
    expect(radioButtons[1]).not.toBeChecked();
    fireEvent.click(radioButtons[1]);

    await waitFor(() => {
      radioButtons = screen.getAllByTestId('soil-types-is-default-radio-button');
      expect(radioButtons[0]).not.toBeChecked();
      expect(radioButtons[1]).toBeChecked();
    });
  });

  it('update switch toggle on configuration are reflected', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <SoilTypes />
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    let switchToggles = screen.getAllByTestId('soil-types-action-switch-button');
    expect(switchToggles[0]).toBeInTheDocument();
    expect(switchToggles[0]).not.toBeChecked();
    fireEvent.click(switchToggles[0]);

    await waitFor(() => {
      switchToggles = screen.getAllByTestId('soil-types-action-switch-button');
      expect(switchToggles[0]).toBeChecked();
    });
  });
});
