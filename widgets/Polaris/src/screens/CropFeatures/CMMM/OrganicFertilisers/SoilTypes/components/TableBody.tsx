import React from 'react';
import { useTranslation } from 'react-i18next';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { Caption, RadioButton, Switch } from '@yaradigitallabs/ahua-react';
import { StyledBodyRow, TableCell, StyledRadioButtonGroup } from './SoilTypesTable.styled';
import { SoilType, OrganicFertiliserSoilType } from '@common/types';

enum SoilTypeDataKeys {
  IsDefault = 'isDefault',
  SoilTypeId = 'soilTypeId',
  IsVisibleToUser = 'isVisibleToUser',
}

interface TableBodyProps {
  table: TanstackTable<OrganicFertiliserSoilType>;
  soilTypesData: SoilType[];
  handleRadioButtonUpdate: (data: OrganicFertiliserSoilType) => void;
  toggleSwitch: (data: OrganicFertiliserSoilType) => void;
  tPrefix: string;
}

const TableBody = ({
  table,
  soilTypesData,
  handleRadioButtonUpdate,
  toggleSwitch,
  tPrefix,
}: TableBodyProps) => {
  const { t } = useTranslation();

  return table.getRowModel().rows.map((row, i) => (
    <StyledBodyRow key={row.id + i} data-cy='soil-types-table-body-row'>
      <TableCell key={SoilTypeDataKeys.IsDefault + i}>
        <StyledRadioButtonGroup>
          <RadioButton
            concept='success'
            aria-label='isDefault'
            checked={row.original.isDefault}
            onClick={() => handleRadioButtonUpdate(row.original)}
            data-cy='soil-types-is-default-radio-button'
          />
        </StyledRadioButtonGroup>
      </TableCell>
      <TableCell key={SoilTypeDataKeys.SoilTypeId + i} data-cy='soil-types-id-text'>
        <Caption>
          {t(
            soilTypesData?.find((soilType) => soilType.id === row.original.soilTypeId)?.name || '',
          )}
        </Caption>
      </TableCell>
      <TableCell key={SoilTypeDataKeys.IsVisibleToUser + i}>
        <Switch
          css={{
            cursor: 'pointer',
            width: '42px',
          }}
          data-cy='soil-types-action-switch-button'
          label={t(`${tPrefix}.table.actionContentCell`)}
          labelPosition='right'
          checked={row.original.isVisibleToUser}
          onClick={() => toggleSwitch(row.original)}
        />
      </TableCell>
    </StyledBodyRow>
  ));
};

export default TableBody;
