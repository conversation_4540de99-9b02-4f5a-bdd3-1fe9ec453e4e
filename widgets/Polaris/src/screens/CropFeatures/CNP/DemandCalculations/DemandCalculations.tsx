import React, { FC } from 'react';
import { DemandCalculationsEmptyState } from '../../shared/DemandCalculationsRenderer/DemandCalculationsEmptyState/DemandCalculationsEmptyState';

interface DemandCalculations {
  setActiveTab: (tab: string) => void;
}

const DemandCalculations: FC<DemandCalculations> = ({ setActiveTab }) => {
  return (
    <>
      <DemandCalculationsEmptyState setActiveTab={setActiveTab} />
    </>
  );
};

export default DemandCalculations;
