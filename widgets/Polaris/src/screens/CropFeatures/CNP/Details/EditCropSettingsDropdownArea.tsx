import { Select } from '@yaradigitallabs/ahua-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { SelectWrapper } from '@widgets/Polaris/src/components/Select/SelectWrapper';
import { CanModifyGrowthScaleResponse, GrowthScale, BaseUnit } from '@common/types';

type SingleUnitSelection =
  | {
      value: string;
      text: string;
    }
  | undefined;

interface CNPCropSettingsDropdown {
  selectedYieldUnit: BaseUnit | null;
  growthScale: GrowthScale | null;
  selectedUnitSolid: BaseUnit | null;
  yieldAndSolidsUnit: SingleUnitSelection[] | undefined;
  growthScales: SingleUnitSelection[] | undefined;
  demandUnits: SingleUnitSelection[] | undefined;
  onYieldUnitChange: (value: string) => void;
  onGrowthScaleChange: (value: string) => void;
  ondUnitSolidChange: (value: string) => void;
  canModifyResponse: CanModifyGrowthScaleResponse;
  growthScalesChangeDisabled: boolean | undefined;
}

const CNPEditCropSettingsDropdownArea: React.FC<CNPCropSettingsDropdown> = ({
  selectedYieldUnit,
  selectedUnitSolid,
  growthScale,
  demandUnits,
  yieldAndSolidsUnit,
  growthScales,
  onYieldUnitChange,
  onGrowthScaleChange,
  ondUnitSolidChange,

  canModifyResponse,
  growthScalesChangeDisabled,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <SelectWrapper dataCy='growth-scale-dropdown'>
        <Select
          ariaLabel={t(`polaris.cnpDetails.cropSettings.growthScaleLabel`)}
          cover={!canModifyResponse.success || growthScalesChangeDisabled ? 'fill' : 'outline'}
          css={{ width: '100%' }}
          value={
            growthScale && canModifyResponse.success && !growthScalesChangeDisabled
              ? growthScale?.id
              : null
          }
          items={growthScales || []}
          placeholder={
            !canModifyResponse.success || growthScalesChangeDisabled
              ? growthScale?.name
              : t(`polaris.cnpDetails.cropSettings.growthScaleLabel`)
          }
          position='popper'
          size='n'
          helper-text={
            growthScalesChangeDisabled
              ? t(`polaris.cnpDetails.cropSettings.growthScaleHelperText`)
              : !canModifyResponse.success
              ? canModifyResponse.message
              : null
          }
          disabled={!canModifyResponse.success || growthScalesChangeDisabled}
          onChange={onGrowthScaleChange}
          label={t(`polaris.cnpDetails.cropSettings.growthScaleLabel`)}
        />
      </SelectWrapper>

      <SelectWrapper dataCy='yield-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.cnpDetails.cropSettings.yieldUnit`)}
          css={{ width: '100%' }}
          value={selectedYieldUnit ? selectedYieldUnit?.id : null}
          items={yieldAndSolidsUnit || []}
          placeholder={t(`polaris.cnpDetails.cropSettings.yieldUnit`)}
          position='popper'
          size='n'
          onChange={onYieldUnitChange}
          label={t(`polaris.cnpDetails.cropSettings.yieldUnit`)}
        />
      </SelectWrapper>

      <SelectWrapper dataCy='recommendation-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.cnpDetails.cropSettings.recommendationUnit`)}
          css={{ width: '100%' }}
          value={selectedUnitSolid ? selectedUnitSolid?.id : null}
          items={demandUnits || []}
          placeholder={t(`polaris.cnpDetails.cropSettings.recommendationUnit`)}
          position='popper'
          size='n'
          onChange={ondUnitSolidChange}
          label={t(`polaris.cnpDetails.cropSettings.recommendationUnit`)}
        />
      </SelectWrapper>
    </>
  );
};

export default CNPEditCropSettingsDropdownArea;
