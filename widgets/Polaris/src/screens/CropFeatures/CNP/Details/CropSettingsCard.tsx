import React from 'react';
import { Card, Label, Select, Stack, Status, Caption } from '@yaradigitallabs/ahua-react';
import { CropSettingsCNPCardLogic } from '@polaris-hooks/index';
import { CropRegion } from '@common/types';
import { useTranslation } from 'react-i18next';
import { SelectWrapper } from '@widgets/Polaris/src/components/Select/SelectWrapper';
import {
  EditButtonWrapper,
  StyledCropSettingsCardBody,
  StyledCropSettingsCardTail,
} from '@widgets/Polaris/styles/Polaris/screens/NPDetails/page-details-crop-settings-edit.styled';

interface CropSettingsCardProps {
  cropRegion: CropRegion | null;
  setIsEditCropSettingOpened: React.Dispatch<boolean>;
}

const CropSettingsCard: React.FC<CropSettingsCardProps> = ({
  cropRegion,
  setIsEditCropSettingOpened,
}) => {
  const { t } = useTranslation();

  const {
    growthScaleItems,
    yieldItems,
    recommendationItems,

    growthScaleValue,
    yieldValue,
    recommendationValue,
  } = CropSettingsCNPCardLogic({ cropRegion });

  return (
    <Card data-cy='crop-settings-card'>
      <div className='card-header'>
        <Card.Head
          data-cy='crop-settings-card-header'
          title={t(`polaris.cnpDetails.cropSettings.title`)}
        >
          <Card.HeadActions>
            <EditButtonWrapper
              onClick={() => setIsEditCropSettingOpened(true)}
              variant='ghost'
              title={t(`polaris.cnpDetails.cropSettings.editButton`)}
              iconLeading='Edit'
              type='button'
              size='xs'
              data-cy='crop-settings-edit-button'
            >
              {t(`polaris.cnpDetails.cropSettings.editButton`)}
            </EditButtonWrapper>
          </Card.HeadActions>
        </Card.Head>
      </div>
      <Card.Divider />
      <Card.Content css={{ padding: '0' }}>
        <StyledCropSettingsCardBody
          data-cy='crop-settings-card-body'
          css={{ height: '87px', paddingLeft: '0 !important' }}
          orientation='horizontal'
        >
          <SelectWrapper dataCy='growth-scale-select'>
            <Select
              label={t(`polaris.cnpDetails.cropSettings.growthScale`)}
              ariaLabel='Growth scale select element'
              items={growthScaleItems}
              value={growthScaleValue}
              readOnly
              size={'s'}
            />
          </SelectWrapper>
          <SelectWrapper dataCy='yield-unit-select'>
            <Select
              label={t(`polaris.cnpDetails.cropSettings.yieldUnit`)}
              ariaLabel='Yield unit select element'
              items={yieldItems}
              value={yieldValue}
              readOnly
              size={'s'}
            />
          </SelectWrapper>
          <SelectWrapper dataCy='demand-unit-select'>
            <Select
              label={t(`polaris.cnpDetails.cropSettings.recommendationUnit`)}
              ariaLabel='Recommendation unit select element'
              items={recommendationItems}
              value={recommendationValue}
              readOnly
              size={'s'}
            />
          </SelectWrapper>
        </StyledCropSettingsCardBody>
      </Card.Content>
      <Card.Divider />
      <StyledCropSettingsCardTail orientation='vertical'>
        <Label data-cy='crop-setting-partners-title' size='xs' className='partners-title'>
          {t(`polaris.cnpDetails.cropSettings.partnersTitle`)}
        </Label>
        <Stack className='stack' direction='horizontal' gap='$x2'>
          {cropRegion?.tagsConfiguration.partnerTags?.map((tag) => (
            <Status
              key={tag.id}
              data-cy={`${tag.name}-status`}
              size='s'
              variant='neutral'
              className='stack-status'
            >
              {tag.name}
            </Status>
          ))}
          {cropRegion?.tagsConfiguration.partnerTags?.length === 0 && (
            <Caption
              className='partners-caption'
              size='s'
              variant='inverse'
              data-cy='crop-setting-partners-caption'
            >
              {t(`polaris.cnpDetails.cropSettings.partnersCaption`)}
            </Caption>
          )}
        </Stack>
      </StyledCropSettingsCardTail>
    </Card>
  );
};

export default CropSettingsCard;
