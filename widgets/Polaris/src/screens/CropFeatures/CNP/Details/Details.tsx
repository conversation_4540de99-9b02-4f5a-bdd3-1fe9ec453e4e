import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useNavigate } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import {
  Card,
  Label,
  Title,
  Stack,
  Status,
  IconButton,
  Tooltip,
} from '@yaradigitallabs/ahua-react';
import '../../../../../styles/Polaris/screens/NPDetails/_page-details-configure-nav.scss';
import {
  useFetchCNPPlanValidations,
  filterUnitsByTag,
  useSetNavbar,
  useGetGrowthScales,
  useUpdatePlanValidations,
} from '@polaris-hooks/index';
import { MMMValidationUpdateResult, PlanValidation, ValidationStatus } from '@common/types';
import { ROUTES } from '@src/routes';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { useFetchCNPPartners } from '@polaris-hooks/index';
import {
  DetailsContainer,
  CardHeadWrapper,
  StyledCardTail,
} from '@widgets/Polaris/styles/Polaris/screens/NPDetails/PageDetails.styled';
import CropSettingsCard from './CropSettingsCard';
import { getUpdateDetails, formatDateString, determineNewStatus } from '@widgets/Polaris/utils';
import { METHOD, PLAN_NAME_PARAM, UNIT_TAGS } from '@common/constants';
import {
  SoilValidationSwitch as PlanValidationSwitch,
  EditCropSettings,
} from '../../../../components';
import { getTypedValidationStatus } from './utils/CNPDetailsUtils';

const CNPDetails = () => {
  const { t } = useTranslation();
  const [isEditCropSettingOpened, setIsEditCropSettingOpened] = useState(false);
  const navigate = useNavigate();
  const [localPlanValidation, setLocalPlanValidation] = useState<PlanValidation | undefined>();

  const {
    selectedFeature,
    selectedCropDescription,
    selectedCrop,
    selectedRegion,
    selectedCountry,
    selectedCountryUnits,
    cropRegion,
    selectedPlanValidation,
    methods: { setPlanValidation, setCropRegion, setSelectedPlanConfigTab },
  } = useAppContext();

  useSetNavbar(
    generatePath(`${ROUTES.cropFeatures}/${ROUTES.cropNutritionPlan}`, {
      cropRegionId: cropRegion?.id || '',
    }),
    true,
    'cnpDetails',
  );

  const { user } = useAuth0();
  const planValidations = useFetchCNPPlanValidations(cropRegion?.id, selectedCountry?.id);
  const filteredYieldUnits = filterUnitsByTag(selectedCountryUnits, UNIT_TAGS.YIELD_UNIT);
  const filteredDemandUnits = filterUnitsByTag(selectedCountryUnits, UNIT_TAGS.DEMAND_UNIT);
  const { data: growthScalesData } = useGetGrowthScales();
  const currentPlanValidation = planValidations?.[0];

  // nutrient-planning service call to check for CNP plan validations,
  // if a CNP plan is validated, we cannot edit Growth Scales in a Crop Region
  const cnpPlanValidations = useFetchCNPPlanValidations(cropRegion?.id, selectedCountry?.id);
  const isCNPPlanValidated =
    cnpPlanValidations?.[0]?.soilAnalysisStatus === ValidationStatus.VALIDATED;

  const { tags, isLoading } = useFetchCNPPartners(cropRegion?.id, 0);

  const handlePlanConfigurationNavigation = (planName: string) => {
    //Clear the previously selected tab in Plan Validation
    setSelectedPlanConfigTab(null);
    //navigate to the specific plan configuration
    const path = generatePath(`${ROUTES.cropFeatures}/${ROUTES.nutritionPlanConfiguration}`, {
      planName,
      cropRegionId: cropRegion?.id || '',
    });
    navigate(path, { state: { planName } });
  };

  const renderingPlanValidation = selectedPlanValidation || localPlanValidation;
  const isInitiallyValidated =
    currentPlanValidation?.soilAnalysisStatus === ValidationStatus.VALIDATED;

  const typedValidationStatus = getTypedValidationStatus(
    renderingPlanValidation?.soilAnalysisStatus || '',
  );
  const updateDetails = getUpdateDetails(
    renderingPlanValidation,
    typedValidationStatus,
    formatDateString,
    t,
  );

  useEffect(() => {
    setLocalPlanValidation(currentPlanValidation);
  }, [currentPlanValidation]);

  const { trigger: triggerPlanValidationUpdate } = useUpdatePlanValidations(
    currentPlanValidation?.id,
  );
  const planValidationUpdated = useCallback(async () => {
    const result: MMMValidationUpdateResult = {
      isSuccessful: false,
      errorLocations: [],
    };

    if (!renderingPlanValidation) {
      return result;
    }

    const newStatus = typedValidationStatus && determineNewStatus(typedValidationStatus);
    const currentTimestamp = new Date().toISOString();
    const body = JSON.stringify({
      ...renderingPlanValidation,
      soilAnalysisStatus: newStatus,
      modified: currentTimestamp,
      modifiedBy: user?.email,
    });

    try {
      const response = await triggerPlanValidationUpdate({
        method: METHOD.PUT,
        body: body,
      });
      if (response && typeof response === 'object' && !Array.isArray(response)) {
        setPlanValidation(response);
        setLocalPlanValidation(response);
      }

      result.isSuccessful = true;
      return result;
    } catch (error) {
      console.error('Failed to update plan validation:', error);
      return result;
    }
  }, [renderingPlanValidation, triggerPlanValidationUpdate, user?.email]);

  const soilValidationSwitch = (
    <Card.Head title={t(`polaris.cnpDetails.soilNutritionPlan.title`)} caption={updateDetails}>
      <PlanValidationSwitch
        onTogglePlanValidation={planValidationUpdated}
        isInitiallyActive={isInitiallyValidated}
        canToggle={
          !!renderingPlanValidation &&
          renderingPlanValidation.soilAnalysisStatus !== ValidationStatus.NOT_SET
        }
        featureTransKeyPrefix='cnpDetails.soilValidation'
      />
    </Card.Head>
  );

  const soilValidationCard = (
    <Card data-cy='soil-nutrition-card'>
      <CardHeadWrapper className='card-header'>
        {planValidations && planValidations.length > 0 ? (
          <Tooltip text={updateDetails} concept='inverse' data-cy='soil-nutrition-card-tooltip'>
            {soilValidationSwitch}
          </Tooltip>
        ) : (
          soilValidationSwitch
        )}
      </CardHeadWrapper>

      <Card.Divider />

      <Card.Body className='plan-details-configure-nav-content'>
        <Card.Content
          title={t(`polaris.cnpDetails.soilNutritionPlan.content.title`)}
          description={t(`polaris.cnpDetails.soilNutritionPlan.content.description`)}
        />
        <Card.Content
          title={t(`polaris.cnpDetails.soilNutritionPlan.partners.title`)}
          description={t(`polaris.cnpDetails.soilNutritionPlan.partners.description`)}
        />
        <Stack
          data-cy='soil-nutrition-card-stack'
          css={{ paddingTop: 0 }}
          direction='horizontal'
          gap='8px'
        >
          {!isLoading &&
            tags.map((tag) => (
              <Status
                key={tag.id}
                data-cy={`partner-tag-${tag.name}`}
                size='s'
                variant='neutral'
                className='stack-status'
              >
                {tag.name}
              </Status>
            ))}
        </Stack>
      </Card.Body>

      <Card.Divider />

      <StyledCardTail>
        <Card.Action
          data-cy='soil-nutrition-card-test-button'
          label={t(`polaris.cnpDetails.soilNutritionPlan.buttons.test`)}
          variant={'outline'}
        />
        <Card.Action
          data-cy='soil-nutrition-card-configure-button'
          label={t(`polaris.cnpDetails.soilNutritionPlan.buttons.configure`)}
          variant={'primary'}
          onClick={() => handlePlanConfigurationNavigation(PLAN_NAME_PARAM.CNP_SOIL)}
        />
      </StyledCardTail>
    </Card>
  );

  const leafValidationSwitch = (
    <Card.Head title={t(`polaris.cnpDetails.leafNutritionPlan.title`)} caption={'--'}>
      <PlanValidationSwitch
        onTogglePlanValidation={async () => {
          console.log('TBI');
          return {
            isSuccessful: false,
            errorLocations: [],
          };
        }}
        isInitiallyActive={false}
        canToggle={false}
        featureTransKeyPrefix='cnpDetails.leafNutritionPlan'
      />
    </Card.Head>
  );

  const leafValidationCard = (
    <Card data-cy='leaf-nutrition-card' css={{ display: 'flex', flexDirection: 'column' }}>
      <CardHeadWrapper className='card-header'>{leafValidationSwitch}</CardHeadWrapper>

      <Card.Divider />

      <Card.Body
        css={{
          flexGrow: '1',
          alignItems: 'center',
          paddingLeft: '$x3',
        }}
        orientation='horizontal'
        data-cy='leaf-nutrition-card-body'
      >
        <IconButton
          css={{ height: '$x4', paddingLeft: '$x2 !important' }}
          size='xs'
          icon='Info'
          colorConcept='neutral'
          data-cy='leaf-nutrition-card-info-button'
        ></IconButton>
        <Label
          data-cy='leaf-nutrition-card-helper-text'
          size='xs'
          css={{ color: '$colors$black60' }}
        >
          {' '}
          {t(`polaris.cnpDetails.leafNutritionPlan.content.helperText`)}
        </Label>
      </Card.Body>

      <Card.Divider />

      <StyledCardTail>
        <Card.Action
          label={t(`polaris.cnpDetails.leafNutritionPlan.buttons.test`)}
          variant={'outline'}
          disabled
          data-cy='left-nutrition-card-test-button'
        />
        <Card.Action
          label={t(`polaris.cnpDetails.leafNutritionPlan.buttons.configure`)}
          variant={'primary'}
          disabled
          data-cy='leaf-nutrition-card-test-button'
          onClick={() => handlePlanConfigurationNavigation(PLAN_NAME_PARAM.CNP_LEAF)}
        />
      </StyledCardTail>
    </Card>
  );

  return (
    <DetailsContainer data-cy='crop-nutrition-details-content'>
      <Title data-cy='cnp-details-title' size='n' css={{ paddingBottom: '$x4' }}>
        {t(`polaris.cnpDetails.title`, {
          defaultValue: 'Polaris',
          selectedCropSubclassName: selectedCrop?.name,
          selectedCropDescriptionName: selectedCropDescription?.name,
          selectedFeatureName: selectedFeature?.name,
          selectedRegionName: selectedRegion?.name,
          selectedCountryName: selectedCountry?.name,
          interpolation: { escapeValue: false },
        })}
      </Title>

      <CropSettingsCard
        cropRegion={cropRegion}
        setIsEditCropSettingOpened={setIsEditCropSettingOpened}
      />

      <Stack css={{ paddingLeft: '0px', paddingTop: '$x6' }} direction='horizontal' gap='22px'>
        {soilValidationCard}

        {leafValidationCard}
      </Stack>

      <EditCropSettings
        dataPrefix='cnp'
        selectedFeature={selectedFeature}
        selectedCropRegion={cropRegion}
        selectedCrop={selectedCrop}
        selectedCountry={selectedCountry}
        selectedRegion={selectedRegion}
        setCropRegion={setCropRegion}
        selectedCropDescription={selectedCropDescription}
        isEditCropSettingOpened={isEditCropSettingOpened}
        setIsEditCropSettingOpened={setIsEditCropSettingOpened}
        yieldUnitsData={filteredYieldUnits}
        demandUnitsData={filteredDemandUnits}
        growthScalesData={growthScalesData}
        growthScalesChangeDisabled={isCNPPlanValidated}
      />
    </DetailsContainer>
  );
};

export default CNPDetails;
