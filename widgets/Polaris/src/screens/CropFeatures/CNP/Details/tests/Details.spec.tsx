/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  features<PERSON><PERSON><PERSON>,
  cropRegionsHandler,
  cnpPartnersHandler,
  planValidationHandler,
  updatedPlanValidationsHandler,
  growthScalesHandler,
  yieldSolidUnitsHandler,
  updateCropRegionHandler,
  canModifyGrowthScaleHandler,
  unitCountriesHandler,
  mockAppProviderValue,
  snackbarInitialStateMock,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import { NavbarProvider } from '@libs/nav-context';
import CNPDetails from '../Details';
import { urlMapPartners } from '@widgets/Polaris/src/screens/Home';
import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';
const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
  planValidationHandler,
  updatedPlanValidationsHandler,
  growthScalesHandler,
  yieldSolidUnitsHandler,
  updateCropRegionHandler,
  canModifyGrowthScaleHandler,
  unitCountriesHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('widget: Home', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  const mockSetDisplaySnackbar = jest.fn();

  it('should initialize the context with default values and render the CNP Details page', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CNPDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();

    expect(component.getByTestId('crop-nutrition-details-content')).toBeInTheDocument();
    expect(component.getByTestId('crop-settings-card')).toBeInTheDocument();
    expect(component.getByTestId('soil-nutrition-card')).toBeInTheDocument();
    expect(component.getByTestId('leaf-nutrition-card')).toBeInTheDocument();
  });

  it('should show EditCropSetting popup after clicking on edit button', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CNPDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const editButton = component.getByTestId('crop-settings-edit-button');
    expect(editButton).toBeInTheDocument();
    fireEvent.click(editButton);
    waitFor(() => {
      expect(component.getByTestId('cnp-edit-popup-container')).toBeInTheDocument();
    });
  });

  it('should switch state when CNP plan validate switch is clicked', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {(_context) => (
                <SnackbarContext.Provider
                  value={{
                    displaySnackbar: snackbarInitialStateMock,
                    setDisplaySnackbar: mockSetDisplaySnackbar,
                  }}
                >
                  <CNPDetails />
                </SnackbarContext.Provider>
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const switchBtn = component.getAllByTestId('soil-validation-switch-btn');
    expect(switchBtn[0]).toHaveAttribute('data-state', 'unchecked');
    fireEvent.click(switchBtn[0]);
    waitFor(() => {
      expect(switchBtn[0]).toHaveAttribute('data-state', 'checked');
    });
  });
});

describe('urlMapPartners', () => {
  beforeEach(() => {
    process.env.POLARIS_API = 'https://example.com/api';
  });

  it('returns empty array if no partners', () => {
    expect(urlMapPartners(undefined)).toEqual([]);
  });
  it('should return an empty array when input is an empty array', () => {
    expect(urlMapPartners([])).toEqual([]);
  });

  test('returns array of URLs mapped from partner IDs', () => {
    const partners = ['partner1, partner2, partner3'];
    const partnersArray = partners.flatMap((el) => el.split(', '));
    const expectedUrls = partnersArray.map(
      (partnerId) => `https://example.com/api/partner-tags/${partnerId}`,
    );
    const result = urlMapPartners(partners);
    expect(result).toEqual(expectedUrls);
  });
});
