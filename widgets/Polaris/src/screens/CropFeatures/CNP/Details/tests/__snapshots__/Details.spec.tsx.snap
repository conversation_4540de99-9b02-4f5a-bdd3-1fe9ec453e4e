// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: Home should initialize the context with default values and render the CNP Details page 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-hAzCDl"
        data-cy="crop-nutrition-details-content"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ijZBIvl-css"
          data-cy="cnp-details-title"
        >
          polaris.cnpDetails.title
        </h1>
        <a
          class="c-wzBoY"
          data-cy="crop-settings-card"
        >
          <div
            class="card-header"
          >
            <div
              class="c-jCarvd c-jCarvd-ikfQhrm-css"
            >
              <div
                class="c-cVKzzi"
              >
                <div
                  class="c-cXFqtJ"
                >
                  <h1
                    class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                  >
                    polaris.cnpDetails.cropSettings.title
                  </h1>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                />
              </div>
              <div
                class="c-hcUxto"
              >
                <button
                  class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-kkaoVB"
                  data-cy="crop-settings-edit-button"
                  title="polaris.cnpDetails.cropSettings.editButton"
                  type="button"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                    />
                  </svg>
                  <span
                    class="c-iepcqn"
                  >
                    polaris.cnpDetails.cropSettings.editButton
                  </span>
                </button>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-hPExTj c-hPExTj-idtkdsG-css"
          >
            <div
              class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-kVBBIh-iivCToc-css"
              data-cy="crop-settings-card-body"
            >
              <div
                data-cy="growth-scale-select"
                style="display: inline-block; width: 100%;"
              >
                <div
                  class="c-jGFTiO c-jGFTiO-ubosY-state-default"
                >
                  <div
                    class="c-kFLrJl"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                    >
                      polaris.cnpDetails.cropSettings.growthScale
                    </label>
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                    >
                      -
                    </label>
                    <button
                      aria-autocomplete="none"
                      aria-controls="radix-:r0:"
                      aria-expanded="false"
                      aria-label="Growth scale select element"
                      class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                      data-state="closed"
                      dir="ltr"
                      role="combobox"
                      tabindex="0"
                      type="button"
                    >
                      <span
                        style="pointer-events: none;"
                      >
                        <div
                          class="c-fSebPZ"
                        >
                          <svg
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M9 4.5H6a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-11a3 3 0 0 0-3-3h-3m-6 0h6m-6 0v-2m0 2V6m6-1.5v-2m0 2V6m-3.557 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"
                            />
                          </svg>
                        </div>
                      </span>
                      <svg
                        aria-hidden="true"
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18 9.75l-5 6h-2l-5-6"
                        />
                      </svg>
                    </button>
                  </div>
                  <p
                    class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                  />
                </div>
              </div>
              <div
                data-cy="yield-unit-select"
                style="display: inline-block; width: 100%;"
              >
                <div
                  class="c-jGFTiO c-jGFTiO-ubosY-state-default"
                >
                  <div
                    class="c-kFLrJl"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                    >
                      polaris.cnpDetails.cropSettings.yieldUnit
                    </label>
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                    >
                      -
                    </label>
                    <button
                      aria-autocomplete="none"
                      aria-controls="radix-:r1:"
                      aria-expanded="false"
                      aria-label="Yield unit select element"
                      class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                      data-state="closed"
                      dir="ltr"
                      role="combobox"
                      tabindex="0"
                      type="button"
                    >
                      <span
                        style="pointer-events: none;"
                      >
                        <div
                          class="c-fSebPZ"
                        >
                          <svg
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                            />
                          </svg>
                        </div>
                      </span>
                      <svg
                        aria-hidden="true"
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18 9.75l-5 6h-2l-5-6"
                        />
                      </svg>
                    </button>
                  </div>
                  <p
                    class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                  />
                </div>
              </div>
              <div
                data-cy="demand-unit-select"
                style="display: inline-block; width: 100%;"
              >
                <div
                  class="c-jGFTiO c-jGFTiO-ubosY-state-default"
                >
                  <div
                    class="c-kFLrJl"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                    >
                      polaris.cnpDetails.cropSettings.recommendationUnit
                    </label>
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                    >
                      -
                    </label>
                    <button
                      aria-autocomplete="none"
                      aria-controls="radix-:r2:"
                      aria-expanded="false"
                      aria-label="Recommendation unit select element"
                      class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                      data-state="closed"
                      dir="ltr"
                      role="combobox"
                      tabindex="0"
                      type="button"
                    >
                      <span
                        style="pointer-events: none;"
                      >
                        <div
                          class="c-fSebPZ"
                        >
                          <svg
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M8 5a3 3 0 0 1-3 3m3-3a3 3 0 1 0-3 3m3-3h8M5 8v8m14-8a3 3 0 1 0-3-3m3 3a3 3 0 0 1-3-3m3 3v8m-3 3a3 3 0 1 0 3-3m-3 3a3 3 0 0 1 3-3m-3 3H8m0 0a3 3 0 1 1-3-3m3 3a3 3 0 0 0-3-3"
                            />
                          </svg>
                        </div>
                      </span>
                      <svg
                        aria-hidden="true"
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18 9.75l-5 6h-2l-5-6"
                        />
                      </svg>
                    </button>
                  </div>
                  <p
                    class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-iTKOFX-orientation-vertical c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-kdEBiP"
          >
            <label
              class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-ibPNjhd-css partners-title"
              data-cy="crop-setting-partners-title"
            >
              polaris.cnpDetails.cropSettings.partnersTitle
            </label>
            <div
              class="c-bfPkPS c-bfPkPS-iczWuCV-css stack"
            >
              <div
                class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s stack-status"
                data-cy="FFDP Services-status"
                variant="neutral"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                >
                  FFDP Services
                </label>
              </div>
              <div
                class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s stack-status"
                data-cy="GEOS-status"
                variant="neutral"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                >
                  GEOS
                </label>
              </div>
              <div
                class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s stack-status"
                data-cy="MyKWS-status"
                variant="neutral"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                >
                  MyKWS
                </label>
              </div>
            </div>
          </div>
        </a>
        <div
          class="c-bfPkPS c-bfPkPS-ibJIBTx-css"
        >
          <a
            class="c-wzBoY"
            data-cy="soil-nutrition-card"
          >
            <div
              class="c-ceYNSH card-header"
            >
              <div
                class="c-jCarvd c-jCarvd-ikfQhrm-css"
              >
                <div
                  class="c-cVKzzi"
                >
                  <div
                    class="c-cXFqtJ"
                  >
                    <h1
                      class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                    >
                      polaris.cnpDetails.soilNutritionPlan.title
                    </h1>
                  </div>
                  <p
                    class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                  >
                    --
                  </p>
                </div>
                <div
                  class="validate-switch"
                  data-state="closed"
                >
                  <div
                    class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-isXzsQ-css"
                    >
                      switchLabel
                    </label>
                    <button
                      aria-checked="false"
                      class="c-dyBzGm c-dyBzGm-ihXMnoP-css"
                      data-cy="soil-validation-switch-btn"
                      data-state="unchecked"
                      role="switch"
                      type="button"
                      value="on"
                    >
                      <span
                        class="c-bILIWL"
                        data-state="unchecked"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="c-fZEhOm"
            />
            <div
              class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical plan-details-configure-nav-content"
            >
              <div
                class="c-hPExTj"
              >
                <h1
                  class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
                >
                  polaris.cnpDetails.soilNutritionPlan.content.title
                </h1>
                <p
                  class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
                >
                  polaris.cnpDetails.soilNutritionPlan.content.description
                </p>
              </div>
              <div
                class="c-hPExTj"
              >
                <h1
                  class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
                >
                  polaris.cnpDetails.soilNutritionPlan.partners.title
                </h1>
                <p
                  class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
                >
                  polaris.cnpDetails.soilNutritionPlan.partners.description
                </p>
              </div>
              <div
                class="c-bfPkPS c-bfPkPS-idtVjeT-css"
              />
            </div>
            <div
              class="c-fZEhOm"
            />
            <div
              class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
            >
              <button
                class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-qswFQ-variant-outline c-hRrCwb-ibPNjhd-css"
                data-cy="soil-nutrition-card-test-button"
              >
                <span
                  class="c-iepcqn"
                >
                  polaris.cnpDetails.soilNutritionPlan.buttons.test
                </span>
              </button>
              <button
                class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary c-hRrCwb-ibPNjhd-css"
                data-cy="soil-nutrition-card-configure-button"
              >
                <span
                  class="c-iepcqn"
                >
                  polaris.cnpDetails.soilNutritionPlan.buttons.configure
                </span>
              </button>
            </div>
          </a>
          <a
            class="c-wzBoY c-wzBoY-ifixGjY-css"
            data-cy="leaf-nutrition-card"
          >
            <div
              class="c-ceYNSH card-header"
            >
              <div
                class="c-jCarvd c-jCarvd-ikfQhrm-css"
              >
                <div
                  class="c-cVKzzi"
                >
                  <div
                    class="c-cXFqtJ"
                  >
                    <h1
                      class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                    >
                      polaris.cnpDetails.leafNutritionPlan.title
                    </h1>
                  </div>
                  <p
                    class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                  >
                    --
                  </p>
                </div>
                <div
                  class="validate-switch"
                  data-state="closed"
                >
                  <div
                    class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-isXzsQ-css"
                    >
                      switchLabel
                    </label>
                    <button
                      aria-checked="false"
                      class="c-dyBzGm c-dyBzGm-ihXMnoP-css"
                      data-cy="soil-validation-switch-btn"
                      data-state="unchecked"
                      role="switch"
                      type="button"
                      value="on"
                    >
                      <span
                        class="c-bILIWL"
                        data-state="unchecked"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="c-fZEhOm"
            />
            <div
              class="c-kVBBIh c-kVBBIh-eJHswK-orientation-horizontal c-kVBBIh-ikCdwbd-css"
              data-cy="leaf-nutrition-card-body"
            >
              <button
                class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-dxftns-size-xs c-kAXHSi-crlJmD-variant-ghost c-dexIdH-igvNcMJ-css"
                data-cy="leaf-nutrition-card-info-button"
                type="button"
              >
                <svg
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                  />
                </svg>
              </button>
              <label
                class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iliVNCR-css"
                data-cy="leaf-nutrition-card-helper-text"
              >
                 
                polaris.cnpDetails.leafNutritionPlan.content.helperText
              </label>
            </div>
            <div
              class="c-fZEhOm"
            />
            <div
              class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
            >
              <button
                class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv c-hRrCwb-ibPNjhd-css"
                data-cy="left-nutrition-card-test-button"
                disabled=""
              >
                <span
                  class="c-iepcqn"
                >
                  polaris.cnpDetails.leafNutritionPlan.buttons.test
                </span>
              </button>
              <button
                class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-bETQVM-variant-primary c-hRrCwb-evtOnb-cv c-hRrCwb-ibPNjhd-css"
                data-cy="leaf-nutrition-card-test-button"
                disabled=""
              >
                <span
                  class="c-iepcqn"
                >
                  polaris.cnpDetails.leafNutritionPlan.buttons.configure
                </span>
              </button>
            </div>
          </a>
        </div>
        <div
          class="crop-settings-edit-popup-root"
        />
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-hAzCDl"
      data-cy="crop-nutrition-details-content"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ijZBIvl-css"
        data-cy="cnp-details-title"
      >
        polaris.cnpDetails.title
      </h1>
      <a
        class="c-wzBoY"
        data-cy="crop-settings-card"
      >
        <div
          class="card-header"
        >
          <div
            class="c-jCarvd c-jCarvd-ikfQhrm-css"
          >
            <div
              class="c-cVKzzi"
            >
              <div
                class="c-cXFqtJ"
              >
                <h1
                  class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                >
                  polaris.cnpDetails.cropSettings.title
                </h1>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
              />
            </div>
            <div
              class="c-hcUxto"
            >
              <button
                class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-kkaoVB"
                data-cy="crop-settings-edit-button"
                title="polaris.cnpDetails.cropSettings.editButton"
                type="button"
              >
                <svg
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                  />
                </svg>
                <span
                  class="c-iepcqn"
                >
                  polaris.cnpDetails.cropSettings.editButton
                </span>
              </button>
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-hPExTj c-hPExTj-idtkdsG-css"
        >
          <div
            class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-kVBBIh-iivCToc-css"
            data-cy="crop-settings-card-body"
          >
            <div
              data-cy="growth-scale-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cnpDetails.cropSettings.growthScale
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r0:"
                    aria-expanded="false"
                    aria-label="Growth scale select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 4.5H6a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-11a3 3 0 0 0-3-3h-3m-6 0h6m-6 0v-2m0 2V6m6-1.5v-2m0 2V6m-3.557 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="yield-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cnpDetails.cropSettings.yieldUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r1:"
                    aria-expanded="false"
                    aria-label="Yield unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="demand-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.cnpDetails.cropSettings.recommendationUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r2:"
                    aria-expanded="false"
                    aria-label="Recommendation unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8 5a3 3 0 0 1-3 3m3-3a3 3 0 1 0-3 3m3-3h8M5 8v8m14-8a3 3 0 1 0-3-3m3 3a3 3 0 0 1-3-3m3 3v8m-3 3a3 3 0 1 0 3-3m-3 3a3 3 0 0 1 3-3m-3 3H8m0 0a3 3 0 1 1-3-3m3 3a3 3 0 0 0-3-3"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-cvAFBk c-cvAFBk-iTKOFX-orientation-vertical c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-kdEBiP"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-ibPNjhd-css partners-title"
            data-cy="crop-setting-partners-title"
          >
            polaris.cnpDetails.cropSettings.partnersTitle
          </label>
          <div
            class="c-bfPkPS c-bfPkPS-iczWuCV-css stack"
          >
            <div
              class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s stack-status"
              data-cy="FFDP Services-status"
              variant="neutral"
            >
              <label
                class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
              >
                FFDP Services
              </label>
            </div>
            <div
              class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s stack-status"
              data-cy="GEOS-status"
              variant="neutral"
            >
              <label
                class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
              >
                GEOS
              </label>
            </div>
            <div
              class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s stack-status"
              data-cy="MyKWS-status"
              variant="neutral"
            >
              <label
                class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
              >
                MyKWS
              </label>
            </div>
          </div>
        </div>
      </a>
      <div
        class="c-bfPkPS c-bfPkPS-ibJIBTx-css"
      >
        <a
          class="c-wzBoY"
          data-cy="soil-nutrition-card"
        >
          <div
            class="c-ceYNSH card-header"
          >
            <div
              class="c-jCarvd c-jCarvd-ikfQhrm-css"
            >
              <div
                class="c-cVKzzi"
              >
                <div
                  class="c-cXFqtJ"
                >
                  <h1
                    class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                  >
                    polaris.cnpDetails.soilNutritionPlan.title
                  </h1>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                >
                  --
                </p>
              </div>
              <div
                class="validate-switch"
                data-state="closed"
              >
                <div
                  class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-isXzsQ-css"
                  >
                    switchLabel
                  </label>
                  <button
                    aria-checked="false"
                    class="c-dyBzGm c-dyBzGm-ihXMnoP-css"
                    data-cy="soil-validation-switch-btn"
                    data-state="unchecked"
                    role="switch"
                    type="button"
                    value="on"
                  >
                    <span
                      class="c-bILIWL"
                      data-state="unchecked"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical plan-details-configure-nav-content"
          >
            <div
              class="c-hPExTj"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
              >
                polaris.cnpDetails.soilNutritionPlan.content.title
              </h1>
              <p
                class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
              >
                polaris.cnpDetails.soilNutritionPlan.content.description
              </p>
            </div>
            <div
              class="c-hPExTj"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
              >
                polaris.cnpDetails.soilNutritionPlan.partners.title
              </h1>
              <p
                class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
              >
                polaris.cnpDetails.soilNutritionPlan.partners.description
              </p>
            </div>
            <div
              class="c-bfPkPS c-bfPkPS-idtVjeT-css"
            />
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
          >
            <button
              class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-qswFQ-variant-outline c-hRrCwb-ibPNjhd-css"
              data-cy="soil-nutrition-card-test-button"
            >
              <span
                class="c-iepcqn"
              >
                polaris.cnpDetails.soilNutritionPlan.buttons.test
              </span>
            </button>
            <button
              class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary c-hRrCwb-ibPNjhd-css"
              data-cy="soil-nutrition-card-configure-button"
            >
              <span
                class="c-iepcqn"
              >
                polaris.cnpDetails.soilNutritionPlan.buttons.configure
              </span>
            </button>
          </div>
        </a>
        <a
          class="c-wzBoY c-wzBoY-ifixGjY-css"
          data-cy="leaf-nutrition-card"
        >
          <div
            class="c-ceYNSH card-header"
          >
            <div
              class="c-jCarvd c-jCarvd-ikfQhrm-css"
            >
              <div
                class="c-cVKzzi"
              >
                <div
                  class="c-cXFqtJ"
                >
                  <h1
                    class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                  >
                    polaris.cnpDetails.leafNutritionPlan.title
                  </h1>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                >
                  --
                </p>
              </div>
              <div
                class="validate-switch"
                data-state="closed"
              >
                <div
                  class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-isXzsQ-css"
                  >
                    switchLabel
                  </label>
                  <button
                    aria-checked="false"
                    class="c-dyBzGm c-dyBzGm-ihXMnoP-css"
                    data-cy="soil-validation-switch-btn"
                    data-state="unchecked"
                    role="switch"
                    type="button"
                    value="on"
                  >
                    <span
                      class="c-bILIWL"
                      data-state="unchecked"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-kVBBIh c-kVBBIh-eJHswK-orientation-horizontal c-kVBBIh-ikCdwbd-css"
            data-cy="leaf-nutrition-card-body"
          >
            <button
              class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-dxftns-size-xs c-kAXHSi-crlJmD-variant-ghost c-dexIdH-igvNcMJ-css"
              data-cy="leaf-nutrition-card-info-button"
              type="button"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                />
              </svg>
            </button>
            <label
              class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iliVNCR-css"
              data-cy="leaf-nutrition-card-helper-text"
            >
               
              polaris.cnpDetails.leafNutritionPlan.content.helperText
            </label>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
          >
            <button
              class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv c-hRrCwb-ibPNjhd-css"
              data-cy="left-nutrition-card-test-button"
              disabled=""
            >
              <span
                class="c-iepcqn"
              >
                polaris.cnpDetails.leafNutritionPlan.buttons.test
              </span>
            </button>
            <button
              class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-bETQVM-variant-primary c-hRrCwb-evtOnb-cv c-hRrCwb-ibPNjhd-css"
              data-cy="leaf-nutrition-card-test-button"
              disabled=""
            >
              <span
                class="c-iepcqn"
              >
                polaris.cnpDetails.leafNutritionPlan.buttons.configure
              </span>
            </button>
          </div>
        </a>
      </div>
      <div
        class="crop-settings-edit-popup-root"
      />
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
