import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SetStateAction } from 'react';
import { PrePostCropAddState } from '../PrePostCrop.type';
import { CropSequence, CropDropdownValue } from '@common/types';

export interface PrePostCropAdd {
  isOpen: boolean;
  setIsOpen: Dispatch<boolean>;
  addCropSeqHandler: MouseEventHandler<HTMLButtonElement> | undefined;
  cropDropdownData: CropDropdownValue[];
  prePostCropAddState: PrePostCropAddState;
  setPrePostCropAddState: Dispatch<SetStateAction<PrePostCropAddState>>;
  cropSequenceChange: (value: string) => void;
  selectedPrePostCrop: CropSequence | null;
}
