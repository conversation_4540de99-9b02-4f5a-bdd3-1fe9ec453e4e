import { styled } from '@yaradigitallabs/ahua-react';

export const crossIcon = {
  padding: 0,
};

export const IsPrePostWrapper = styled('div', {
  display: 'flex',
  gap: '16px',
});
export const CheckboxButtonWrapper = styled('div', {
  width: '256px',
  border: '1px solid var(--colors-border-input-enabled)',
  borderRadius: 'var(--space-x2)',
  paddingLeft: '$x3',
  height: '$x14',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  cursor: 'pointer',
});
