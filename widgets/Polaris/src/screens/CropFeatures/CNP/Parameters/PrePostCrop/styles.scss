.pre-post-crop-container {
  margin-top: var(--space-x6);
}
.table-cell-tooltip-main {
  & > button {
    padding: 0;
  }
}
.table-cell-tooltip {
  & > p {
    font-size: 10px;
    text-align: center;
    line-height: 14px;
    white-space: pre-wrap;
  }
}
.is-crop-cell {
  & > svg {
    &[title='clear circle'] {
      stroke: var(--colors-black30);
    }
  }
}
.table-header-row {
  border-bottom-width: 2px;
  & > th {
    position: relative;
  }
  & .tooltip-wrapper {
    & > button {
      padding: 0;
      margin-right: 10px;
      & > svg {
        min-width: var(--space-x4);
        min-height: var(--space-x4);
        width: var(--space-x4);
        height: var(--space-x4);
      }
    }
    & > label {
      line-height: 17.5px;
    }
  }
}
.table-data-row {
  line-height: 0;
}
.table-actions-cell {
  & > button > svg {
    stroke: var(--colors-neutral-contrast);
  }
  &:last-child {
    text-align: center;
    justify-content: center;
  }
}
.maps-in-ahdb {
  & .yes {
    color: var(--colors-green60);
  }
  & .no {
    color: var(--colors-red60);
  }
}
.ahdb-table-cell-tooltip {
    & > p {
      font-size: var(--space-x3);
    }
}