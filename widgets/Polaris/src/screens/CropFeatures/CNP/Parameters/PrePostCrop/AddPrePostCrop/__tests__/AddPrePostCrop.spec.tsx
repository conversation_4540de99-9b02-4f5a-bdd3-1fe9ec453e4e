/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { createCropSequenceHandler, cropSequenceHandler } from '@common/mocks';
import { PrePostCropAdd } from '../AddPrePostCrop.type';
import { AddPrePostCrop } from '../AddPrePostCrop';

const server = setupServer(cropSequenceHandler, createCropSequenceHandler);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('widget: AddPrePostCrop', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const PrePostCropAddProps = {
    setIsOpen: jest.fn(),
    addCropSeqHandler: jest.fn(),
    cropDropdownData: [{ text: 'Maize', value: '22d-d33-8f7' }],
    prePostCropAddState: {
      selectedCrop: null,
      checkedBoxes: [false, false],
      hasErrorCrop: false,
    },
    setPrePostCropAddState: jest.fn(),
    cropSequenceChange: jest.fn(),
  } as unknown as PrePostCropAdd;

  test('AddPrePostCrop: should not show modal when isOpen is false', async () => {
    const props = { ...PrePostCropAddProps, isOpen: false };
    render(<AddPrePostCrop {...props} />);
    expect(
      screen.queryByLabelText(
        'polaris.cnpDetails.planConfiguration.prePostCropParams.addPrePostTitle',
      ),
    ).not.toBeInTheDocument();
  });
  test('AddPrePostCrop: should show modal when isOpen is true', async () => {
    const props = { ...PrePostCropAddProps, isOpen: true };
    render(<AddPrePostCrop {...props} />);
    expect(
      screen.getByLabelText(
        'polaris.cnpDetails.planConfiguration.prePostCropParams.addPrePostTitle',
      ),
    ).toBeInTheDocument();

    const checkBox = screen.getByTestId('0.is-pre-post-or-both');
    fireEvent.click(checkBox);
  });

  test('AddPrePostCrop: should match snapshot with default values', () => {
    const props = { ...PrePostCropAddProps, isOpen: true };
    const component = render(<AddPrePostCrop {...props} />);
    expect(component).toMatchSnapshot();
  });
});
