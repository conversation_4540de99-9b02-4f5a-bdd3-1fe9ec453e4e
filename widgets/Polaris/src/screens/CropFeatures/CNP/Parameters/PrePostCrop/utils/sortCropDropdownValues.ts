import { cropDropdownValues } from '../../../../../Home';
import { CropSubClass, CropDescription, CropSequence } from '@common/types';
export const sortedCrops = (
  cropSubClasses: CropSubClass[],
  cropDescriptions: CropDescription[],
) => {
  return cropDropdownValues(cropSubClasses, cropDescriptions)?.sort((c1, c2) =>
    c1.text > c2.text ? 1 : -1,
  );
};

export const getShowPagination = (
  pageIndex: number,
  cropSequences: CropSequence[] | undefined,
  cropSeqHasNextPage: boolean,
  isCSLoading: boolean,
  isCSCreating: boolean,
  isCSUpdating: boolean,
) => {
  let show;
  if (pageIndex === 0 && cropSequences && (!isCSLoading || !isCSCreating || !isCSUpdating)) {
    show = !cropSeqHasNextPage && cropSequences?.length <= 5;
  }
  return show;
};
