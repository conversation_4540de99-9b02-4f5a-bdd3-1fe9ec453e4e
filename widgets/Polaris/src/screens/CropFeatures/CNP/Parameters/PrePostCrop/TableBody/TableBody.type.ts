import { CropSequence } from '@common/types';
import React from 'react';
import { PrePostCropAddState } from '../PrePostCrop.type';

export type TableBodyProps = {
  cropSequence: CropSequence;
  setRefetch: React.Dispatch<React.SetStateAction<boolean>>;
  handleEditDialog: (cropSequence: CropSequence) => void;
  setPrePostCropAddState: React.Dispatch<React.SetStateAction<PrePostCropAddState>>;
  isAhdb: boolean;
};
