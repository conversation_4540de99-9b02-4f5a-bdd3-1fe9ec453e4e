// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: TableBody TableBody: should match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <tr
        class="c-eDGYZe table-data-row"
      >
        <td
          class="c-doquzR"
          data-cy="crop-2fe4-2ad3-2fs3-2342"
        >
          <p
            class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
          >
            undefined undefined
          </p>
        </td>
        <td
          class="c-doquzR is-crop-cell"
          data-cy="is-pre-crop-2fe4-2ad3-2fs3-2342"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-giEKfI-colorConcept-success c-nJRoe-iPJLV-css"
            title="check circle"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.375 9.375l-5.71 5.816h-.813l-2.227-2.195M12 22C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
            />
          </svg>
        </td>
        <td
          class="c-doquzR is-crop-cell"
          data-cy="is-post-crop-2fe4-2ad3-2fs3-2342"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-giEKfI-colorConcept-success c-nJRoe-iPJLV-css"
            title="check circle"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.375 9.375l-5.71 5.816h-.813l-2.227-2.195M12 22C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
            />
          </svg>
        </td>
        <td
          class="c-doquzR table-cell-tooltip-main"
        >
          <button
            class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost"
            data-cy="tooltip-2fe4-2ad3-2fs3-2342"
            data-state="closed"
            type="button"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
              />
            </svg>
          </button>
        </td>
        <td
          class="c-doquzR c-cIAltW table-actions-cell"
          data-cy="pre-post-crop-action-cell-2fe4-2ad3-2fs3-2342"
        >
          <button
            aria-expanded="false"
            aria-haspopup="menu"
            class="c-bvmDGo"
            data-state="closed"
            icon="MoreHorizontal"
            id="radix-:rd:"
            title=""
            type="button"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5 10.5a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zm7 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zm7 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3z"
              />
            </svg>
          </button>
        </td>
      </tr>
    </div>
  </body>,
  "container": <div>
    <tr
      class="c-eDGYZe table-data-row"
    >
      <td
        class="c-doquzR"
        data-cy="crop-2fe4-2ad3-2fs3-2342"
      >
        <p
          class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css"
        >
          undefined undefined
        </p>
      </td>
      <td
        class="c-doquzR is-crop-cell"
        data-cy="is-pre-crop-2fe4-2ad3-2fs3-2342"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-giEKfI-colorConcept-success c-nJRoe-iPJLV-css"
          title="check circle"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.375 9.375l-5.71 5.816h-.813l-2.227-2.195M12 22C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
          />
        </svg>
      </td>
      <td
        class="c-doquzR is-crop-cell"
        data-cy="is-post-crop-2fe4-2ad3-2fs3-2342"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-giEKfI-colorConcept-success c-nJRoe-iPJLV-css"
          title="check circle"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.375 9.375l-5.71 5.816h-.813l-2.227-2.195M12 22C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
          />
        </svg>
      </td>
      <td
        class="c-doquzR table-cell-tooltip-main"
      >
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-eZMZDm-size-n c-kAXHSi-crlJmD-variant-ghost"
          data-cy="tooltip-2fe4-2ad3-2fs3-2342"
          data-state="closed"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
            />
          </svg>
        </button>
      </td>
      <td
        class="c-doquzR c-cIAltW table-actions-cell"
        data-cy="pre-post-crop-action-cell-2fe4-2ad3-2fs3-2342"
      >
        <button
          aria-expanded="false"
          aria-haspopup="menu"
          class="c-bvmDGo"
          data-state="closed"
          icon="MoreHorizontal"
          id="radix-:rd:"
          title=""
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5 10.5a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zm7 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3zm7 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3z"
            />
          </svg>
        </button>
      </td>
    </tr>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
