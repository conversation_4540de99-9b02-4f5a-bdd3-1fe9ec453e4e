// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: AddPrePostCrop AddPrePostCrop: should match snapshot with default values 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    />
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:rc:"
      aria-labelledby="radix-:rb:"
      class="c-cPoUYR c-cPoUYR-ieoIBVf-css"
      data-cy="opened-add pre-crops and post-crops"
      data-state="open"
      id="radix-:ra:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-cVIuYM dialog-header-main"
      >
        <h2
          class="c-bALNxX"
          data-cy="dialog-polaris.cnpDetails.planConfiguration.prePostCropParams.addPrePostTitle"
          id="radix-:rb:"
        >
          polaris.cnpDetails.planConfiguration.prePostCropParams.addPrePostTitle
        </h2>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs c-dexIdH-iivYAPe-css"
          data-cy="dialog-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-jndegn c-jndegn-igwRkrt-css dialog-middle"
      >
        <div
          class="add-body-wrapper"
          data-cy="add-pre-post-crop-body-wrapper"
        >
          <div
            data-cy="add-pre-post-crop-body-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default crop-name"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-n c-iLfdtR-gBsWrO-iconPresent-false"
                >
                  polaris.common.crop
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-bkfUjw-selectSize-n"
                >
                  polaris.common.crop
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:rd:"
                  aria-expanded="false"
                  aria-label="polaris.common.crop"
                  class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    />
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <h1
            class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-ijhmiWA-css"
            data-cy="is-pre-post-crop-or-both-title"
          >
            polaris.cnpDetails.planConfiguration.prePostCropParams.isPrePostOrBoth
          </h1>
          <div
            aria-label="Display Option"
            class="c-KWjXx"
            data-cy="is-pre-post-crop-or-both-title-wrapper"
          >
            <div
              class="c-dvOcsw"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                data-cy="polaris.cnpDetails.planConfiguration.prePostCropParams.preCrop"
                for="option 0"
              >
                polaris.cnpDetails.planConfiguration.prePostCropParams.preCrop
              </label>
              <div
                class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
              >
                <button
                  aria-checked="false"
                  aria-label="Checkbox false"
                  class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                  data-cy="0.is-pre-post-or-both"
                  data-state="unchecked"
                  role="checkbox"
                  type="button"
                  value="on"
                />
              </div>
            </div>
            <div
              class="c-dvOcsw"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                data-cy="polaris.cnpDetails.planConfiguration.prePostCropParams.postCrop"
                for="option 1"
              >
                polaris.cnpDetails.planConfiguration.prePostCropParams.postCrop
              </label>
              <div
                class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
              >
                <button
                  aria-checked="false"
                  aria-label="Checkbox false"
                  class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                  data-cy="1.is-pre-post-or-both"
                  data-state="unchecked"
                  role="checkbox"
                  type="button"
                  value="on"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-ctwkvW"
      >
        <div
          class="c-hcqlDB"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary"
            data-cy="dialog-save-btn"
          >
            <span
              class="c-iepcqn"
            >
              save
            </span>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
