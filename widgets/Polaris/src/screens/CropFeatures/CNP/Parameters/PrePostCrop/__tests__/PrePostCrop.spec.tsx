import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  features<PERSON>and<PERSON>,
  cropRegionsHandler,
  cnpPartnersHandler,
  cropSequenceHandler,
  createCropSequenceHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { BrowserRouter as Router } from 'react-router-dom';
import { NavbarProvider } from '@libs/nav-context';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { PrePostCrop } from '../index';
import { useTranslation } from 'react-i18next';
import { cropDropdownValues } from '@widgets/Polaris/src/screens/Home';
import { CropDescription, CropSubClass } from '@common/types';
import { userEvent } from '@testing-library/user-event';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
  cropSequenceHandler,
  createCropSequenceHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

jest.mock('@polaris-hooks/index', () => ({
  ...jest.requireActual('@polaris-hooks/index'),
  useFetchCropDescriptionsMapping: () => ({
    cropDescMappingsData: [],
    isCropDescMappingsFetching: false,
  }),
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('PrePostCrop renders', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });

  const { t } = useTranslation();
  it('should initialize the context with default values and not render empty screen', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component.getByTestId('pre-post-crop-section')).toBeInTheDocument();
    expect(
      component.queryByLabelText(
        t(`polaris.cnpDetails.planConfiguration.prePostCropParams.emptyScreenText`),
      ),
    ).not.toBeInTheDocument();
  });

  it('PrePostCrop: Should open collapsible on click to display empty screen', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const button = component.getByRole('button', {
      name: /polaris\.cnpdetails\.planconfiguration\.prepostcropparams\.title/i,
    });
    fireEvent.click(button);
    expect(component.getByText('polaris.common.addButton')).toBeInTheDocument();
    expect(
      component.getByText('polaris.cnpDetails.planConfiguration.prePostCropParams.emptyScreenText'),
    ).toBeInTheDocument();
  });

  it('PrePostCrop: Should open collapsible on click to display table with data', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const collapsibleBtn = screen.getByRole('button', {
      name: t('polaris.cnpDetails.planConfiguration.prePostCropParams.title'),
    });
    expect(collapsibleBtn).toBeInTheDocument();

    fireEvent.click(collapsibleBtn);

    await waitFor(() => component.getByTestId('empty-screen-text'));
    await waitFor(() => component.getByTestId(t('polaris.common.crop')));
    waitFor(() => {
      expect(
        component.getByTestId('is-pre-crop-84bc8ea7-31ce-43f5-86f6-30f7916667b4'),
      ).toBeInTheDocument();
      expect(
        component.getByTestId('is-post-crop-724313ac-0450-4cbd-963d-ce0315868f65'),
      ).toBeInTheDocument();
    });
  });
  it('PrePostCrop: should go next, prev, first, and last page of the table on click', async () => {
    const { getByTestId, getByLabelText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const collapsibleBtn = screen.getByRole('button', {
      name: t('polaris.cnpDetails.planConfiguration.prePostCropParams.title'),
    });
    expect(collapsibleBtn).toBeInTheDocument();

    fireEvent.click(collapsibleBtn);

    await waitFor(() => getByTestId('empty-screen-text'));
    await waitFor(() => getByTestId(t('polaris.common.crop')));

    // checking table data exist after getting the API data
    await waitFor(() => {
      expect(getByTestId('is-pre-crop-724313ac-0450-4cbd-963d-ce0315868f65')).toBeInTheDocument();
    });

    const nextBtn = getByLabelText('Go to next page');
    const prevBtn = getByLabelText('Go to previous page');
    const firstBtn = getByLabelText('Go to first page');
    const lastBtn = getByLabelText('Go to last page');
    expect(nextBtn).toBeInTheDocument();
    expect(prevBtn).toBeInTheDocument();
    expect(firstBtn).toBeInTheDocument();
    expect(lastBtn).toBeInTheDocument();

    fireEvent.click(nextBtn);
    expect(getByTestId('crop-724313ac-0450-4cbd-963d-ce0315868f65')).toBeInTheDocument();
    fireEvent.click(prevBtn);
    expect(getByTestId('crop-724313ac-0450-4cbd-963d-ce0315868f65')).toBeInTheDocument();
    fireEvent.click(firstBtn);
    expect(getByTestId('crop-724313ac-0450-4cbd-963d-ce0315868f65')).toBeInTheDocument();
    fireEvent.click(lastBtn);
    expect(getByTestId('crop-724313ac-0450-4cbd-963d-ce0315868f65')).toBeInTheDocument();
  });

  it('PrePostCrop: should open pre-post crop add modal on click of add button', async () => {
    const { getByTestId, getByLabelText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const collapsibleBtn = screen.getByRole('button', {
      name: t('polaris.cnpDetails.planConfiguration.prePostCropParams.title'),
    });
    expect(collapsibleBtn).toBeInTheDocument();

    fireEvent.click(collapsibleBtn);
    await waitFor(() => getByTestId('empty-screen-text'));
    await waitFor(() => getByTestId(t('polaris.common.crop')));
    const addButton = getByTestId('collapsible-add-button');
    expect(addButton).toBeInTheDocument();
    // click add button to open the pre-post crop modal
    fireEvent.click(addButton);
    waitFor(() =>
      getByLabelText(t('polaris.cnpDetails.planConfiguration.prePostCropParams.addPrePostTitle')),
    );
    waitFor(() =>
      getByLabelText(t('polaris.cnpDetails.planConfiguration.prePostCropParams.addPrePostTitle')),
    );
    waitFor(() => {
      expect(getByTestId(t('add-pre-post-crop-body-wrapper'))).toBeInTheDocument();
      expect(getByTestId(t('is-pre-post-crop-or-both-title'))).toBeInTheDocument();
    });
  });

  it('PrePostCrop: Should match snapshot', async () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });
  it('PrePostCrop: should return an empty array when cropResponse is undefined', () => {
    const result = cropDropdownValues(undefined, []);
    expect(result).toEqual([]);
  });
  it('PrePostCrop: should handle cases where cropDescription is not found for a cropSubClass', () => {
    const cropResponse = [{ id: 'x4s-43a-42b-id1', name: 'Crop1' }] as CropSubClass[];
    const filteredCropDescriptions = [
      { cropSubClassId: 'x4s-43a-42b-id1', name: 'Description1', id: '1' },
    ] as CropDescription[];
    const result = cropDropdownValues(cropResponse, filteredCropDescriptions);
    expect(result).toEqual([{ value: '1', text: 'Crop1 Description1' }]);
  });
  it('PrePostCrop: should return an array of dropdown values based on cropResponse and filteredCropDescriptions', () => {
    const cropResponse = [{ id: 'x4s-43a-42b-id1', name: 'Crop1' }] as CropSubClass[];
    const filteredCropDescriptions = [
      { cropSubClassId: 'x4s-43a-42b-id1', name: 'Description1', id: '1' },
    ] as CropDescription[];
    const result = cropDropdownValues(cropResponse, filteredCropDescriptions);
    expect(result).toEqual([{ value: '1', text: 'Crop1 Description1' }]);
  });

  it('successfully trigger list item action menu', async () => {
    const user = userEvent.setup();
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <PrePostCrop />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const collapsible = screen.getByTestId('pre-post-crop-section');
    expect(collapsible).toBeInTheDocument();
    const button = screen.getByRole('button', {
      name: 'polaris.cnpDetails.planConfiguration.prePostCropParams.title',
    });
    await user.click(button);
    await waitFor(() => {
      expect(button).toHaveAttribute('aria-expanded', 'true');
    });

    const actionMenuButton = screen.getByTestId(
      'pre-post-crop-action-cell-84bc8ea7-31ce-43f5-86f6-30f7916667b4',
    );

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const triggerButton = actionMenuButton.querySelector('button[aria-haspopup="menu"]')!;

    expect(triggerButton).toBeInTheDocument();

    await user.click(triggerButton);
    await waitFor(() => {
      expect(triggerButton).toHaveAttribute('aria-expanded', 'true');
    });
  });
});
