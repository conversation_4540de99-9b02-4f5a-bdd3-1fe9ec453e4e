import React, { useEffect } from 'react';
import { CheckBox, Label, Select, Title } from '@yaradigitallabs/ahua-react';
import { ModalDialog } from '../../../../../../components/Dialog';
import { useTranslation } from 'react-i18next';
import { crossIcon, CheckboxButtonWrapper, IsPrePostWrapper } from './AddPrePostCrop.styled';
import { PrePostCropAdd } from './AddPrePostCrop.type';
import './styles.scss';
import { useGetCropDescription } from '@polaris-hooks/index';
import { ACTIONS } from '@common/constants';
import { SelectWrapper } from '@widgets/Polaris/src/components';

export const AddPrePostCrop: React.FC<PrePostCropAdd> = ({
  isOpen,
  setIsOpen,
  addCropSeqHandler,
  cropDropdownData,
  prePostCropAddState,
  setPrePostCropAddState,
  cropSequenceChange,
  selectedPrePostCrop,
}) => {
  const { t } = useTranslation();
  const keyPrefix = 'polaris.cnpDetails.planConfiguration.prePostCropParams';

  const { data: cropDescription, isLoading: isCropDescriptionFetching } = useGetCropDescription(
    selectedPrePostCrop?.cropDescriptionId,
  );

  useEffect(() => {
    if (selectedPrePostCrop) {
      setPrePostCropAddState((prevState) => ({
        ...prevState,
        selectedCropDescription: cropDescription,
        checkedBoxes: [selectedPrePostCrop?.isPreCrop, selectedPrePostCrop?.isPostCrop],
      }));
    }
  }, [selectedPrePostCrop, isOpen]);

  const prePostCrops = [
    {
      value: 'preCrop',
      label: t(`${keyPrefix}.preCrop`),
    },
    {
      value: 'postCrop',
      label: t(`${keyPrefix}.postCrop`),
    },
  ];
  const { selectedCropDescription, checkedBoxes, hasErrorCrop } = prePostCropAddState;

  const cropCheckboxHandler = (position: number) => {
    const modifiedCheckboxes = prePostCropAddState.checkedBoxes.map((item, index) =>
      index === position ? !item : item,
    );

    setPrePostCropAddState((prevState) => ({
      ...prevState,
      checkedBoxes: modifiedCheckboxes,
      touchedFields: {
        ...prevState.touchedFields,
        checkedBoxes: true,
      },
    }));
  };

  return (
    <ModalDialog
      isOpen={isOpen}
      onChange={setIsOpen}
      title={t(`${keyPrefix}.addPrePostTitle`, {
        actionName: !selectedPrePostCrop ? ACTIONS.ADD : ACTIONS.EDIT,
      })}
      css={{ crossIcon }}
      dataCy={`${isOpen ? 'opened' : 'closed'}-add pre-crops and post-crops`}
      onSaveClick={addCropSeqHandler}
      actionType={!selectedPrePostCrop ? ACTIONS.ADD : ACTIONS.EDIT}
    >
      {!isCropDescriptionFetching && (
        <div data-cy='add-pre-post-crop-body-wrapper' className='add-body-wrapper'>
          <SelectWrapper dataCy='add-pre-post-crop-body-select'>
            <Select
              className={!hasErrorCrop && 'crop-name'}
              ariaLabel={t('polaris.common.crop')}
              cover={'outline'}
              value={selectedCropDescription ? selectedCropDescription?.id : null}
              items={cropDropdownData}
              loadingText={''}
              placeholder={t('polaris.common.crop')}
              position='popper'
              size='n'
              variant={hasErrorCrop ? 'error' : 'default'}
              helper-text={hasErrorCrop ? t(`polaris.error.isRequired`, { name: 'Crop' }) : null}
              onChange={cropSequenceChange}
              label={selectedCropDescription !== null ? t('polaris.common.crop') : null}
            />
          </SelectWrapper>

          <Title
            data-cy='is-pre-post-crop-or-both-title'
            size='xs'
            css={{ padding: '28px 0 8px 0' }}
          >
            {t(`${keyPrefix}.isPrePostOrBoth`)}
          </Title>
          <IsPrePostWrapper
            aria-label='Display Option'
            data-cy='is-pre-post-crop-or-both-title-wrapper'
          >
            {prePostCrops.map((option, index) => (
              <CheckboxButtonWrapper
                key={option.value}
                className={checkedBoxes[index] ? 'selected' : ''}
                onClick={() => cropCheckboxHandler(index)}
              >
                <Label htmlFor={`option ${index}`} data-cy={`${option.label}`}>
                  {option.label}
                </Label>
                <CheckBox
                  data-cy={`${index}.is-pre-post-or-both`}
                  ariaLabel={`Checkbox ${checkedBoxes[index]}`}
                  concept='brand'
                  checked={checkedBoxes[index]}
                />
              </CheckboxButtonWrapper>
            ))}
          </IsPrePostWrapper>
        </div>
      )}
    </ModalDialog>
  );
};
