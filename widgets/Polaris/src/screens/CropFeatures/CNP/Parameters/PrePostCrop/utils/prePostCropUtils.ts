import React from 'react';
import { CropDescription, CropSequence } from '@common/types';
import { PrePostCropAddState } from '../PrePostCrop.type';

export const addPrePostInitialState = {
  selectedCropDescription: null,
  checkedBoxes: [false, false],
  hasErrorCrop: false,
  touchedFields: {
    selectedCropDescription: false,
    checkedBoxes: false,
  },
  isPrevPage: false,
};

export const handleEditDialog = (
  cropSequence: CropSequence,
  setIsOpen: (value: React.SetStateAction<boolean>) => void,
  setSelectedPrePostCrop: (value: React.SetStateAction<CropSequence | null>) => void,
  setPrePostCropAddState: (value: React.SetStateAction<PrePostCropAddState>) => void,
) => {
  setIsOpen(true);
  setSelectedPrePostCrop(cropSequence);
  setPrePostCropAddState(addPrePostInitialState);
};

export const onNextPage = (
  setPageIndex: React.Dispatch<React.SetStateAction<number>>,
  setPrePostCropAddState: React.Dispatch<React.SetStateAction<PrePostCropAddState>>,
) => {
  setPageIndex((pageIndex) => pageIndex + 1);
  setPrePostCropAddState(addPrePostInitialState);
};

export const onPrevPage = (
  setPageIndex: React.Dispatch<React.SetStateAction<number>>,
  setPrePostCropAddState: React.Dispatch<React.SetStateAction<PrePostCropAddState>>,
) => {
  setPageIndex((pageIndex) => pageIndex - 1);
  setPrePostCropAddState((prevState) => ({
    ...prevState,
    isPrevPage: true,
  }));
};

export const onFirstPage = (setPageIndex: React.Dispatch<React.SetStateAction<number>>) => {
  setPageIndex(0);
};

export const onLastPage = (
  setPageIndex: React.Dispatch<React.SetStateAction<number>>,
  maxPageIndex: number,
) => {
  setPageIndex(maxPageIndex);
};

export const cropSequenceChange = (
  value: string,
  cropDescriptions: CropDescription[],
  setPrePostCropAddState: React.Dispatch<React.SetStateAction<PrePostCropAddState>>,
) => {
  const selectedCropDescription = cropDescriptions.find((el) => el.id === value);
  setPrePostCropAddState((prevState) => ({
    ...prevState,
    selectedCropDescription: selectedCropDescription,
    hasErrorCrop: false,
    touchedFields: {
      ...prevState.touchedFields,
      selectedCropDescription: true,
    },
  }));
};

export const prePostAddBtnHandler = (
  setRefetch: React.Dispatch<React.SetStateAction<boolean>>,
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedPrePostCrop: React.Dispatch<React.SetStateAction<null | CropSequence>>,
  setPrePostCropAddState: React.Dispatch<React.SetStateAction<PrePostCropAddState>>,
) => {
  setRefetch(false);
  setIsOpen(true);
  setSelectedPrePostCrop(null);
  setPrePostCropAddState(addPrePostInitialState);
};
