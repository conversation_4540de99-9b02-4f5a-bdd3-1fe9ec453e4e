import {
  addPrePostInitialState,
  cropSequenceChange,
  handleEditDialog,
  onFirstPage,
  onLastPage,
  onNextPage,
  onPrevPage,
  prePostAddBtnHandler,
} from './prePostCropUtils';
import { CropSequence } from '@common/types';
import { cropDescriptionResponseMock } from '@common/mocks';

const cropSequenceMock: CropSequence = {
  cropRegionId: 'e09bbeb5-0d94-44e2-a50b-390921e9d7b7',
  cropDescriptionId: '672540dd-fcf0-44b7-99a1-cc4e7928b5e5',
  isPreCrop: false,
  isPostCrop: false,
  created: '2024-05-14T15:42:50.922Z',
  modified: '2024-05-14T15:42:50.922Z',
  modifiedBy: '<EMAIL>',
  id: '75e8713f-a43b-478e-91bf-5a656b08e1c3',
  deleted: null,
};

describe('Utils Tests', () => {
  it('should set initial state correctly in handleEditDialog', () => {
    const setIsOpen = jest.fn();
    const setSelectedPrePostCrop = jest.fn();
    const setPrePostCropAddState = jest.fn();
    handleEditDialog(cropSequenceMock, setIsOpen, setSelectedPrePostCrop, setPrePostCropAddState);
    expect(setIsOpen).toHaveBeenCalledWith(true);
    expect(setSelectedPrePostCrop).toHaveBeenCalledWith(cropSequenceMock);
    expect(setPrePostCropAddState).toHaveBeenCalledWith(addPrePostInitialState);
  });

  it('should increment pageIndex and reset state in onNextPage', () => {
    const setPageIndex = jest.fn();
    const setPrePostCropAddState = jest.fn();

    onNextPage(setPageIndex, setPrePostCropAddState);

    expect(setPageIndex).toHaveBeenCalledWith(expect.any(Function));
    setPageIndex.mock.calls;
    expect(setPrePostCropAddState).toHaveBeenCalledWith(addPrePostInitialState);
  });

  it('should decrement pageIndex and set isPrevPage in onPrevPage', () => {
    const setPageIndex = jest.fn();
    const setPrePostCropAddState = jest.fn();

    onPrevPage(setPageIndex, setPrePostCropAddState);

    expect(setPageIndex).toHaveBeenCalledWith(expect.any(Function));
    setPageIndex.mock.calls;
    expect(setPrePostCropAddState).toHaveBeenCalledWith(expect.any(Function));
    setPrePostCropAddState.mock.calls[0][0]({});
  });

  it('should set pageIndex to 0 in onFirstPage', () => {
    const setPageIndex = jest.fn();

    onFirstPage(setPageIndex);

    expect(setPageIndex).toHaveBeenCalledWith(0);
  });

  it('should set pageIndex to maxPageIndex in onLastPage', () => {
    const setPageIndex = jest.fn();
    const maxPageIndex = 5;
    onLastPage(setPageIndex, maxPageIndex);

    expect(setPageIndex).toHaveBeenCalledWith(maxPageIndex);
  });
  it('should update state correctly in cropSequenceChange', () => {
    const setPrePostCropAddState = jest.fn();

    cropSequenceChange(
      '21d8bfa5-68f0-4c95-a8e3-53fba79aaa0c',
      cropDescriptionResponseMock,
      setPrePostCropAddState,
    );

    expect(setPrePostCropAddState).toHaveBeenCalledWith(expect.any(Function));
    const updateStateFn = setPrePostCropAddState.mock.calls[0][0];
    const prevState = {
      selectedCropDescription: null,
      checkedBoxes: [false, false],
      hasErrorCrop: false,
      touchedFields: {
        selectedCropDescription: false,
        checkedBoxes: false,
      },
      isPrevPage: false,
    };

    const newState = updateStateFn(prevState);
    expect(newState).toEqual({
      ...prevState,
      selectedCropDescription: cropDescriptionResponseMock[0],
      hasErrorCrop: false,
      touchedFields: {
        ...prevState.touchedFields,
        selectedCropDescription: true,
      },
    });
  });

  it('should set initial state and open dialog in prePostAddBtnHandler', () => {
    const setRefetch = jest.fn();
    const setIsOpen = jest.fn();
    const setSelectedPrePostCrop = jest.fn();
    const setPrePostCropAddState = jest.fn();

    prePostAddBtnHandler(setRefetch, setIsOpen, setSelectedPrePostCrop, setPrePostCropAddState);

    expect(setRefetch).toHaveBeenCalledWith(false);
    expect(setIsOpen).toHaveBeenCalledWith(true);
    expect(setSelectedPrePostCrop).toHaveBeenCalledWith(null);
    expect(setPrePostCropAddState).toHaveBeenCalledWith(addPrePostInitialState);
  });
});
