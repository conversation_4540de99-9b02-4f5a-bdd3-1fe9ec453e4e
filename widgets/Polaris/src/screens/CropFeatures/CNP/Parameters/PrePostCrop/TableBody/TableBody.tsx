import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, AhuaIcon, IconButton, Tooltip, Caption } from '@yaradigitallabs/ahua-react';

import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import '../styles.scss';
import {
  useFetchCropDescriptionsMapping,
  useGetCropDescription,
  useGetCropSubClass,
  useDeleteCropSequence,
} from '@polaris-hooks/index';
import { modifiedByDetails } from '../../../../../../../utils/formatDateString/formatDateString';
import { ConfirmationDialog } from '../../../../../../components/ConfirmationDialog';
import { TableBodyProps } from './TableBody.type';

export default function TableBody({
  cropSequence,
  setRefetch,
  handleEditDialog,
  setPrePostCropAddState,
  isAhdb,
}: TableBodyProps) {
  const { t } = useTranslation();
  const translationKeyPrefix = 'polaris.cnpDetails.planConfiguration';
  const { setDisplaySnackbar } = useSnackbar();

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteDialogTitle, setDeleteDialogTitle] = useState('');

  const {
    data: deletedCrop,
    trigger: deleteCropSequence,
    isMutating,
    error,
  } = useDeleteCropSequence();

  const { data: cropDescription, isLoading: isCropDescriptionFetching } = useGetCropDescription(
    cropSequence?.cropDescriptionId,
  );
  const { data: cropSubClass, isLoading: isCropSubclassFetching } = useGetCropSubClass(
    cropDescription?.cropSubClassId,
  );
  const { cropDescMappingsData, isCropDescMappingsFetching } = useFetchCropDescriptionsMapping();
  const handleOpenDeleteDialog = () => {
    setDeleteDialogTitle(
      t(`${translationKeyPrefix}.prePostCropParams.dialog.title`, {
        cropName: `${cropSubClass?.name} ${cropDescription?.name}`,
        interpolation: { escapeValue: false },
      }),
    );
    setOpenDeleteDialog(true);
  };

  const handleDeleteItem = useCallback(() => {
    deleteCropSequence(cropSequence?.id);
    setDisplaySnackbar({
      open: false,
    });
    setPrePostCropAddState((prevState) => ({
      ...prevState,
      isPrevPage: false,
    }));
  }, [cropSequence, deleteCropSequence]);

  const mapsInAhdbData = useMemo(() => {
    if (isAhdb) {
      return cropDescMappingsData?.find(
        (cd) => cd.polarisCropDescriptionId === cropSequence.cropDescriptionId,
      );
    }
    return null;
  }, [cropDescMappingsData, cropSequence]);

  useEffect(() => {
    setOpenDeleteDialog(false);
    if (deletedCrop && Object.keys(deletedCrop).length) {
      setDisplaySnackbar({
        title: t('polaris.common.snackbarDeleteMessage', { name: 'Parameter' }),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 4000,
        open: true,
      });
      setRefetch(true);
    }
  }, [deletedCrop, error]);

  //crop modification details(date and time)
  const modifiedDetail = modifiedByDetails(cropSequence);

  const CheckOrClearIcon: React.FC<{
    status: boolean;
    dataCy: string;
  }> = ({ status, dataCy }): JSX.Element => {
    return (
      <Table.Cell data-cy={dataCy} className='is-crop-cell'>
        {status ? (
          <AhuaIcon icon='CheckCircle' title='check circle' colorConcept='success' />
        ) : (
          <AhuaIcon icon='ClearCircle' title='clear circle' colorConcept='neutral' />
        )}
      </Table.Cell>
    );
  };

  const crop =
    !isCropSubclassFetching &&
    !isCropDescriptionFetching &&
    `${cropSubClass?.name} ${cropDescription?.name}`;

  return (
    <>
      <Table.Row key={cropSequence?.id} className='table-data-row'>
        <Table.Cell data-cy={`crop-${cropSequence?.id}`}>
          <Caption size='n'>{crop}</Caption>
        </Table.Cell>
        <CheckOrClearIcon
          status={cropSequence.isPreCrop}
          dataCy={`is-pre-crop-${cropSequence?.id}`}
        />
        <CheckOrClearIcon
          status={cropSequence.isPostCrop}
          dataCy={`is-post-crop-${cropSequence?.id}`}
        />
        {isAhdb && (
          <Table.Cell data-cy='maps-in-ahdb' className='maps-in-ahdb'>
            {!isCropDescMappingsFetching && mapsInAhdbData && (
              <>
                <Caption size='n' className={mapsInAhdbData ? 'yes' : 'no'}>
                  {t(`${mapsInAhdbData ? 'polaris.common.yes' : 'polaris.common.no'}`)}
                </Caption>
              </>
            )}
          </Table.Cell>
        )}
        <Table.Cell className='table-cell-tooltip-main'>
          <Tooltip
            concept='inverse'
            text={modifiedDetail}
            maxWidth={165}
            tipVisibility={true}
            position={'top'}
            className='table-cell-tooltip'
            size={'xs'}
          >
            <IconButton
              data-cy={`tooltip-${cropSequence?.id}`}
              icon='Info'
              colorConcept='neutral'
            />
          </Tooltip>
        </Table.Cell>
        <Table.ActionsCell
          className='table-actions-cell'
          data-cy={`pre-post-crop-action-cell-${cropSequence?.id}`}
        >
          <Table.ActionMenu title=''>
            <Table.ActionMenuItem
              icon='Edit'
              onClick={() => handleEditDialog(cropSequence)}
              data-cy='pre-post-crop-edit-action'
            >
              {t(`polaris.common.table.actions.edit`)}
            </Table.ActionMenuItem>
            <Table.ActionMenuItem
              icon='Delete'
              onClick={handleOpenDeleteDialog}
              data-cy='pre-post-crop-delete-action'
            >
              {t(`polaris.common.table.actions.delete`)}
            </Table.ActionMenuItem>
          </Table.ActionMenu>
        </Table.ActionsCell>
      </Table.Row>
      <ConfirmationDialog
        open={openDeleteDialog}
        title={deleteDialogTitle}
        description={t(`${translationKeyPrefix}.prePostCropParams.dialog.description`)}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={t('polaris.common.yesDelete')}
        cancelButton={t('polaris.common.cancel')}
        onOk={handleDeleteItem}
        onCancel={() => setOpenDeleteDialog(false)}
        isLoading={isMutating}
      />
    </>
  );
}
