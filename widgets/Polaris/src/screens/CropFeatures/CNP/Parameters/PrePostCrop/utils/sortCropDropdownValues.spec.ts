import { sortedCrops } from './sortCropDropdownValues';
import { cropDescriptionResponseMock, cropSubClassResponseMock } from '@common/mocks';

describe('sortedCrops', () => {
  it('should return an empty array if inputs are empty', () => {
    expect(sortedCrops([], [])).toEqual([]);
  });

  it('should correctly sort the crops alphabetically by text', () => {
    const result = sortedCrops(cropSubClassResponseMock, cropDescriptionResponseMock);
    const expected = [
      {
        value: 'exampleDescriptionId2',
        text: 'Almonds N/A',
      },
      {
        value: '21d8bfa5-68f0-4c95-a8e3-53fba79aaa0c',
        text: 'Spring oats Generic',
      },
    ];

    expect(result).toEqual(expected);
  });
});
