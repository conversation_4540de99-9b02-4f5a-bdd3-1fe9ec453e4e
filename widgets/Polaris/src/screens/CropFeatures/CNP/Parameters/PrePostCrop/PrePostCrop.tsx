import React, { useMemo, useState } from 'react';
import { IconButton, Label, Table, Tooltip } from '@yaradigitallabs/ahua-react';
import TableSection from '../../../../../components/Table/Table';
import CollapsibleSection from '../../../../../components/Collapsible/Collapsible';
import {
  useGetCrops,
  usePrePostCrop,
  useCreateCropSequence,
  useFetchCropSequences,
  useUpdateCropSequence,
} from '@polaris-hooks/index';
import TableBody from './TableBody/TableBody';
import { CropSequence } from '@common/types';
import { useTranslation } from 'react-i18next';
import { AddPrePostCrop } from './AddPrePostCrop';
import { useAzure } from '@yaradigitallabs/sh-user-management-browser';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  PrePostCropAddState,
  EmptyStateAdditionalStyles,
  TooltipWrapper,
  tooltipStyles,
} from './index';
import { METHOD } from '@common/constants/api';
import { EmptyStateComponent, isAhdbActive } from '@widgets/Polaris/src/components';
import {
  onLastPage,
  onNextPage,
  onPrevPage,
  onFirstPage,
  sortedCrops,
  handleEditDialog,
  getShowPagination,
  cropSequenceChange,
  prePostAddBtnHandler,
  addPrePostInitialState,
} from './utils';

export function PrePostCrop() {
  const { t } = useTranslation();
  const keyPrefix = 'polaris.cnpDetails.planConfiguration.prePostCropParams';
  const { user } = useAzure();
  const { setDisplaySnackbar } = useSnackbar();
  const [pageIndex, setPageIndex] = useState<number>(0);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedPrePostCrop, setSelectedPrePostCrop] = useState<null | CropSequence>(null);

  const [prePostCropAddState, setPrePostCropAddState] =
    useState<PrePostCropAddState>(addPrePostInitialState);
  const {
    cropSequences,
    isCSLoading,
    cursor: cropSequencesCursor,
    hasNextPage: cropSequencesHasNextPage,
    maxPageIndex,
    setRefetch,
  } = useFetchCropSequences(pageIndex);
  const { isDisabledNext } = usePrePostCrop(cropSequences, pageIndex, setPageIndex);

  const { trigger: createCropSequence, isMutating: isCSCreating } = useCreateCropSequence();
  const { trigger: updateCropSequence, isMutating: isCSUpdating } = useUpdateCropSequence(
    selectedPrePostCrop?.id,
  );

  const {
    isLoading,
    cropRegion,
    selectedRegion,
    selectedCountry,
    cropSubClasses,
    cropDescriptions,
  } = useGetCrops();

  const isAhdb = (selectedCountry && isAhdbActive(selectedCountry)) || false;

  const cropDropdownData = useMemo(() => {
    if (!cropSubClasses && !cropDescriptions) return [];
    return sortedCrops(cropSubClasses, cropDescriptions) || [];
  }, [isLoading, selectedRegion, selectedCountry, cropSubClasses, cropDescriptions]);

  const prePostCropColumnNames = [
    { title: t('polaris.common.crop') },
    { title: t(`${keyPrefix}.preCrop`) },
    { title: t(`${keyPrefix}.postCrop`) },
    { title: isAhdb && t(`${keyPrefix}.mapsInAhdb`) },
    { title: t('polaris.common.lastModified') },
    { title: t('polaris.common.actions') },
  ];

  const tableHeader = useMemo(() => {
    return (
      <Table.Row className='table-header-row'>
        {prePostCropColumnNames.map((crop, i) => (
          <React.Fragment key={`${crop?.title}-${i}`}>
            {crop.title && (
              <Table.Head
                data-cy={`${crop.title}`}
                style={{
                  width: crop.title === t(`${keyPrefix}.mapsInAhdb`) && '103px',
                }}
              >
                <>
                  {isAhdb && i === 3 ? (
                    <TooltipWrapper className='tooltip-wrapper'>
                      <Tooltip
                        concept='inverse'
                        css={tooltipStyles}
                        text={t(`${keyPrefix}.mapsInAhdbMessage`)}
                        maxWidth={232}
                        minWidth={232}
                        tipVisibility={true}
                        position={'top'}
                        className='ahdb-table-cell-tooltip'
                        size={'xs'}
                      >
                        <IconButton
                          data-cy={`ahdb-tooltip-${crop?.title}`}
                          icon='Info'
                          colorConcept='neutral'
                        />
                      </Tooltip>
                      <Label size={'n'} data-cy={`ahdb-tooltip-text-${crop.title}`}>
                        {crop.title}
                      </Label>
                    </TooltipWrapper>
                  ) : (
                    crop.title
                  )}
                </>
              </Table.Head>
            )}
          </React.Fragment>
        ))}
      </Table.Row>
    );
  }, [prePostCropColumnNames]);

  const tableBody = useMemo(() => {
    return (
      <>
        {cropSequences?.map((cropSequence: CropSequence) => (
          <TableBody
            cropSequence={cropSequence}
            setRefetch={setRefetch}
            key={cropSequence?.id}
            handleEditDialog={(cropSequence) =>
              handleEditDialog(
                cropSequence,
                setIsOpen,
                setSelectedPrePostCrop,
                setPrePostCropAddState,
              )
            }
            setPrePostCropAddState={setPrePostCropAddState}
            isAhdb={isAhdb}
          />
        ))}
      </>
    );
  }, [cropSequences]);

  const addCropSeqHandler = async () => {
    let response: CropSequence | undefined;
    const [preCrop, postCrop] = prePostCropAddState.checkedBoxes;
    const { touchedFields } = prePostCropAddState;
    const areFieldsTouched = touchedFields.selectedCropDescription || touchedFields.checkedBoxes;

    const modifiedTimestamp = new Date().toISOString();
    const cropDescription = cropDropdownData?.find(
      ({ value }) => value === prePostCropAddState?.selectedCropDescription?.id,
    );

    const cropSequence = {
      cropRegionId: cropRegion?.id || '',
      cropDescriptionId: cropDescription?.value || '',
      isPreCrop: preCrop || false,
      isPostCrop: postCrop || false,
      created: modifiedTimestamp,
      modified: modifiedTimestamp,
      modifiedBy: user?.email,
    };

    if (prePostCropAddState?.selectedCropDescription && !selectedPrePostCrop) {
      try {
        response = await createCropSequence({
          method: METHOD.POST,
          body: JSON.stringify(cropSequence),
        });
      } catch (error) {
        console.error('Error creating crop-sequence data:', error);
      }
    } else if (
      selectedPrePostCrop &&
      prePostCropAddState?.selectedCropDescription &&
      areFieldsTouched
    ) {
      try {
        response = await updateCropSequence({
          method: METHOD.PUT,
          body: JSON.stringify(cropSequence),
        });
      } catch (error) {
        console.error('Error updating crop-sequence data:', error);
      }
    }

    if (response) {
      setDisplaySnackbar({
        title: selectedPrePostCrop
          ? t('polaris.common.changesSaved')
          : t(`${keyPrefix}.parametersAdded`),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 4000,
        open: true,
      });
      setRefetch(true);
      setIsOpen(false);
      setSelectedPrePostCrop(null);
    } else if (!areFieldsTouched && selectedPrePostCrop !== null) {
      setIsOpen(false);
    }

    setPrePostCropAddState((state) => ({
      ...state,
      hasErrorCrop: !prePostCropAddState?.selectedCropDescription ? true : false,
    }));
  };

  const hasMoreEntitiesAfterDelete =
    cropSequences && cropSequencesCursor > 5 && !cropSequences?.length;
  const shouldShowPagination = getShowPagination(
    pageIndex,
    cropSequences,
    cropSequencesHasNextPage,
    isCSLoading,
    isCSCreating,
    isCSUpdating,
  );

  return (
    <div className='pre-post-crop-container'>
      <CollapsibleSection
        dataCY='pre-post-crop-section'
        headerTitle={t(`${keyPrefix}.title`)}
        showCardContent={true}
        cardContentText={t(`${keyPrefix}.subTitle`)}
        onAddBtnClick={() =>
          prePostAddBtnHandler(
            setRefetch,
            setIsOpen,
            setSelectedPrePostCrop,
            setPrePostCropAddState,
          )
        }
      >
        {(cropSequences && cropSequences?.length > 0) || hasMoreEntitiesAfterDelete ? (
          <TableSection
            tableHeader={tableHeader}
            tableBody={tableBody}
            showPagination={prePostCropAddState?.isPrevPage || !shouldShowPagination}
            pageIndex={pageIndex}
            pageCount={pageIndex + 1}
            canNextPage={cropSequencesHasNextPage && !isDisabledNext}
            canPreviousPage={pageIndex > 0}
            onFirstPage={() => onFirstPage(setPageIndex)}
            onNextPage={() => onNextPage(setPageIndex, setPrePostCropAddState)}
            onPrevPage={() => onPrevPage(setPageIndex, setPrePostCropAddState)}
            onLastPage={() => onLastPage(setPageIndex, maxPageIndex)}
            variant={'prepost'}
            canGoLastPage={pageIndex === maxPageIndex || isDisabledNext}
          />
        ) : (
          <EmptyStateComponent
            dataCy='pre-post-crop-empty-state'
            message={t(`${keyPrefix}.emptyScreenText`)}
            styles={{ additionalStyles: EmptyStateAdditionalStyles }}
          />
        )}
      </CollapsibleSection>
      <AddPrePostCrop
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        addCropSeqHandler={addCropSeqHandler}
        cropDropdownData={cropDropdownData}
        prePostCropAddState={prePostCropAddState}
        setPrePostCropAddState={setPrePostCropAddState}
        cropSequenceChange={(value: string) =>
          cropSequenceChange(value, cropDescriptions, setPrePostCropAddState)
        }
        selectedPrePostCrop={selectedPrePostCrop}
      />
    </div>
  );
}
