/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';

import TableBody from '../TableBody';
import { CropSequence } from '@common/types';
import { userEvent } from '@testing-library/user-event';

// Mock all the hooks
jest.mock('../../../../../../../hooks', () => ({
  useFetchCropDescriptionsMapping: () => ({
    cropDescMappingsData: [],
    isCropDescMappingsFetching: false,
  }),
  useGetCropDescription: () => ({
    data: null,
    isLoading: false,
  }),
  useGetCropSubClass: () => ({
    data: null,
    isLoading: false,
  }),
  useDeleteCropSequence: () => ({
    data: null,
    trigger: jest.fn(),
    isMutating: false,
    error: null,
  }),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

jest.mock('@libs/snackbar-context/snackbar-context', () => ({
  useSnackbar: () => ({
    setDisplaySnackbar: jest.fn(),
  }),
}));

const cropSequenceData = {
  id: '2fe4-2ad3-2fs3-2342',
  cropSubClass: { name: 'Cauliflower N/A' },
  cropRegionId: '5yu4-6ed3-2ze3-6342',
  cropDescriptionId: '64f4-2sw3-2tw3-2342',
  isPreCrop: true,
  isPostCrop: true,
  created: '',
  modified: '',
  modifiedBy: '',
  deleted: null,
} as unknown as CropSequence;
const mockSetRefetch = jest.fn();
const mockHandleEditDialog = jest.fn();
const setPrePostState = jest.fn();

const mockProps = {
  cropSequence: cropSequenceData,
  setRefetch: mockSetRefetch,
  handleEditDialog: mockHandleEditDialog,
  setPrePostCropAddState: setPrePostState,
  isAhdb: false,
};

describe('widget: TableBody', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('TableBody: should render check-circle when isPreCrop and isPostCrop is true', () => {
    const component = render(
      <TableBody
        isAhdb={false}
        cropSequence={cropSequenceData}
        setRefetch={mockSetRefetch}
        handleEditDialog={mockHandleEditDialog}
        setPrePostCropAddState={setPrePostState}
      />,
    );

    expect(component.getByTestId('is-pre-crop-2fe4-2ad3-2fs3-2342')).toBeInTheDocument();
  });
  it('TableBody: should render clear-circle when isPreCrop and isPostCrop is false', () => {
    const component = render(
      <TableBody
        cropSequence={{
          ...cropSequenceData,
          isPreCrop: false,
          isPostCrop: false,
        }}
        isAhdb={false}
        setRefetch={mockSetRefetch}
        handleEditDialog={mockHandleEditDialog}
        setPrePostCropAddState={setPrePostState}
      />,
    );

    expect(component.getByTestId('is-pre-crop-2fe4-2ad3-2fs3-2342')).toBeInTheDocument();
  });

  test('TableBody: should match snapshot', () => {
    const component = render(
      <TableBody
        isAhdb={false}
        cropSequence={cropSequenceData}
        setRefetch={mockSetRefetch}
        handleEditDialog={mockHandleEditDialog}
        setPrePostCropAddState={setPrePostState}
      />,
    );
    expect(component).toMatchSnapshot();
  });

  it('opens and handles delete dialog correctly', async () => {
    const user = userEvent.setup();
    render(<TableBody {...mockProps} />);
    const actionMenuButton = screen.getByTestId('pre-post-crop-action-cell-2fe4-2ad3-2fs3-2342');
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const triggerButton = actionMenuButton.querySelector('button[aria-haspopup="menu"]')!;
    await user.click(triggerButton);
    expect(triggerButton).toBeInTheDocument();

    const deleteButton = screen.getByTestId('pre-post-crop-delete-action');
    await user.click(deleteButton);
    const confirmationDialog = screen.getByTestId('dialog-content');
    expect(confirmationDialog).toBeInTheDocument();
    const confirmButton = screen.getByTestId('dialog-confirmButton');
    expect(confirmButton).toBeInTheDocument();
    await waitFor(() => {
      user.click(confirmButton);
    });
  });
});
