// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PrePostCrop renders PrePostCrop: Should match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="pre-post-crop-container"
      >
        <div
          class="c-eNnZw collapsible-section"
          data-cy="pre-post-crop-section"
          data-state="closed"
        >
          <button
            aria-controls="radix-:r1o:"
            aria-expanded="false"
            class="c-cUgXyc"
            data-state="closed"
            type="button"
          >
            polaris.cnpDetails.planConfiguration.prePostCropParams.title
            <div
              class="c-irPLE"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 9.75l-5 6h-2l-5-6"
                />
              </svg>
            </div>
          </button>
          <div
            class="c-fLVWxk"
            data-state="closed"
            hidden=""
            id="radix-:r1o:"
            style=""
          />
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="pre-post-crop-container"
    >
      <div
        class="c-eNnZw collapsible-section"
        data-cy="pre-post-crop-section"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r1o:"
          aria-expanded="false"
          class="c-cUgXyc"
          data-state="closed"
          type="button"
        >
          polaris.cnpDetails.planConfiguration.prePostCropParams.title
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 9.75l-5 6h-2l-5-6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="closed"
          hidden=""
          id="radix-:r1o:"
          style=""
        />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
