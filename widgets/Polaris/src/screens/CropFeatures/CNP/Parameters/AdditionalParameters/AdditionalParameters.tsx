import React, { useCallback, useState } from 'react';
import CollapsibleSection from '../../../../../components/Collapsible/Collapsible';
import TableSection from '../../../../../components/Table/Table';
import { TableBody, TableHeader } from './components/index';
import { useTranslation } from 'react-i18next';
import { AdditionalParametersProps } from './AdditionalParameters.type';
import { useAdditionalParameters, useUpdateCropRegion, useTableConfig } from '@polaris-hooks/index';
import { AdditionalProperty } from '@common/types';
import './styles.scss';
import { displaySnackbarMessage } from '@widgets/Polaris/utils/successSnackbar/successSnackbar';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { deleteAdditionalProperty, getColumnDefs, updateParameters } from './utils';
import { AdditionalParametersPopup, EmptyStateComponent } from '@widgets/Polaris/src/components';
import { EmptyStateAdditionalStyles } from '@widgets/Polaris/src/screens/CropFeatures/CNP/Parameters/AdditionalParameters/AdditionalParameters.styled';
import { isAhdbActive } from '@widgets/Polaris/src/components/AdditionalParametersPopup/utils';
import { AhdbTabs } from '@common/constants';

export function AdditionalParameters({ cropRegion }: AdditionalParametersProps) {
  const { t } = useTranslation('polaris');
  const [ahdbParam] = AhdbTabs;

  const { showAdditionalProperties, additionalProperties, showEmptyState } =
    useAdditionalParameters(cropRegion, t);
  const keyPrefix = 'polaris.cnpDetails.configuration.additionalParameters';
  const [showDialog, setShowDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [initialData, setInitialData] = useState<AdditionalProperty>();

  const [activeTab, setActiveTab] = useState<string>(ahdbParam);
  const {
    selectedCountry,
    methods: { setCropRegion },
  } = useAppContext();
  const isAhdb = selectedCountry && isAhdbActive(selectedCountry);

  const { trigger: triggerUpdateCropRegion, isCropRegionUpdating } = useUpdateCropRegion(
    cropRegion?.id,
  );
  const { setDisplaySnackbar } = useSnackbar();
  const { table, paginationState, autoResetPageIndexRef } = useTableConfig<AdditionalProperty>(
    additionalProperties || [],
    () => getColumnDefs({ t, keyPrefix }),
    { pageIndex: 0, pageSize: 5 },
  );

  const [resetSearch, setResetSearch] = React.useState<boolean>(false);

  const handleDeleteItem = useCallback(
    (parameterName: string) => {
      // If the last row of the table is deleted, the table should go to the previous page
      // skip page reset to avoid the table to go to the first page
      autoResetPageIndexRef.current = false;
      if (table.getRowCount() % 5 === 1) {
        table.previousPage();
      }
      deleteAdditionalProperty({
        cropRegion,
        parameterName,
        triggerUpdateCropRegion,
        setCropRegion,
        displaySnackbarMessage,
        setDisplaySnackbar,
        t,
      });
    },
    [
      cropRegion,
      triggerUpdateCropRegion,
      setCropRegion,
      displaySnackbarMessage,
      setDisplaySnackbar,
      t,
    ],
  );

  const handleUpdateParameters = async (
    updatedParameters: AdditionalProperty[] | undefined,
  ): Promise<void> => {
    await updateParameters(cropRegion, updatedParameters, triggerUpdateCropRegion, setCropRegion);
  };

  const handleAddParameter = async (newParameter: AdditionalProperty): Promise<void> => {
    const updatedParameters = [...(additionalProperties || []), newParameter];
    await handleUpdateParameters(updatedParameters);
  };

  const handleEditParameter = async (updatedParameter: AdditionalProperty): Promise<void> => {
    if (!initialData) {
      console.error('No initial data provided for editing.');
      return;
    }
    const updatedParameters = additionalProperties?.map((p) =>
      p.Name === initialData.Name ? { ...p, ...updatedParameter } : p,
    );
    await handleUpdateParameters(updatedParameters);
  };

  const handleEditItem = (parameter: AdditionalProperty): void => {
    setInitialData(parameter);
    setIsEditMode(true);
    setShowDialog(true);
  };

  const handlePopupOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      setIsEditMode(false);
      setInitialData(undefined);
    }
    setShowDialog(isOpen);
    setActiveTab(ahdbParam);
  };

  const handleAddBtnClick = useCallback((): void => {
    setIsEditMode(false);
    setInitialData(undefined);
    setShowDialog(true);
  }, []);

  return (
    <>
      <CollapsibleSection
        headerTitle={t(`${keyPrefix}.title`)}
        showCardContent
        cardContentText={t(`${keyPrefix}.subtitle`)}
        onAddBtnClick={handleAddBtnClick}
        showMoreDetails
        disabledMoreDetails={!showAdditionalProperties}
        dataCY={'additional-parameters-collapsible'}
        ahdbTooltipMsg={
          (isAhdb && t(`${keyPrefix}.additionalParametersPopup.ahdbTooltipInfo`)) || ''
        }
      >
        {showAdditionalProperties && (
          <>
            <TableSection
              showPagination={table.getRowCount() ? table.getRowCount() > 5 : false}
              tableHeader={
                <TableHeader
                  table={table}
                  resetSearch={resetSearch}
                  autoResetPageIndexRef={autoResetPageIndexRef}
                />
              }
              tableBody={
                <TableBody
                  table={table}
                  isLoading={isCropRegionUpdating}
                  handleDeleteItem={(parameterName: string) => handleDeleteItem(parameterName)}
                  handleEditItem={handleEditItem}
                />
              }
              pageCount={table.getPageCount()}
              pageIndex={paginationState.pageIndex}
              canNextPage={table.getCanNextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              onNextPage={() => table.nextPage()}
              onPrevPage={() => table.previousPage()}
              onLastPage={() => table.lastPage()}
              onFirstPage={() => table.firstPage()}
              variant={table.getFilteredRowModel().rows.length !== 0 ? 'static' : 'dynamic'}
            />
            {table.getFilteredRowModel().rows.length === 0 && (
              <EmptyStateComponent
                dataCy='additional-parameters-empty-search-state'
                styles={{ additionalStyles: EmptyStateAdditionalStyles }}
                message={t(`polaris.common.emptySearchState.text`)}
                searchTerm={` ${table
                  .getState()
                  .columnFilters.map((filter) => filter.value)
                  .join(' ')}`}
                actionText={t(`polaris.common.emptySearchState.button`)}
                onActionClick={() => {
                  setResetSearch(!resetSearch);
                  table.resetColumnFilters(true);
                }}
              />
            )}
          </>
        )}
        {showEmptyState && (
          <EmptyStateComponent
            dataCy='additional-parameters-empty-state'
            message={t(`${keyPrefix}.emptyStateText`)}
            styles={{ additionalStyles: EmptyStateAdditionalStyles }}
          />
        )}
      </CollapsibleSection>
      <AdditionalParametersPopup
        showDialog={showDialog}
        onOpenChange={handlePopupOpenChange}
        cropRegion={cropRegion}
        onSave={handleAddParameter}
        onEdit={handleEditParameter}
        isEditMode={isEditMode}
        initialData={initialData}
        triggerUpdateCropRegion={triggerUpdateCropRegion}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />
    </>
  );
}
