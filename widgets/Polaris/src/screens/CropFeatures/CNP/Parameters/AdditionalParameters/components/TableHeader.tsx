import React, { Fragment } from 'react';
import { AdditionalProperty } from '@common/types';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { Label, Table } from '@yaradigitallabs/ahua-react';
import { TableSortHeadStyled } from '../AdditionalParameters.styled';
import { flexRender } from '@tanstack/react-table';
import { Searchbar } from '@widgets/Polaris/src/components/Searchbar/Searchbar';

export type TableHeaderProps = {
  table: TanstackTable<AdditionalProperty>;
  resetSearch: boolean;
  autoResetPageIndexRef: React.MutableRefObject<boolean>;
};

export function TableHeader({
  table,
  resetSearch,
  autoResetPageIndexRef,
}: TableHeaderProps): JSX.Element {
  return (
    <>
      {table.getHeaderGroups().map((headerGroup, index) => {
        const key = headerGroup.headers[index].id;
        return (
          <Fragment key={key}>
            <Table.Row className='table-header-row'>
              {headerGroup.headers.map((header) => {
                return (
                  <TableSortHeadStyled
                    direction={header.column.getIsSorted()}
                    active={Boolean(header.column.getIsSorted())}
                    className={header.column.getCanSort() ? 'cursor-pointer select-none' : ''}
                    onClick={header.column.getToggleSortingHandler()}
                    key={`${header.id}-${header.column.columnDef.header}`}
                    color={'primary'}
                    colSpan={header.colSpan}
                  >
                    <Label size={'n'}>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </Label>
                  </TableSortHeadStyled>
                );
              })}
            </Table.Row>
            <Table.Row>
              {headerGroup.headers.map((header) => {
                return (
                  <Table.Cell key={`${header.id}-${headerGroup.id}`}>
                    {header.column.getCanFilter() ? (
                      <Searchbar
                        column={header.column}
                        resetSearch={resetSearch}
                        autoResetPageIndexRef={autoResetPageIndexRef}
                      />
                    ) : null}
                  </Table.Cell>
                );
              })}
            </Table.Row>
          </Fragment>
        );
      })}
    </>
  );
}
