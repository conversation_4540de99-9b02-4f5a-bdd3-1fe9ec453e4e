import {
  DeleteAdditionalPropertyArgs,
  RequestConfig,
  TableConfigProps,
} from '../AdditionalParameters.type';
import { ColumnDef } from '@tanstack/react-table';
import { AdditionalProperty, CropRegion } from '@common/types';
import { generateTranslationKey, toPascalCase } from '@widgets/Polaris/utils';
import { TranslataionKeys } from '@widgets/Polaris/consts';
import {
  ahdbGrassFieldTypes,
  ahdbGrassGrowthClasses,
  ahdbGrassSeasons,
  ahdbGrassYieldTypes,
  ahdbPreviousGrasses,
  ahdbAdditionalPropertyNames,
  ATFARM_CROP_TYPES,
} from '../AdditionalParameters.constants';
import { METHOD } from '@common/constants';

export async function deleteAdditionalProperty({
  cropRegion,
  parameterName,
  triggerUpdateCropRegion,
  setCropRegion,
  displaySnackbarMessage,
  t,
  setDisplaySnackbar,
}: DeleteAdditionalPropertyArgs): Promise<void> {
  const updatedParameters = cropRegion?.additionalProperties?.filter(
    (param) => param.Name !== parameterName,
  );
  const updatedCropRegionData = {
    ...cropRegion,
    additionalProperties: updatedParameters,
  };

  try {
    const updatedData = await triggerUpdateCropRegion({
      method: METHOD.PUT,
      body: JSON.stringify(updatedCropRegionData),
    });

    if (updatedData) {
      setCropRegion(updatedData);
    }

    displaySnackbarMessage(
      t('common.snackbarDeleteMessage', { name: 'Parameter' }),
      setDisplaySnackbar,
    );
  } catch (error) {
    console.error('Error deleting additional parameter:', error);
  }
}

export function getColumnDefs({ t, keyPrefix }: TableConfigProps): ColumnDef<AdditionalProperty>[] {
  return [
    {
      header: t(`table.header.parameter`, { keyPrefix }),
      accessorKey: 'Name',
      enableColumnFilter: true,
    },
    {
      header: t(`table.header.ordinal`, { keyPrefix }),
      accessorKey: 'Ordinal',
      enableColumnFilter: false,
    },
    {
      header: t(`table.header.valueType`, { keyPrefix }),
      accessorKey: 'ValueType',
      enableColumnFilter: true,
    },
    {
      header: t(`table.header.actions`, { keyPrefix }),
      accessorKey: 'actions',
      enableSorting: false,
      enableColumnFilter: false,
    },
  ];
}

/**
 * Updates the parameters of a crop region.
 * @param {Object} cropRegion - The current crop region data.
 * @param {Array} updatedParameters - The new parameters to update.
 * @param {Function} triggerUpdateCropRegion - Function to call the API for updating the crop region.
 * @param {Function} setCropRegion - State setter function to update the crop region in the state.
 * @returns {Promise<void>}
 */
export async function updateParameters(
  cropRegion: CropRegion | null,
  updatedParameters: AdditionalProperty[] | undefined,
  triggerUpdateCropRegion: (config: RequestConfig) => Promise<CropRegion | undefined>,
  setCropRegion: (cropRegion: CropRegion | null) => void,
): Promise<void> {
  const updatedCropRegionData = {
    ...cropRegion,
    additionalProperties: updatedParameters,
  };

  try {
    const updatedData = await triggerUpdateCropRegion({
      method: METHOD.PUT,
      body: JSON.stringify(updatedCropRegionData),
    });
    if (updatedData) {
      setCropRegion(updatedData);
    }
  } catch (error) {
    console.error('Error updating parameters:', error);
    throw error;
  }
}

export const isGrasslandCrop = (cropType: string): boolean =>
  cropType === ATFARM_CROP_TYPES.GRASSLAND;

export const generateAhdbAdditionalParameters = (cropType = ''): AdditionalProperty[] => {
  const defaultYear = new Date().getFullYear();
  const keyPrefix = 'polaris.cnpDetails.configuration.additionalParameters';
  const ahdbParams = [
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.previousGrass),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '1',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.previousGrass,
      Options: ahdbPreviousGrasses.map(({ id, name }) => ({
        Name: name,
        TranslationKey: generateTranslationKey(TranslataionKeys.ADDITIONAL_PARAMETERS, name),
        Value: id.toString(),
      })),
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.previousGrass,
      ),
      UnitTags: null,
      ValueType: 'list',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.harvestYear),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: defaultYear.toString(),
      Max: defaultYear + 10,
      Min: defaultYear - 1,
      Name: ahdbAdditionalPropertyNames.harvestYear,
      Options: null,
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.harvestYear,
      ),
      UnitTags: null,
      ValueType: 'number',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.sowingDate),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.sowingDate,
      Options: null,
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.sowingDate,
      ),
      UnitTags: null,
      ValueType: 'date',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.analysisDate),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.analysisDate,
      Options: null,
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.analysisDate,
      ),
      UnitTags: null,
      ValueType: 'date',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.selectedSoilReleaseNaturalPotash),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: 'false',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.selectedSoilReleaseNaturalPotash,
      Options: null,
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.selectedSoilReleaseNaturalPotash,
      ),
      UnitTags: null,
      ValueType: 'boolean',
    },
  ];

  const grassLandCropAhdbParams = [
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.fieldType), // TODO check
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '1',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.fieldType,
      Options: ahdbGrassFieldTypes.map(({ id, name }) => ({
        Name: name,
        TranslationKey: `${keyPrefix}${name}`,
        Value: id.toString(),
      })),
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.fieldType,
      ),
      UnitTags: null,
      ValueType: 'list',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.growthClass),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '3',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.growthClass,
      Options: ahdbGrassGrowthClasses.map(({ id, name }) => ({
        Name: name,
        TranslationKey: generateTranslationKey(TranslataionKeys.ADDITIONAL_PARAMETERS, name),
        Value: id.toString(),
      })),
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.growthClass,
      ),
      UnitTags: null,
      ValueType: 'list',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.season),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '0',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.season,
      Options: ahdbGrassSeasons.map(({ id, name }) => ({
        Name: name,
        TranslationKey: generateTranslationKey(TranslataionKeys.ADDITIONAL_PARAMETERS, name),
        Value: id.toString(),
      })),
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.season,
      ),
      UnitTags: null,
      ValueType: 'list',
    },
    {
      ApiPropertyName: toPascalCase(ahdbAdditionalPropertyNames.yieldType),
      BaseUnit: null,
      CalculationType: 'ALL',
      DefaultValue: '1',
      Max: null,
      Min: null,
      Name: ahdbAdditionalPropertyNames.yieldType,
      Options: ahdbGrassYieldTypes.map(({ id, name }) => ({
        Name: name,
        TranslationKey: generateTranslationKey(TranslataionKeys.ADDITIONAL_PARAMETERS, name),
        Value: id.toString(),
      })),
      Ordinal: 0,
      Required: true,
      TranslationKey: generateTranslationKey(
        TranslataionKeys.ADDITIONAL_PARAMETERS,
        ahdbAdditionalPropertyNames.yieldType,
      ),
      UnitTags: null,
      ValueType: 'list',
    },
  ];

  return isGrasslandCrop(cropType) ? [...ahdbParams, ...grassLandCropAhdbParams] : ahdbParams;
};
