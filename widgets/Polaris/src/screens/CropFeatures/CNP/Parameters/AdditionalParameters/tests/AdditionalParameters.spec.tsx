import React from 'react';
import { AdditionalParameters } from '../AdditionalParameters';
import { render, screen, waitFor } from '@testing-library/react';
import {
  mockAppProviderValue,
  testCropRegionNoParameters,
  testCropRegionWithParameters,
  unitCountriesHandler,
  updateCropRegionHandler,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { userEvent } from '@testing-library/user-event';
import { setupServer } from 'msw/node';
import { act } from '@testing-library/react';

const server = setupServer(updateCropRegionHandler, unitCountriesHandler);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

const mockUseEffect = jest.fn();

jest.spyOn(React, 'useEffect').mockImplementation(mockUseEffect);

describe('AdditionalParameters', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AdditionalParameters cropRegion={null} />
      </AppContext.Provider>,
    );
  });

  it('Renders component with no parameters', async () => {
    let component;
    await act(async () => {
      component = render(
        <AppContext.Provider value={mockAppProviderValue}>
          <AdditionalParameters cropRegion={testCropRegionNoParameters} />
        </AppContext.Provider>,
      );
    });

    expect(
      screen.getByText('polaris.cnpDetails.configuration.additionalParameters.title'),
    ).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('displays empty state when there are no additional properties', async () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AdditionalParameters cropRegion={testCropRegionNoParameters} />
      </AppContext.Provider>,
    );
    const collapsible = screen.getByTestId('additional-parameters-collapsible');
    expect(collapsible).toBeInTheDocument();

    const button = screen.getByRole('button', {
      name: 'polaris.cnpDetails.configuration.additionalParameters.title',
    });
    await act(async () => {
      userEvent.click(button);
    });
    await waitFor(() =>
      expect(screen.getByTestId('additional-parameters-empty-state')).toBeInTheDocument(),
    );
  });

  // TODO: Find out why this is not working and fix it, also add more coverage to this comp test
  // it('Checks all functionality exists including buttons and functions', async () => {
  //   const component = render(
  //     <AppContext.Provider value={mockAppProviderValue}>
  //       <AdditionalParameters cropRegion={testCropRegionWithParameters} />,
  //     </AppContext.Provider>,
  //   );
  //   const collapsible = screen.getByTestId('additional-parameters-collapsible');
  //   expect(collapsible).toBeInTheDocument();
  //   const button = screen.getByRole('button', {
  //     name: 'polaris.cnpDetails.configuration.additionalParameters.title',
  //   });
  //   await act(async () => {
  //     userEvent.click(button);
  //   });
  //   const subtitle = screen.getByTestId('additional-parameters-collapsible');
  //   expect(subtitle).toBeInTheDocument();
  // const nextPageButton = screen.getByLabelText('Go to next page');
  // expect(nextPageButton).toBeInTheDocument();
  // const lastPageButton = screen.getByLabelText('Go to last page');
  // expect(lastPageButton).toBeInTheDocument();
  // fireEvent.click(nextPageButton);
  // expect(screen.getAllByText('Next page test')[0]).toBeInTheDocument();
  // const prevPageButton = screen.getByLabelText('Go to previous page');
  // fireEvent.click(prevPageButton);
  // fireEvent.click(lastPageButton);
  // fireEvent.click(screen.getByLabelText('Go to first page'));
  // expect(component).toMatchSnapshot();
  // });

  it('opens and interacts with the add parameter popup', async () => {
    const user = userEvent.setup();
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AdditionalParameters cropRegion={testCropRegionWithParameters} />
      </AppContext.Provider>,
    );
    const collapsible = screen.getByTestId('additional-parameters-collapsible');
    expect(collapsible).toBeInTheDocument();
    const button = screen.getByRole('button', {
      name: 'polaris.cnpDetails.configuration.additionalParameters.title',
    });
    await user.click(button);
    await waitFor(() => expect(button).toHaveAttribute('aria-expanded', 'true'));
    const addButton = screen.getByTestId('collapsible-add-button');
    await user.click(addButton);
    waitFor(() => expect(screen.getByTestId('additional-properties-content')).toBeInTheDocument());
  });
});
