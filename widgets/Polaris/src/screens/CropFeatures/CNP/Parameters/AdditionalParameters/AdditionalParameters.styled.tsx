import { CSSProperties } from 'react';
import { styled, Table } from '@yaradigitallabs/ahua-react';

const EMPTY_STATE_HEIGHT = 272;

export const EmptyStateAdditionalStyles: CSSProperties = {
  height: EMPTY_STATE_HEIGHT,
};

export const TableSortHeadStyled = styled(Table.SortHead, {
  svg: {
    color: '$brand-contrast',
    stroke: '$brand-contrast',
  },
});

export const TableCellStyled = styled(Table.Cell, {
  variants: {
    hasEllipsis: {
      true: {
        maxWidth: '98px',
        ['& p']: {
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      },
    },
  },
});
