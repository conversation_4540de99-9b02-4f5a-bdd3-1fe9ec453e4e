import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import {
  TableBody,
  TableBodyProps,
} from '@widgets/Polaris/src/screens/CropFeatures/CNP/Parameters/AdditionalParameters/components';
import { AdditionalProperty, CalculationType } from '@common/types';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { userEvent } from '@testing-library/user-event';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (
      key: string,
      {
        parameterName,
      }: {
        parameterName?: string;
      } = {},
    ) => {
      if (parameterName) {
        return `Delete ${parameterName}`;
      }
      return key;
    },
  }),
}));

// eslint-disable-next-line
const createMockTable = (data: any[]): TanstackTable<AdditionalProperty> => {
  const baseMock = {
    getRowModel: () => ({
      rows: data.map((item, index) => ({
        id: `row-${index}`,
        original: item,
        getVisibleCells: () => [
          {
            column: {
              id: 'actions',
              columnDef: { cell: jest.fn() },
            },
            getValue: jest.fn(() => item.Name),
          },
        ],
      })),
    }),
  };
  return baseMock as unknown as TanstackTable<AdditionalProperty>;
};

const mockData: AdditionalProperty[] = [
  {
    ApiPropertyName: 'Nmin',
    BaseUnit: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    CalculationType: CalculationType.ALL,
    DefaultValue: '50',
    Max: 250,
    Min: 0,
    Name: 'N min',
    Ordinal: 1,
    Required: true,
    TranslationKey: 'additional.parameters.n_min',
    UnitTags: 'NMinUnit',
    ValueType: 'numberWithUnit',
  },
];

const mockHandleDeleteItem = jest.fn();
const mockHandleEditItem = jest.fn();

const mockTableProps: TableBodyProps = {
  table: createMockTable(mockData),
  handleDeleteItem: mockHandleDeleteItem,
  handleEditItem: mockHandleEditItem,
  isLoading: false,
};

describe('<TableBody />', () => {
  it('opens the delete dialog with the correct title and click cancel', async () => {
    const user = userEvent.setup();
    render(<TableBody {...mockTableProps} />);

    const actionMenuButton = screen.getByTestId('additional-parameters-action-cell-row-0');
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const triggerButton = actionMenuButton.querySelector('button[aria-haspopup="menu"]')!;

    expect(triggerButton).toBeInTheDocument();

    await user.click(triggerButton);

    await waitFor(() => {
      expect(triggerButton).toHaveAttribute('aria-expanded', 'true');
    });

    const menu = screen.getByRole('menu');
    const menuItems = screen.getAllByRole('menuitem');
    expect(menu).toBeInTheDocument();
    expect(menuItems.length).toBeGreaterThan(0);

    const deleteMenuItem = screen.getByTestId('additional-parameters-delete-action');
    await user.click(deleteMenuItem);
    expect(await screen.findByText('Delete N min')).toBeInTheDocument();

    const cancelButton = screen.getByTestId('dialog-closeButton');
    await user.click(cancelButton);

    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('opens the delete dialog with the correct title and confirms deletion', async () => {
    const user = userEvent.setup();
    render(<TableBody {...mockTableProps} />);

    const actionMenuButton = screen.getByTestId('additional-parameters-action-cell-row-0');
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const triggerButton = actionMenuButton.querySelector('button[aria-haspopup="menu"]')!;

    expect(triggerButton).toBeInTheDocument();

    await user.click(triggerButton);
    await waitFor(() => {
      expect(triggerButton).toHaveAttribute('aria-expanded', 'true');
    });

    const deleteMenuItem = screen.getByTestId('additional-parameters-delete-action');
    await user.click(deleteMenuItem);

    expect(await screen.findByText('Delete N min')).toBeInTheDocument();

    const confirmButton = screen.getByTestId('dialog-confirmButton');
    await user.click(confirmButton);

    expect(mockHandleDeleteItem).toHaveBeenCalledWith('N min');

    await waitFor(() => {
      expect(screen.queryByText('Delete N min')).not.toBeInTheDocument();
    });
  });
});
