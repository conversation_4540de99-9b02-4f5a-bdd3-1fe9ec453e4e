import React, { useState } from 'react';
import { AdditionalProperty } from '@common/types';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { Caption, Table } from '@yaradigitallabs/ahua-react';
import { flexRender } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';
import { ConfirmationDialog } from '@widgets/Polaris/src/components/ConfirmationDialog';
import { TableCellStyled } from '../AdditionalParameters.styled';

export type TableBodyProps = {
  table: TanstackTable<AdditionalProperty>;
  handleDeleteItem: (parameterName: string) => void;
  handleEditItem: (parameter: AdditionalProperty) => void;
  isLoading: boolean;
};

export function TableBody({ table, handleDeleteItem, handleEditItem, isLoading }: TableBodyProps) {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const translationPrefix = 'cnpDetails.configuration.additionalParameters';
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteDialogTitle, setDeleteDialogTitle] = useState('');
  const [currentDeletingItem, setCurrentDeletingItem] = useState('');

  const handleOpenDeleteDialog = (parameterName: string) => {
    setCurrentDeletingItem(parameterName);
    setDeleteDialogTitle(t(`${translationPrefix}.dialog.title`, { parameterName }));
    setOpenDeleteDialog(true);
  };

  const confirmDelete = () => {
    handleDeleteItem(currentDeletingItem);
    setOpenDeleteDialog(false);
  };

  return (
    <>
      {table.getRowModel().rows.map((row) => {
        return (
          <Table.Row key={row.id}>
            {row.getVisibleCells().map((cell) => {
              const isOrdinalColumn = cell.column.id.includes('Ordinal');
              return cell.column.id !== 'actions' ? (
                <TableCellStyled key={`${row.id}-${cell.id}`} hasEllipsis={isOrdinalColumn}>
                  <Caption>{flexRender(cell.column.columnDef.cell, cell.getContext())}</Caption>
                </TableCellStyled>
              ) : (
                <Table.ActionsCell
                  key={`${row.id}-${cell.id}`}
                  data-cy={`additional-parameters-action-cell-${row.id}`}
                >
                  <Table.ActionMenu title=''>
                    <Table.ActionMenuItem
                      icon='Edit'
                      title={t(`${translationPrefix}.table.actions.edit`)}
                      onClick={() => handleEditItem(row.original)}
                      data-cy='additional-parameters-edit-action'
                    >
                      {t(`${translationPrefix}.table.actions.edit`)}
                    </Table.ActionMenuItem>
                    <Table.ActionMenuItem
                      icon='Delete'
                      title={t(`${translationPrefix}.table.actions.delete`)}
                      onClick={() => handleOpenDeleteDialog(row.original.Name)}
                      data-cy='additional-parameters-delete-action'
                    >
                      {t(`${translationPrefix}.table.actions.delete`)}
                    </Table.ActionMenuItem>
                  </Table.ActionMenu>
                </Table.ActionsCell>
              );
            })}
          </Table.Row>
        );
      })}
      <ConfirmationDialog
        open={openDeleteDialog}
        title={deleteDialogTitle}
        description={t(`${translationPrefix}.dialog.description`)}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={t('common.yesDelete')}
        cancelButton={t('common.cancel')}
        onOk={confirmDelete}
        onCancel={() => setOpenDeleteDialog(false)}
        isLoading={isLoading}
      />
    </>
  );
}
