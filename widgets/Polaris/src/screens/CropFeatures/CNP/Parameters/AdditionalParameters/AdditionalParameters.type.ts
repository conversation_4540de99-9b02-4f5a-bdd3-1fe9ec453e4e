import { METHOD } from '@common/constants';
import { CropRegion, SnackBarType } from '@common/types';
import { TFunction } from 'i18next';

export interface AdditionalParametersProps {
  cropRegion: CropRegion | null;
}

export interface RequestConfig {
  method: METHOD.PUT;
  body: string;
}

export interface DeleteAdditionalPropertyArgs {
  cropRegion: CropRegion | null;
  parameterName: string;
  triggerUpdateCropRegion: (config: RequestConfig) => Promise<CropRegion | undefined>;
  setCropRegion: (cropRegion: CropRegion | null) => void;
  displaySnackbarMessage: (
    message: string,
    setDisplaySnackbar: React.Dispatch<React.SetStateAction<SnackBarType>>,
  ) => void;
  t: TFunction;
  setDisplaySnackbar: React.Dispatch<React.SetStateAction<SnackBarType>>;
}

export interface TableConfigProps {
  t: TFunction;
  keyPrefix: string;
}
