import { CropRegion } from '@common/types';
import {
  deleteAdditionalProperty,
  updateParameters,
  isGrasslandCrop,
  generateAhdbAdditionalParameters,
} from './additionalPropertiesUtils';
import { additionalProperties, testCropRegionWithParameters } from '@common/mocks';
import { RequestConfig } from '../AdditionalParameters.type';

const defaultYear = new Date().getFullYear();

const expectedAhdbParamsWithGrassland = [
  {
    ApiPropertyName: 'PreviousGrass',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '1',
    Max: null,
    Min: null,
    Name: 'Previous grass',
    Options: [
      {
        Name: 'Not grass in previous 3 Harvest years (HY)',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.notGrassInPrevious3HarvestYearsHy',
        Value: '1',
      },
      {
        Name: 'Ploughed last HY. 2 or more cuts, little manure',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy2OrMoreCutsLittleManure',
        Value: '2',
      },
      {
        Name: 'Ploughed last HY. 1-2yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy12yrLey1OrMoreCuts250kgnHa',
        Value: '3',
      },
      {
        Name: 'Ploughed last HY. 1-2yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy12yrLeyGrazedOnly250kgnHa',
        Value: '4',
      },
      {
        Name: 'Ploughed last HY. 1-2yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy12yrLeyGrazedOnly250kgnHa',
        Value: '5',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLey1OrMoreCuts250kgnHa',
        Value: '6',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLeyGrazedOnly250kgnHa',
        Value: '7',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, 1 cut + grazing, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLey1CutGrazing250kgnHa',
        Value: '8',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLeyGrazedOnly250kgnHa',
        Value: '9',
      },
      {
        Name: 'Ploughed HY-2. 2 or more cuts, little manure',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy22OrMoreCutsLittleManure',
        Value: '10',
      },
      {
        Name: 'Ploughed HY-2. 1-2yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy212yrLey1OrMoreCuts250kgnHa',
        Value: '11',
      },
      {
        Name: 'Ploughed HY-2. 1-2yr ley, grazed, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy212yrLeyGrazed250kgnHa',
        Value: '12',
      },
      {
        Name: 'Ploughed HY-2. 1-2yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy212yrLeyGrazedOnly250kgnHa',
        Value: '13',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLey1OrMoreCuts250kgnHa',
        Value: '14',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLeyGrazedOnly250kgnHa',
        Value: '15',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, 1 cut + grazing, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLey1CutGrazing250kgnHa',
        Value: '16',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLeyGrazedOnly250kgnHa',
        Value: '17',
      },
      {
        Name: 'Ploughed HY-3. 2 or more cuts, little manure',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy32OrMoreCutsLittleManure',
        Value: '18',
      },
      {
        Name: 'Ploughed HY-3. 1-2yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy312yrLey1OrMoreCuts250kgnHa',
        Value: '19',
      },
      {
        Name: 'Ploughed HY-3. 1-2yr ley, grazed, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy312yrLeyGrazed250kgnHa',
        Value: '20',
      },
      {
        Name: 'Ploughed HY-3. 1-2yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy312yrLeyGrazedOnly250kgnHa',
        Value: '21',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLey1OrMoreCuts250kgnHa',
        Value: '22',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLeyGrazedOnly250kgnHa',
        Value: '23',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, 1 cut + grazing, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLey1CutGrazing250kgnHa',
        Value: '24',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLeyGrazedOnly250kgnHa',
        Value: '25',
      },
    ],
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.previousGrass',
    UnitTags: null,
    ValueType: 'list',
  },
  {
    ApiPropertyName: 'HarvestYear',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: defaultYear.toString(),
    Max: defaultYear + 10,
    Min: defaultYear - 1,
    Name: 'Harvest year',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.harvestYear',
    UnitTags: null,
    ValueType: 'number',
  },
  {
    ApiPropertyName: 'SowingDate',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '',
    Max: null,
    Min: null,
    Name: 'Sowing date',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.sowingDate',
    UnitTags: null,
    ValueType: 'date',
  },
  {
    ApiPropertyName: 'SoilAnalysisDate',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '',
    Max: null,
    Min: null,
    Name: 'Soil analysis date',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.soilAnalysisDate',
    UnitTags: null,
    ValueType: 'date',
  },
  {
    ApiPropertyName: 'SelectedSoilReferenceNaturalPotash',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: 'false',
    Max: null,
    Min: null,
    Name: 'Selected soil reference natural potash',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey:
      'polaris.cnpDetails.configuration.additionalParameters.selectedSoilReferenceNaturalPotash',
    UnitTags: null,
    ValueType: 'boolean',
  },
  {
    ApiPropertyName: 'FieldType',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '1',
    Max: null,
    Min: null,
    Name: 'Field type',
    Options: [
      {
        Name: 'Grazing',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParametersGrazing',
        Value: '1',
      },
      {
        Name: 'Hay',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParametersHay',
        Value: '2',
      },
      {
        Name: 'Silage',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParametersSilage',
        Value: '3',
      },
      {
        Name: 'Clover',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParametersClover',
        Value: '4',
      },
      {
        Name: 'Grazing & Hay',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParametersGrazing & Hay',
        Value: '5',
      },
      {
        Name: 'Grazing & Silage',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParametersGrazing & Silage',
        Value: '6',
      },
    ],
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.fieldType',
    UnitTags: null,
    ValueType: 'list',
  },
  {
    ApiPropertyName: 'GrowthClass',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '3',
    Max: null,
    Min: null,
    Name: 'Growth class',
    Options: [
      {
        Name: 'Very poor',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.veryPoor',
        Value: '1',
      },
      {
        Name: 'Poor',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.poor',
        Value: '2',
      },
      {
        Name: 'Average',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.average',
        Value: '3',
      },
      {
        Name: 'Good',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.good',
        Value: '4',
      },
      {
        Name: 'Very good',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.veryGood',
        Value: '5',
      },
    ],
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.growthClass',
    UnitTags: null,
    ValueType: 'list',
  },
  {
    ApiPropertyName: 'Season',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '0',
    Max: null,
    Min: null,
    Name: 'Season',
    Options: [
      {
        Name: 'None',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.none',
        Value: '0',
      },
      {
        Name: 'Autumn',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.autumn',
        Value: '1',
      },
      {
        Name: 'Spring',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.spring',
        Value: '2',
      },
    ],
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.season',
    UnitTags: null,
    ValueType: 'list',
  },
  {
    ApiPropertyName: 'YieldType',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '1',
    Max: null,
    Min: null,
    Name: 'Yield type',
    Options: [
      {
        Name: 'Dry Matter',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.dryMatter',
        Value: '1',
      },
      {
        Name: 'Fresh Weight',
        TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.freshWeight',
        Value: '2',
      },
    ],
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.yieldType',
    UnitTags: null,
    ValueType: 'list',
  },
];

const expectedAhdbParamsWithoutGrassland = [
  {
    ApiPropertyName: 'PreviousGrass',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '1',
    Max: null,
    Min: null,
    Name: 'Previous grass',
    Options: [
      {
        Name: 'Not grass in previous 3 Harvest years (HY)',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.notGrassInPrevious3HarvestYearsHy',
        Value: '1',
      },
      {
        Name: 'Ploughed last HY. 2 or more cuts, little manure',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy2OrMoreCutsLittleManure',
        Value: '2',
      },
      {
        Name: 'Ploughed last HY. 1-2yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy12yrLey1OrMoreCuts250kgnHa',
        Value: '3',
      },
      {
        Name: 'Ploughed last HY. 1-2yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy12yrLeyGrazedOnly250kgnHa',
        Value: '4',
      },
      {
        Name: 'Ploughed last HY. 1-2yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy12yrLeyGrazedOnly250kgnHa',
        Value: '5',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLey1OrMoreCuts250kgnHa',
        Value: '6',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLeyGrazedOnly250kgnHa',
        Value: '7',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, 1 cut + grazing, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLey1CutGrazing250kgnHa',
        Value: '8',
      },
      {
        Name: 'Ploughed last HY. 3-5yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedLastHy35yrLeyGrazedOnly250kgnHa',
        Value: '9',
      },
      {
        Name: 'Ploughed HY-2. 2 or more cuts, little manure',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy22OrMoreCutsLittleManure',
        Value: '10',
      },
      {
        Name: 'Ploughed HY-2. 1-2yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy212yrLey1OrMoreCuts250kgnHa',
        Value: '11',
      },
      {
        Name: 'Ploughed HY-2. 1-2yr ley, grazed, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy212yrLeyGrazed250kgnHa',
        Value: '12',
      },
      {
        Name: 'Ploughed HY-2. 1-2yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy212yrLeyGrazedOnly250kgnHa',
        Value: '13',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLey1OrMoreCuts250kgnHa',
        Value: '14',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLeyGrazedOnly250kgnHa',
        Value: '15',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, 1 cut + grazing, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLey1CutGrazing250kgnHa',
        Value: '16',
      },
      {
        Name: 'Ploughed HY-2. 3-5yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy235yrLeyGrazedOnly250kgnHa',
        Value: '17',
      },
      {
        Name: 'Ploughed HY-3. 2 or more cuts, little manure',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy32OrMoreCutsLittleManure',
        Value: '18',
      },
      {
        Name: 'Ploughed HY-3. 1-2yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy312yrLey1OrMoreCuts250kgnHa',
        Value: '19',
      },
      {
        Name: 'Ploughed HY-3. 1-2yr ley, grazed, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy312yrLeyGrazed250kgnHa',
        Value: '20',
      },
      {
        Name: 'Ploughed HY-3. 1-2yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy312yrLeyGrazedOnly250kgnHa',
        Value: '21',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, 1 or more cuts, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLey1OrMoreCuts250kgnHa',
        Value: '22',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, grazed only, <250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLeyGrazedOnly250kgnHa',
        Value: '23',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, 1 cut + grazing, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLey1CutGrazing250kgnHa',
        Value: '24',
      },
      {
        Name: 'Ploughed HY-3. 3-5yr ley, grazed only, >250kgN/ha',
        TranslationKey:
          'polaris.cnpDetails.configuration.additionalParameters.ploughedHy335yrLeyGrazedOnly250kgnHa',
        Value: '25',
      },
    ],
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.previousGrass',
    UnitTags: null,
    ValueType: 'list',
  },
  {
    ApiPropertyName: 'HarvestYear',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: defaultYear.toString(),
    Max: defaultYear + 10,
    Min: defaultYear - 1,
    Name: 'Harvest year',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.harvestYear',
    UnitTags: null,
    ValueType: 'number',
  },
  {
    ApiPropertyName: 'SowingDate',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '',
    Max: null,
    Min: null,
    Name: 'Sowing date',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.sowingDate',
    UnitTags: null,
    ValueType: 'date',
  },
  {
    ApiPropertyName: 'SoilAnalysisDate',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: '',
    Max: null,
    Min: null,
    Name: 'Soil analysis date',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey: 'polaris.cnpDetails.configuration.additionalParameters.soilAnalysisDate',
    UnitTags: null,
    ValueType: 'date',
  },
  {
    ApiPropertyName: 'SelectedSoilReferenceNaturalPotash',
    BaseUnit: null,
    CalculationType: 'ALL',
    DefaultValue: 'false',
    Max: null,
    Min: null,
    Name: 'Selected soil reference natural potash',
    Options: null,
    Ordinal: 0,
    Required: true,
    TranslationKey:
      'polaris.cnpDetails.configuration.additionalParameters.selectedSoilReferenceNaturalPotash',
    UnitTags: null,
    ValueType: 'boolean',
  },
];

describe('additional parameters utils', () => {
  describe('deleteAdditionalProperty', () => {
    let mockSetCropRegion: jest.Mock;
    let mockSnackbar: jest.Mock;
    let mockSetDisplaySnackbar: jest.Mock;
    let mockTrigger: jest.Mock;
    let consoleSpy: jest.SpyInstance;

    beforeEach(() => {
      mockSetCropRegion = jest.fn();
      mockSnackbar = jest.fn();
      mockSetDisplaySnackbar = jest.fn();
      mockTrigger = jest.fn();
      consoleSpy = jest.spyOn(console, 'error').mockImplementation(jest.fn());
    });

    afterEach(() => {
      jest.clearAllMocks();
      consoleSpy.mockRestore();
    });

    const mockCropRegion: CropRegion = {
      additionalProperties: [{ Name: 'A' }, { Name: 'B' }],
    } as CropRegion;
    const mockT = (key: string) => key;

    it('deletes the specified property and updates the crop region', async () => {
      const mockTrigger = jest.fn().mockResolvedValue({
        ...mockCropRegion,
        additionalProperties: [{ Name: 'B' }],
      });

      await deleteAdditionalProperty({
        cropRegion: mockCropRegion,
        parameterName: 'A',
        triggerUpdateCropRegion: mockTrigger,
        setCropRegion: mockSetCropRegion,
        displaySnackbarMessage: mockSnackbar,
        t: mockT,
        setDisplaySnackbar: mockSetDisplaySnackbar,
      });

      expect(mockTrigger).toHaveBeenCalled();
      expect(mockSetCropRegion).toHaveBeenCalledWith({
        ...mockCropRegion,
        additionalProperties: [{ Name: 'B' }],
      });
      expect(mockSnackbar).toHaveBeenCalledWith(
        'common.snackbarDeleteMessage',
        mockSetDisplaySnackbar,
      );
    });

    it('does not call setCropRegion if updatedData is null', async () => {
      mockTrigger.mockResolvedValue(null);

      await deleteAdditionalProperty({
        cropRegion: mockCropRegion,
        parameterName: 'A',
        triggerUpdateCropRegion: mockTrigger,
        setCropRegion: mockSetCropRegion,
        displaySnackbarMessage: mockSnackbar,
        t: mockT,
        setDisplaySnackbar: mockSetDisplaySnackbar,
      });

      expect(mockSetCropRegion).not.toHaveBeenCalled();
    });

    it('handles errors when deleting a property fails', async () => {
      const error = new Error('Failed to update crop region');
      mockTrigger.mockRejectedValue(error);

      await deleteAdditionalProperty({
        cropRegion: {
          additionalProperties: [{ Name: 'A' }, { Name: 'B' }],
        } as CropRegion,
        parameterName: 'A',
        triggerUpdateCropRegion: mockTrigger,
        setCropRegion: mockSetCropRegion,
        displaySnackbarMessage: mockSnackbar,
        t: (key: string) => key,
        setDisplaySnackbar: mockSetDisplaySnackbar,
      });

      expect(mockTrigger).toHaveBeenCalled();
      expect(mockSetCropRegion).not.toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Error deleting additional parameter:', error);
      expect(mockSnackbar).not.toHaveBeenCalled();
    });
  });

  describe('updateParameters', () => {
    let mockTriggerUpdate: (config: RequestConfig) => Promise<CropRegion | undefined>;
    let mockSetCropRegion: (cropRegion: CropRegion | null) => void;

    beforeEach(() => {
      mockTriggerUpdate = jest.fn();
      mockSetCropRegion = jest.fn();
      console.error = jest.fn();
    });

    afterEach(() => {
      jest.resetAllMocks();
    });

    it('should call setCropRegion with updated data on successful update', async () => {
      const updatedCropRegion = {
        id: '123',
        additionalProperties: [{ name: 'New Prop' }],
      };
      // @ts-ignore
      mockTriggerUpdate.mockResolvedValue(updatedCropRegion);

      await updateParameters(
        testCropRegionWithParameters,
        additionalProperties,
        mockTriggerUpdate,
        mockSetCropRegion,
      );

      expect(mockTriggerUpdate).toHaveBeenCalled();
      expect(mockSetCropRegion).toHaveBeenCalledWith(updatedCropRegion);
    });

    it('should not call setCropRegion if updatedData is null', async () => {
      // @ts-ignore
      mockTriggerUpdate.mockResolvedValue(null);

      await updateParameters(
        testCropRegionWithParameters,
        additionalProperties,
        mockTriggerUpdate,
        mockSetCropRegion,
      );

      expect(mockTriggerUpdate).toHaveBeenCalled();
      expect(mockSetCropRegion).not.toHaveBeenCalled();
    });

    it('should handle errors during update', async () => {
      const error = new Error('Update failed');
      // @ts-ignore
      mockTriggerUpdate.mockRejectedValue(error);

      await expect(
        updateParameters(
          testCropRegionWithParameters,
          additionalProperties,
          mockTriggerUpdate,
          mockSetCropRegion,
        ),
      ).rejects.toThrow('Update failed');

      expect(mockTriggerUpdate).toHaveBeenCalled();
      expect(mockSetCropRegion).not.toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith('Error updating parameters:', error);
    });
  });
});

describe('isGrasslandCrop', () => {
  it('returns true if given crop type is grassland', () => {
    expect(isGrasslandCrop('grassland')).toBeTruthy();
  });
  it("returns false if given crop type isn't grassland", () => {
    expect(isGrasslandCrop('other')).toBeFalsy();
  });
});

describe('generateAhdbAdditionalParameters', () => {
  it('returns list correctly without grassland crops', () => {
    const ahdbParams = generateAhdbAdditionalParameters('other');
    expect(ahdbParams).toEqual(expectedAhdbParamsWithoutGrassland);
  });
  it('returns list correctly with grassland crops', () => {
    const ahdbParams = generateAhdbAdditionalParameters('grassland');
    expect(ahdbParams).toEqual(expectedAhdbParamsWithGrassland);
  });
});
