import React from 'react';
import { AdditionalParameters } from './AdditionalParameters/index';
import { CalculationParameters } from './CalculationParameters/index';
import { PrePostCrop } from './PrePostCrop';
import { CnpParametersProps } from './Parameters.type';

const CnpParameters: React.FC<CnpParametersProps> = ({ cropRegion }) => {
  return (
    <div className='cnp-parameters'>
      <CalculationParameters />
      <PrePostCrop />
      <AdditionalParameters cropRegion={cropRegion} />
    </div>
  );
};

export default CnpParameters;
