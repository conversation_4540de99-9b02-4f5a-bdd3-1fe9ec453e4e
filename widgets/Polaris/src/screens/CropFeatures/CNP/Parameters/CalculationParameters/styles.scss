.green-p {
  color: var(--colors-green60);
  font-family: var(--fonts-default);
}

.red-p {
  color: var(--colors-red60);
  font-family: var(--fonts-default);
}

.info-wrapper {
  display: flex;
  gap: 8px;
  button {
    svg {
      min-width: var(--sizes-x4);
      min-height: var(--sizes-x4);
      width: var(--sizes-x4);
      height: var(--sizes-x4);
    }
  }
}

.info-p {
  margin-top: 1px;
}

.info-subtitle {
  font-family: var(--fonts-default);
  color: rgba(15, 15, 15, 0.8);
  font-size: 12px;
  font-weight: var(--fontWeights-regular);
}
.parameter-edit-popup-select {
  & > p {
    margin-top: var(--space-x1);
    color: var(--colors-neutral-contrast);
  }
}