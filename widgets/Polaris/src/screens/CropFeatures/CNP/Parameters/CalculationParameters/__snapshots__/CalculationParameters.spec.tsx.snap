// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: CalculationParameters should initialize the context with default values and render the CalculationParameters component and match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div>
        <div
          class="c-eNnZw collapsible-section"
          data-cy="collapsible-section-calculation-parameters"
          data-state="open"
        >
          <button
            aria-controls="radix-:r0:"
            aria-expanded="true"
            class="c-cUgXyc"
            data-state="open"
            type="button"
          >
            <div>
              <h1
                class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
                data-cy="calculation-parameters-header-title"
              >
                polaris.cnpDetails.planConfiguration.calculationParameters.headerTitle
              </h1>
              <h2
                class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css info-subtitle"
                data-cy="calculation-parameters-header-subtitle"
              >
                polaris.cnpDetails.planConfiguration.calculationParameters.headerSubtitle
              </h2>
            </div>
            <div
              class="c-irPLE"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 15.75l-5-6h-2l-5 6"
                />
              </svg>
            </div>
          </button>
          <div
            class="c-fLVWxk"
            data-state="open"
            id="radix-:r0:"
            style="transition-duration: 0s; animation-name: none;"
          >
            <div
              class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
            />
            <div
              class="c-jqtSSh c-jqtSSh-hihSaP-variant-dynamic table-bottom-padding"
              data-cy="table-section"
            >
              <table
                class="c-kwAGqj table"
              >
                <thead>
                  <tr
                    class="c-eDGYZe"
                  >
                    <th
                      class="c-kxWgPf"
                      data-cy="head-table-parametar"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.parameters
                    </th>
                    <th
                      class="c-kxWgPf"
                    >
                      <div
                        class="info-wrapper"
                        data-cy="info-wrapper"
                      >
                        <button
                          class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-dxftns-size-xs c-kAXHSi-crlJmD-variant-ghost c-dexIdH-iivYAPe-css"
                          data-cy="default-value-info-icon"
                          data-state="closed"
                          type="button"
                        >
                          <svg
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                            />
                          </svg>
                        </button>
                        <p
                          class="info-p"
                          data-cy="default-value"
                        >
                          polaris.cnpDetails.planConfiguration.calculationParameters.defaultValue
                        </p>
                      </div>
                    </th>
                    <th
                      class="c-kxWgPf"
                      data-cy="head-table-show-in-atfarm"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.showInAtfarm
                    </th>
                    <th
                      class="c-kxWgPf"
                      data-cy="head-table-actions"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-yield"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.yield
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-yield"
                    >
                      42 (polaris.common.none)
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-yield"
                    >
                      <p
                        class="green-p"
                      >
                        polaris.common.yes
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-yield"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-preCropTypeId"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.preCropTypeId
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-preCropTypeId"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-preCropTypeId"
                    >
                      <p
                        class="green-p"
                      >
                        polaris.common.yes
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-preCropTypeId"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-preCropYield"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.preCropYield
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-preCropYield"
                    >
                      0 (polaris.common.none)
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-preCropYield"
                    >
                      <p
                        class="red-p"
                      >
                        polaris.common.no
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-preCropYield"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-preCropResidueManagementId"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.preCropResidueManagementId
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-preCropResidueManagementId"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-preCropResidueManagementId"
                    >
                      <p
                        class="red-p"
                      >
                        polaris.common.no
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-preCropResidueManagementId"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-postCropTypeId"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.postCropTypeId
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-postCropTypeId"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-postCropTypeId"
                    >
                      <p
                        class="red-p"
                      >
                        polaris.common.no
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-postCropTypeId"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-postCropYield"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.postCropYield
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-postCropYield"
                    >
                      0 (polaris.common.none)
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-postCropYield"
                    >
                      <p
                        class="red-p"
                      >
                        polaris.common.no
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-postCropYield"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-postCropResidueManagementId"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.postCropResidueManagementId
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-postCropResidueManagementId"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-postCropResidueManagementId"
                    >
                      <p
                        class="red-p"
                      >
                        polaris.common.no
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-postCropResidueManagementId"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-soilTypeId"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.soilTypeId
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-soilTypeId"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-soilTypeId"
                    >
                      <p
                        class="green-p"
                      >
                        polaris.common.yes
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-soilTypeId"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-cropResidueManagementId"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.cropResidueManagementId
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-cropResidueManagementId"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-cropResidueManagementId"
                    >
                      <p
                        class="red-p"
                      >
                        polaris.common.no
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-cropResidueManagementId"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  <tr
                    class="c-eDGYZe"
                  >
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-item-previouslyAppliedProducts"
                    >
                      polaris.cnpDetails.planConfiguration.calculationParameters.previouslyAppliedProducts
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-defaultValue-previouslyAppliedProducts"
                    >
                      polaris.common.none
                    </td>
                    <td
                      class="c-doquzR"
                      data-cy="table-cell-showInAtfarm-previouslyAppliedProducts"
                    >
                      <p
                        class="green-p"
                      >
                        polaris.common.yes
                      </p>
                    </td>
                    <td
                      class="c-doquzR c-cIAltW"
                      data-cy="table-cell-edit-previouslyAppliedProducts"
                    >
                      <button
                        class="c-bvmDGo"
                        icon="Edit"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                          />
                        </svg>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div>
      <div
        class="c-eNnZw collapsible-section"
        data-cy="collapsible-section-calculation-parameters"
        data-state="open"
      >
        <button
          aria-controls="radix-:r0:"
          aria-expanded="true"
          class="c-cUgXyc"
          data-state="open"
          type="button"
        >
          <div>
            <h1
              class="c-iFoEyZ c-iFoEyZ-cPvAZn-size-s c-iFoEyZ-iPJLV-css"
              data-cy="calculation-parameters-header-title"
            >
              polaris.cnpDetails.planConfiguration.calculationParameters.headerTitle
            </h1>
            <h2
              class="c-iAVmsd c-iAVmsd-dDOYgV-size-n c-iAVmsd-iPJLV-css info-subtitle"
              data-cy="calculation-parameters-header-subtitle"
            >
              polaris.cnpDetails.planConfiguration.calculationParameters.headerSubtitle
            </h2>
          </div>
          <div
            class="c-irPLE"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 15.75l-5-6h-2l-5 6"
              />
            </svg>
          </div>
        </button>
        <div
          class="c-fLVWxk"
          data-state="open"
          id="radix-:r0:"
          style="transition-duration: 0s; animation-name: none;"
        >
          <div
            class="c-PJLV c-PJLV-cZSuGO-mode-light c-PJLV-COvYd-orientation-horizontal c-PJLV-iCOvYd-css"
          />
          <div
            class="c-jqtSSh c-jqtSSh-hihSaP-variant-dynamic table-bottom-padding"
            data-cy="table-section"
          >
            <table
              class="c-kwAGqj table"
            >
              <thead>
                <tr
                  class="c-eDGYZe"
                >
                  <th
                    class="c-kxWgPf"
                    data-cy="head-table-parametar"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.parameters
                  </th>
                  <th
                    class="c-kxWgPf"
                  >
                    <div
                      class="info-wrapper"
                      data-cy="info-wrapper"
                    >
                      <button
                        class="c-dexIdH c-kAXHSi c-kAXHSi-LORVq-colorConcept-neutral c-kAXHSi-dxftns-size-xs c-kAXHSi-crlJmD-variant-ghost c-dexIdH-iivYAPe-css"
                        data-cy="default-value-info-icon"
                        data-state="closed"
                        type="button"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                          />
                        </svg>
                      </button>
                      <p
                        class="info-p"
                        data-cy="default-value"
                      >
                        polaris.cnpDetails.planConfiguration.calculationParameters.defaultValue
                      </p>
                    </div>
                  </th>
                  <th
                    class="c-kxWgPf"
                    data-cy="head-table-show-in-atfarm"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.showInAtfarm
                  </th>
                  <th
                    class="c-kxWgPf"
                    data-cy="head-table-actions"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.actions
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-yield"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.yield
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-yield"
                  >
                    42 (polaris.common.none)
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-yield"
                  >
                    <p
                      class="green-p"
                    >
                      polaris.common.yes
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-yield"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-preCropTypeId"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.preCropTypeId
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-preCropTypeId"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-preCropTypeId"
                  >
                    <p
                      class="green-p"
                    >
                      polaris.common.yes
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-preCropTypeId"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-preCropYield"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.preCropYield
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-preCropYield"
                  >
                    0 (polaris.common.none)
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-preCropYield"
                  >
                    <p
                      class="red-p"
                    >
                      polaris.common.no
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-preCropYield"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-preCropResidueManagementId"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.preCropResidueManagementId
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-preCropResidueManagementId"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-preCropResidueManagementId"
                  >
                    <p
                      class="red-p"
                    >
                      polaris.common.no
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-preCropResidueManagementId"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-postCropTypeId"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.postCropTypeId
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-postCropTypeId"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-postCropTypeId"
                  >
                    <p
                      class="red-p"
                    >
                      polaris.common.no
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-postCropTypeId"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-postCropYield"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.postCropYield
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-postCropYield"
                  >
                    0 (polaris.common.none)
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-postCropYield"
                  >
                    <p
                      class="red-p"
                    >
                      polaris.common.no
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-postCropYield"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-postCropResidueManagementId"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.postCropResidueManagementId
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-postCropResidueManagementId"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-postCropResidueManagementId"
                  >
                    <p
                      class="red-p"
                    >
                      polaris.common.no
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-postCropResidueManagementId"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-soilTypeId"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.soilTypeId
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-soilTypeId"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-soilTypeId"
                  >
                    <p
                      class="green-p"
                    >
                      polaris.common.yes
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-soilTypeId"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-cropResidueManagementId"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.cropResidueManagementId
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-cropResidueManagementId"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-cropResidueManagementId"
                  >
                    <p
                      class="red-p"
                    >
                      polaris.common.no
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-cropResidueManagementId"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr
                  class="c-eDGYZe"
                >
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-item-previouslyAppliedProducts"
                  >
                    polaris.cnpDetails.planConfiguration.calculationParameters.previouslyAppliedProducts
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-defaultValue-previouslyAppliedProducts"
                  >
                    polaris.common.none
                  </td>
                  <td
                    class="c-doquzR"
                    data-cy="table-cell-showInAtfarm-previouslyAppliedProducts"
                  >
                    <p
                      class="green-p"
                    >
                      polaris.common.yes
                    </p>
                  </td>
                  <td
                    class="c-doquzR c-cIAltW"
                    data-cy="table-cell-edit-previouslyAppliedProducts"
                  >
                    <button
                      class="c-bvmDGo"
                      icon="Edit"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-ikjVVMp-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
