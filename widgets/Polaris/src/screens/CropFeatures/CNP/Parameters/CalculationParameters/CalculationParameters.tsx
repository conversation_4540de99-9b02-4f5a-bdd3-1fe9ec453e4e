import React, { useState } from 'react';
import CollapsibleSection from '../../../../../components/Collapsible/Collapsible';
import TableSection from '../../../../../components/Table/Table';
import { useTranslation } from 'react-i18next';
import { IconButton, Table, Tooltip, Subtitle, Title } from '@yaradigitallabs/ahua-react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { CalculationParameter } from '@common/types';
import './styles.scss';
import {
  useFetchCalculationParameter,
  useFetchCropResidueManagements,
  useFetchSoilTypeCountries,
  useFetchSoilTypes,
  filterUnitsByTag,
  useUpdateCropRegion,
} from '@polaris-hooks/index';
import { ParameterEditPopup } from '@widgets/Polaris/src/components/ParameterEditPopup';
import { BaseUnit } from '@common/types';
import { handleEdit, updateCalcParameter } from './utils/calculationParametersUtils';
import {
  UNIT_TAGS,
  NONE_OPTION_VALUE,
  NONE_VALUE_UUID,
  PARAMETERS_WITH_DROPDOWN,
} from '@common/constants';

export function CalculationParameters() {
  const { t } = useTranslation();
  const {
    cropRegion,
    selectedCountry,
    selectedCountryUnits,
    methods: { setCropRegion },
  } = useAppContext();
  const yesText = t('polaris.common.yes');
  const noText = t('polaris.common.no');
  const [isEditPopupOpen, setIsEditPopupOpen] = useState(false);
  const [currentParameter, setCurrentParameter] = useState<CalculationParameter | null>(null);

  const { trigger: triggerUpdateCropRegion } = useUpdateCropRegion(cropRegion?.id);
  const filteredUnits = filterUnitsByTag(selectedCountryUnits, UNIT_TAGS.YIELD_UNIT);
  const { cropResidueManagements = [], isCRMLoading } = useFetchCropResidueManagements();
  const { soilTypes = [], isSoilTypesLoading } = useFetchSoilTypes();
  const { soilTypeCountries = [], isSoilTypeCountriesLoading } = useFetchSoilTypeCountries(
    selectedCountry?.id,
  );
  const handleUpdateCalcParameter = async (updatedParameterData: CalculationParameter) => {
    const response = await updateCalcParameter(
      cropRegion,
      currentParameter,
      updatedParameterData,
      triggerUpdateCropRegion,
    );
    if (response) {
      setCropRegion(response);
    }
  };

  /**
   * Handles the calculation parameter item and converts it back the correct string to display in default value table cell
   * @param {CalculationParameter} item - CalculationParameter item that need to be converted into the correct value
   * @param {BaseUnit[] | undefined} units - An array of base units from which the unit name can be found
   * @returns {string} A string representation of the default value
   * */
  const displayDefaultValue = (
    item: CalculationParameter,
    units: BaseUnit[] | undefined,
  ): string => {
    // Check if the current item is one of the parameters that should display a unit
    const shouldDisplayUnit = PARAMETERS_WITH_DROPDOWN.includes(item.name);
    const cropResideManagement = cropResidueManagements?.find(
      (rm) => rm?.id === item?.defaultValue,
    );
    const soilType = soilTypes?.find((st) => st.id === item.defaultValue);
    const paramDefaultValue = useFetchCalculationParameter(
      item.defaultValue,
      item.name,
      soilType,
      cropResideManagement,
    );
    const isParameterLoading =
      isSoilTypeCountriesLoading || isCRMLoading || isSoilTypesLoading || !paramDefaultValue;

    if (shouldDisplayUnit) {
      const matchingUnit = units?.find((unit) => unit.id === item.defaultUnitId);
      const unitDisplayName = matchingUnit ? matchingUnit.name : t('polaris.common.none');

      // Display the default value along with the unit name or 'None' if no matching unit
      return `${item.defaultValue} (${unitDisplayName})`;
    } else {
      return isParameterLoading ||
        item.defaultValue === NONE_OPTION_VALUE ||
        item.defaultValue === NONE_VALUE_UUID ||
        paramDefaultValue === NONE_OPTION_VALUE
        ? t('polaris.common.none')
        : paramDefaultValue;
    }
  };

  /** Default Value with info icon and tooltip table header component */
  const DefaultValue = (): JSX.Element => {
    return (
      <div className='info-wrapper' data-cy='info-wrapper'>
        <Tooltip
          data-cy='default-value-tooltip'
          css={{ padding: '$x2 $x5' }}
          concept='inverse'
          maxWidth={211}
          minWidth={211}
          position='top'
          text={t('polaris.cnpDetails.planConfiguration.calculationParameters.tooltipText')}
          tipVisibility
        >
          <IconButton
            colorConcept='neutral'
            icon='Info'
            size='xs'
            css={{ padding: 0 }}
            data-cy='default-value-info-icon'
          />
        </Tooltip>
        <p data-cy='default-value' className='info-p'>
          {t('polaris.cnpDetails.planConfiguration.calculationParameters.defaultValue')}
        </p>
      </div>
    );
  };

  /** Represents the Table Header of the Calculation parameters table */
  const CalculationParametersHeader = (): JSX.Element => (
    <Table.Row>
      <Table.Head data-cy='head-table-parametar'>
        {t('polaris.cnpDetails.planConfiguration.calculationParameters.parameters')}
      </Table.Head>
      <Table.Head>
        <DefaultValue />
      </Table.Head>
      <Table.Head data-cy='head-table-show-in-atfarm'>
        {t('polaris.cnpDetails.planConfiguration.calculationParameters.showInAtfarm')}
      </Table.Head>
      <Table.Head data-cy='head-table-actions'>
        {t('polaris.cnpDetails.planConfiguration.calculationParameters.actions')}
      </Table.Head>
    </Table.Row>
  );

  /** Represents the Table Body of the Calculation parameters table */
  const CalculationParametersBody = (): JSX.Element => (
    <>
      {cropRegion?.calculationParameters.map((item, index) => (
        <Table.Row key={index}>
          <Table.Cell data-cy={`table-cell-item-${item.name}`}>
            {t(`polaris.cnpDetails.planConfiguration.calculationParameters.${item.name}`)}
          </Table.Cell>
          <Table.Cell data-cy={`table-cell-defaultValue-${item.name}`}>
            {displayDefaultValue(item, filteredUnits)}
          </Table.Cell>
          <Table.Cell data-cy={`table-cell-showInAtfarm-${item.name}`}>
            <p className={item.shouldDisplay ? 'green-p' : 'red-p'}>
              {item.shouldDisplay ? yesText : noText}
            </p>
          </Table.Cell>
          <Table.ActionsCell data-cy={`table-cell-edit-${item.name}`}>
            <Table.Action
              icon='Edit'
              onClick={() => handleEdit(item, setCurrentParameter, setIsEditPopupOpen)}
            />
          </Table.ActionsCell>
        </Table.Row>
      ))}
    </>
  );

  /** Collapsable headerTitle component */
  const HeaderTitle = (): JSX.Element => (
    <div>
      <Title size='s' data-cy='calculation-parameters-header-title'>
        {t('polaris.cnpDetails.planConfiguration.calculationParameters.headerTitle')}
      </Title>
      <Subtitle className='info-subtitle' data-cy='calculation-parameters-header-subtitle'>
        {t('polaris.cnpDetails.planConfiguration.calculationParameters.headerSubtitle')}
      </Subtitle>
    </div>
  );

  return (
    <>
      <div>
        <CollapsibleSection
          dataCY={'collapsible-section-calculation-parameters'}
          defaultOpen={true}
          showCardContent={false}
          headerTitle={<HeaderTitle />}
        >
          <TableSection
            data-cy='table-section-calculation-parameters'
            showPagination={false}
            tableHeader={<CalculationParametersHeader />}
            tableBody={<CalculationParametersBody />}
          />
        </CollapsibleSection>
      </div>
      {currentParameter && (
        <ParameterEditPopup
          isOpen={isEditPopupOpen}
          onOpenChange={setIsEditPopupOpen}
          cropRegion={cropRegion}
          onSave={(updatedParameter) => {
            handleUpdateCalcParameter(updatedParameter);
            setIsEditPopupOpen(false);
            setCurrentParameter(null);
          }}
          parameterData={currentParameter}
          translatedParameterName={t(
            `polaris.cnpDetails.planConfiguration.calculationParameters.${currentParameter.name}`,
          )}
          cropResidueManagements={cropResidueManagements}
          soilTypes={soilTypes}
          soilTypeCountries={soilTypeCountries}
        />
      )}
    </>
  );
}
