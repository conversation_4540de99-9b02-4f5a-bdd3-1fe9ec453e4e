import React from 'react';
import { render, screen } from '@testing-library/react';
import { NavbarProvider } from '@libs/nav-context';
import { CalculationParameters } from './index';
import { BrowserRouter as Router } from 'react-router-dom';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { setupServer } from 'msw/node';
import {
  cropResidueManagementsHandler,
  soilTypeCountriesHandler,
  soilTypesHandler,
  unitCountriesHandler,
  updateCropRegionHandler,
  mockAppProviderValue,
} from '@common/mocks';

const server = setupServer(
  updateCropRegionHandler,
  unitCountriesHandler,
  cropResidueManagementsHandler,
  soilTypeCountriesHandler,
  soilTypesHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('widget: CalculationParameters', () => {
  beforeEach(() => {
    // You can add setup code that runs before each test if needed
  });

  it('should initialize the context with default values and render the CalculationParameters component and match snapshot', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <CalculationParameters />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();

    const collapsible = screen.getByTestId('collapsible-section-calculation-parameters');
    expect(collapsible).toBeInTheDocument();

    const iconButton = screen.getByTestId('default-value-info-icon');
    expect(iconButton).toBeInTheDocument();
  });
});
