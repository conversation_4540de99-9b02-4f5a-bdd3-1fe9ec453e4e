/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  BaseUnit,
  CalculationParameter,
  CropResidueManagement,
  CropSequence,
  SoilType,
  SoilTypeCountry,
} from '@common/types';
import { SWRMutationConfiguration } from 'swr/mutation';
import { CropRegion } from '@common/types';
import { HandleEditFunction } from './calculationParametersUtils.type';
import {
  CALCULATION_PARAM_TYPE,
  METHOD,
  NONE_OPTION_VALUE,
  NONE_VALUE_UUID,
} from '@common/constants';

type TriggerUpdateCropRegion = (
  extraArgument: RequestInit,
  options?: SWRMutationConfiguration<CropRegion, any, RequestInit, string, CropRegion> | undefined,
) => Promise<CropRegion | undefined>;

export const updateCalcParameter = async (
  cropRegion: CropRegion | null,
  currentParameter: CalculationParameter | null,
  updatedParameterData: CalculationParameter,
  triggerUpdate: TriggerUpdateCropRegion,
) => {
  if (!cropRegion || !currentParameter) return;

  const updatedParameters = cropRegion.calculationParameters.map((param) =>
    param.name === currentParameter.name ? { ...param, ...updatedParameterData } : param,
  );

  const updatedCropRegionData = {
    ...cropRegion,
    calculationParameters: updatedParameters,
  };

  try {
    const response = await triggerUpdate({
      method: METHOD.PUT,
      body: JSON.stringify(updatedCropRegionData),
    });
    return response;
  } catch (error) {
    console.error('Error updating crop region:', error);
    throw error;
  }
};

export const handleEdit: HandleEditFunction = (
  parameter,
  setCurrentParameter,
  setIsEditPopupOpen,
) => {
  setCurrentParameter(parameter);
  setIsEditPopupOpen(true);
};

// Transforms the parameter's default value into a string for input field initialization.
export const getInitialEditedValue = (parameterData: CalculationParameter): string => {
  const nonYieldParameter =
    parameterData?.defaultValue === NONE_VALUE_UUID
      ? NONE_OPTION_VALUE
      : parameterData?.defaultValue;
  if (typeof parameterData.defaultValue === 'number') {
    return parameterData.defaultValue.toString();
  } else if (typeof parameterData.defaultValue === 'string') {
    return nonYieldParameter.toString();
  } else if (Array.isArray(parameterData.defaultValue)) {
    return parameterData.defaultValue.join(', ');
  }
  return parameterData.defaultValue || '';
};

export const getCropSequencesByType = (
  cropSequences: CropSequence[],
  type: string,
): CropSequence[] | undefined => {
  const { PRE_CROP_TYPE_ID, POST_CROP_TYPE_ID } = CALCULATION_PARAM_TYPE;
  if (type === PRE_CROP_TYPE_ID) {
    return cropSequences?.filter((en) => en.isPreCrop);
  } else if (type === POST_CROP_TYPE_ID) {
    return cropSequences?.filter((en) => en.isPostCrop);
  }
};

export const getFormattedOptions = (
  parameters: SoilTypeCountry[] | BaseUnit[] | CropResidueManagement[],
  value_none_option: {
    value: string;
    text: string;
  },
  soilTypes?: SoilType[],
) => {
  if (soilTypes && soilTypes?.length > 0) {
    return [
      value_none_option,
      ...parameters
        .map((parameter) => {
          if ('soilTypeId' in parameter) {
            const soilType = soilTypes?.find(({ id }) => id === parameter.soilTypeId);

            if (!soilType) {
              return null;
            }
            return {
              value: String(soilType?.id),
              text: soilType?.name,
            };
          }
        })
        .filter((soil) => !!soil),
    ];
  }
  return [
    value_none_option,
    ...parameters.map((en) => ({
      value: en.id,
      text: 'name' in en ? en.name : '',
    })),
  ];
};
