import { CalculationParameter } from '@common/types';
import { cropRegionsResponse } from '../../../../../Home/mock-data/MockData';

import {
  getInitialEditedValue,
  handleEdit,
  updateCalcParameter,
} from './calculationParametersUtils';

const mockCropRegionData = cropRegionsResponse[0];
const mockCurrentParameter = {
  defaultValue: '5',
  name: 'yield',
  defaultUnitId: '2f4b4990-93db-4e6e-972c-e9d9670acc5c',
  shouldDisplay: true,
  type: 'number',
};

const mockTriggerUpdate = jest.fn().mockResolvedValue({
  ...mockCropRegionData,
  calculationParameters: mockCropRegionData.calculationParameters.map((param) =>
    param.name === mockCurrentParameter.name
      ? { ...param, value: mockCurrentParameter.name }
      : param,
  ),
});

describe('calcUtils', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {}); // eslint-disable-line @typescript-eslint/no-empty-function
    jest.clearAllMocks();
  });

  // Restore the original console.error after each test
  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('updateCalcParameter', () => {
    it('updates the calculation parameter successfully', async () => {
      const updatedParameterData = {
        ...mockCurrentParameter,
        defaultValue: 'Updated Value',
      };

      const result = await updateCalcParameter(
        mockCropRegionData,
        mockCurrentParameter,
        updatedParameterData,
        mockTriggerUpdate,
      );

      expect(mockTriggerUpdate).toHaveBeenCalled();
      expect(result).toMatchObject({
        ...mockCropRegionData,
        calculationParameters: [{ name: 'yield', value: 'yield' }],
      });
    });

    it('handles errors during update', async () => {
      mockTriggerUpdate.mockRejectedValue(new Error('Failed to update'));
      const updatedParameterData = {
        ...mockCurrentParameter,
        defaultValue: 'Updated Value',
      };

      await expect(
        updateCalcParameter(
          mockCropRegionData,
          mockCurrentParameter,
          updatedParameterData,
          mockTriggerUpdate,
        ),
      ).rejects.toThrow('Failed to update');

      expect(mockTriggerUpdate).toHaveBeenCalled();
    });

    it('does not attempt an update if currentParameter is null', async () => {
      const updatedParameterData = {
        ...mockCurrentParameter,
        defaultValue: 'Updated Value',
      };

      const result = await updateCalcParameter(
        mockCropRegionData,
        null,
        updatedParameterData,
        mockTriggerUpdate,
      );

      expect(mockTriggerUpdate).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });
  });

  describe('handleEdit', () => {
    it('should set the current parameter and open the edit popup', () => {
      const mockSetCurrentParameter = jest.fn();
      const mockSetIsEditPopupOpen = jest.fn();

      const mockParameter: CalculationParameter = {
        name: 'yield',
        defaultValue: '5',
        defaultUnitId: '2f4b4990-93db-4e6e-972c-e9d9670acc5c',
        shouldDisplay: true,
        type: 'number',
      };

      handleEdit(mockParameter, mockSetCurrentParameter, mockSetIsEditPopupOpen);

      expect(mockSetCurrentParameter).toHaveBeenCalledWith(mockParameter);
      expect(mockSetIsEditPopupOpen).toHaveBeenCalledWith(true);
    });
  });

  describe('getInitialEditedValue', () => {
    it('converts numeric defaultValue to string', () => {
      const parameterData: CalculationParameter = {
        name: 'Test Parameter',
        shouldDisplay: true,
        defaultValue: 42,
        defaultUnitId: null,
        type: 'number',
      };
      expect(getInitialEditedValue(parameterData)).toBe('42');
    });

    it('keeps string defaultValue as is', () => {
      const parameterData: CalculationParameter = {
        name: 'Test Parameter',
        shouldDisplay: true,
        defaultValue: 'Default Value',
        defaultUnitId: null,
        type: 'string',
      };
      expect(getInitialEditedValue(parameterData)).toBe('Default Value');
    });

    it('joins array defaultValue into a comma-separated string', () => {
      const parameterData: CalculationParameter = {
        name: 'Test Parameter',
        shouldDisplay: true,
        defaultValue: ['Value1', 'Value2', 'Value3'],
        defaultUnitId: null,
        type: 'array',
      };
      expect(getInitialEditedValue(parameterData)).toBe('Value1, Value2, Value3');
    });

    it('returns an empty string if defaultValue is null', () => {
      const parameterData: CalculationParameter = {
        name: 'Test Parameter',
        shouldDisplay: true,
        defaultValue: '',
        defaultUnitId: null,
        type: 'id',
      };
      expect(getInitialEditedValue(parameterData)).toBe('');
    });
  });
});
