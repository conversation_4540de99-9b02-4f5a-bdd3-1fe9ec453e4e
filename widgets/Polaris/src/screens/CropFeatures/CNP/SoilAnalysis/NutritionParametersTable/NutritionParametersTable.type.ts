import {
  CropDemandAnalysis,
  CropDemandAnalysisNutrient,
  CropDemandAnalysisDefaultParameters,
} from '@common/types';

export interface NutritionParametersTableProps {
  cropDemandAnalysesData: CropDemandAnalysis;
  setModified: React.Dispatch<React.SetStateAction<string>>;
  setModifiedBy: React.Dispatch<React.SetStateAction<string>>;
  setExpression: React.Dispatch<React.SetStateAction<string>>;
  setIsExpressionBuilderOpen: React.Dispatch<React.SetStateAction<boolean>>;
  cropDemandAnalysisNutrients: CropDemandAnalysisDefaultParameters[] | undefined;
  isDefaultActive: boolean;
}

export enum UpdateAnalysisActions {
  SET_SELECTED_NUTRIENT = 'SET_SELECTED_NUTRIENT',
  SET_UPDATE_TYPE = 'SET_UPDATE_TYPE',
}

export interface UpdateAnalysisState {
  selectedNutrient: CropDemandAnalysisNutrient | null;
  updateType: string;
}

export interface SetSelectedNutrientAction {
  type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT;
  payload: CropDemandAnalysisNutrient | null;
}

export interface SetUpdateTypeAction {
  type: UpdateAnalysisActions.SET_UPDATE_TYPE;
  payload: string;
}

export type AnalysisActions = SetSelectedNutrientAction | SetUpdateTypeAction;
