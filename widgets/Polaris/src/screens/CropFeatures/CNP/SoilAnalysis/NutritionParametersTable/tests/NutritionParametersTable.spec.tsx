import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountriesHandler,
  updateCropRegionHandler,
  cropDemandAnalysisNutrientsHandler,
  allUnitsHandler,
  cropDemandAnalysisUpdateHandler,
  cropDemandAnalysesDataHandler,
  deleteCropDemandAnalysisNutrientHandler,
  defaultCropDemandAnalysisNutrientMock,
  cropDemandAnalysesData,
} from '@common/mocks';
import { NutritionParametersTable } from '../NutritionParametersTable';
import { CropDemandAnalysis } from '@common/types';

const server = setupServer(
  updateCropRegionHandler,
  unitCountriesHandler,
  allUnitsHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisUpdateHandler,
  cropDemandAnalysesDataHandler,
  deleteCropDemandAnalysisNutrientHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('NutritionParametersTable', () => {
  it('should render without crashing', async () => {
    const component = render(
      <NutritionParametersTable
        setExpression={jest.fn()}
        setModified={jest.fn()}
        setModifiedBy={jest.fn()}
        setIsExpressionBuilderOpen={jest.fn()}
        cropDemandAnalysesData={cropDemandAnalysesData.entities[0] as CropDemandAnalysis}
        cropDemandAnalysisNutrients={defaultCropDemandAnalysisNutrientMock}
        isDefaultActive={false}
      />,
    );
    expect(
      screen.getByText('cnpDetails.planConfiguration.soilAnalysis.addTableButton'),
    ).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should match snapshot', () => {
    const { asFragment } = render(
      <NutritionParametersTable
        setExpression={jest.fn()}
        setModified={jest.fn()}
        setModifiedBy={jest.fn()}
        setIsExpressionBuilderOpen={jest.fn()}
        cropDemandAnalysesData={cropDemandAnalysesData.entities[0] as CropDemandAnalysis}
        cropDemandAnalysisNutrients={defaultCropDemandAnalysisNutrientMock}
        isDefaultActive={false}
      />,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should click checkbox', async () => {
    const { getByTestId } = render(
      <NutritionParametersTable
        setExpression={jest.fn()}
        setModified={jest.fn()}
        setModifiedBy={jest.fn()}
        setIsExpressionBuilderOpen={jest.fn()}
        cropDemandAnalysesData={cropDemandAnalysesData.entities[0] as CropDemandAnalysis}
        cropDemandAnalysisNutrients={defaultCropDemandAnalysisNutrientMock}
        isDefaultActive={false}
      />,
    );
    await waitFor(async () => {
      expect(getByTestId('nutrient-0-checkbox')).toBeInTheDocument();
      const firstCheckbox = getByTestId('nutrient-0-checkbox');
      fireEvent.click(firstCheckbox);
    });
  });

  it('should show delete popups', async () => {
    const { getByTestId, getByText } = render(
      <NutritionParametersTable
        setExpression={jest.fn()}
        setModified={jest.fn()}
        setModifiedBy={jest.fn()}
        setIsExpressionBuilderOpen={jest.fn()}
        cropDemandAnalysesData={cropDemandAnalysesData.entities[0] as CropDemandAnalysis}
        cropDemandAnalysisNutrients={defaultCropDemandAnalysisNutrientMock}
        isDefaultActive={false}
      />,
    );
    await waitFor(() => {
      //Click on delete icon
      const deleteIcon1 = getByTestId('delete-nutrient-0');
      fireEvent.click(deleteIcon1);
      //Cancel delete
      const cancelButtonDelete = getByText('common.cancel');
      fireEvent.click(cancelButtonDelete);
      //Click on delete icon
      fireEvent.click(deleteIcon1);
      //Confirm delete
      const confirmButtonDelete = getByText('common.yesDelete');
      fireEvent.click(confirmButtonDelete);

      //Click on delete default
      const deleteIcon3 = getByTestId('delete-nutrient-3');
      fireEvent.click(deleteIcon3);
      //Confirm info
      const confirmButtonInfo = getByText('common.ok');
      fireEvent.click(confirmButtonInfo);
    });
  });

  it('should display ExpressionBuilder on click of expression', async () => {
    const setExpressionMock = jest.fn();
    const setModifiedMock = jest.fn();
    const setModifiedByMock = jest.fn();
    const setIsExpressionBuilderOpenMock = jest.fn();
    const { getByTestId } = render(
      <NutritionParametersTable
        setExpression={setExpressionMock}
        setModified={setModifiedMock}
        setModifiedBy={setModifiedByMock}
        setIsExpressionBuilderOpen={setIsExpressionBuilderOpenMock}
        cropDemandAnalysesData={cropDemandAnalysesData.entities[0] as CropDemandAnalysis}
        cropDemandAnalysisNutrients={defaultCropDemandAnalysisNutrientMock}
        isDefaultActive={false}
      />,
    );

    await waitFor(() => {
      const validityExpression = getByTestId('validity-expression-0');
      fireEvent.click(validityExpression);

      expect(setExpressionMock).toHaveBeenCalled();
      expect(setIsExpressionBuilderOpenMock).toHaveBeenCalled();
    });
  });
});
