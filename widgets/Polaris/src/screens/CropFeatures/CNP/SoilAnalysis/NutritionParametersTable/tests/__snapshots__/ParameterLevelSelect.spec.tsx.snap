// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Parameters Level Select should match snapshot 1`] = `
<DocumentFragment>
  <div
    style="display: flex; align-items: center;"
  >
    <span
      class="circle"
      data-color="1"
      style="width: 13.3px;"
    />
    <div
      data-cy="nutrient-classification-select"
      style="display: inline-block; width: 100%;"
    >
      <div
        class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
      >
        <div
          class="c-kFLrJl"
        >
          <label
            class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
          >
            LOW
          </label>
          <button
            aria-autocomplete="none"
            aria-controls="radix-:r2:"
            aria-expanded="false"
            aria-label="Select nutrient classification-LOW"
            class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
            data-state="closed"
            dir="ltr"
            role="combobox"
            tabindex="0"
            type="button"
          >
            <span
              style="pointer-events: none;"
            >
              <div
                class="c-fSebPZ"
              />
            </span>
            <svg
              aria-hidden="true"
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 9.75l-5 6h-2l-5-6"
              />
            </svg>
          </button>
        </div>
        <p
          class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
        />
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`Parameters Level Select should render without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        style="display: flex; align-items: center;"
      >
        <span
          class="circle"
          data-color="1"
          style="width: 13.3px;"
        />
        <div
          data-cy="nutrient-classification-select"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
              >
                LOW
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r0:"
                aria-expanded="false"
                aria-label="Select nutrient classification-LOW"
                class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="0"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      style="display: flex; align-items: center;"
    >
      <span
        class="circle"
        data-color="1"
        style="width: 13.3px;"
      />
      <div
        data-cy="nutrient-classification-select"
        style="display: inline-block; width: 100%;"
      >
        <div
          class="c-jGFTiO c-jGFTiO-ubosY-state-default select-nutrient-parameter-level"
        >
          <div
            class="c-kFLrJl"
          >
            <label
              class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-jqMIxA-textType-item"
            >
              LOW
            </label>
            <button
              aria-autocomplete="none"
              aria-controls="radix-:r0:"
              aria-expanded="false"
              aria-label="Select nutrient classification-LOW"
              class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
              data-state="closed"
              dir="ltr"
              role="combobox"
              tabindex="0"
              type="button"
            >
              <span
                style="pointer-events: none;"
              >
                <div
                  class="c-fSebPZ"
                />
              </span>
              <svg
                aria-hidden="true"
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 9.75l-5 6h-2l-5-6"
                />
              </svg>
            </button>
          </div>
          <p
            class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
          />
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
