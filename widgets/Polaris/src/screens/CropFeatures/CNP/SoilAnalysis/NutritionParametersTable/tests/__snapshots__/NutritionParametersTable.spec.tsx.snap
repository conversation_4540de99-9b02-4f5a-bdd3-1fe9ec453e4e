// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NutritionParametersTable should match snapshot 1`] = `
<DocumentFragment>
  <div
    class="c-fIXtlw"
    data-cy="nutrition-parameter-levels-table"
  >
    <table
      class="c-kwAGqj table"
    >
      <thead>
        <tr
          class="c-eDGYZe header-row"
        >
          <th
            class="c-kxWgPf"
          >
            cnpDetails.planConfiguration.soilAnalysis.table.header.default
          </th>
          <th
            class="c-kxWgPf parameter-level"
          >
            cnpDetails.planConfiguration.soilAnalysis.table.header.parameterLevel
          </th>
          <th
            class="c-kxWgPf"
          >
            cnpDetails.planConfiguration.soilAnalysis.table.header.validityExp
          </th>
          <th
            class="c-kxWgPf table-actions"
          >
            cnpDetails.planConfiguration.soilAnalysis.table.header.action
          </th>
        </tr>
      </thead>
      <tbody />
    </table>
    <button
      class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-ieICmTD-css"
      type="button"
    >
      <svg
        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.072 6v12M18 12H6"
        />
      </svg>
      <span
        class="c-iepcqn"
      >
        cnpDetails.planConfiguration.soilAnalysis.addTableButton
      </span>
    </button>
  </div>
</DocumentFragment>
`;

exports[`NutritionParametersTable should render without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-fIXtlw"
        data-cy="nutrition-parameter-levels-table"
      >
        <table
          class="c-kwAGqj table"
        >
          <thead>
            <tr
              class="c-eDGYZe header-row"
            >
              <th
                class="c-kxWgPf"
              >
                cnpDetails.planConfiguration.soilAnalysis.table.header.default
              </th>
              <th
                class="c-kxWgPf parameter-level"
              >
                cnpDetails.planConfiguration.soilAnalysis.table.header.parameterLevel
              </th>
              <th
                class="c-kxWgPf"
              >
                cnpDetails.planConfiguration.soilAnalysis.table.header.validityExp
              </th>
              <th
                class="c-kxWgPf table-actions"
              >
                cnpDetails.planConfiguration.soilAnalysis.table.header.action
              </th>
            </tr>
          </thead>
          <tbody />
        </table>
        <button
          class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-ieICmTD-css"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.072 6v12M18 12H6"
            />
          </svg>
          <span
            class="c-iepcqn"
          >
            cnpDetails.planConfiguration.soilAnalysis.addTableButton
          </span>
        </button>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-fIXtlw"
      data-cy="nutrition-parameter-levels-table"
    >
      <table
        class="c-kwAGqj table"
      >
        <thead>
          <tr
            class="c-eDGYZe header-row"
          >
            <th
              class="c-kxWgPf"
            >
              cnpDetails.planConfiguration.soilAnalysis.table.header.default
            </th>
            <th
              class="c-kxWgPf parameter-level"
            >
              cnpDetails.planConfiguration.soilAnalysis.table.header.parameterLevel
            </th>
            <th
              class="c-kxWgPf"
            >
              cnpDetails.planConfiguration.soilAnalysis.table.header.validityExp
            </th>
            <th
              class="c-kxWgPf table-actions"
            >
              cnpDetails.planConfiguration.soilAnalysis.table.header.action
            </th>
          </tr>
        </thead>
        <tbody />
      </table>
      <button
        class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-ieICmTD-css"
        type="button"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.072 6v12M18 12H6"
          />
        </svg>
        <span
          class="c-iepcqn"
        >
          cnpDetails.planConfiguration.soilAnalysis.addTableButton
        </span>
      </button>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
