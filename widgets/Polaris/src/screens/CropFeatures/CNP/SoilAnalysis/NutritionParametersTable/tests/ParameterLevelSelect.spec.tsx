import React from 'react';
import { render } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountries<PERSON>and<PERSON>,
  updateCropRegionHandler,
  cropDemandAnalysisNutrientsHandler,
  allUnitsHandler,
  cropDemandAnalysisUpdateHandler,
  cropDemandAnalysesDataHandler,
  cropDemandAnalysesResponse,
} from '@common/mocks';
import { CropDemandAnalysisNutrient } from '@common/types';
import { ParameterLevelSelect } from '../components/ParameterLevelSelect';

const server = setupServer(
  updateCropRegionHandler,
  unitCountriesHandler,
  allUnitsHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisUpdateHandler,
  cropDemandAnalysesDataHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));
const onUpdate = jest.fn();

describe('Parameters Level Select', () => {
  it('should render without crashing', async () => {
    const component = render(
      <ParameterLevelSelect
        analysesNutrient={
          cropDemandAnalysesResponse.entities[0] as unknown as CropDemandAnalysisNutrient
        }
        cropDemandAnalysesNutrientsData={
          cropDemandAnalysesResponse.entities as unknown as CropDemandAnalysisNutrient[]
        }
        onUpdate={onUpdate}
      />,
    );

    const select = component.getByTestId('nutrient-classification-select');
    expect(select).toBeInTheDocument();

    expect(component.getByText('LOW')).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });

  it('should match snapshot', () => {
    const { asFragment } = render(
      <ParameterLevelSelect
        analysesNutrient={
          cropDemandAnalysesResponse.entities[0] as unknown as CropDemandAnalysisNutrient
        }
        cropDemandAnalysesNutrientsData={
          cropDemandAnalysesResponse.entities as unknown as CropDemandAnalysisNutrient[]
        }
        onUpdate={onUpdate}
      />,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
