import React, { FC, useEffect } from 'react';
import { CropDemandAnalysisNutrient } from '@common/types';
import { useUpdateCropDemandAnalysisNutrient } from '@polaris-hooks/index';
import { SelectWrapper } from '@widgets/Polaris/src/components';
import { Select } from '@yaradigitallabs/ahua-react';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { useTranslation } from 'react-i18next';
import { METHOD, ORDER } from '@common/constants';

export type ParameterLevelSelectProps = {
  onUpdate: (analysesNutrient: CropDemandAnalysisNutrient) => void;
  analysesNutrient: CropDemandAnalysisNutrient;
  cropDemandAnalysesNutrientsData: CropDemandAnalysisNutrient[];
};
export const ParameterLevelSelect: FC<ParameterLevelSelectProps> = ({
  onUpdate,
  analysesNutrient,
  cropDemandAnalysesNutrientsData,
}) => {
  const { trigger, CropDemandAnalysisNutrient } = useUpdateCropDemandAnalysisNutrient(
    analysesNutrient?.id,
  );
  const { setDisplaySnackbar } = useSnackbar();
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cnpDetails.planConfiguration.soilAnalysis',
  });

  const onChange = async (value: string, analysesNutrient: CropDemandAnalysisNutrient) => {
    const updateResponse = await trigger({
      method: METHOD.PUT,
      body: JSON.stringify({
        ...analysesNutrient,
        nutrientClassification: value,
      }),
    });
    if (updateResponse?.id)
      setDisplaySnackbar({
        title: t(`dialog.updateMessage`),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
  };

  useEffect(() => {
    if (CropDemandAnalysisNutrient) {
      onUpdate(CropDemandAnalysisNutrient);
    }
  }, [CropDemandAnalysisNutrient?.nutrientClassification]);

  const getDropDownData = () => {
    const allEntries = Object.entries(ORDER).map(([key, value]) => ({
      value: value + Math.random(),
      text: key,
    }));

    const existingParameterValues = cropDemandAnalysesNutrientsData?.map((el) => ({
      value: el.id,
      text: el.nutrientClassification,
    }));

    return [
      {
        value: analysesNutrient.id,
        text: analysesNutrient.nutrientClassification,
      },
      ...allEntries.filter((el) => {
        return existingParameterValues?.every((elem) => elem.text !== el.text);
      }),
    ].sort((a, b) => ORDER[a.text] - ORDER[b.text]);
  };
  const dropDownData = getDropDownData();

  return (
    <div
      key={`${analysesNutrient.id}-${analysesNutrient.nutrientClassification}-${
        ORDER[analysesNutrient.nutrientClassification]
      }`}
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <span
        className={'circle'}
        data-color={ORDER[analysesNutrient.nutrientClassification]}
        style={{ width: '13.3px' }}
      ></span>
      <SelectWrapper
        key={`${analysesNutrient.id}-${ORDER[analysesNutrient.nutrientClassification]}`}
        dataCy='nutrient-classification-select'
      >
        <Select
          key={`${analysesNutrient.id}-${ORDER[analysesNutrient.nutrientClassification]}`}
          className='select-nutrient-parameter-level'
          ariaLabel={`Select nutrient classification-${analysesNutrient.nutrientClassification}`}
          cover='outline'
          items={dropDownData}
          position='popper'
          size='s'
          value={analysesNutrient.id}
          onChange={(value: string) => {
            const parameterLevel = dropDownData.find((el) => el.value === value)?.text;
            if (parameterLevel) onChange(parameterLevel, analysesNutrient);
          }}
        />
      </SelectWrapper>
    </div>
  );
};
