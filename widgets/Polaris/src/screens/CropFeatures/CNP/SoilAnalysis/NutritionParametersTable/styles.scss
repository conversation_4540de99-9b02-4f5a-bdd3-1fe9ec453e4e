.header-row {
  border-top: 2px solid var(--colors-black10) !important;
  .table-actions {
    width: 68px;
  }
  .parameter-level {
    width: 138px;
  }
  th {
    line-height: 17.5px;
    vertical-align: middle;
    padding-top: 0;
    padding-bottom: 0;
    &:nth-child(1) {
      width: 51px;
    }
    &:nth-last-child(0) {
      width: 72px;
    }
  }
}

.table-body-row {
  &:nth-child(1) td {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  td {
    position: relative;
    padding-bottom: 0 !important;
    padding-top: 0 !important;
    &:nth-child(2) {
      font-weight: var(--fontWeights-regular);
      font-size: 14px;
      line-height: 19.6px;
      font-family: var(--fonts-default);
      color: var(--colors-neutral-darkest);
    }
  }
}

table {
  tr {
    &:nth-child(even) {
      td > p > span > button {
        background: rgba(15, 15, 15, 0);
      }
    }
  }
}

.input-text-color {
  color: var(--colors-brand-dark) !important;
  cursor: pointer;
}

.read-only {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-color: var(--colors-neutral-light);
  pointer-events: all;
  cursor: pointer;
}

.circle {
  width: var(--space-x3);
  height: var(--space-x3);
  border-radius: 50%;
  display: inline-block;
}

.circle[data-color='0'] {
  background-color: var(--colors-blue30);
}

.circle[data-color='1'] {
  background-color: var(--colors-green30);
}

.circle[data-color='2'] {
  background-color: var(--colors-yellow30);
}

.circle[data-color='3'] {
  background-color: var(--colors-orange30);
}

.circle[data-color='4'] {
  background-color: var(--colors-red30);
}

.hoverable-row {
  cursor: pointer;
  color: var(--colors-brand-dark) !important;
}

.hoverable-row .icons {
  display: none;
  right: 0;
}

.hoverable-row:hover .icons {
  display: inline-block;
}

.icon-container {
  display: flex;
  gap: var(--space-x2);
  position: absolute;
  right: 0;
  top: 10px;
}

.select-nutrient-parameter-level {
  div > button {
    border: 0;
    padding: 0;
    padding-inline-start: 0 !important;
    padding-inline-end: 0 !important;

    &:nth-child(even) {
      background: rgba(15, 15, 15, 0);
    }
  }
  div > label {
    margin-right: 0;
    font-weight: normal;
  }
}
