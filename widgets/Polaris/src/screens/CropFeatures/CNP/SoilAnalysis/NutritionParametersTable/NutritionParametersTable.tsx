import React, { useCallback, useEffect, useMemo, useReducer, useState } from 'react';
import { Button, Caption, CheckBox, IconButton, Table } from '@yaradigitallabs/ahua-react';
import { TableContainer } from '../SoilAnalysis.styled';
import { useTranslation } from 'react-i18next';
import './styles.scss';
import {
  NutritionParametersTableProps,
  UpdateAnalysisActions,
} from './NutritionParametersTable.type';
import {
  useBulkCreateCropDemandAnalysisNutrients,
  useDeleteCropDemandAnalysisNutrient,
  useFetchCropDemandAnalysisNutrients,
  useUpdateCropDemandAnalysisNutrient,
} from '@polaris-hooks/index';
import { CropDemandAnalysisNutrient } from '@common/types';
import { ConfirmationDialog } from '@widgets/Polaris/src/components/ConfirmationDialog';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { ParameterLevelSelect } from './components/ParameterLevelSelect';
import { AddNutritionParamPopup, AddParamStateProps } from '../AddNutritionParamPopup';
import { analysisInitialState, analysisReducer, getSecondaryParamData } from '../utils';
import { displaySnackbarMessage } from '@widgets/Polaris/utils';
import { useClipboard } from '@polaris-hooks/index';
import { EXPRESSION_FIELDS, FIELD_MODULES, METHOD, ORDER } from '@common/constants';
import { addNewParamState, UpdateType } from './NutritionParametersTable.constants';
import { getParameterLevel } from '@widgets/Polaris/src/components';

export function NutritionParametersTable({
  cropDemandAnalysesData,
  setExpression,
  setModified,
  setModifiedBy,
  setIsExpressionBuilderOpen,
  cropDemandAnalysisNutrients,
  isDefaultActive,
}: NutritionParametersTableProps) {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });

  const translationPrefix = 'cnpDetails.planConfiguration.soilAnalysis';

  const [state, dispatch] = useReducer(analysisReducer, analysisInitialState);
  const [openInfoDialog, setOpenInfoDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [deleteDialogTitle, setDeleteDialogTitle] = useState('');
  const [isAddPopupOpened, setIsAddPopupOpened] = useState<boolean>(false);
  const [currentDeleteItem, setCurrentDeleteItem] = useState<CropDemandAnalysisNutrient | null>(
    null,
  );
  const [currentUpdateItem, setCurrentUpdateItem] = useState<CropDemandAnalysisNutrient | null>(
    null,
  );
  const [addParamState, setAddParamState] = useState<AddParamStateProps>(addNewParamState);

  const { copyToClipboard, pasteFromClipboard } = useClipboard();
  const {
    bulkCreateCropDemandAnalysisNutrientsData,
    triggerBulkCreateCropDemandAnalysisNutrients,
  } = useBulkCreateCropDemandAnalysisNutrients();

  const { trigger: triggerUpdateCropDemandAnalysisNutrient, CropDemandAnalysisNutrient } =
    useUpdateCropDemandAnalysisNutrient(state.selectedNutrient?.id);
  const {
    deletedCropDemandAnalysis,
    triggerDelete,
    isMutating: isBulkCropDemandADeleting,
  } = useDeleteCropDemandAnalysisNutrient(currentDeleteItem?.id);

  const { orderedCropDemandAnalysesData = [], isCropDemandAnalysisNutrientsLoading } =
    useFetchCropDemandAnalysisNutrients(
      cropDemandAnalysesData?.id,
      CropDemandAnalysisNutrient || bulkCreateCropDemandAnalysisNutrientsData?.[0],
      deletedCropDemandAnalysis?.id,
      currentUpdateItem?.nutrientClassification,
      cropDemandAnalysisNutrients,
    );

  const { setDisplaySnackbar } = useSnackbar();

  const secondaryLevelsData = useMemo(() => {
    return getSecondaryParamData(orderedCropDemandAnalysesData);
  }, [orderedCropDemandAnalysesData?.length]);

  const handleOpenDeleteDialog = (analysisNutrient: CropDemandAnalysisNutrient) => {
    setCurrentDeleteItem(analysisNutrient);
    setDeleteDialogTitle(
      t(`${translationPrefix}.dialog.title`, {
        cropName: analysisNutrient.nutrientClassification,
      }),
    );
    setOpenDeleteDialog(true);
  };

  const handleDeleteItem = async () => {
    const response = await triggerDelete({
      method: METHOD.DELETE,
    });
    if (response?.id)
      setDisplaySnackbar({
        title: t(`${translationPrefix}.dialog.deleteMessage`),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
  };
  const confirmDelete = async () => {
    await handleDeleteItem();
    setOpenDeleteDialog(false);
  };
  const closeInfo = () => {
    setOpenInfoDialog(false);
  };

  /** Function that opens the ExpressionBuilder and sets the correct data from states
   * @param {CropDemandAnalysisNutrient} analysesNutrient - The specific nutrient analysis data to be edited
   */
  const openExpressionBuilder = (analysesNutrient: CropDemandAnalysisNutrient) => {
    setExpression(analysesNutrient?.validityExpression ?? '');
    setModified(analysesNutrient?.modified?.toString());
    setModifiedBy(analysesNutrient?.modifiedBy);
    setIsExpressionBuilderOpen(true);
  };

  const handleCopy = useCallback(
    (e: React.MouseEvent, analysesNutrient: CropDemandAnalysisNutrient): void => {
      e.stopPropagation();
      if (analysesNutrient.validityExpressionTree) {
        copyToClipboard({
          expressionString: analysesNutrient.validityExpression ?? '',
          expressionTree: analysesNutrient.validityExpressionTree,
          field: EXPRESSION_FIELDS.VALIDITY_EXPRESSION,
          fieldModule: FIELD_MODULES.ANALYSIS_DEMAND,
        });
        dispatch({
          type: UpdateAnalysisActions.SET_UPDATE_TYPE,
          payload: UpdateType.PASTE,
        });
        displaySnackbarMessage(t(`common.clipboardCopyMessage`), setDisplaySnackbar);
      }
    },
    [copyToClipboard, dispatch, setDisplaySnackbar, t],
  );

  const handlePaste = useCallback(
    async (e: React.MouseEvent, analysesNutrient: CropDemandAnalysisNutrient): Promise<void> => {
      e.stopPropagation();

      pasteFromClipboard((data) => {
        const { expressionString, expressionTree, field, fieldModule } = data;
        if (
          field === EXPRESSION_FIELDS.VALIDITY_EXPRESSION &&
          fieldModule === FIELD_MODULES.ANALYSIS_DEMAND
        ) {
          setExpression(expressionString);
          dispatch({
            type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT,
            payload: {
              ...analysesNutrient,
              validityExpression: expressionString,
              validityExpressionTree: expressionTree,
            },
          });
          dispatch({
            type: UpdateAnalysisActions.SET_UPDATE_TYPE,
            payload: UpdateType.PASTE,
          });
        }
      });
    },
    [pasteFromClipboard, setExpression, dispatch],
  );

  const handleCheckboxChange = (analysesNutrient: CropDemandAnalysisNutrient): void => {
    dispatch({
      type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT,
      payload: analysesNutrient,
    });
    dispatch({
      type: UpdateAnalysisActions.SET_UPDATE_TYPE,
      payload: UpdateType.DEFAULT,
    });
  };

  const handleDefaultUpdate = useCallback(async () => {
    if (!state.selectedNutrient) return;

    const hasMoreThanFive = orderedCropDemandAnalysesData?.length > 5;
    const existingDefaultParamNutrient = orderedCropDemandAnalysesData?.find(
      (nutrientAnalysis: CropDemandAnalysisNutrient) =>
        nutrientAnalysis.isDefault &&
        nutrientAnalysis.secondaryParamOrder?.orderNo ===
          state?.selectedNutrient?.secondaryParamOrder?.orderNo,
    );

    if (hasMoreThanFive && existingDefaultParamNutrient) {
      dispatch({
        type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT,
        payload: existingDefaultParamNutrient,
      });
    } else if (!hasMoreThanFive) {
      const shouldBeDisabled = orderedCropDemandAnalysesData?.find(
        (nutrientAnalysis: CropDemandAnalysisNutrient) =>
          nutrientAnalysis.isDefault && nutrientAnalysis.id !== state?.selectedNutrient?.id,
      );
      if (shouldBeDisabled) {
        dispatch({
          type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT,
          payload: shouldBeDisabled,
        });
      }
    }

    await triggerUpdateCropDemandAnalysisNutrient({
      method: METHOD.PUT,
      body: JSON.stringify({
        ...state.selectedNutrient,
        isDefault: !state.selectedNutrient.isDefault,
      }),
    });
  }, [
    state.selectedNutrient,
    orderedCropDemandAnalysesData,
    dispatch,
    triggerUpdateCropDemandAnalysisNutrient,
  ]);

  const handlePasteUpdate = useCallback(async (): Promise<void> => {
    if (!state.selectedNutrient) return;
    await triggerUpdateCropDemandAnalysisNutrient({
      method: METHOD.PUT,
      body: JSON.stringify({
        ...state.selectedNutrient,
        validityExpression: state.selectedNutrient.validityExpression,
        validityExpressionTree: state.selectedNutrient.validityExpressionTree,
      }),
    });
    displaySnackbarMessage(t(`common.changeSaved`), setDisplaySnackbar);
  }, [state.selectedNutrient, triggerUpdateCropDemandAnalysisNutrient, setDisplaySnackbar, t]);

  useEffect(() => {
    switch (state.updateType) {
      case UpdateType.DEFAULT:
        handleDefaultUpdate();
        break;
      case UpdateType.PASTE:
        handlePasteUpdate();
        break;
      default:
        break;
    }
  }, [state.selectedNutrient]);

  return (
    <>
      {!(isBulkCropDemandADeleting && isCropDemandAnalysisNutrientsLoading) && (
        <React.Fragment>
          {orderedCropDemandAnalysesData?.length > 5 ? (
            <>
              {displayTable(secondaryLevelsData?.secondaryVeryLowLevels)}
              {displayTable(secondaryLevelsData?.secondaryLowLevels)}
              {displayTable(secondaryLevelsData?.secondaryMediumLevels)}
              {displayTable(secondaryLevelsData?.secondaryHighLevels)}
              {displayTable(secondaryLevelsData?.secondaryVeryHighLevels)}
            </>
          ) : (
            displayTable(orderedCropDemandAnalysesData)
          )}
        </React.Fragment>
      )}
      <ConfirmationDialog
        open={openDeleteDialog}
        title={deleteDialogTitle}
        description={t(`${translationPrefix}.dialog.description`)}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={t(`common.yesDelete`)}
        okButtonConcept='destructive'
        cancelButton={t('common.cancel')}
        onOk={confirmDelete}
        onCancel={() => setOpenDeleteDialog(false)}
        isLoading={isBulkCropDemandADeleting}
      />
      <ConfirmationDialog
        open={openInfoDialog}
        title={t(`${translationPrefix}.dialog.info.title`)}
        description={t(`${translationPrefix}.dialog.info.description`)}
        icon='Info'
        iconColorConcept='brand'
        okButton={t(`common.ok`)}
        onOk={closeInfo}
        hideCancelButton
      />
      <AddNutritionParamPopup
        isAddPopupOpened={isAddPopupOpened}
        setIsAddPopupOpened={setIsAddPopupOpened}
        cropDemandAnalysesData={cropDemandAnalysesData}
        addParamState={addParamState}
        setAddParamState={setAddParamState}
        cropDemandAnalysisNutrients={orderedCropDemandAnalysesData}
        triggerUpdateCropDemandAnalysisNutrient={triggerUpdateCropDemandAnalysisNutrient}
        triggerBulkCreateCropDemandAnalysisNutrients={triggerBulkCreateCropDemandAnalysisNutrients}
        setCurrentUpdateItem={setCurrentUpdateItem}
      />
    </>
  );

  // Represents Table of the analysis methods for default and secondary parameter expression
  function displayTable(nutrientLevels: CropDemandAnalysisNutrient[] | undefined): JSX.Element {
    return (
      <TableContainer data-cy='nutrition-parameter-levels-table'>
        <Table className='table'>
          <thead>
            <Table.Row className='header-row'>
              <Table.Head>{t(`${translationPrefix}.table.header.default`)}</Table.Head>
              {isDefaultActive && (
                <Table.Head className='parameter-level'>
                  {t(`${translationPrefix}.table.header.secondaryParamLevel`)}
                </Table.Head>
              )}
              <Table.Head className='parameter-level'>
                {t(`${translationPrefix}.table.header.parameterLevel`)}
              </Table.Head>
              <Table.Head>{t(`${translationPrefix}.table.header.validityExp`)}</Table.Head>
              <Table.Head className='table-actions'>
                {t(`${translationPrefix}.table.header.action`)}
              </Table.Head>
            </Table.Row>
          </thead>
          <tbody>
            {nutrientLevels?.map((analysesNutrient, index) => {
              const orderKey = getParameterLevel(analysesNutrient?.secondaryParamOrder?.orderName);
              return (
                <Table.Row key={analysesNutrient.id} className='table-body-row'>
                  <Table.Cell>
                    <CheckBox
                      onCheckedChange={(value: boolean) => {
                        if (value) handleCheckboxChange(analysesNutrient);
                      }}
                      checked={analysesNutrient.isDefault}
                      data-cy={`nutrient-${index}-checkbox`}
                    />
                  </Table.Cell>
                  {isDefaultActive && (
                    <Table.Cell>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <span className={'circle'} data-color={orderKey && ORDER[orderKey]}></span>
                        <span style={{ paddingLeft: '8px' }}>
                          {analysesNutrient?.secondaryParamOrder?.orderName}
                        </span>
                      </div>
                    </Table.Cell>
                  )}
                  <Table.Cell>
                    {nutrientLevels?.length < 5 ? (
                      <ParameterLevelSelect
                        onUpdate={setCurrentUpdateItem}
                        analysesNutrient={analysesNutrient}
                        cropDemandAnalysesNutrientsData={nutrientLevels}
                      />
                    ) : (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <span className={'circle'} data-color={orderKey && ORDER[orderKey]}></span>
                        <span style={{ paddingLeft: '8px' }}>
                          {analysesNutrient.nutrientClassification}
                        </span>
                      </div>
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <Caption
                      className='hoverable-row'
                      data-cy={`validity-expression-${index}`}
                      onClick={() => openExpressionBuilder(analysesNutrient)}
                    >
                      {analysesNutrient.validityExpression}
                      <span className='icon-container'>
                        <IconButton
                          title='Copy'
                          type='filled'
                          colorConcept='brand'
                          icon='Copy'
                          className='icons'
                          size='xs'
                          css={{ padding: '$space$x1' }}
                          onClick={(e) => handleCopy(e, analysesNutrient)}
                        ></IconButton>
                        <IconButton
                          title='Paste'
                          type='filled'
                          colorConcept='brand'
                          icon='File'
                          size='xs'
                          className='icons'
                          css={{ padding: '$space$x1' }}
                          onClick={(e) => handlePaste(e, analysesNutrient)}
                        ></IconButton>
                      </span>
                    </Caption>
                  </Table.Cell>
                  <Table.ActionsCell>
                    <Table.Action
                      icon='Delete'
                      onClick={() => {
                        if (!analysesNutrient.isDefault) {
                          handleOpenDeleteDialog(analysesNutrient);
                        } else {
                          setOpenInfoDialog(true);
                        }
                      }}
                      data-cy={`delete-nutrient-${index}`}
                    />
                  </Table.ActionsCell>
                </Table.Row>
              );
            })}
          </tbody>
        </Table>
        <Button
          type='button'
          iconLeading='Plus'
          label='Add another'
          size='s'
          disabled={nutrientLevels?.length === 5}
          variant='ghost'
          css={{ backgroundColor: 'white', paddingLeft: 0 }}
          onClick={() => {
            // This condition will be removed when secondary parameter activation is implemented
            if (orderedCropDemandAnalysesData?.length <= 5) {
              setIsAddPopupOpened(!isAddPopupOpened);
              setAddParamState(addNewParamState);
            }
          }}
        >
          {t(`${translationPrefix}.addTableButton`)}
        </Button>
      </TableContainer>
    );
  }
}
