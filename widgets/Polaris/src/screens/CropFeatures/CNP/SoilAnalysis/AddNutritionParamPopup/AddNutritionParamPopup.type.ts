import { Dispatch, SetStateAction } from 'react';
import {
  CropDemandAnalysis,
  CropDemandAnalysisNutrient,
  CropDemandAnalysisDefaultParameters,
} from '@common/types';

export interface AddNutritionParamPopupProps {
  isAddPopupOpened: boolean;
  setIsAddPopupOpened: Dispatch<SetStateAction<boolean>>;
  cropDemandAnalysesData: CropDemandAnalysis;
  addParamState: AddParamStateProps;
  setAddParamState: Dispatch<SetStateAction<AddParamStateProps>>;
  cropDemandAnalysisNutrients: CropDemandAnalysisNutrient[] | undefined;
  triggerUpdateCropDemandAnalysisNutrient: (config: {
    method: string;
    body: string;
  }) => Promise<CropDemandAnalysisNutrient | undefined>;
  triggerBulkCreateCropDemandAnalysisNutrients: (config: {
    method: string;
    body: string;
  }) => Promise<CropDemandAnalysisNutrient[] | undefined>;
  setCurrentUpdateItem: Dispatch<SetStateAction<CropDemandAnalysisNutrient | null>>;
}
export interface AddParamStateProps {
  defaultCropDemandAnalysesNutrient: CropDemandAnalysisNutrient | undefined;
  selectedCropDemandAnalyses: CropDemandAnalysisDefaultParameters | null;
  isDefaultChecked: boolean;
  parameters: CropDemandAnalysisDefaultParameters[];
  error: boolean;
}

export interface Parameter {
  text: string;
  value: string;
}
