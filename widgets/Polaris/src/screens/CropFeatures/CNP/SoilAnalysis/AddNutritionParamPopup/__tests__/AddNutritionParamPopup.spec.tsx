/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { AddNutritionParamPopup, AddNutritionParamPopupProps } from '../index';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import {
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisUpdateHandler,
  updateCropRegionHandler,
  mockAppProviderValue,
  snackbarInitialStateMock,
} from '@common/mocks';
import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';
import { act } from '@testing-library/react';

const server = setupServer(
  updateCropRegionHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisUpdateHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

describe('widget: AddPrePostCrop', () => {
  const mockSetDisplaySnackbar = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn().mockResolvedValue(void 0),
        readText: jest.fn().mockResolvedValue(
          JSON.stringify({
            expressionString: 'Analysis.S > 0.0 AND Analysis.S < 0.0',
            expressionTree: {},
            field: 'validityExpression',
            fieldModule: 'AnalysisDemand',
          }),
        ),
      },
    });
  });

  const addNutritionParamPopup = {
    isAddPopupOpened: false,
    setIsAddPopupOpened: jest.fn(),
    cropDemandAnalysisNutrients: [{ text: 'LOW', value: 'LOW' }],
    cropDemandAnalysesData: {},
    setAddParamState: jest.fn(),
    addParamState: {
      selectedCropDemandAnalyses: {
        validityExpression: 'Analysis.S > 0.0 AND Analysis.S < 0.0',
        validityExpressionTree: {
          /* Mock the expected structure here */
        },
        nutrientClassification: 'LOW',
      },
      isDefaultChecked: false,
      parameters: [],
      error: false,
    },
  } as unknown as AddNutritionParamPopupProps;

  test('AddNutritionParamPopup: should not show modal when isAddPopupOpened is false', () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: false };
    render(<AddNutritionParamPopup {...props} />);
    expect(
      screen.queryByText(
        'polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.addAnotherLevel',
      ),
    ).not.toBeInTheDocument();
  });

  test('AddNutritionParamPopup: should not show modal when isAddPopupOpened is true', async () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    render(<AddNutritionParamPopup {...props} />);
    expect(
      screen.getByText('polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.addAnotherLevel'),
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.validityExpression',
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByText('polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.setAsDefault'),
    ).toBeInTheDocument();
    expect(
      screen.getByText('polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.yesSetAsDefault'),
    ).toBeInTheDocument();
  });

  test('AddNutritionParamPopup: should change validity expression', () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AddNutritionParamPopup {...props} />
      </AppContext.Provider>,
    );
    const validityExpressionInput = component.getByTestId('validity-expression');
    expect(validityExpressionInput).toBeInTheDocument();
    fireEvent.click(validityExpressionInput, {
      target: { value: 'Analysis.N >= 3.0 AND Analysis.N < 4.0' },
    });
    fireEvent.change(validityExpressionInput, { target: { value: '' } });
    expect(validityExpressionInput).toHaveValue('Analysis.S > 0.0 AND Analysis.S < 0.0');
  });

  test('AddNutritionParamPopup: should be checked default parameter event', async () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: snackbarInitialStateMock,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AddNutritionParamPopup {...props} />
      </SnackbarContext.Provider>,
    );
    const validityExpressionInput = screen.getByTestId('validity-expression');
    const copyIcon = screen.getByTestId('copy-validity-expression');
    const pasteIcon = screen.getByTestId('paste-validity-expression');
    const defaultCheckbox = screen.getByTestId('set-as-default');

    expect(copyIcon).toBeInTheDocument();
    expect(pasteIcon).toBeInTheDocument();

    await act(async () => {
      fireEvent.click(copyIcon);
      fireEvent.click(pasteIcon);
    });

    await act(async () => {
      fireEvent.click(defaultCheckbox);
      fireEvent.click(validityExpressionInput, {
        target: { value: 'Analysis.N >= 3.0 AND Analysis.N < 4.0' },
      });
    });

    expect(defaultCheckbox).toBeInTheDocument();
  });

  test('should match snapshot when isAddPopupOpened is true', async () => {
    const props = { ...addNutritionParamPopup, isAddPopupOpened: true };
    const component = render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: snackbarInitialStateMock,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AddNutritionParamPopup {...props} />
      </SnackbarContext.Provider>,
    );
    expect(component).toMatchSnapshot();
  });
});
