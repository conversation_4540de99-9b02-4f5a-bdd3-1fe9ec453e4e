import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { IconButton, Input, Select, Title } from '@yaradigitallabs/ahua-react';
import { DefaultClassificationEnum } from '../../../shared/constants';

import { ACTIONS, EXPRESSION_FIELDS, FIELD_MODULES } from '@common/constants';
import './AddNutritionParamPopup.scss';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { useExpressionBuilder, useUpdateCropDemandAnalysisNutrient } from '@polaris-hooks/index';
import { ExpressionBuilder, LabelWithCheckbox, ModalDialog } from '@widgets/Polaris/src/components';
import {
  AddNutritionParamPopupProps,
  Parameter,
  crossIcon,
  DescriptionStyle,
  LevelColumnMain,
  ValidityExpressionMain,
} from './index';
import {
  getParametersName,
  setCropDemandAnalysis,
  setRemainingNutrientParams,
  addNutrientParameterUtility,
  getDefaultCropDemandAnalysisNutrients,
  getInitialValues,
} from '../utils';
import './AddNutritionParamPopup.scss';
import { displaySnackbarMessage } from '@widgets/Polaris/utils';
import { useClipboard } from '@polaris-hooks/useClipboard';
import { ExpressionData } from '@common/types';

export const AddNutritionParamPopup: React.FC<AddNutritionParamPopupProps> = ({
  isAddPopupOpened,
  setIsAddPopupOpened,
  cropDemandAnalysesData,
  addParamState,
  setAddParamState,
  cropDemandAnalysisNutrients,
  triggerBulkCreateCropDemandAnalysisNutrients,
  setCurrentUpdateItem,
}) => {
  const { t } = useTranslation();
  const { setDisplaySnackbar } = useSnackbar();

  const { trigger: triggerUpdateCropDemandAnalysisNutrient } = useUpdateCropDemandAnalysisNutrient(
    addParamState.defaultCropDemandAnalysesNutrient?.id,
  );
  const prefix = 'polaris.cnpDetails.planConfiguration.soilAnalysis.dialog';
  const {
    expression,
    setExpression,
    modified,
    modifiedBy,
    isExpressionBuilderOpen,
    setIsExpressionBuilderOpen,
  } = useExpressionBuilder();
  const { selectedNutrient } = useAppContext();
  const { copyToClipboard, pasteFromClipboard } = useClipboard();

  useEffect(() => {
    // generate and save default nutrient parameters to show in the add 'another row' popup parameter
    if (selectedNutrient && cropDemandAnalysesData) {
      setRemainingNutrientParams(
        setAddParamState,
        cropDemandAnalysisNutrients,
        getDefaultCropDemandAnalysisNutrients({
          cropDemandAnalysis: cropDemandAnalysesData,
          clayClassification: DefaultClassificationEnum.DEFAULT,
          nutrientName: selectedNutrient?.elementalName || '',
        })?.defaultCropDemandAnalysisNutrients,
      );
    }
  }, [isAddPopupOpened, selectedNutrient, cropDemandAnalysesData]);

  const cropDANutrientsParameters: Parameter[] = useMemo(() => {
    if (!addParamState?.parameters?.length) return [];
    return getParametersName(addParamState?.parameters);
  }, [addParamState?.parameters]);

  const openExpressionBuilder = (expression: string): void => {
    if (expression) {
      setExpression(expression);
    }
    setIsExpressionBuilderOpen(true);
    setIsAddPopupOpened(false);
  };

  const updateCropDemandAnalysis = (value: string): void => {
    setCropDemandAnalysis(value, setAddParamState);
    setIsExpressionBuilderOpen(false);
  };

  const addNewParameterHandler = async (): Promise<void> => {
    const nutrient = await addNutrientParameterUtility(
      addParamState,
      setAddParamState,
      setDisplaySnackbar,
      setIsAddPopupOpened,
      triggerUpdateCropDemandAnalysisNutrient,
      triggerBulkCreateCropDemandAnalysisNutrients,
      t(`polaris.common.levelAdded`, {
        paramLevel: addParamState.selectedCropDemandAnalyses?.nutrientClassification,
      }),
      expression,
    );
    if (!nutrient) return;
    setCurrentUpdateItem(nutrient);
  };

  const handleCopy = (e: React.MouseEvent): void => {
    e.stopPropagation();
    if (!addParamState.selectedCropDemandAnalyses?.validityExpressionTree) {
      return;
    }

    copyToClipboard({
      expressionString: expression,
      expressionTree: addParamState.selectedCropDemandAnalyses.validityExpressionTree,
      field: EXPRESSION_FIELDS.VALIDITY_EXPRESSION,
      fieldModule: FIELD_MODULES.ANALYSIS_DEMAND,
    });
    displaySnackbarMessage(t('polaris.common.clipboardCopyMessage'), setDisplaySnackbar);
  };

  const handlePaste = (e: React.MouseEvent): void => {
    e.stopPropagation();
    pasteFromClipboard((data: ExpressionData) => {
      const { expressionString, expressionTree, field, fieldModule } = data;
      if (
        field === EXPRESSION_FIELDS.VALIDITY_EXPRESSION &&
        fieldModule === FIELD_MODULES.ANALYSIS_DEMAND
      ) {
        // @ts-ignore
        setAddParamState((prevState) => ({
          ...prevState,
          selectedCropDemandAnalyses: {
            ...prevState.selectedCropDemandAnalyses,
            validityExpression: expressionString,
            validityExpressionTree: expressionTree,
          },
        }));
        setExpression(expressionString);
      }
    });
  };

  useEffect(() => {
    if (isAddPopupOpened && selectedNutrient) {
      const initialValues = getInitialValues(selectedNutrient.elementalName);

      setAddParamState((prevState) => ({
        ...prevState,
        selectedCropDemandAnalyses: {
          ...prevState.selectedCropDemandAnalyses,
          ...initialValues,
        },
      }));

      setExpression(initialValues?.validityExpression || '');
    }
  }, [isAddPopupOpened, selectedNutrient]);

  return (
    <>
      <ExpressionBuilder
        expression={expression}
        modified={modified}
        modifiedBy={modifiedBy}
        isExpressionBuilderOpen={isExpressionBuilderOpen}
        onCloseExpressionBuilder={() => {
          setIsExpressionBuilderOpen(!isExpressionBuilderOpen);
          setIsAddPopupOpened(true);
        }}
      />

      <ModalDialog
        isOpen={isAddPopupOpened}
        onChange={setIsAddPopupOpened}
        title={t(`${prefix}.addAnotherLevel`, {
          actionName: ACTIONS.ADD,
        })}
        css={{ crossIcon }}
        dataCy={`${isAddPopupOpened ? 'opened' : 'closed'}-add another level`}
        onSaveClick={addNewParameterHandler}
        actionType={ACTIONS.ADD}
      >
        <LevelColumnMain>
          <Select
            ariaLabel='Select unit'
            cover='outline'
            items={cropDANutrientsParameters || []}
            position='popper'
            size='s'
            label={t(`${prefix}.parameterLevel`)}
            onChange={updateCropDemandAnalysis}
            value={
              addParamState?.selectedCropDemandAnalyses
                ? addParamState.selectedCropDemandAnalyses.nutrientClassification
                : undefined
            }
            variant={addParamState?.error ? 'error' : 'default'}
            helper-text={
              addParamState?.error
                ? t(`polaris.common.isRequired`, {
                    fieldName: t(`${prefix}.paramLevel`),
                  })
                : null
            }
          />
          <ValidityExpressionMain
            data-cy='validity-expression-main'
            className='validity-expression-container'
          >
            <Input
              size='s'
              type='text'
              className='input-text-color'
              value={expression}
              onChange={() => {
                /* Intentionally empty to maintain controlled component with no dynamic change handling */
              }}
              onClick={() => openExpressionBuilder(expression)}
              variant={'default'}
              label={t(`${prefix}.validityExpression`)}
              data-cy='validity-expression'
              css={{ paddingTop: '18px' }}
            />
            <span className='icon-validity-expression'>
              <IconButton
                type='filled'
                colorConcept='brand'
                icon='Copy'
                className='icons'
                size='xs'
                css={{ padding: '$space$x1' }}
                onClick={handleCopy}
                data-cy='copy-validity-expression'
              />
              <IconButton
                type='filled'
                colorConcept='brand'
                icon='File'
                size='xs'
                className='icons'
                css={{ padding: '$space$x1' }}
                onClick={handlePaste}
                data-cy='paste-validity-expression'
              />
            </span>
          </ValidityExpressionMain>
          <Title size='xs' data-cy='set-as-default-caption' style={DescriptionStyle}>
            {t(`${prefix}.setAsDefault`)}
          </Title>

          <LabelWithCheckbox
            labelText={t(`${prefix}.yesSetAsDefault`)}
            checked={addParamState?.isDefaultChecked}
            dataCy='set-as-default'
            checkboxClickHandler={() =>
              setAddParamState((paramState) => ({
                ...paramState,
                isDefaultChecked: !paramState.isDefaultChecked,
              }))
            }
          />
        </LevelColumnMain>
      </ModalDialog>
    </>
  );
};
