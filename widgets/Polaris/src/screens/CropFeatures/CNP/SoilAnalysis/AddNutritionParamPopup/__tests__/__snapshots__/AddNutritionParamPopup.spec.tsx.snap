// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`widget: AddPrePostCrop should match snapshot when isAddPopupOpened is true 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    />
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:r13:"
      aria-labelledby="radix-:r12:"
      class="c-cPoUYR c-cPoUYR-ieoIBVf-css"
      data-cy="opened-add another level"
      data-state="open"
      id="radix-:r11:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-cVIuYM dialog-header-main"
      >
        <h2
          class="c-bALNxX"
          data-cy="dialog-polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.addAnotherLevel"
          id="radix-:r12:"
        >
          polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.addAnotherLevel
        </h2>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs c-dexIdH-iivYAPe-css"
          data-cy="dialog-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-jndegn c-jndegn-igwRkrt-css dialog-middle"
      >
        <div
          class="c-jnTsVE"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-ubosY-textType-labelAsPlaceholder"
              >
                polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.parameterLevel
              </label>
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-PJLV-hDbASB-selectSize-s"
              />
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r14:"
                aria-expanded="false"
                aria-label="Select unit"
                class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="0"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
          <div
            class="c-cmpvrW validity-expression-container"
            data-cy="validity-expression-main"
          >
            <div
              class="c-gJoajD c-gJoajD-iPJLV-css"
            >
              <input
                class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-cfuiQQ-cv c-exiTqG-bTMGXY-cv c-exiTqG-ijkjzsQ-css input-text-color"
                data-cy="validity-expression"
                placeholder="polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.validityExpression"
                type="text"
                value=""
              />
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
              >
                polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.validityExpression
              </label>
              <span
                class="c-fcBbhr"
              />
            </div>
            <span
              class="icon-validity-expression"
            >
              <button
                class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs c-kAXHSi-fXSdpP-variant-filled c-dexIdH-ifUlxCL-css icons"
                data-cy="copy-validity-expression"
                type="button"
              >
                <svg
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15.7 17h1.673c1.146 0 1.718 0 2.154-.228a2 2 0 0 0 .845-.845c.228-.436.228-1.008.228-2.154V6.6c0-1.5 0-2.25-.382-2.776a1.998 1.998 0 0 0-.442-.442C19.25 3 18.5 3 17 3h-4.4c-.932 0-1.398 0-1.765.152a2 2 0 0 0-1.083 1.083C9.6 4.602 9.6 5.068 9.6 6M7 21.2h5c1.5 0 2.25 0 2.776-.382.17-.123.319-.273.442-.443.382-.525.382-1.275.382-2.775V9.8c0-1.5 0-2.25-.382-2.776a1.998 1.998 0 0 0-.442-.442C14.25 6.2 13.5 6.2 12 6.2H7c-1.5 0-2.25 0-2.776.382a2 2 0 0 0-.442.442C3.4 7.55 3.4 8.3 3.4 9.8v7.8c0 1.5 0 2.25.382 2.775a2 2 0 0 0 .442.443C4.75 21.2 5.5 21.2 7 21.2z"
                  />
                </svg>
              </button>
              <button
                class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs c-kAXHSi-fXSdpP-variant-filled c-dexIdH-ifUlxCL-css icons"
                data-cy="paste-validity-expression"
                type="button"
              >
                <svg
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 2H7a3 3 0 00-3 3v14a3 3 0 003 3h10a3 3 0 003-3V7m-5-5h2a3 3 0 013 3v2m-5-5v2a3 3 0 003 3h2"
                  />
                </svg>
              </button>
            </span>
          </div>
          <h1
            class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css"
            data-cy="set-as-default-caption"
            style="font-weight: 700;"
          >
            polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.setAsDefault
          </h1>
          <div
            class="c-cnNYRG"
          >
            <label
              class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
              data-cy="polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.yesSetAsDefault"
              for="option undefined"
            >
              polaris.cnpDetails.planConfiguration.soilAnalysis.dialog.yesSetAsDefault
            </label>
            <div
              class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
            >
              <button
                aria-checked="false"
                aria-label="Checkbox false"
                class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                data-cy="set-as-default"
                data-state="unchecked"
                role="checkbox"
                type="button"
                value="on"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-ctwkvW"
      >
        <div
          class="c-hcqlDB"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary"
            data-cy="dialog-save-btn"
          >
            <span
              class="c-iepcqn"
            >
              save
            </span>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
