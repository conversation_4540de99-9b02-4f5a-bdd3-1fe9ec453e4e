.collapsible-section {
    & > button {
      height: var(--space-x14);
      border: 1px solid rgba(15, 15, 15, 0.2);
      font-family: var(--fonts-default);
      padding: var(--space-x4);
      font-size: var(--space-x4);
      font-weight: var(--fontWeights-bold);
      letter-spacing: -0.01em;
      color: var(--colors-neutral-darkest);
      height: var(--sizes-x14);
      &[data-state='open'] {
        border-bottom-right-radius: unset;
        border-bottom-left-radius: unset;
        border-bottom: 0;
      }
      & > div > svg {
        color: var(--colors-brand-contrast);
      }
    }
    & > div {
      border: 1px solid rgba(15, 15, 15, 0.2);
      gap: 8px;
      border-radius: 0 0 8px 8px;
    }
  }

  .icon-button {
    background: 'white' !important
  }

  .icon-button:not(svg) {
    background: 'white' !important
  }