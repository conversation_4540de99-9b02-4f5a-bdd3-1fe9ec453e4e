import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import NutrientTile from '../NutrientTile';
import { nutrientMock } from '@common/mocks';

describe('NutrientTile', () => {
  const onSelectMock = jest.fn();

  test('renders correctly with required props', () => {
    render(<NutrientTile nutrient={nutrientMock} onSelect={onSelectMock} />);
    expect(screen.getByText('Sulphur')).toBeInTheDocument();
  });

  test('clicking the tile triggers onSelect callback', () => {
    render(<NutrientTile nutrient={nutrientMock} onSelect={onSelectMock} />);
    fireEvent.click(screen.getByText('Sulphur'));
    expect(onSelectMock).toHaveBeenCalledWith(nutrientMock);
  });

  test('Copy and Delete buttons appear and work as expected', () => {
    const onCopyMock = jest.fn();
    const onDeleteMock = jest.fn();
    render(
      <NutrientTile
        nutrient={nutrientMock}
        onSelect={onSelectMock}
        selected={true}
        variant='list'
        onCopy={onCopyMock}
        onDelete={onDeleteMock}
      />,
    );

    // Ensure buttons are rendered
    const copyButton = screen.getByTestId('nutrient-tile-copy-btn');
    const deleteButton = screen.getByTestId('nutrient-tile-delete-btn');
    expect(copyButton).toBeInTheDocument();
    expect(deleteButton).toBeInTheDocument();

    fireEvent.click(copyButton);
    expect(onCopyMock).toHaveBeenCalledWith(nutrientMock.id);
    expect(onDeleteMock).not.toHaveBeenCalled();

    fireEvent.click(deleteButton);
    expect(onDeleteMock).toHaveBeenCalledWith(nutrientMock.id);
  });

  test('renders null if nutrient is undefined', () => {
    const { container } = render(<NutrientTile onSelect={onSelectMock} />);
    expect(container.firstChild).toBeNull();
  });
});
