import React, { FC } from 'react';
import {
  ActionButtonStyle,
  IconButtonStyle,
  NutrientElementalName,
  NutrientName,
  TileWrapper,
} from './NutrientTile.styled';
import { Nutrient } from '@common/types';
import { getDisplayElementName } from '@widgets/Polaris/src/screens/CropFeatures/CNP/SoilAnalysis/utils/cropDemandAnalysisHelpers';

interface NutrientTileProps {
  nutrient?: Nutrient;
  onSelect: (nutrient: Nutrient) => void;
  selected?: boolean;
  disabled?: boolean;
  onDelete?: (nutrientId: string) => void;
  onCopy?: (nutrientId: string) => void;
  variant?: 'popup' | 'list';
  dataCy?: string;
}

const NutrientTile: FC<NutrientTileProps> = ({
  nutrient,
  onSelect,
  selected = false,
  disabled = false,
  onDelete,
  onCopy,
  variant = 'list',
  dataCy = 'nutrient-tile',
}) => {
  if (!nutrient) {
    return null;
  }

  const { elementalName, name, id } = nutrient;
  const displayElementalName = getDisplayElementName(elementalName);
  const handleClick = () => {
    if (nutrient) {
      onSelect(nutrient);
    }
  };
  const displayNutrientTileOnList = variant === 'list' ? { title: name } : {};

  return (
    <TileWrapper
      onClick={handleClick}
      selected={selected}
      disabled={disabled}
      variant={variant}
      data-cy={dataCy}
    >
      <NutrientElementalName data-cy='nutrient-tile-elementalName'>
        {displayElementalName}
      </NutrientElementalName>
      <NutrientName {...displayNutrientTileOnList} data-cy='nutrient-tile-name'>
        {name}
      </NutrientName>
      <ActionButtonStyle>
        <>
          {onCopy && (
            <IconButtonStyle
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                e.stopPropagation();
                onCopy(id);
              }}
              colorConcept='brand'
              icon='Copy'
              data-cy='nutrient-tile-copy-btn'
            />
          )}
          {onDelete && (
            <IconButtonStyle
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                e.stopPropagation();
                onDelete(id);
              }}
              colorConcept='brand'
              icon='Delete'
              data-cy='nutrient-tile-delete-btn'
            />
          )}
        </>
      </ActionButtonStyle>
    </TileWrapper>
  );
};

export default NutrientTile;
