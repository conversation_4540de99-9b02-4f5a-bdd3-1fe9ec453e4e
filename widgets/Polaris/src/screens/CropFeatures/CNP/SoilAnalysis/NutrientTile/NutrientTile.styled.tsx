import { IconButton, styled } from '@yaradigitallabs/ahua-react';

export const ActionButtonStyle = styled('div', {
  width: '100%',
  justifyContent: 'space-around',
});

export const IconButtonStyle = styled(IconButton, {
  width: 16,
  height: 16,
  padding: '0 !important',

  '& svg': {
    width: '100%',
    height: '100%',
  },
});

export const TileWrapper = styled('div', {
  cursor: 'pointer',
  display: 'flex',
  flexDirection: 'column',
  padding: '4px 8px',
  gap: '8px',
  width: '90px',
  height: '92px',
  boxShadow: `
    0px 1px 10px 0px rgba(15, 15, 15, 0.12),
    0px 4px 5px 0px rgba(15, 15, 15, 0.14),
    0px 2px 4px 0px rgba(15, 15, 15, 0.16)
  `,
  borderRadius: '8px',
  alignItems: 'center',
  justifyContent: 'center',
  boxSizing: 'border-box',
  background: '$white100',

  variants: {
    variant: {
      list: {
        '& > span:nth-child(2)': {
          lineHeight: '15px',
          maxWidth: '100%',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          color: '$black70',
        },
        '&:hover': {
          background: '$blue0',
          border: '1px solid $blue30',

          [`& ${ActionButtonStyle}`]: {
            display: 'flex',
          },
        },
      },
      popup: {
        '& > span:nth-child(2)': {
          fontSize: '10px',
          lineHeight: '12.5px',
          color: '$black70',
        },
      },
    },
    selected: {
      true: {
        background: '$blue0',
        border: '1px solid $blue30',
      },
    },
    disabled: {
      true: {
        background: '$black5',
        border: '1px solid $black30',
        pointerEvents: 'none',
        color: '$black60',
      },
    },
  },
  [`& ${ActionButtonStyle}`]: {
    display: 'none',
  },
});

export const NutrientElementalName = styled('span', {
  fontWeight: 700,
});

export const NutrientName = styled('span', {
  fontSize: '12px',
  textAlign: 'center',
});
