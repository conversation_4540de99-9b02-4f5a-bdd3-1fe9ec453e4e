import {
  addNutrientParameterUtility,
  enrichCropDemandAnalysisData,
  getAlreadyAddedNutrients,
  getAnalysisBaseUnit,
  getDefaultAnalysisAndBaseValueExpressionData,
  getDefaultCropDemandAnalysis,
  getDefaultCropDemandAnalysisNutrients,
  getDisplayElementName,
  getEngineNutrientName,
  getInitialValues,
  getUnitOptionsByTag,
  getValidityExpressionTree,
  handleAddNutrientUtility,
  handleNutrientSelect,
} from '../cropDemandAnalysisHelpers';
import {
  cropDemandAnalysisMock,
  enrichedCropDemandMock,
  filteredByCurrentCountryMock,
  nutrientMock,
  nutrientsMock,
  simplifiedNewCropDemandAnalysisMock,
  simplifiedNutrientsMock,
  unitsMock,
} from '@common/mocks';
import { ComparisonExpression, ValidityCategory } from '../../SoilAnalysis.type';
import { ExpressionNode } from '@common/types';
import { METHOD, ParameterLevelEnum } from '@common/constants';
import { AddParamStateProps } from '../../AddNutritionParamPopup';

describe('cropDemandAnalysisHelpers', () => {
  describe('getDisplayElementName', () => {
    test('returns "SOB" for "SumOfBases"', () => {
      const input = 'SumOfBases';
      const expectedOutput = 'SOB';
      expect(getDisplayElementName(input)).toBe(expectedOutput);
    });

    test('returns the original input for elements not in displayNameMapping', () => {
      const inputs = ['Nitrogen', 'Potassium', 'Phosphorus'];
      inputs.forEach((input) => {
        expect(getDisplayElementName(input)).toBe(input);
      });
    });
  });

  describe('getValidityExpressionTree', () => {
    test('correctly replaces nutrientName "BsSat" with "BaseSat"', () => {
      const result = getValidityExpressionTree(ParameterLevelEnum.LOW, 'BsSat');
      const comparisonExpression = result.value[0] as ComparisonExpression;
      const actualValue = comparisonExpression.value[0].value;

      expect(actualValue).toBe('Analysis.BaseSat');
    });

    test('creates correct expression for non-VERY_LOW categories', () => {
      const category = 'MEDIUM';
      const result = getValidityExpressionTree(category as ValidityCategory, 'Potassium');
      const gteExpression = result.value.find((exp) => exp.type === 'gte') as ComparisonExpression;
      const ltExpression = result.value.find((exp) => exp.type === 'lt') as ComparisonExpression;

      expect(result.type).toBe('andExpression');
      expect(gteExpression).toBeDefined();
      expect(ltExpression).toBeDefined();
      expect(gteExpression?.value?.[1]?.value).toBe(2.0);
      expect(ltExpression?.value[1].value).toBe(3.0);
    });

    test('creates correct expression for VERY_LOW category', () => {
      const result = getValidityExpressionTree(ParameterLevelEnum.VERY_LOW, 'Nitrogen');
      expect(result.type).toBe('lt');
      expect(result.value[1].value).toBe(1.0);
    });

    test('creates correct expression for HIGH category', () => {
      const result = getValidityExpressionTree(ParameterLevelEnum.HIGH, 'Phosphorus');
      expect(result.type).toBe('andExpression');
      const expressions = result.value as ComparisonExpression[];
      const gteExpression = expressions.find((exp) => exp.type === 'gte');
      const ltExpression = expressions.find((exp) => exp.type === 'lt');

      expect(gteExpression).toBeDefined();
      expect(ltExpression).toBeDefined();

      if (gteExpression && ltExpression) {
        expect(gteExpression.value[1].value).toBe(3.0);
        expect(ltExpression.value[1].value).toBe(4.0);
      }
    });

    test('creates correct expression for VERY_HIGH category', () => {
      const result = getValidityExpressionTree(ParameterLevelEnum.VERY_HIGH, 'Potassium');
      expect(result.type).toBe('gte');
      expect(result.value[1].value).toBe(4.0);
    });
    test('handles nutrient names other than "BsSat" without replacement', () => {
      const nutrientName = 'Magnesium';
      const result = getValidityExpressionTree(ParameterLevelEnum.MEDIUM, nutrientName);
      const comparisonExpression = result.value[0] as ComparisonExpression;
      const actualValue = comparisonExpression.value[0].value;

      expect(actualValue).toBe(`Analysis.${nutrientName}`);
    });
  });

  describe('getAnalysisBaseUnit', () => {
    test('returns undefined for undefined or empty units array', () => {
      expect(getAnalysisBaseUnit(undefined, 'SUnit')).toBeUndefined();
      expect(getAnalysisBaseUnit([], 'SUnit')).toBeUndefined();
    });

    test('returns the id of a unit with a tag matching ${elementalName}Unit', () => {
      expect(getAnalysisBaseUnit(unitsMock, 'SUnit')).toBe('2cb13868-0c99-4f69-9afd-a4fb1cfc39c0');
      expect(getAnalysisBaseUnit(unitsMock, 'KUnit')).toBe('84e54963-e641-4ed0-9957-76778d19b979');
    });

    test('returns the id of a unit with a tag exactly matching elementalName when no ${elementalName}Unit tag is found', () => {
      expect(getAnalysisBaseUnit(unitsMock, 'SUnit')).toBe('2cb13868-0c99-4f69-9afd-a4fb1cfc39c0');
    });

    test('returns undefined if no matching unit is found', () => {
      expect(getAnalysisBaseUnit(unitsMock, 'Oxygen')).toBeUndefined();
    });
  });

  describe('getUnitOptionsByTag', () => {
    test('filters units by tag for non-base units', () => {
      const options = getUnitOptionsByTag({
        units: unitsMock,
        tag: 'K',
        isBaseUnit: false,
      });
      expect(options).toEqual([
        {
          value: '84e54963-e641-4ed0-9957-76778d19b979',
          label: 'mg K2O na 100 g soil',
        },
        {
          label: 'mg/100g',
          value: 'e6bf94f2-3e56-4ebe-9966-6eeaef42f702',
        },
        {
          label: 'Kg/ac',
          value: 'd0cb546e-d8d3-4df2-a42f-aec93943d5d5',
        },
        {
          label: 'mg/Kg',
          value: '1df069b5-fa02-4c00-9779-446c68e69a3f',
        },
        {
          label: 'mg/l',
          value: '960c13db-4af2-4ffb-825e-5809ee062599',
        },
      ]);
    });

    test('excludes units with "Unit" suffix when searching for special tags as base units', () => {
      const options = getUnitOptionsByTag({
        units: unitsMock,
        tag: 'S',
        isBaseUnit: true,
      });
      expect(options).not.toContainEqual(
        expect.objectContaining({ label: expect.stringContaining('Unit') }),
      );
    });

    test('returns an empty array when no units match', () => {
      const options = getUnitOptionsByTag({
        units: unitsMock,
        tag: 'Oxygen',
        isBaseUnit: true,
      });
      expect(options).toEqual([]);
    });
  });

  describe('getEngineNutrientName', () => {
    it('transforms BsSat to BaseSat', () => {
      expect(getEngineNutrientName('BsSat', false)).toBe('BaseSat');
      expect(getEngineNutrientName('BsSat', true)).toBe('BaseSat');
    });

    it('appends _0020 to NO3 and NH4 for soil analysis', () => {
      expect(getEngineNutrientName('NO3', true)).toBe('NO3_0020');
      expect(getEngineNutrientName('NH4', true)).toBe('NH4_0020');
    });

    it('returns nutrient name as is for other cases and for NO3 and NH4 when not for soil analysis', () => {
      expect(getEngineNutrientName('NO3', false)).toBe('NO3');
      expect(getEngineNutrientName('NH4', false)).toBe('NH4');
      expect(getEngineNutrientName('K', true)).toBe('K');
      expect(getEngineNutrientName('P', false)).toBe('P');
    });
  });

  describe('getDefaultAnalysisAndBaseValueExpressionData', () => {
    it('returns correct structure and values for nutrient transformations', () => {
      const testData = [
        {
          nutrient: 'BsSat',
          isForSoilAnalysis: false,
          expectedSuffix: 'BaseSat',
        },
        {
          nutrient: 'NO3',
          isForSoilAnalysis: true,
          expectedSuffix: 'NO3_0020',
        },
        { nutrient: 'NH4', isForSoilAnalysis: false, expectedSuffix: 'NH4' },
      ];

      testData.forEach(({ nutrient, isForSoilAnalysis, expectedSuffix }) => {
        const result = getDefaultAnalysisAndBaseValueExpressionData(nutrient, isForSoilAnalysis);
        const expectedValue = `Analysis.Input_${expectedSuffix}`;
        const expectedBaseValue = `Analysis.${expectedSuffix}`;
        expect(result).toEqual({
          analysisValueExpression: expectedValue,
          analysisValueExpressionTree: {
            id: 1,
            type: 'number',
            value: expectedValue,
          },
          baseValueExpression: expectedBaseValue,
          baseValueExpressionTree: {
            id: 1,
            type: 'number',
            value: expectedBaseValue,
          },
        });
      });
    });
  });

  describe('getDefaultCropDemandAnalysis', () => {
    const mockParameters = {
      cropRegionId: 'cropRegionId123',
      countryId: 'countryId123',
      nutrientId: 'nutrientId123',
      nutrientName: 'N',
      analysisBaseUnitId: 'analysisBaseUnitId123',
      demandBaseUnitId: 'demandBaseUnitId123',
      analysisMethodId: undefined,
    };

    it('returns correct structure and values with all parameters provided', () => {
      const result = getDefaultCropDemandAnalysis(mockParameters);

      expect(result).toMatchObject({
        countryId: mockParameters.countryId,
        cropRegionId: mockParameters.cropRegionId,
        nutrientId: mockParameters.nutrientId,
        analysisMethodId: '49b5c641-c53e-4c00-bc8b-e5ff0f209a9e',
        analysisBaseUnitId: mockParameters.analysisBaseUnitId,
        demandBaseUnitId: mockParameters.demandBaseUnitId,
      });
    });

    it('uses default analysisMethodId when not provided', () => {
      const customParams = { ...mockParameters, analysisMethodId: undefined };
      const result = getDefaultCropDemandAnalysis(customParams);
      expect(result.analysisMethodId).toBe('49b5c641-c53e-4c00-bc8b-e5ff0f209a9e');
    });

    it('integrates with getDefaultAnalysisAndBaseValueExpressionData correctly', () => {
      const result = getDefaultCropDemandAnalysis(mockParameters);
      expect(result.analysisValueExpression).toContain(mockParameters.nutrientName);
      expect(result.baseValueExpressionTree).toMatchObject({ type: 'number' });
    });
  });

  describe('getDefaultCropDemandAnalysisNutrients', () => {
    const mockCropDemandAnalysis = cropDemandAnalysisMock[0];

    it('correctly handles nutrientName transformation and default clayClassification', () => {
      const { defaultCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: mockCropDemandAnalysis,
        clayClassification: 'DEFAULT',
        nutrientName: 'BsSat',
      });

      expect(defaultCropDemandAnalysisNutrients[0].validityExpression).toContain('BaseSat');
      expect(defaultCropDemandAnalysisNutrients[0].clayClassification).toBe('DEFAULT');
    });

    it('generates validity expressions and trees for all categories', () => {
      const nutrientName = 'K';
      const { defaultCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: mockCropDemandAnalysis,
        clayClassification: 'DEFAULT',
        nutrientName,
      });

      expect(
        defaultCropDemandAnalysisNutrients.find(
          (categoryObj) => categoryObj.nutrientClassification === ParameterLevelEnum.VERY_LOW,
        )?.validityExpression,
      ).toBe(`Analysis.K < 1.0`);
    });

    it('correctly assigns properties from cropDemandAnalysis', () => {
      const { defaultCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: mockCropDemandAnalysis,
        clayClassification: 'DEFAULT',
        nutrientName: 'N',
      });

      expect(
        defaultCropDemandAnalysisNutrients.every(
          (categoryObj) => categoryObj.cropRegionId === mockCropDemandAnalysis.cropRegionId,
        ),
      ).toBe(true);
      expect(
        defaultCropDemandAnalysisNutrients.every(
          (categoryObj) => categoryObj.countryId === mockCropDemandAnalysis.countryId,
        ),
      ).toBe(true);
      expect(
        defaultCropDemandAnalysisNutrients.every(
          (categoryObj) => categoryObj.cropDemandAnalysisId === mockCropDemandAnalysis.id,
        ),
      ).toBe(true);
    });

    it('marks the MEDIUM category as default', () => {
      const { defaultCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: undefined,
        clayClassification: 'DEFAULT',
        nutrientName: 'N',
      });

      expect(
        defaultCropDemandAnalysisNutrients?.find(
          (categoryObj) => categoryObj.nutrientClassification === 'MEDIUM',
        )?.isDefault,
      ).toBe(true);
      expect(
        defaultCropDemandAnalysisNutrients
          ?.filter((categoryObj) => categoryObj.nutrientClassification !== 'MEDIUM')
          .every((categoryObj) => !categoryObj.isDefault),
      ).toBe(true);
    });

    it('generates secondary parameters when isSecondaryRequired is true', () => {
      const { secondaryCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: mockCropDemandAnalysis,
        clayClassification: 'DEFAULT',
        nutrientName: 'N',
        isSecondaryRequired: true,
      });

      expect(secondaryCropDemandAnalysisNutrients.length).toBeGreaterThan(0);
      expect(secondaryCropDemandAnalysisNutrients[0].secondaryParamOrder).toBeDefined();
    });

    it('handles undefined cropDemandAnalysis gracefully', () => {
      const { defaultCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: undefined,
        clayClassification: 'DEFAULT',
        nutrientName: 'N',
      });

      expect(
        defaultCropDemandAnalysisNutrients.every(
          (categoryObj) => categoryObj.cropRegionId === undefined,
        ),
      ).toBe(true);
      expect(
        defaultCropDemandAnalysisNutrients.every(
          (categoryObj) => categoryObj.countryId === undefined,
        ),
      ).toBe(true);
      expect(
        defaultCropDemandAnalysisNutrients.every(
          (categoryObj) => categoryObj.cropDemandAnalysisId === undefined,
        ),
      ).toBe(true);
    });
  });

  describe('enrichCropDemandAnalysisData', () => {
    it('should return enriched data when cropDemandAnalysisData, nutrientsData, and newCropDemandAnalysis are provided', () => {
      const enrichedData = enrichCropDemandAnalysisData(cropDemandAnalysisMock, nutrientsMock, []);
      enrichedData.forEach((item) => {
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('nutrient');
      });
    });

    it('correctly enriches crop demand analysis data with nutrient details', () => {
      const result = enrichCropDemandAnalysisData(
        [],
        simplifiedNutrientsMock,
        simplifiedNewCropDemandAnalysisMock,
      );

      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('handleAddNutrientUtility', () => {
    const mockTriggerCreateCropDemandAnalysis = jest.fn();
    const mockTriggerBulkCreateCropDemandAnalysis = jest.fn();
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    const mockConsoleError = jest
      .spyOn(console, 'error')
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      .mockImplementation(() => {});

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('calls triggerCreateCropDemandAnalysis and triggerBulkCreateCropDemandAnalysis with correct parameters', async () => {
      const mockSetNewCropDemandAnalysisData = jest.fn();
      await handleAddNutrientUtility({
        selectedNutrients: nutrientsMock,
        triggerCreateCropDemandAnalysis: mockTriggerCreateCropDemandAnalysis,
        triggerBulkCreateCropDemandAnalysisNutrients: mockTriggerBulkCreateCropDemandAnalysis,
        setNewCropDemandAnalysisData: mockSetNewCropDemandAnalysisData,
        selectedCountryUnits: filteredByCurrentCountryMock,
        cropRegionId: 'cropRegion1',
        currentCountryId: 'country1',
        addCropDemandAnalyses: jest.fn(),
      });

      expect(mockTriggerCreateCropDemandAnalysis).toHaveBeenCalledWith(
        expect.objectContaining({
          method: METHOD.POST,
          body: expect.stringContaining(`"countryId":"country1"`),
        }),
      );

      expect(mockTriggerBulkCreateCropDemandAnalysis).toHaveBeenCalledWith(
        expect.objectContaining({
          method: METHOD.POST,
          body: expect.any(String),
        }),
      );

      const secondCallBody = mockTriggerBulkCreateCropDemandAnalysis.mock.calls[0][0].body;
      const data = JSON.parse(secondCallBody);
      expect(data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            clayClassification: expect.any(String),
            nutrientClassification: expect.any(String),
          }),
        ]),
      );
    });

    it('handles errors thrown by triggerCreateCropDemandAnalysis correctly', async () => {
      // Set up the mock to throw an error
      const error = new Error('Test error');
      mockTriggerCreateCropDemandAnalysis.mockRejectedValue(error);

      await handleAddNutrientUtility({
        selectedNutrients: nutrientsMock,
        triggerCreateCropDemandAnalysis: mockTriggerCreateCropDemandAnalysis,
        triggerBulkCreateCropDemandAnalysisNutrients: mockTriggerBulkCreateCropDemandAnalysis,
        setNewCropDemandAnalysisData: jest.fn(),
        selectedCountryUnits: filteredByCurrentCountryMock,
        cropRegionId: 'cropRegion1',
        currentCountryId: 'country1',
        addCropDemandAnalyses: jest.fn(),
      });

      // Verify console.error was called with the expected error message
      expect(mockConsoleError).toHaveBeenCalledWith(
        expect.stringContaining('Error creating analysis for nutrient'),
        error,
      );
    });

    afterAll(() => {
      mockConsoleError.mockRestore();
    });
  });

  describe('handleNutrientSelect', () => {
    it('calls setSelectedNutrient with the given nutrient', () => {
      const mockSetSelectedNutrient = jest.fn();
      handleNutrientSelect(mockSetSelectedNutrient, nutrientMock);
      expect(mockSetSelectedNutrient).toHaveBeenCalledWith(nutrientMock);
    });
  });

  describe('getAlreadyAddedNutrients', () => {
    it('returns an array of nutrientIds from enrichedData', () => {
      const result = getAlreadyAddedNutrients(enrichedCropDemandMock);

      expect(result).toEqual(['0163eeff-5d87-4749-bcf7-3e4be6732808']);
    });

    it('returns an empty array if enrichedData is undefined or empty', () => {
      expect(getAlreadyAddedNutrients([])).toEqual([]);
    });
  });

  describe('addNutrientParameterUtility', () => {
    let mockSetAddParamState: jest.Mock;
    let mockSetDisplaySnackbar: jest.Mock;
    let mockSetIsAddParamOpened: jest.Mock;
    let mockTriggerUpdateCropDemandAnalysis: jest.Mock;
    let mockTriggerBulkCreateCropDemandAnalysis: jest.Mock;
    const mockSnackbarMsg = 'Operation successful';

    beforeEach(() => {
      mockSetAddParamState = jest.fn();
      mockSetDisplaySnackbar = jest.fn();
      mockSetIsAddParamOpened = jest.fn();
      mockTriggerUpdateCropDemandAnalysis = jest.fn().mockResolvedValue(undefined);
      mockTriggerBulkCreateCropDemandAnalysis = jest.fn().mockResolvedValue(undefined);
    });

    it('sets error to true if selectedCropDemandAnalyses is not defined', async () => {
      const addParamState: AddParamStateProps = {
        selectedCropDemandAnalyses: null,
        defaultCropDemandAnalysesNutrient: undefined,
        isDefaultChecked: false,
        parameters: [],
        error: false,
      };

      await addNutrientParameterUtility(
        addParamState,
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerUpdateCropDemandAnalysis,
        mockTriggerBulkCreateCropDemandAnalysis,
        'validityExpression',
        mockSnackbarMsg,
      );

      expect(mockSetAddParamState).toHaveBeenCalledWith(expect.any(Function));
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(addParamState);
      expect(newState.error).toBe(true);
    });

    it('calls only triggerBulkCreateCropDemandAnalysis if isDefaultChecked is false', async () => {
      const addParamState: AddParamStateProps = {
        selectedCropDemandAnalyses: {
          id: '1',
          validityExpressionTree: {},
        } as never,
        defaultCropDemandAnalysesNutrient: {
          id: '2',
          validityExpressionTree: {},
        } as never,
        isDefaultChecked: false,
        parameters: [],
        error: false,
      };

      await addNutrientParameterUtility(
        addParamState,
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerUpdateCropDemandAnalysis,
        mockTriggerBulkCreateCropDemandAnalysis,
        'validityExpression',
        mockSnackbarMsg,
      );

      expect(mockTriggerUpdateCropDemandAnalysis).not.toHaveBeenCalled();
      expect(mockTriggerBulkCreateCropDemandAnalysis).toHaveBeenCalledWith(expect.any(Object));
    });

    it('handles errors in the try-catch block', async () => {
      mockTriggerBulkCreateCropDemandAnalysis.mockRejectedValueOnce(new Error('Test Error'));

      const addParamState: AddParamStateProps = {
        selectedCropDemandAnalyses: {
          id: '1',
          validityExpressionTree: {},
        } as never,
        defaultCropDemandAnalysesNutrient: {
          id: '2',
          validityExpressionTree: {},
        } as never,
        isDefaultChecked: false,
        parameters: [],
        error: false,
      };

      console.error = jest.fn();

      await addNutrientParameterUtility(
        addParamState,
        mockSetAddParamState,
        mockSetDisplaySnackbar,
        mockSetIsAddParamOpened,
        mockTriggerUpdateCropDemandAnalysis,
        mockTriggerBulkCreateCropDemandAnalysis,
        'validityExpression',
        mockSnackbarMsg,
      );

      expect(mockTriggerBulkCreateCropDemandAnalysis).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith(
        'Error creating and updating crop demand analysis nutrient data:',
        expect.any(Error),
      );
    });
  });

  describe('getInitialValues', () => {
    it('transforms elementalName "BsSat" to "BaseSat" in validityExpression and validityExpressionTree', () => {
      const initialValues = getInitialValues('BsSat');

      expect(initialValues.validityExpression).toBe(
        'Analysis.BaseSat > 0.0 AND Analysis.BaseSat < 0.0',
      );

      const gtExpression = initialValues?.validityExpressionTree?.value[0] as ExpressionNode;
      const ltExpression = initialValues?.validityExpressionTree?.value[1] as ExpressionNode;

      expect(gtExpression.value[0].value).toBe('Analysis.BaseSat');
      expect(ltExpression.value[0].value).toBe('Analysis.BaseSat');
    });

    it('handles normal elementalName correctly', () => {
      const initialValues = getInitialValues('Nitrogen');
      expect(initialValues.validityExpression).toBe(
        'Analysis.Nitrogen > 0.0 AND Analysis.Nitrogen < 0.0',
      );

      const gtExpression = initialValues?.validityExpressionTree?.value[0] as ExpressionNode;
      const ltExpression = initialValues?.validityExpressionTree?.value[1] as ExpressionNode;
      expect(gtExpression.value[0].value).toBe('Analysis.Nitrogen');
      expect(ltExpression.value[0].value).toBe('Analysis.Nitrogen');
    });
  });
});
