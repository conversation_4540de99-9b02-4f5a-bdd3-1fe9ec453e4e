import { CropDemandAnalysis, CropDemandAnalysisNutrient, Nutrient } from '@common/types';
import { ORDER } from '@common/constants';
import { DefaultClassificationEnum } from '../../../shared/constants';

// To return secondary parameter expression based on `Consider secondary parameters` selection
export const getActivatedExpression = (
  isPlanActive: boolean | null,
  selectedNutrient: Nutrient | null,
): string => {
  const expression = `Analysis.${selectedNutrient?.elementalName} == -1 ? "MEDIUM"
    : (Analysis.${selectedNutrient?.elementalName} < 1 ? "VERY_LOW"
    : (Analysis.${selectedNutrient?.elementalName} < 2 ? "LOW"
    : (Analysis.${selectedNutrient?.elementalName} < 3 ? "MEDIUM"
    : (Analysis.${selectedNutrient?.elementalName} < 4 ? "HIGH" : "VERY_HIGH"))))`;

  return isPlanActive ? expression : DefaultClassificationEnum.DEFAULT;
};
export const isDefaultActive = (
  cDemandAnalysisData: CropDemandAnalysis | undefined,
  isDefault?: boolean,
): boolean => {
  if (isDefault) {
    return cDemandAnalysisData?.clayClassificationExpression === DefaultClassificationEnum.DEFAULT;
  }
  return cDemandAnalysisData?.clayClassificationExpression !== DefaultClassificationEnum.DEFAULT;
};

export const getSecondaryParamData = (
  orderedCropDemandAnalysesData: CropDemandAnalysisNutrient[],
) => {
  if (orderedCropDemandAnalysesData?.length > 5) {
    const secondaryVeryLowLevels = orderedCropDemandAnalysesData?.filter(
      (level) => level?.secondaryParamOrder?.orderNo === ORDER.VERY_LOW,
    );
    const secondaryLowLevels = orderedCropDemandAnalysesData?.filter(
      (level) => level?.secondaryParamOrder?.orderNo === ORDER.LOW,
    );
    const secondaryMediumLevels = orderedCropDemandAnalysesData?.filter(
      (level) => level?.secondaryParamOrder?.orderNo === ORDER.MEDIUM,
    );
    const secondaryHighLevels = orderedCropDemandAnalysesData?.filter(
      (level) => level?.secondaryParamOrder?.orderNo === ORDER.HIGH,
    );
    const secondaryVeryHighLevels = orderedCropDemandAnalysesData?.filter(
      (level) => level?.secondaryParamOrder?.orderNo === ORDER.VERY_HIGH,
    );
    return {
      secondaryVeryLowLevels,
      secondaryLowLevels,
      secondaryMediumLevels,
      secondaryHighLevels,
      secondaryVeryHighLevels,
    };
  }
  return {};
};
