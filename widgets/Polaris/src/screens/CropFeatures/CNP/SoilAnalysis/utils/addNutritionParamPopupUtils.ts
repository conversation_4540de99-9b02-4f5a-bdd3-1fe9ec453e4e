import { CropDemandAnalysisNutrient, CropDemandAnalysisDefaultParameters } from '@common/types';
import { AddParamStateProps, Parameter } from '../AddNutritionParamPopup';
import { Dispatch, SetStateAction } from 'react';

export const setRemainingNutrientParams = (
  setAddParamState: Dispatch<SetStateAction<AddParamStateProps>>,
  cropDemandAnalysesNutrientsData: CropDemandAnalysisNutrient[] | undefined,
  defaultCropDemandAnalysisNutrients: CropDemandAnalysisDefaultParameters[] = [],
): void => {
  const defaultCDemandANutrients = defaultCropDemandAnalysisNutrients?.filter(
    (dn) =>
      !cropDemandAnalysesNutrientsData?.some(
        (cn) => dn.nutrientClassification === cn.nutrientClassification,
      ),
  );

  const nutrientAnalysis = cropDemandAnalysesNutrientsData?.find((n) => n.isDefault);

  setAddParamState((addParamState) => ({
    ...addParamState,
    parameters: defaultCDemandANutrients,
    defaultCropDemandAnalysesNutrient: nutrientAnalysis,
  }));
};

export const getParametersName = (
  parameters: CropDemandAnalysisDefaultParameters[],
): Parameter[] => {
  return (
    parameters?.map((parameter) => ({
      text: parameter.nutrientClassification || '',
      value: parameter.nutrientClassification || '',
    })) || []
  );
};

export const setCropDemandAnalysis = (
  value: string,
  setAddParamState: Dispatch<SetStateAction<AddParamStateProps>>,
): void => {
  setAddParamState((addParamState) => {
    const existingCropDemandAnalyses = addParamState.selectedCropDemandAnalyses;
    const selectedCropDemandAnalyses: CropDemandAnalysisDefaultParameters =
      addParamState.parameters?.find((item) => item.nutrientClassification === value) || {
        ...existingCropDemandAnalyses,
        isDefault: existingCropDemandAnalyses?.isDefault || false,
        clayClassification: existingCropDemandAnalyses?.clayClassification || '',
        nutrientClassification: value,
        validityExpression: existingCropDemandAnalyses?.validityExpression || '',
        validityExpressionTree: existingCropDemandAnalyses?.validityExpressionTree,
        error: Boolean(value),
      };

    return {
      ...addParamState,
      error: false,
      selectedCropDemandAnalyses: selectedCropDemandAnalyses,
    };
  });
};
