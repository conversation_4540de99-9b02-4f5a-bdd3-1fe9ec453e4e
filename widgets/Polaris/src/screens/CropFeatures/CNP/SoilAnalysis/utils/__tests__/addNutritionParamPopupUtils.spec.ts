import {
  CropDemandAnalysisNutrient,
  CropDemandAnalysisDefaultParameters,
  ExpressionNode,
  ExpressionTree,
  SimpleExpressionTree,
} from '@common/types';
import { AddParamStateProps, Parameter } from '../../AddNutritionParamPopup';
import {
  getParametersName,
  setCropDemandAnalysis,
  setRemainingNutrientParams,
} from '../addNutritionParamPopupUtils';
import { Dispatch, SetStateAction } from 'react';
import { act } from '@testing-library/react';

const parameters: CropDemandAnalysisDefaultParameters[] = [
  {
    cropRegionId: '1',
    countryId: 'US',
    cropDemandAnalysisId: '101',
    clayClassification: 'High',
    nutrientClassification: 'Protein',
    isDefault: true,
    validityExpression: 'expression1',
    validityExpressionTree: {} as ExpressionNode,
    soilDemandExpression: 'expression2',
    soilDemandExpressionTree: {} as SimpleExpressionTree,
    cropDemandExpression: 'expression3',
    cropDemandExpressionTree: {} as SimpleExpressionTree,
  },
  {
    cropRegionId: '2',
    countryId: 'IN',
    cropDemandAnalysisId: '102',
    clayClassification: 'Medium',
    nutrientClassification: 'Carbohydrate',
    isDefault: false,
    validityExpression: 'expression4',
    validityExpressionTree: {} as ExpressionNode,
    soilDemandExpression: 'expression5',
    soilDemandExpressionTree: {} as SimpleExpressionTree,
    cropDemandExpression: 'expression6',
    cropDemandExpressionTree: {} as SimpleExpressionTree,
  },
];
const mockSetAddParamState: Dispatch<SetStateAction<AddParamStateProps>> = jest.fn();
const cropDemandAnalysesNutrientsData: CropDemandAnalysisNutrient[] = [
  {
    id: '201',
    cropDemandAnalysisId: '301',
    countryId: 'US',
    cropRegionId: '1',
    insights: null,
    clayClassification: 'Low',
    nutrientClassification: 'Fat',
    validityExpression: 'expression7',
    validityExpressionTree: {} as ExpressionNode,
    soilDemandExpression: 'expression8',
    soilDemandExpressionTree: {} as SimpleExpressionTree,
    cropDemandExpression: 'expression9',
    cropDemandExpressionTree: {} as SimpleExpressionTree,
    ExpressionTree: {} as ExpressionTree,
    isDefault: true,
    created: '2023-01-01',
    modified: '2023-01-02',
    modifiedBy: 'user1',
    deleted: null,
  },
  {
    id: '202',
    cropDemandAnalysisId: '302',
    countryId: 'IN',
    cropRegionId: '2',
    insights: null,
    clayClassification: 'Medium',
    nutrientClassification: 'Carbohydrate',
    validityExpression: 'expression10',
    validityExpressionTree: {} as ExpressionNode,
    soilDemandExpression: 'expression11',
    soilDemandExpressionTree: {} as SimpleExpressionTree,
    cropDemandExpression: 'expression12',
    cropDemandExpressionTree: {} as SimpleExpressionTree,
    ExpressionTree: {} as ExpressionTree,
    isDefault: false,
    created: '2023-02-01',
    modified: '2023-02-02',
    modifiedBy: 'user2',
    deleted: null,
  },
];

describe('addNutritionParamPopupUtils', () => {
  describe('setRemainingNutrientParams', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should filter out nutrients that are already in cropDemandAnalysesNutrientsData and set defaultCropDemandAnalysesNutrient', () => {
      const initialState: AddParamStateProps = {
        parameters: [],
        selectedCropDemandAnalyses: null,
        defaultCropDemandAnalysesNutrient: undefined,
        isDefaultChecked: false,
        error: false,
      };
      setRemainingNutrientParams(mockSetAddParamState, cropDemandAnalysesNutrientsData, parameters);
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(initialState);

      const expectedParameters: CropDemandAnalysisDefaultParameters[] = [
        {
          cropRegionId: '1',
          countryId: 'US',
          cropDemandAnalysisId: '101',
          clayClassification: 'High',
          nutrientClassification: 'Protein',
          isDefault: true,
          validityExpression: 'expression1',
          validityExpressionTree: {} as ExpressionNode,
          soilDemandExpression: 'expression2',
          soilDemandExpressionTree: {} as SimpleExpressionTree,
          cropDemandExpression: 'expression3',
          cropDemandExpressionTree: {} as SimpleExpressionTree,
        },
      ];

      expect(newState.parameters).toEqual(expectedParameters);
      expect(newState.defaultCropDemandAnalysesNutrient).toEqual(
        cropDemandAnalysesNutrientsData[0],
      );
    });

    it('should set parameters to defaultCropDemandAnalysisNutrients if cropDemandAnalysesNutrientsData is undefined', () => {
      const initialState: AddParamStateProps = {
        parameters: [],
        selectedCropDemandAnalyses: null,
        defaultCropDemandAnalysesNutrient: undefined,
        isDefaultChecked: false,
        error: false,
      };
      setRemainingNutrientParams(mockSetAddParamState, undefined, undefined);
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(initialState);

      expect(newState.defaultCropDemandAnalysesNutrient).toBeUndefined();
    });

    it('should set parameters to an empty array if defaultCropDemandAnalysisNutrients is undefined', () => {
      const initialState: AddParamStateProps = {
        parameters: [],
        selectedCropDemandAnalyses: null,
        defaultCropDemandAnalysesNutrient: undefined,
        isDefaultChecked: false,
        error: false,
      };

      setRemainingNutrientParams(mockSetAddParamState, cropDemandAnalysesNutrientsData, undefined);
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(initialState);

      expect(newState.defaultCropDemandAnalysesNutrient).toEqual(
        cropDemandAnalysesNutrientsData[0],
      );
    });
  });

  describe('getParametersName', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should return an array of parameters with text and value fields', () => {
      const expected: Parameter[] = [
        { text: 'Protein', value: 'Protein' },
        { text: 'Carbohydrate', value: 'Carbohydrate' },
      ];

      const result = getParametersName(parameters);

      expect(result).toEqual(expected);
    });

    it('should return an empty array if parameters is undefined', () => {
      // @ts-ignore
      const result = getParametersName(undefined);
      expect(result).toEqual([]);
    });

    it('should return an empty array if parameters is an empty array', () => {
      const result = getParametersName([]);
      expect(result).toEqual([]);
    });
  });

  describe('setCropDemandAnalysis', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should update the state with the selected crop demand analysis if found', () => {
      // @ts-ignore
      const addParamState: AddParamStateProps = {
        parameters,
        selectedCropDemandAnalyses: null,
        error: true,
      };

      const expectedSelectedCropDemandAnalyses: CropDemandAnalysisDefaultParameters = parameters[0];

      act(() => {
        setCropDemandAnalysis('Protein', mockSetAddParamState);
      });

      expect(mockSetAddParamState).toHaveBeenCalledWith(expect.any(Function));
      // @ts-ignore
      // eslint-disable-next-line @typescript-eslint/ban-types
      const stateUpdater = mockSetAddParamState.mock.calls[0][0] as Function;
      const newState = stateUpdater(addParamState);

      expect(newState.selectedCropDemandAnalyses).toEqual(expectedSelectedCropDemandAnalyses);
      expect(newState.error).toBe(false);
    });
  });
});
