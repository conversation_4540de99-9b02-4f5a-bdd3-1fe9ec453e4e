import {
  BaseUnit,
  CreateCropDemandAnalysisNutrient,
  CropDemandAnalysisNutrient,
  CropDemandAnalysisDefaultParameters,
  ExpressionNode,
  InitialCropDemandAnalysisValues,
  SecondaryParameters,
  SnackBarType,
  EnrichedCropDemandAnalysis,
  Nutrient,
  CropDemandAnalysis,
  GetUnitOptionsByTagArgs,
  GenericObject,
} from '@common/types';
import {
  DEFAULT_ANALYSIS_METHOD_ID,
  DEFAULT_EXPRESSION_VALUES,
  METHOD,
  ORDER,
  ParameterLevelEnum,
} from '@common/constants';
import { AddParamStateProps } from '../AddNutritionParamPopup';
import {
  DefaultCropDemandAnalysisNutrientsParams,
  HandleAddNutrientParams,
  NumberValue,
  ValidityCategory,
} from '../SoilAnalysis.type';
import { DefaultClassificationEnum } from '../../../shared/constants';

export const displayNameMapping: GenericObject<string> = {
  SumOfBases: 'SOB',
};

export const getDisplayElementName = (elementalName: string): string => {
  return displayNameMapping[elementalName] || elementalName;
};

/**
 * Get the correct validity expression tree depending on the category and nutrient
 * @param category Category of the nutrient analysis
 * @param nutrientName Nutrient name
 * @returns The validity expression tree for the given category and nutrient
 */

export function getValidityExpressionTree(
  category: ValidityCategory,
  nutrientName: string,
): ExpressionNode {
  const nutrient = nutrientName === 'BsSat' ? 'BaseSat' : nutrientName;

  // Base expression for all categories
  const baseExpression: NumberValue = {
    id: 2,
    type: 'number',
    value: `Analysis.${nutrient}`,
  };

  // Mappings to simplify the construction of different expressions
  const valueMappings: Record<ParameterLevelEnum, number> = {
    [ParameterLevelEnum.VERY_LOW]: 1.0,
    [ParameterLevelEnum.LOW]: 1.0,
    [ParameterLevelEnum.MEDIUM]: 2.0,
    [ParameterLevelEnum.HIGH]: 3.0,
    [ParameterLevelEnum.VERY_HIGH]: 4.0,
  };

  let result: ExpressionNode = {
    id: 1,
    type: 'lt',
    value: [baseExpression, { id: 3, type: 'number', value: valueMappings[category] }],
  };

  if (category === ParameterLevelEnum.VERY_HIGH) {
    result = {
      id: 1,
      type: 'gte',
      value: [baseExpression, { id: 3, type: 'number', value: 4.0 }],
    };
  } else if (category !== ParameterLevelEnum.VERY_LOW) {
    result = {
      id: 1,
      type: 'andExpression',
      value: [
        {
          id: 2,
          type: 'gte',
          value: [baseExpression, { id: 3, type: 'number', value: valueMappings[category] }],
        },
        {
          id: 4,
          type: 'lt',
          value: [baseExpression, { id: 5, type: 'number', value: valueMappings[category] + 1 }],
        },
      ],
    };
  }

  return result;
}

export function getAnalysisBaseUnit(units: BaseUnit[] | undefined, elementalName: string) {
  if (!units) {
    return undefined;
  }

  const unitWithNutrientUnitTag = units.find((u) =>
    u.tags.split(',').some((tag) => tag === `${elementalName}Unit`),
  );
  if (unitWithNutrientUnitTag) {
    return unitWithNutrientUnitTag.id;
  }

  const unitWithExactNutrientTag = units.find((u) =>
    u.tags.split(',').some((tag) => tag === elementalName),
  );
  return unitWithExactNutrientTag?.id;
}

/**
 * Get unit options for analysis value and base value
 * @param args - The arguments object
 * @param args.units - Array of Unit objects
 * @param args.tag - Specific tag based on the unit
 * @param args.isBaseUnit - Determines if the options belong to base unit
 * @returns An array of options for select input
 */
export const getUnitOptionsByTag = ({ units, tag, isBaseUnit }: GetUnitOptionsByTagArgs) => {
  return units
    ?.filter(({ tags }) => {
      const unitTags = tags.split(',');

      return isBaseUnit
        ? unitTags.includes(tag) &&
            (['pH', 'SMP'].includes(tag) || !unitTags.includes(`${tag}Unit`))
        : unitTags.includes(`${tag}Unit`);
    })
    .map((unit) => ({ value: unit.id, label: unit.name }));
};

/**
 * Get correct nutrient name for the engine.
 * Adjusts nutrient names based on the context (e.g., soil analysis).
 *
 * @param nutrient Nutrient name.
 * @param isForSoilAnalysis Whether the generation is for soil analysis.
 *                          For NO3 and NH4, their names should be `NO3_0020` and `NH4_0020` respectively in this context.
 * @returns The adjusted nutrient name suitable for the engine.
 */
export function getEngineNutrientName(nutrient: string, isForSoilAnalysis: boolean): string {
  switch (nutrient) {
    case 'BsSat':
      return 'BaseSat';
    case 'NO3':
      return isForSoilAnalysis ? 'NO3_0020' : nutrient;
    case 'NH4':
      return isForSoilAnalysis ? 'NH4_0020' : nutrient;
    default:
      return nutrient;
  }
}

export const getDefaultAnalysisAndBaseValueExpressionData = (
  nutrient: string,
  isForSoilAnalysis: boolean,
) => {
  const engineNutrientName = getEngineNutrientName(nutrient, isForSoilAnalysis);

  return {
    analysisValueExpression: `Analysis.Input_${engineNutrientName}`,
    analysisValueExpressionTree: {
      id: 1,
      type: 'number',
      value: `Analysis.Input_${engineNutrientName}`,
    },
    baseValueExpression: `Analysis.${engineNutrientName}`,
    baseValueExpressionTree: {
      id: 1,
      type: 'number',
      value: `Analysis.${engineNutrientName}`,
    },
  };
};

export const getDefaultCropDemandAnalysis = ({
  cropRegionId,
  countryId,
  nutrientId,
  nutrientName,
  analysisBaseUnitId,
  demandBaseUnitId,
  analysisMethodId = DEFAULT_ANALYSIS_METHOD_ID,
}: {
  cropRegionId: string | undefined;
  countryId: string | undefined;
  nutrientId: string;
  nutrientName: string;
  analysisBaseUnitId: string | undefined;
  demandBaseUnitId: string | undefined;
  analysisMethodId?: string;
}) => {
  const defaultExpressions = getDefaultAnalysisAndBaseValueExpressionData(nutrientName, true);
  const { defaultRangeExpression, defaultClassificationExpression, defaultNullExpression } =
    DEFAULT_EXPRESSION_VALUES;

  return {
    countryId,
    cropRegionId,
    nutrientId,
    analysisMethodId,
    analysisBaseUnitId,
    demandBaseUnitId,
    analysisValueExpression: defaultExpressions.analysisValueExpression,
    analysisValueExpressionTree: defaultExpressions.analysisValueExpressionTree,
    baseValueExpression: defaultExpressions.baseValueExpression,
    baseValueExpressionTree: defaultExpressions.baseValueExpressionTree,
    analysisNormalRangeExpression: '"0.0 - 0.0"',
    analysisNormalRangeExpressionTree: defaultRangeExpression,
    clayClassificationExpression: DefaultClassificationEnum.DEFAULT,
    clayClassificationExpressionTree: defaultClassificationExpression,
    removalCoefficientExpression: '0',
    removalCoefficientExpressionTree: defaultNullExpression,
    uptakeCoefficientExpression: '0',
    uptakeCoefficientExpressionTree: defaultNullExpression,
  };
};

/**
 * Create a default soil analysis nutrients
 * @param {Object} options
 * @param {string} options.cropDemandAnalysis Passed crop demand analysis id which determines to which entity belongs to
 * @param {string} options.clayClassification Crop region identifier which entity belongs to
 * @param {string} options.nutrientName Nutrient name
 */

export function getDefaultCropDemandAnalysisNutrients({
  cropDemandAnalysis,
  clayClassification = DefaultClassificationEnum.DEFAULT,
  nutrientName,
  isSecondaryRequired = false,
}: DefaultCropDemandAnalysisNutrientsParams): {
  defaultCropDemandAnalysisNutrients: CropDemandAnalysisDefaultParameters[];
  secondaryCropDemandAnalysisNutrients: SecondaryParameters[];
} {
  const nutrient = nutrientName === 'BsSat' ? 'BaseSat' : nutrientName;
  const categories: ValidityCategory[] = [
    ParameterLevelEnum.VERY_LOW,
    ParameterLevelEnum.LOW,
    ParameterLevelEnum.MEDIUM,
    ParameterLevelEnum.HIGH,
    ParameterLevelEnum.VERY_HIGH,
  ];

  // Define the structure of defaultValidityExpressions based on the actual values it may hold
  const defaultValidityExpressions: Record<ValidityCategory, string> = {
    VERY_LOW: `Analysis.${nutrient} < 1.0`,
    LOW: `Analysis.${nutrient} >= 1.0 AND Analysis.${nutrient} < 2.0`,
    MEDIUM: `Analysis.${nutrient} >= 2.0 AND Analysis.${nutrient} < 3.0`,
    HIGH: `Analysis.${nutrient} >= 3.0 AND Analysis.${nutrient} < 4.0`,
    VERY_HIGH: `Analysis.${nutrient} >= 4.0`,
  };

  const secondaryCropDemandAnalysisNutrients: SecondaryParameters[] = [];
  if (isSecondaryRequired) {
    Object.keys(ORDER).forEach(function (key) {
      const paramOrder = {
        orderNo: ORDER[key],
        orderName: key,
      };
      const categoriesData = categories.map((category) => ({
        cropRegionId: cropDemandAnalysis?.cropRegionId,
        countryId: cropDemandAnalysis?.countryId,
        cropDemandAnalysisId: cropDemandAnalysis?.id,
        clayClassification,
        nutrientClassification: category,
        isDefault: category === ParameterLevelEnum.MEDIUM,
        validityExpression: defaultValidityExpressions[category],
        validityExpressionTree: getValidityExpressionTree(category, nutrient),
        soilDemandExpression: '0',
        soilDemandExpressionTree: DEFAULT_EXPRESSION_VALUES.defaultNullExpression,
        cropDemandExpression: '0',
        cropDemandExpressionTree: DEFAULT_EXPRESSION_VALUES.defaultNullExpression,
        secondaryParamOrder: paramOrder,
      }));
      secondaryCropDemandAnalysisNutrients.push(...categoriesData);
    });
  }

  const defaultCropDemandAnalysisNutrients = categories.map((category) => ({
    cropRegionId: cropDemandAnalysis?.cropRegionId,
    countryId: cropDemandAnalysis?.countryId,
    cropDemandAnalysisId: cropDemandAnalysis?.id,
    clayClassification,
    nutrientClassification: category,
    isDefault: category === ParameterLevelEnum.MEDIUM,
    validityExpression: defaultValidityExpressions[category],
    validityExpressionTree: getValidityExpressionTree(category, nutrient),
    soilDemandExpression: '0',
    soilDemandExpressionTree: DEFAULT_EXPRESSION_VALUES.defaultNullExpression,
    cropDemandExpression: '0',
    cropDemandExpressionTree: DEFAULT_EXPRESSION_VALUES.defaultNullExpression,
  }));

  return {
    defaultCropDemandAnalysisNutrients,
    secondaryCropDemandAnalysisNutrients,
  };
}

export const getAllAnalyses = (
  cropDemandAnalysisData: CropDemandAnalysis[],
  newCropDemandAnalysis: CropDemandAnalysis | CropDemandAnalysis[],
): CropDemandAnalysis[] => {
  return [
    ...(cropDemandAnalysisData || []),
    ...(Array.isArray(newCropDemandAnalysis)
      ? newCropDemandAnalysis
      : [newCropDemandAnalysis]
    ).filter(Boolean),
  ];
};

export const enrichCropDemandAnalysisData = (
  cropDemandAnalysisData: CropDemandAnalysis[],
  nutrientsData: Nutrient[] | undefined,
  newCropDemandAnalysis: CropDemandAnalysis | CropDemandAnalysis[],
): EnrichedCropDemandAnalysis[] => {
  const enrichedData = new Map();

  const allAnalyses = getAllAnalyses(cropDemandAnalysisData, newCropDemandAnalysis);
  allAnalyses?.forEach((analysis) => {
    if (!enrichedData.has(analysis?.nutrientId)) {
      const nutrientDetails = nutrientsData?.find((n) => n.id === analysis?.nutrientId);
      if (nutrientDetails) {
        enrichedData.set(analysis?.nutrientId, {
          ...analysis,
          nutrient: nutrientDetails,
        });
      }
    }
  });

  return Array.from(enrichedData.values());
};

export const handleAddNutrientUtility = async ({
  selectedNutrients,
  triggerCreateCropDemandAnalysis,
  triggerBulkCreateCropDemandAnalysisNutrients,
  selectedCountryUnits,
  cropRegionId,
  currentCountryId,
  addCropDemandAnalyses,
}: HandleAddNutrientParams): Promise<void> => {
  for (const nutrient of selectedNutrients) {
    try {
      const demandBaseUnitIdOptions = getUnitOptionsByTag({
        units: selectedCountryUnits,
        tag: nutrient.elementalName,
        isBaseUnit: true,
      })?.[0]?.value;

      const analysisBaseUnitId = getAnalysisBaseUnit(selectedCountryUnits, nutrient.elementalName);

      const defaultCropDemandAnalysisData = getDefaultCropDemandAnalysis({
        cropRegionId,
        countryId: currentCountryId,
        nutrientId: nutrient.id,
        nutrientName: nutrient.elementalName,
        analysisBaseUnitId,
        demandBaseUnitId: demandBaseUnitIdOptions,
      });

      const analysisResponse = await triggerCreateCropDemandAnalysis({
        method: METHOD.POST,
        body: JSON.stringify(defaultCropDemandAnalysisData),
      });

      addCropDemandAnalyses(analysisResponse);

      const { defaultCropDemandAnalysisNutrients } = getDefaultCropDemandAnalysisNutrients({
        cropDemandAnalysis: analysisResponse,
        clayClassification: DefaultClassificationEnum.DEFAULT,
        nutrientName: nutrient.elementalName,
      });

      await triggerBulkCreateCropDemandAnalysisNutrients({
        method: METHOD.POST,
        body: JSON.stringify(defaultCropDemandAnalysisNutrients),
      });
    } catch (error) {
      console.error(`Error creating analysis for nutrient ${nutrient.id}:`, error);
    }
  }
};

export const handleNutrientSelect = (
  setSelectedNutrient: (data: Nutrient | null) => void,
  nutrient: Nutrient,
) => {
  setSelectedNutrient(nutrient);
};

export const getAlreadyAddedNutrients = (enrichedData: EnrichedCropDemandAnalysis[]) => {
  return enrichedData?.map((nutrientInfo) => nutrientInfo.nutrientId) || [];
};

// USAGE: To add another nutrient level row to the table in the soil analysis, click on the 'Add another level' button and fill in the required details.
// Adding another nutrient level row with setAsDefault checked will add the new row as the default in the table and uncheck the existing default row.
export const addNutrientParameterUtility = async (
  addParamState: AddParamStateProps,
  setAddParamState: React.Dispatch<React.SetStateAction<AddParamStateProps>>,
  setDisplaySnackbar: React.Dispatch<React.SetStateAction<SnackBarType>>,
  setIsAddParamOpened: React.Dispatch<React.SetStateAction<boolean>>,
  triggerUpdateCropDemandAnalysisNutrient: (config: {
    method: string;
    body: string;
  }) => Promise<CropDemandAnalysisNutrient | undefined>,
  triggerBulkCreateCropDemandAnalysisNutrients: (config: {
    method: string;
    body: string;
  }) => Promise<CreateCropDemandAnalysisNutrient[] | undefined>,
  snackbarMsg: string,
  expression?: string,
): Promise<CropDemandAnalysisNutrient | void> => {
  const { defaultCropDemandAnalysesNutrient, selectedCropDemandAnalyses, isDefaultChecked } =
    addParamState;

  if (!selectedCropDemandAnalyses) {
    setAddParamState((paramState) => ({
      ...paramState,
      error: true,
    }));
    return;
  }

  let returnedPromise: CreateCropDemandAnalysisNutrient[] | CropDemandAnalysisNutrient | undefined;

  const bulkCreateCropDemandAnalysisConfiguration = {
    method: METHOD.POST,
    body: JSON.stringify([
      {
        ...selectedCropDemandAnalyses,
        validityExpression: expression ?? '',
        validityExpressionTree: selectedCropDemandAnalyses?.validityExpressionTree ?? {},

        isDefault: isDefaultChecked,
      },
    ]),
  };

  const updateCropDemandAnalysisConfiguration = {
    method: METHOD.PUT,
    body: JSON.stringify({
      ...defaultCropDemandAnalysesNutrient,
      isDefault: false,
    }),
  };

  try {
    if (isDefaultChecked) {
      returnedPromise = await triggerUpdateCropDemandAnalysisNutrient(
        updateCropDemandAnalysisConfiguration,
      );
    }
    returnedPromise = await triggerBulkCreateCropDemandAnalysisNutrients(
      bulkCreateCropDemandAnalysisConfiguration,
    );

    const response = await returnedPromise;

    if (response) {
      setDisplaySnackbar({
        title: snackbarMsg,
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 4000,
        open: true,
      });

      setIsAddParamOpened(false);

      return response[0];
    }
  } catch (err) {
    console.error('Error creating and updating crop demand analysis nutrient data:', err);
  }
};

/**
 * Returns the initial values for the given elemental name.
 *
 * @param {string} elementalName - The name of the elemental parameter.
 * @returns {object} An object containing the initial values for the given elemental name.
 */
export const getInitialValues = (elementalName: string): InitialCropDemandAnalysisValues => {
  const elName = elementalName == 'BsSat' ? 'BaseSat' : elementalName;

  return {
    clayClassification: '',
    nutrientClassification: '',
    isDefault: false,
    validityExpression: `Analysis.${elName} > 0.0 AND Analysis.${elName} < 0.0`,
    validityExpressionTree: {
      id: 1,
      type: 'andExpression',
      value: [
        {
          id: 2,
          type: 'gt',
          value: [
            { id: 3, type: 'number', value: `Analysis.${elName}` },
            { id: 4, type: 'number', value: '0' },
          ],
        },
        {
          id: 5,
          type: 'lt',
          value: [
            { id: 6, type: 'number', value: `Analysis.${elName}` },
            { id: 7, type: 'number', value: '0' },
          ],
        },
      ],
    },
    soilDemandExpression: '0',
    soilDemandExpressionTree: { id: 1, type: 'number', value: 0 },
    cropDemandExpression: '0',
    cropDemandExpressionTree: { id: 1, type: 'number', value: 0 },
  };
};
