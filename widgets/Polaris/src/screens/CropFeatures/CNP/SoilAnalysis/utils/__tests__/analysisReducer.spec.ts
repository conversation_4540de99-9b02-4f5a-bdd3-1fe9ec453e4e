import {
  AnalysisActions,
  UpdateAnalysisActions,
  UpdateAnalysisState,
} from '../../NutritionParametersTable/NutritionParametersTable.type';
import { analysisReducer } from '../analysisReducer';
import {
  CropDemandAnalysisNutrient,
  ExpressionNode,
  ExpressionTree,
  SimpleExpressionTree,
} from '@common/types';

const cropDemandAnalysesNutrientsData: CropDemandAnalysisNutrient[] = [
  {
    id: '201',
    cropDemandAnalysisId: '301',
    countryId: 'US',
    cropRegionId: '1',
    insights: null,
    clayClassification: 'Low',
    nutrientClassification: 'Fat',
    validityExpression: 'expression7',
    validityExpressionTree: {} as ExpressionNode,
    soilDemandExpression: 'expression8',
    soilDemandExpressionTree: {} as SimpleExpressionTree,
    cropDemandExpression: 'expression9',
    cropDemandExpressionTree: {} as SimpleExpressionTree,
    ExpressionTree: {} as ExpressionTree,
    isDefault: true,
    created: '2023-01-01',
    modified: '2023-01-02',
    modifiedBy: 'user1',
    deleted: null,
  },
  {
    id: '202',
    cropDemandAnalysisId: '302',
    countryId: 'IN',
    cropRegionId: '2',
    insights: null,
    clayClassification: 'Medium',
    nutrientClassification: 'Carbohydrate',
    validityExpression: 'expression10',
    validityExpressionTree: {} as ExpressionNode,
    soilDemandExpression: 'expression11',
    soilDemandExpressionTree: {} as SimpleExpressionTree,
    cropDemandExpression: 'expression12',
    cropDemandExpressionTree: {} as SimpleExpressionTree,
    ExpressionTree: {} as ExpressionTree,
    isDefault: false,
    created: '2023-02-01',
    modified: '2023-02-02',
    modifiedBy: 'user2',
    deleted: null,
  },
];

describe('analysisReducer', () => {
  const initialState: UpdateAnalysisState = {
    selectedNutrient: null,
    updateType: '',
  };

  it('should handle SET_SELECTED_NUTRIENT action', () => {
    const action: AnalysisActions = {
      type: UpdateAnalysisActions.SET_SELECTED_NUTRIENT,
      // @ts-ignore
      payload: cropDemandAnalysesNutrientsData,
    };
    const expectedState = {
      ...initialState,
      selectedNutrient: cropDemandAnalysesNutrientsData,
    };
    const result = analysisReducer(initialState, action);
    expect(result).toEqual(expectedState);
  });

  it('should handle SET_UPDATE_TYPE action', () => {
    const action: AnalysisActions = {
      type: UpdateAnalysisActions.SET_UPDATE_TYPE,
      payload: 'DEFAULT',
    };
    const expectedState = {
      ...initialState,
      updateType: 'DEFAULT',
    };
    const result = analysisReducer(initialState, action);
    expect(result).toEqual(expectedState);
  });

  it('should return the current state when action type is unknown', () => {
    const unknownAction = {
      type: 'UNKNOWN_ACTION',
      payload: {},
    };
    // @ts-ignore
    const stateAfterUnknownAction = analysisReducer(initialState, unknownAction);
    expect(stateAfterUnknownAction).toBe(initialState);
  });
});
