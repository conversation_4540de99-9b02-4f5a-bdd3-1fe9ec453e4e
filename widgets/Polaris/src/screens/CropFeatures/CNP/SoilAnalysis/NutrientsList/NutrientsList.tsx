import React, { FC, useEffect, useState } from 'react';
import { AhuaIcon, Title } from '@yaradigitallabs/ahua-react';

import { Nutrient, CropDemandAnalysis } from '@common/types';
import { useTranslation } from 'react-i18next';
import {
  AddButtonStyled,
  CopyButtonStyled,
  NutrientList,
  NutrientListBody,
  TitleStyle,
  HeaderWrapper,
  EmptyStateText,
} from './NutrientList.styled';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import NutrientTile from '@widgets/Polaris/src/screens/CropFeatures/CNP/SoilAnalysis/NutrientTile/NutrientTile';
import {
  enrichCropDemandAnalysisData,
  getAlreadyAddedNutrients,
  handleAddNutrientUtility,
  handleNutrientSelect,
} from '@widgets/Polaris/src/screens/CropFeatures/CNP/SoilAnalysis/utils/cropDemandAnalysisHelpers';
import {
  useCreateCropDemandAnalysis,
  useBulkCreateCropDemandAnalysisNutrients,
  useNutrientsService,
} from '@polaris-hooks/index';
import { useNavbar } from '@libs/nav-context';
import AddNutrientPopup from '../AddNutrientPopup/AddNutrientPopup';
import CopyNutrientPopup from '../CopyNutrientPopup/CopyNutrientPopup';
import { CNP_PLAN_CONFIGURATION_TABS } from '../../../shared/constants';

const NutrientsList: FC = () => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cnpDetails.soilAnalysis.nutrientList',
  });
  const [showDialog, setShowDialog] = useState(false);
  const [showCopyDialog, setShowCopyDialog] = useState(false);
  const [showCopyAllDialog, setShowCopyAllDialog] = useState(false);
  const [newCropDemandAnalysisData, setNewCropDemandAnalysisData] = useState<CropDemandAnalysis[]>(
    [],
  );
  const { data: nutrientsData } = useNutrientsService();
  const {
    cropRegion,
    selectedCountry,
    selectedNutrient,
    selectedCountryUnits,
    selectedPlanConfigTab,
    cropDemandAnalyses: cropDemandAnalysisData,
    methods: { setSelectedNutrient, addCropDemandAnalyses },
  } = useAppContext();
  const { activeRoutePath } = useNavbar();

  const { createdCropDemandAnalysis, trigger: triggerCreateCropDemandAnalysis } =
    useCreateCropDemandAnalysis();
  const { triggerBulkCreateCropDemandAnalysisNutrients } =
    useBulkCreateCropDemandAnalysisNutrients();

  const enrichedData = enrichCropDemandAnalysisData(
    cropDemandAnalysisData,
    nutrientsData,
    newCropDemandAnalysisData,
  );

  const alreadyAddedNutrients = getAlreadyAddedNutrients(enrichedData);
  const visibleNutrientsForSoilAnalysis =
    nutrientsData?.filter((n) => n.isVisibleForSoilAnalysis) || [];
  const handleCopy = () => {
    setShowCopyDialog(true);
  };
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const handleDelete = () => {};

  const handleAddNutrient = async (selectedNutrients: Nutrient[]) => {
    await handleAddNutrientUtility({
      selectedNutrients,
      triggerCreateCropDemandAnalysis,
      triggerBulkCreateCropDemandAnalysisNutrients,
      setNewCropDemandAnalysisData,
      selectedCountryUnits: selectedCountryUnits ?? [],
      cropRegionId: cropRegion?.id,
      currentCountryId: selectedCountry?.id,
      addCropDemandAnalyses,
    });
  };

  // When a new analysis is created, accumulate it
  useEffect(() => {
    if (createdCropDemandAnalysis) {
      setNewCropDemandAnalysisData((prevData) => [
        ...prevData,
        ...(Array.isArray(createdCropDemandAnalysis)
          ? createdCropDemandAnalysis
          : [createdCropDemandAnalysis]),
      ]);
    }
  }, [createdCropDemandAnalysis]);

  useEffect(() => {
    // Whenever the activeRoutePath changes, reset the selected nutrient
    // After refresh to avoid clearing the selected nutrient we check for selected tab
    if (selectedPlanConfigTab !== CNP_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS)
      setSelectedNutrient(null);
  }, [activeRoutePath]);

  return (
    <>
      <div>
        <HeaderWrapper data-cy='nutrient-list-header'>
          <Title data-cy='nutrient-list-title' style={TitleStyle} size='xs'>
            {t('title')}
          </Title>
          <CopyButtonStyled
            size={'s'}
            colorConcept='brand'
            label='Label'
            variant='outline'
            disabled={!enrichedData || enrichedData.length === 0}
            data-cy='nutrient-list-copyAll'
            onClick={() => setShowCopyAllDialog(true)}
          >
            {t('copy')}
          </CopyButtonStyled>
        </HeaderWrapper>
        <NutrientListBody data-cy='nutrient-list-body'>
          <NutrientList
            data-cy={
              enrichedData && enrichedData.length > 0 ? 'nutrient-list' : 'nutrient-list-empty'
            }
          >
            {enrichedData && enrichedData.length > 0 ? (
              enrichedData
                .filter((analysis) => analysis.nutrient !== undefined)
                .map((analysis, index) => (
                  <NutrientTile
                    key={`${analysis.nutrientId}-${analysis.analysisMethodId}`}
                    nutrient={analysis.nutrient}
                    selected={selectedNutrient?.id === analysis?.nutrientId}
                    onSelect={(nutrient) => handleNutrientSelect(setSelectedNutrient, nutrient)}
                    onCopy={() => {
                      handleNutrientSelect(setSelectedNutrient, analysis.nutrient);
                      handleCopy();
                    }}
                    onDelete={handleDelete}
                    variant='list'
                    dataCy={`nutrient-tile-${index}`}
                  />
                ))
            ) : (
              <EmptyStateText>{t('emptyStateText')}</EmptyStateText>
            )}
          </NutrientList>
          <AddButtonStyled
            onClick={() => setShowDialog(true)}
            colorConcept='brand'
            variant='outline'
            data-cy='nutrient-list-add-btn'
          >
            <AhuaIcon icon='Plus' iconSize={'x8'} />
          </AddButtonStyled>
        </NutrientListBody>
      </div>
      {showDialog && (
        <AddNutrientPopup
          showDialog={showDialog}
          onOpenChange={setShowDialog}
          nutrientsData={visibleNutrientsForSoilAnalysis}
          onSave={handleAddNutrient}
          alreadyAddedNutrients={alreadyAddedNutrients}
        />
      )}
      {showCopyDialog && (
        <CopyNutrientPopup
          showDialog={showCopyDialog}
          setShowDialog={setShowCopyDialog}
          selectedNutrient={selectedNutrient}
        />
      )}
      {showCopyAllDialog && (
        <CopyNutrientPopup showDialog={showCopyAllDialog} setShowDialog={setShowCopyAllDialog} />
      )}
    </>
  );
};

export default NutrientsList;
