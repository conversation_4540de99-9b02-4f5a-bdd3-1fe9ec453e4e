import { Button, styled } from '@yaradigitallabs/ahua-react';

export const HeaderWrapper = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '$x4',
  width: '100%',
  gap: '$x2',
});

export const TitleStyle = {
  fontWeight: 500,
};

export const NutrientListBody = styled('div', {
  backgroundColor: 'rgba(15, 15, 15, 0.03)',
  display: 'flex',
  height: 128,
  width: '100%',
  justifyContent: 'space-between',
  alignItems: 'center',
  gap: '$x2',
});

export const NutrientList = styled('div', {
  width: '100%',
  height: '100%',
  display: 'flex',
  justifyContent: 'flex-start',
  alignItems: 'center',
  boxSizing: 'border-box',
  borderRadius: '8px',
  padding: '$x3',
  overflowX: 'auto',
  overflowY: 'hidden',
  gap: '$x4',
  '& > *': {
    flex: '0 0 auto',
  },
  scrollbarWidth: 'thin',
  scrollbarColor: '#0F0F0F1A transparent', // For Firefox, scrollbar thumb and track

  // Styles for WebKit-based browsers
  '&::-webkit-scrollbar': {
    height: '4px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '$black10',
    borderRadius: '4px',
  },
});

export const EmptyStateText = styled('div', {
  fontSize: '14px',
  color: '$black60',
});

export const AddButtonStyled = styled(Button, {
  flex: '0 0 86px',
  height: '100% !important',
  border: '1px solid $black15 !important',
  '& > span': {
    overflow: 'unset',
  },
});

export const CopyButtonStyled = styled(Button, {
  flex: '0 0 86px',
  '& > span': {
    overflow: 'unset',
  },
});
