import React from 'react';
import NutrientsList from '../NutrientsList';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { NavbarProvider } from '@libs/nav-context';
import { setupServer } from 'msw/node';

import {
  allNutrientsHandler,
  allUnitsHandler,
  cropDemandAnalysisHandler,
  cropDemandAnalysisNutrientBulkHandler,
  cropDemandAnalysisNutrientsHandler,
  unitCountriesHandler,
  cropDemandAnalysesDataHandler,
  mockAppProviderValue,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { BrowserRouter as Router } from 'react-router-dom';
import { userEvent } from '@testing-library/user-event';
import * as nutrientUtils from '../../utils/cropDemandAnalysisHelpers';
import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';
import { SNACKBAR_INITIAL_STATE } from '@common/constants';

const server = setupServer(
  unitCountriesHandler,
  allNutrientsHandler,
  cropDemandAnalysisHandler,
  allUnitsHandler,
  cropDemandAnalysisNutrientBulkHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysesDataHandler,
);
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'polaris.cnpDetails.soilAnalysis.nutrientList.emptyStateText')
        return 'Click + to add nutrition parameters for Soil analysis and Demand calculations.';
      return key;
    },
  }),
}));

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

jest.mock('../../utils/cropDemandAnalysisHelpers', () => ({
  ...jest.requireActual('../../utils/cropDemandAnalysisHelpers'),
  handleAddNutrientUtility: jest.fn(),
}));

jest.mock('@polaris-hooks/polarisMockService/useNutrientsService/useNutrientsService', () => {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { nutrientsMock } = require('@common/mocks');
  return {
    useNutrientsService: () => ({
      data: nutrientsMock,
      error: undefined,
    }),
  };
});

describe('NutrientsList Component', () => {
  const mockSetDisplaySnackbar = jest.fn();
  const mockGetBulkCropDemandAnalysisNutrients = jest.fn();

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('renders empty state text when no nutrients are available', () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {(_context) => (
                <NutrientsList
                  getBulkCropDemandAnalysisNutrients={mockGetBulkCropDemandAnalysisNutrients}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(screen.getByText('emptyStateText')).toBeInTheDocument();
  });

  it('renders nutrients when available', () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {(_context) => (
                <NutrientsList
                  getBulkCropDemandAnalysisNutrients={mockGetBulkCropDemandAnalysisNutrients}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const nutrientList =
      screen.queryByTestId('nutrient-list') || screen.queryByTestId('nutrient-list-empty');
    expect(nutrientList).toBeInTheDocument();
  });

  it('opens AddNutrientPopup when Add button is clicked', async () => {
    const user = userEvent.setup();
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {(_context) => (
                <NutrientsList
                  getBulkCropDemandAnalysisNutrients={mockGetBulkCropDemandAnalysisNutrients}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    await user.click(screen.getByTestId('nutrient-list-add-btn'));
    expect(screen.getByTestId('nutrient-popup-content')).toBeInTheDocument();
  });

  it('calls handleAddNutrientUtility when adding a nutrient', async () => {
    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: SNACKBAR_INITIAL_STATE,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AppContext.Provider value={mockAppProviderValue}>
          <Router>
            <NutrientsList
              getBulkCropDemandAnalysisNutrients={mockGetBulkCropDemandAnalysisNutrients}
            />
          </Router>
        </AppContext.Provider>
      </SnackbarContext.Provider>,
    );

    const addButton = screen.getByTestId('nutrient-list-add-btn');
    fireEvent.click(addButton);
    const nutrientTile = screen.getByTestId('soil-nutrient-tile-0');
    fireEvent.click(nutrientTile);

    const saveButton = screen.getByTestId('nutrient-popup-save-btn');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(nutrientUtils.handleAddNutrientUtility).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(mockSetDisplaySnackbar).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'snackbarMessage',
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
        }),
      );
    });
  });

  it('disables the copy button when no nutrients are available', () => {
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <Router>
          <NutrientsList
            getBulkCropDemandAnalysisNutrients={mockGetBulkCropDemandAnalysisNutrients}
          />
        </Router>
      </AppContext.Provider>,
    );

    const copyButton = screen.getByTestId('nutrient-list-copyAll');
    expect(copyButton).toBeDisabled();
  });

  it('shows AddNutrientPopup when Add button is clicked and save nutrient', async () => {
    const user = userEvent.setup();
    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: SNACKBAR_INITIAL_STATE,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AppContext.Provider value={mockAppProviderValue}>
          <NavbarProvider>
            <Router>
              <NutrientsList
                getBulkCropDemandAnalysisNutrients={mockGetBulkCropDemandAnalysisNutrients}
              />
            </Router>
          </NavbarProvider>
        </AppContext.Provider>
      </SnackbarContext.Provider>,
    );

    const addButton = screen.getByTestId('nutrient-list-add-btn');
    await user.click(addButton);
    expect(screen.getByTestId('nutrient-popup-content')).toBeVisible();
    const nutrientTile = screen.getByText('Sulphur');
    expect(nutrientTile).toBeInTheDocument();
    await user.click(nutrientTile);
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));
    await waitFor(() => {
      expect(screen.queryByTestId('nutrient-popup-content')).not.toBeInTheDocument();
    });
    expect(mockSetDisplaySnackbar).toHaveBeenCalled();
  });
});
