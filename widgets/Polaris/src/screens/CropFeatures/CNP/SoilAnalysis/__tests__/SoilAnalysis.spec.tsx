import React from 'react';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { screen, render, waitFor } from '@testing-library/react';
import SoilAnalysis from '../SoilAnalysis';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import {
  cropDemandAnalysisMock,
  mockAppProviderValue,
  mockCNPAppProviderValue,
  nutrientMock,
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  getAnalysisMethodsHandler,
} from '@common/mocks';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import userEvent from '@testing-library/user-event';

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  getAnalysisMethodsHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

describe('SoilAnalysis Component', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <SoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders without crashing', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <SoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(component).toBeDefined();
  });

  it('matches snapshot', () => {
    const { asFragment } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <SoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders title', () => {
    const { getByText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <SoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(getByText('title')).toBeInTheDocument();
  });

  it('render dropdown menu when button icon is clicked', async () => {
    const user = userEvent.setup();
    render(
      <AppContext.Provider
        value={{
          ...mockCNPAppProviderValue,
          selectedNutrient: nutrientMock,
          cropDemandAnalyses: cropDemandAnalysisMock,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <SoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const addDeleteSoildAnalaysisBtn = screen.getByTestId('analysis-method-edit-menu-btn');
    expect(addDeleteSoildAnalaysisBtn).toBeInTheDocument();

    await user.click(addDeleteSoildAnalaysisBtn);
    await waitFor(() => {
      expect(addDeleteSoildAnalaysisBtn).toHaveAttribute('aria-expanded', 'true');
    });

    const menu = screen.getByRole('menu');
    const menuItems = screen.getAllByRole('menuitem');

    expect(menu).toBeInTheDocument();
    expect(menuItems.length).toBeGreaterThan(0);
    expect(screen.getByTestId('add-analysis-method-btn')).toBeInTheDocument();
    expect(screen.getByTestId('delete-analysis-method-btn')).toBeInTheDocument();
  });
});
