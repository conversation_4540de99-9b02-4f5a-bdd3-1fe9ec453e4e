// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SoilAnalysis Component matches snapshot 1`] = `
<DocumentFragment>
  <div>
    <div
      class="c-dvOqFP"
      data-cy="nutrient-list-header"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css"
        data-cy="nutrient-list-title"
        style="font-weight: 500;"
      >
        title
      </h1>
      <button
        class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv c-kMnovV"
        data-cy="nutrient-list-copyAll"
        disabled=""
      >
        <span
          class="c-iepcqn"
        >
          copy
        </span>
      </button>
    </div>
    <div
      class="c-kKBuYN"
      data-cy="nutrient-list-body"
    >
      <div
        class="c-hwiQGu"
        data-cy="nutrient-list-empty"
      >
        <div
          class="c-dHnMqS"
        >
          emptyStateText
        </div>
      </div>
      <button
        class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-qswFQ-variant-outline c-fiBzoV"
        data-cy="nutrient-list-add-btn"
      >
        <span
          class="c-iepcqn"
        >
          <svg
            class="c-nJRoe c-nJRoe-gmtoDF-iconSize-x8 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.072 6v12M18 12H6"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>
</DocumentFragment>
`;
