import { styled } from '@yaradigitallabs/ahua-react';

export const AddContainer = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingBottom: '$x5',
  paddingTop: '$x4',
});

export const InputContainer = styled('div', {
  display: 'flex',
  flexDirection: 'row',
  paddingTop: '$x7',
  paddingRight: '$x4',
  paddingLeft: '$x4',
  paddingBottom: '$x4',
  gap: '$x4',
});

export const FirstRowInput = styled('div', {
  flexGrow: 1,
  width: '33%',
});

export const SecondRowInput = styled('div', {
  width: '50%',
});

export const InputContainerOptional = styled('div', {
  display: 'flex',
  flexDirection: 'row',
  paddingRight: '$x4',
  paddingLeft: '$x4',
  paddingBottom: '$x7',
  gap: '$x4',
});

export const NutritionParameterContainer = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  paddingTop: '$x7',
  paddingRight: '$x4',
  paddingLeft: '$x4',
  paddingBottom: '$x4',
  gap: '$x4',
});

export const SwitchContainer = styled('div', {
  display: 'flex',
  flexDirection: 'row',
  gap: '$x2',
  alignItems: 'center',
});

export const TableContainer = styled('div', {
  padding: '$x4',
  paddingTop: '$x2',
});
