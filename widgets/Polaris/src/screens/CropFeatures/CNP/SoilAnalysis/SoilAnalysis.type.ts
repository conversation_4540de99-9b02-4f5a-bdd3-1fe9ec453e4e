import { ParameterLevelEnum } from '@common/constants';
import { BaseUnit, Nutrient, CropDemandAnalysis } from '@common/types';

export type ValidityCategory =
  | ParameterLevelEnum.VERY_LOW
  | ParameterLevelEnum.LOW
  | ParameterLevelEnum.MEDIUM
  | ParameterLevelEnum.HIGH
  | ParameterLevelEnum.VERY_HIGH;
export interface NumberValue {
  id: number;
  type: 'number';
  value: number | string;
}

export interface ComparisonExpression {
  id: number;
  type: 'lt' | 'gte';
  value: [NumberValue, { id: number; type: 'number'; value: number }];
}

export interface DefaultCropDemandAnalysisNutrientsParams {
  cropDemandAnalysis: CropDemandAnalysis | undefined;
  clayClassification?: string;
  nutrientName: string;
  isSecondaryRequired?: boolean;
}

export interface HandleAddNutrientParams {
  selectedNutrients: Nutrient[];
  /* eslint-disable @typescript-eslint/no-explicit-any */
  triggerCreateCropDemandAnalysis: (config: { method: string; body: string }) => Promise<any>;
  triggerBulkCreateCropDemandAnalysisNutrients: (config: {
    method: string;
    body: string;
  }) => Promise<any>;
  setNewCropDemandAnalysisData: React.Dispatch<React.SetStateAction<CropDemandAnalysis[]>>;
  addCropDemandAnalyses: (data: CropDemandAnalysis) => void;
  selectedCountryUnits?: BaseUnit[];
  cropRegionId?: string;
  currentCountryId?: string;
}
