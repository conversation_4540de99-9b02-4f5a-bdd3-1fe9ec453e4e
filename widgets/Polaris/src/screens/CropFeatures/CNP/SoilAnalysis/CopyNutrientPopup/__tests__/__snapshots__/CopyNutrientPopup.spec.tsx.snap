// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CopyNutrientPopup does not render 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div />
  </body>,
  "container": <div />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`CopyNutrientPopup renders without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    />
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:r2:"
      aria-labelledby="radix-:r1:"
      class="c-cPoUYR c-cPoUYR-ieoIBVf-css"
      data-cy="copyNutrient-undefined-dialog"
      data-state="open"
      id="radix-:r0:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-cVIuYM dialog-header-headline-main"
      >
        <div
          class="c-eolNzW"
          data-cy="dialog-header-body"
        >
          <h1
            class="c-halGGX c-halGGX-ktDOnG-size-s c-halGGX-iPJLV-css dialog-location"
            data-cy="dialog-location"
          >
            polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.location
          </h1>
          <p
            class="c-gIhYmC c-gIhYmC-bAcCCY-size-xs c-gIhYmC-iiffMpN-css dialog-title"
            data-cy="dialog-title"
          >
            polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.copyNutrientTitle
          </p>
        </div>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs c-dexIdH-idtkdsG-css"
          data-cy="dialog-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-jndegn c-jndegn-igwRkrt-css dialog-middle"
      >
        <div
          data-cy="country-select-copy-nutrient"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.countryDropdown.placeholder
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r3:"
                aria-expanded="false"
                aria-label="polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.countryDropdown.ariaLabel"
                class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="0"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
        <div
          data-cy="region-select-copy-nutrient"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-WRIat-ubosY-disabled-true"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.regionDropdown.placeholder
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r4:"
                aria-disabled="true"
                aria-expanded="false"
                aria-label="polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.regionDropdown.ariaLabel"
                class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-dxyUev-disabled-true c-fExIjO-bOaAOS-cover-fill c-fExIjO-dMTwlH-cv c-fExIjO-iPJLV-css"
                data-disabled=""
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="-1"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
        <div
          data-cy="crop-select-copy-nutrient"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-WRIat-ubosY-disabled-true"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.cropDropdown.placeholder
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:r5:"
                aria-disabled="true"
                aria-expanded="false"
                aria-label="polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.cropDropdown.ariaLabel"
                class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-dxyUev-disabled-true c-fExIjO-bOaAOS-cover-fill c-fExIjO-dMTwlH-cv c-fExIjO-iPJLV-css"
                data-disabled=""
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="-1"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
        <div
          class="radio-container"
        >
          <h1
            class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-ijZBIvl-css"
          >
            polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.helperTextSingle
          </h1>
          <div
            aria-label="Display Option"
            aria-required="false"
            class="c-fixGjY c-kiVxIX"
            data-cy="copy-analyses-nutrient-radio-btn-group"
            dir="ltr"
            role="radiogroup"
            style="outline: none;"
            tabindex="0"
          >
            <div
              class="c-haqbFw"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                for="displayOption0"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.no
              </label>
              <div
                class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
              >
                <button
                  aria-checked="false"
                  class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                  data-cy="copy-analyses-nutrient-radio-btn-polaris.cnpdetails.soilanalysis.nutrientlist.nutrientpopup.no"
                  data-radix-collection-item=""
                  data-state="unchecked"
                  id="displayOption0"
                  role="radio"
                  tabindex="-1"
                  type="button"
                  value="no"
                />
              </div>
            </div>
            <div
              class="c-haqbFw c-haqbFw-kvhFal-checked-true"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css label-color-contrast"
                for="displayOption1"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.yes
              </label>
              <div
                class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
              >
                <button
                  aria-checked="true"
                  class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                  data-cy="copy-analyses-nutrient-radio-btn-polaris.cnpdetails.soilanalysis.nutrientlist.nutrientpopup.yes"
                  data-radix-collection-item=""
                  data-state="checked"
                  id="displayOption1"
                  role="radio"
                  tabindex="-1"
                  type="button"
                  value="yes"
                >
                  <span
                    class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                    data-state="checked"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-ctwkvW"
      >
        <div
          class="c-hcqlDB"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary"
            data-cy="dialog-save-btn"
          >
            <span
              class="c-iepcqn"
            >
              save
            </span>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`CopyNutrientPopup renders without crashing copy all 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    />
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:ra:"
      aria-labelledby="radix-:r9:"
      class="c-cPoUYR c-cPoUYR-ieoIBVf-css"
      data-cy="copyNutrient-all-dialog"
      data-state="open"
      id="radix-:r8:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-cVIuYM dialog-header-headline-main"
      >
        <div
          class="c-eolNzW"
          data-cy="dialog-header-body"
        >
          <h1
            class="c-halGGX c-halGGX-ktDOnG-size-s c-halGGX-iPJLV-css dialog-location"
            data-cy="dialog-location"
          >
            polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.location
          </h1>
          <p
            class="c-gIhYmC c-gIhYmC-bAcCCY-size-xs c-gIhYmC-iiffMpN-css dialog-title"
            data-cy="dialog-title"
          >
            polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.copyNutrientsTitle
          </p>
        </div>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs c-dexIdH-idtkdsG-css"
          data-cy="dialog-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-jndegn c-jndegn-igwRkrt-css dialog-middle"
      >
        <div
          data-cy="country-select-copy-nutrient"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.countryDropdown.placeholder
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:rb:"
                aria-expanded="false"
                aria-label="polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.countryDropdown.ariaLabel"
                class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="0"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
        <div
          data-cy="region-select-copy-nutrient"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-WRIat-ubosY-disabled-true"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.regionDropdown.placeholder
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:rc:"
                aria-disabled="true"
                aria-expanded="false"
                aria-label="polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.regionDropdown.ariaLabel"
                class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-dxyUev-disabled-true c-fExIjO-bOaAOS-cover-fill c-fExIjO-dMTwlH-cv c-fExIjO-iPJLV-css"
                data-disabled=""
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="-1"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
        <div
          data-cy="crop-select-copy-nutrient"
          style="display: inline-block; width: 100%;"
        >
          <div
            class="c-jGFTiO c-jGFTiO-ubosY-state-default c-jGFTiO-ifGHEql-css"
          >
            <div
              class="c-kFLrJl"
            >
              <label
                class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iPJLV-css c-WRIat c-WRIat-VZmBx-iconPresent-false c-WRIat-fcBbhr-textType-placeholder c-WRIat-ubosY-disabled-true"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.cropDropdown.placeholder
              </label>
              <button
                aria-autocomplete="none"
                aria-controls="radix-:rd:"
                aria-disabled="true"
                aria-expanded="false"
                aria-label="polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.cropDropdown.ariaLabel"
                class="c-fExIjO c-fExIjO-bOMjcI-size-n c-fExIjO-kRNjEX-variant-default c-fExIjO-dxyUev-disabled-true c-fExIjO-bOaAOS-cover-fill c-fExIjO-dMTwlH-cv c-fExIjO-iPJLV-css"
                data-disabled=""
                data-state="closed"
                dir="ltr"
                role="combobox"
                tabindex="-1"
                type="button"
              >
                <span
                  style="pointer-events: none;"
                >
                  <div
                    class="c-fSebPZ"
                  />
                </span>
                <svg
                  aria-hidden="true"
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 9.75l-5 6h-2l-5-6"
                  />
                </svg>
              </button>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
            />
          </div>
        </div>
        <div
          class="radio-container"
        >
          <h1
            class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-ijZBIvl-css"
          >
            polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.helperTextMultiple
          </h1>
          <div
            aria-label="Display Option"
            aria-required="false"
            class="c-fixGjY c-kiVxIX"
            data-cy="copy-analyses-nutrient-radio-btn-group"
            dir="ltr"
            role="radiogroup"
            style="outline: none;"
            tabindex="0"
          >
            <div
              class="c-haqbFw"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                for="displayOption0"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.no
              </label>
              <div
                class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
              >
                <button
                  aria-checked="false"
                  class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                  data-cy="copy-analyses-nutrient-radio-btn-polaris.cnpdetails.soilanalysis.nutrientlist.nutrientpopup.no"
                  data-radix-collection-item=""
                  data-state="unchecked"
                  id="displayOption0"
                  role="radio"
                  tabindex="-1"
                  type="button"
                  value="no"
                />
              </div>
            </div>
            <div
              class="c-haqbFw c-haqbFw-kvhFal-checked-true"
            >
              <label
                class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css label-color-contrast"
                for="displayOption1"
              >
                polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.yes
              </label>
              <div
                class="c-hcxFDL c-hcxFDL-vQeKi-concept-brand"
              >
                <button
                  aria-checked="true"
                  class="c-thNAo c-thNAo-bcWJEY-concept-brand"
                  data-cy="copy-analyses-nutrient-radio-btn-polaris.cnpdetails.soilanalysis.nutrientlist.nutrientpopup.yes"
                  data-radix-collection-item=""
                  data-state="checked"
                  id="displayOption1"
                  role="radio"
                  tabindex="-1"
                  type="button"
                  value="yes"
                >
                  <span
                    class="c-jKYcpo c-jKYcpo-cYFWyN-concept-brand"
                    data-state="checked"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-ctwkvW"
      >
        <div
          class="c-hcqlDB"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary"
            data-cy="dialog-save-btn"
          >
            <span
              class="c-iepcqn"
            >
              save
            </span>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  />,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
