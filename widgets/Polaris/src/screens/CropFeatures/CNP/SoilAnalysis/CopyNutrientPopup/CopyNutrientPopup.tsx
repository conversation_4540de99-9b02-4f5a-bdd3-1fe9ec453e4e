import React, { Di<PERSON><PERSON>, FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalDialog, SelectWrapper } from '@widgets/Polaris/src/components';
import {
  Country,
  CropDemandAnalysis,
  CropDemandAnalysisNutrient,
  CropDescription,
  CropRegion,
  CropSubClass,
  Nutrient,
  PaginatedResponse,
  Region,
  SnackBarType,
} from '@common/types';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { Label, RadioButton, Select, Title } from '@yaradigitallabs/ahua-react';
import {
  createDropdownValues,
  cropDropdownValues,
  findCountry,
  findCrop,
  findRegion,
} from '../../../../Home';
import {
  useBulkCreateCropDemandAnalyses,
  useCropRegions,
  useBulkDeleteCropDemandAnalysisNutrients,
  useFetchRegionsData,
  useGetCropDescriptions,
  useGetCropSubClasses,
  useBulkDeleteCropDemandAnalyses,
  useFetchCropDemandAnalyses,
  useBulkCreateCropDemandAnalysisNutrients,
  useCropDemandAnalysisNutrients,
} from '@polaris-hooks/index';
import { displaySnackbarMessage } from '@widgets/Polaris/utils';
import {
  RadioButtonGroupStyled,
  RadioButtonWrapper,
} from '@widgets/Polaris/src/components/ParameterEditPopup/ParameterEditPopup.styled';
import { DisplayOption } from '@widgets/Polaris/src/components/ParameterEditPopup/ParameterEditPopup.type';
import './styles.css';
import { ACTIONS, METHOD } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';

export interface CopyNutrientPopupProps {
  showDialog: boolean;
  setShowDialog: Dispatch<boolean>;
  selectedNutrient?: Nutrient | null;
}

const CopyNutrientPopup: FC<CopyNutrientPopupProps> = ({
  showDialog,
  setShowDialog,
  selectedNutrient,
}) => {
  //state
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null);
  const [selectedCrop, setSelectedCrop] = useState<CropSubClass | null>(null);
  const [selectedCropDescription, setSelectedCropDescription] = useState<CropDescription | null>(
    null,
  );
  const [hasErrorCountry, setHasErrorCountry] = useState(false);
  const [hasErrorRegion, setHasErrorRegion] = useState(false);
  const [hasErrorCropDescription, setHasErrorCropDescription] = useState(false);
  const [areFieldsDisabled, setAreFieldsDisabled] = useState(true);
  const [displayValue, setDisplayValue] = useState(DisplayOption.Yes);
  const [selectedCropRegion, setSelectedCropRegion] = useState<CropRegion | null>(null);
  const [existingCropDemandAnalysis, setExistingCropDemandAnalysis] = useState<
    CropDemandAnalysis[]
  >([]);
  const [analysesNutrientsToCreateArray, setAnalysesNutrientsToCreateArray] = useState<
    CropDemandAnalysisNutrient[] | null
  >(null);
  const [analysesNutrientsToDelete, setAnalysesNutrientsToDelete] = useState<
    CropDemandAnalysisNutrient[] | null
  >(null);
  //hooks
  const {
    selectedCountry: selectedCountryContext,
    countries,
    selectedRegion: selectedRegionContext,
    selectedCropDescription: selectedCropDescriptionContext,
    cropDemandAnalyses,
  } = useAppContext();
  const selectedCropDemandAnalysesData = selectedNutrient
    ? cropDemandAnalyses.filter((el) => el.nutrientId === selectedNutrient.id)
    : cropDemandAnalyses;

  const { t } = useTranslation('polaris');
  const keyPrefix = 'polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup';
  const { setDisplaySnackbar } = useSnackbar();

  const fetchRegionsFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!selectedCountry?.id) return;
    return [
      {
        key: 'countryId',
        type: FilterType.EQ,
        value: selectedCountry.id,
      },
    ];
  }, [selectedCountry?.id]);
  const { cropRegionsData, trigger: getCropRegions } = useCropRegions();
  const { regionsData } = useFetchRegionsData(fetchRegionsFilters, Boolean(selectedCountry?.id));

  const fetchCropDescriptionsFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!cropRegionsData?.length) return;
    const cropDescriptionIds = cropRegionsData.map((region) => region.cropDescriptionId);
    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: cropDescriptionIds.join(','),
      },
    ];
  }, [cropRegionsData]);

  const { cropDescriptions, isLoading } = useGetCropDescriptions(fetchCropDescriptionsFilters);

  const fetchCropSubclassesFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!cropDescriptions?.length) return;
    const cropSubClassIds = cropDescriptions.map((region) => region.cropSubClassId);
    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: cropSubClassIds.join(','),
      },
    ];
  }, [cropDescriptions]);

  const { cropSubClasses, isLoadingSC } = useGetCropSubClasses(fetchCropSubclassesFilters);
  const cropDemandAnalysesData = useFetchCropDemandAnalyses(selectedCropRegion?.id);
  const { triggerBulkDeleteCropDemandAnalyses } = useBulkDeleteCropDemandAnalyses();
  const { triggerBulkPermDeleteCropDemandAnalysisNutrients } =
    useBulkDeleteCropDemandAnalysisNutrients();
  const { triggerBulkCreateCropDemandAnalysisNutrients } =
    useBulkCreateCropDemandAnalysisNutrients();
  const { triggerBulkCreateCropDemandAnalyses } = useBulkCreateCropDemandAnalyses();
  const { trigger: triggerGetCropDemandAnalysisNutrients } = useCropDemandAnalysisNutrients();
  //functions and handlers
  const countryChangeHandler = useCallback(
    (value: string) => {
      const foundCountry = findCountry(countries, value);
      if (foundCountry) {
        setSelectedCountry(foundCountry);
        setSelectedRegion(null);
      }
      setAreFieldsDisabled(false);
      setSelectedCrop(null);
      setSelectedCropDescription(null);
      setHasErrorCropDescription(false);
      setHasErrorCountry(false);
    },
    [countries],
  );
  const regionChange = useCallback(
    (value: string) => {
      const region = findRegion(regionsData, value);

      if (region) {
        setSelectedRegion(region);
        setSelectedCrop(null);
        setHasErrorRegion(false);
        setSelectedCropDescription(null);
      }
    },
    [regionsData],
  );
  const cropChange = useCallback(
    (value: string) => {
      cropDescriptions?.map((cd: CropDescription) => {
        if (cd.id === value) {
          const selectedCropRegion = cropRegionsData?.find(
            (cropRegion) => cropRegion.cropDescriptionId === cd.id,
          );
          selectedCropRegion && setSelectedCropRegion(selectedCropRegion);
          setSelectedCropDescription(cd);
          const selectedCropDetails = findCrop(cd.cropSubClassId, cropSubClasses);
          if (selectedCropDetails) {
            setSelectedCrop(selectedCropDetails);
            setHasErrorCropDescription(false);
          }
        }
      });
    },
    [cropDescriptions, cropRegionsData, cropSubClasses],
  );
  const countriesDropdownData = useMemo(() => {
    if (!countries) return [];

    return createDropdownValues(countries);
  }, [countries]);
  const regionsDropdownData = useMemo(() => {
    if (!regionsData) return [];

    return createDropdownValues(regionsData);
  }, [regionsData, selectedCountry]);
  const cropDropdownData = useMemo(() => {
    if (!cropSubClasses && !cropDescriptions) return [];
    const isSameCountryAndRegion =
      selectedCountry?.name === selectedCountryContext?.name &&
      selectedRegion?.name === selectedRegionContext?.name;
    //filter only when the selected country and region are the same as the context
    return isSameCountryAndRegion
      ? cropDropdownValues(cropSubClasses, cropDescriptions).filter(
          //exclude selected CropDescription from the dropdown
          (el) => el.value !== selectedCropDescriptionContext?.id,
        )
      : cropDropdownValues(cropSubClasses, cropDescriptions);
  }, [
    isLoading,
    isLoadingSC,
    selectedRegion?.name,
    selectedCountry?.name,
    cropSubClasses,
    cropDescriptions,
    selectedCountryContext?.name,
    selectedRegionContext?.name,
  ]);
  const radioOptions = [
    { value: 'no', label: t(`${keyPrefix}.no`) },
    { value: 'yes', label: t(`${keyPrefix}.yes`) },
  ];
  const deleteExistingCropDemandAnalysis = async () => {
    if (existingCropDemandAnalysis && existingCropDemandAnalysis.length > 0) {
      const cropDemandIdsToDelete = existingCropDemandAnalysis.map((analysis) => analysis.id);
      triggerBulkDeleteCropDemandAnalyses(cropDemandIdsToDelete);
      await triggerBulkPermDeleteCropDemandAnalysisNutrients({
        method: METHOD.DELETE,
        body: JSON.stringify({
          ids: analysesNutrientsToDelete?.map((el) => el.id),
        }),
      });
    }
  };

  const createNewAnalysisMethods = (selectedData: CropDemandAnalysis[]) => {
    return selectedData
      ? selectedData
          ?.map((el) => {
            if (selectedCountry?.id && selectedCropRegion?.id) {
              return {
                ...el,
                countryId: selectedCountry.id,
                cropRegionId: selectedCropRegion.id,
              };
            }
          })
          .filter((data) => !!data)
      : [];
  };
  const createNewDemandAnalysisNutrients = async (
    newNutrientAnalysisData: CropDemandAnalysis[],
  ) => {
    if (newNutrientAnalysisData && newNutrientAnalysisData.length > 0) {
      const response = await triggerBulkCreateCropDemandAnalyses({
        method: METHOD.POST,
        body: JSON.stringify(newNutrientAnalysisData),
      });
      return response?.flatMap((createdCropDemandAnalysis) => {
        if (analysesNutrientsToCreateArray) {
          return analysesNutrientsToCreateArray.map((nutrient) => {
            return {
              ...nutrient,
              cropDemandAnalysisId: createdCropDemandAnalysis.id,
              countryId: selectedCountry?.id,
              cropRegionId: selectedCropRegion?.id,
            };
          });
        }
      });
    }
  };
  const displayMessage = useCallback(
    (
      messageKey: string,
      nutrientName: string | undefined,
      colorConcept: SnackBarType['colorConcept'] = 'successLight',
      placement: SnackBarType['placement'] = 'bottomRight',
      icon: SnackBarType['icon'] = 'Check',
    ) => {
      displaySnackbarMessage(
        t(messageKey, {
          selectedCropName: selectedCrop?.name,
          selectedCropDescriptionName: selectedCropDescription?.name,
          selectedCountryName: selectedCountry?.name,
          selectedRegionName: selectedRegion?.name,
          selectedNutrientName: nutrientName,
          interpolation: { escapeValue: false },
        }),
        setDisplaySnackbar,
        colorConcept,
        placement,
        icon,
      );
    },
    [
      selectedCrop?.name,
      selectedCropDescription?.name,
      selectedCountry?.name,
      selectedRegion?.name,
    ],
  );
  const onSaveClickHandler = async () => {
    //validateFields
    if (!selectedCountry) {
      setHasErrorCountry(true);
      return;
    }
    if (!selectedRegion) {
      setHasErrorRegion(true);
      return;
    }
    if (!selectedCropDescription) {
      setHasErrorCropDescription(true);
      return;
    }

    //copyNutrientAnalysis
    if (displayValue === DisplayOption.Yes) {
      await deleteExistingCropDemandAnalysis();
    } else {
      if (existingCropDemandAnalysis && existingCropDemandAnalysis.length > 0) {
        if (selectedNutrient) {
          displayMessage(
            `${keyPrefix}.copyNutrientError`,
            selectedNutrient?.elementalName,
            'destructiveLight',
            'bottomRight',
            'Close',
          );
        } else {
          displaySnackbarMessage(
            t(`${keyPrefix}.copyNutrientsError`, {
              interpolation: { escapeValue: false },
            }),
            setDisplaySnackbar,
            'destructiveLight',
            'bottomRight',
            'Close',
          );
        }

        return;
      }
    }

    const newNutrientAnalysisData = createNewAnalysisMethods(selectedCropDemandAnalysesData);
    const newDemandAnalysisNutrients = await createNewDemandAnalysisNutrients(
      newNutrientAnalysisData,
    );
    await triggerBulkCreateCropDemandAnalysisNutrients({
      method: METHOD.POST,
      body: JSON.stringify(newDemandAnalysisNutrients),
    });
    displayMessage(`${keyPrefix}.copyNutrientMessage`, selectedNutrient?.elementalName);

    setShowDialog(false);
  };

  useEffect(() => {
    //fetch Crop Region data based on the selected Region
    const fetchCropRegionsData = async () => {
      try {
        await getCropRegions({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'regionId',
                value: selectedRegion?.id,
                type: FilterType.EQ,
              },
            ],
          }),
        });
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    if (selectedRegion) fetchCropRegionsData();
  }, [selectedCountry, selectedRegion]);

  useEffect(() => {
    // get existing nutrient analysis data for the selected Crop Region
    if (cropDemandAnalysesData) {
      const existingNutrientAnalysisData = selectedNutrient
        ? cropDemandAnalysesData.filter((el) => el.nutrientId === selectedNutrient?.id)
        : cropDemandAnalysesData;
      setExistingCropDemandAnalysis(existingNutrientAnalysisData);
      //get existing nutrients for the specific analysis in order to save them in the state
      const getCropDemandAnalysisNutrients = (
        analysisData: CropDemandAnalysis[],
      ): Promise<PaginatedResponse<CropDemandAnalysisNutrient[]> | undefined> =>
        triggerGetCropDemandAnalysisNutrients({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'cropDemandAnalysisId',
                value: analysisData.map((analysis) => analysis.id).join(','),
                type: FilterType.IN,
              },
            ],
          }),
        });

      getCropDemandAnalysisNutrients(existingNutrientAnalysisData).then((nutrient) => {
        if (nutrient) {
          setAnalysesNutrientsToDelete(nutrient?.entities);
        }
      });

      getCropDemandAnalysisNutrients(selectedCropDemandAnalysesData).then((nutrient) => {
        if (nutrient) {
          setAnalysesNutrientsToCreateArray(nutrient?.entities);
        }
      });
    }
  }, [
    cropDemandAnalysesData?.length,
    selectedNutrient?.id,
    selectedCropDemandAnalysesData?.length,
  ]);

  if (!showDialog) return null;

  return (
    <ModalDialog
      isOpen={showDialog}
      onChange={setShowDialog}
      title={
        selectedNutrient
          ? t(`${keyPrefix}.copyNutrientTitle`, {
              nutrient: selectedNutrient?.elementalName,
            })
          : t(`${keyPrefix}.copyNutrientsTitle`)
      }
      titleHeadline={t(`${keyPrefix}.location`, {
        selectedLocation: `${selectedRegionContext?.name}, ${selectedCountryContext?.name}`,
      })}
      css={{ crossIcon: { padding: '0' } }}
      dataCy={`copyNutrient-${selectedNutrient ? selectedNutrient?.elementalName : 'all'}-dialog`}
      onSaveClick={onSaveClickHandler}
      actionType={ACTIONS.ADD}
    >
      <SelectWrapper dataCy='country-select-copy-nutrient'>
        <Select
          ariaLabel={t(`${keyPrefix}.countryDropdown.ariaLabel`)}
          cover='outline'
          css={{ width: '100%' }}
          value={selectedCountry?.id}
          items={countriesDropdownData}
          placeholder={t(`${keyPrefix}.countryDropdown.placeholder`)}
          position='popper'
          size='n'
          variant={hasErrorCountry ? 'error' : 'default'}
          helper-text={hasErrorCountry ? t('polaris.error.isRequired', { name: 'Country' }) : null}
          onChange={countryChangeHandler}
          label={
            selectedCountry !== null ? t(`${keyPrefix}.countryDropdown.placeholder`) : undefined
          }
        />
      </SelectWrapper>
      <SelectWrapper dataCy='region-select-copy-nutrient'>
        <Select
          ariaLabel={t(`${keyPrefix}.regionDropdown.ariaLabel`)}
          cover={areFieldsDisabled ? 'fill' : 'outline'}
          css={{ width: '100%' }}
          items={regionsDropdownData}
          value={selectedRegion?.id}
          placeholder={t(`${keyPrefix}.regionDropdown.placeholder`)}
          position='popper'
          size='n'
          variant={hasErrorRegion ? 'error' : 'default'}
          helper-text={hasErrorRegion ? t('polaris.error.isRequired', { name: 'Region' }) : null}
          disabled={areFieldsDisabled}
          onChange={regionChange}
          label={selectedRegion !== null ? t(`${keyPrefix}.regionDropdown.placeholder`) : undefined}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='crop-select-copy-nutrient'>
        <Select
          ariaLabel={t(`${keyPrefix}.cropDropdown.ariaLabel`)}
          cover={areFieldsDisabled || !selectedRegion ? 'fill' : 'outline'}
          css={{ width: '100%' }}
          items={cropDropdownData}
          value={selectedCropDescription?.id}
          placeholder={t(`${keyPrefix}.cropDropdown.placeholder`)}
          position='popper'
          size='n'
          variant={hasErrorCropDescription ? 'error' : 'default'}
          helper-text={
            hasErrorCropDescription ? t('polaris.error.isRequired', { name: 'Crop' }) : null
          }
          disabled={areFieldsDisabled || !selectedRegion}
          onChange={cropChange}
          label={
            selectedCropDescription !== null
              ? t(`${keyPrefix}.cropDropdown.placeholder`)
              : undefined
          }
        />
      </SelectWrapper>
      <div className='radio-container'>
        <Title css={{ paddingBottom: '$x4' }} size='xs'>
          {selectedNutrient
            ? t(`${keyPrefix}.helperTextSingle`)
            : t(`${keyPrefix}.helperTextMultiple`)}
        </Title>
        <RadioButtonGroupStyled
          value={displayValue}
          onValueChange={setDisplayValue}
          aria-label='Display Option'
          data-cy='copy-analyses-nutrient-radio-btn-group'
        >
          {radioOptions.map((option, index) => (
            <RadioButtonWrapper key={option.value} checked={displayValue === option.value}>
              <Label
                className={option.value === DisplayOption.Yes ? 'label-color-contrast' : ''}
                htmlFor={`displayOption${index}`}
              >
                {option.label}
              </Label>
              <RadioButton
                value={option.value}
                id={`displayOption${index}`}
                data-cy={`copy-analyses-nutrient-radio-btn-${option.label.toLowerCase()}`}
              />
            </RadioButtonWrapper>
          ))}
        </RadioButtonGroupStyled>
      </div>
    </ModalDialog>
  );
};

export default CopyNutrientPopup;
