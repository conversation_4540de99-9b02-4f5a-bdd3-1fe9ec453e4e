import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import CopyNutrientPopup, { CopyNutrientPopupProps } from '../CopyNutrientPopup';
import { setupServer } from 'msw/node';
import {
  allUnitsHandler,
  cropDemandAnalysisHandler,
  cropDemandAnalysisNutrientBulkHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysesDataHandler,
  countriesHandler,
  regionsHandler,
  cropRegionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  mockAppProviderValue,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropRegionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  cropDemandAnalysisHandler,
  allUnitsHandler,
  cropDemandAnalysisNutrientBulkHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysesDataHandler,
);
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('CopyNutrientPopup', () => {
  const mockPropsSingle = {
    showDialog: true,
    setShowDialog: jest.fn(),
    selectedNutrient: 'Nitrogen',
    getBulkCropDemandAnalysisNutrients: jest.fn(() => Promise.resolve([[]])),
  } as unknown as CopyNutrientPopupProps;

  const mockPropsMultiple = {
    showDialog: true,
    setShowDialog: jest.fn(),
    getBulkCropDemandAnalysisNutrients: jest.fn(() => Promise.resolve([[]])),
  } as unknown as CopyNutrientPopupProps;

  const mockClosed = {
    ...mockPropsSingle,
    showDialog: false,
  };

  it('does not render', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockClosed} />
      </AppContext.Provider>,
    );
    expect(component).toMatchSnapshot();
    const dialog = component.findAllByTestId('copyNutrient-N-dialog');
    dialog.then((dialog) => expect(dialog).toBeUndefined());
  });

  it('renders without crashing', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsSingle} />
      </AppContext.Provider>,
    );
    expect(component).toMatchSnapshot();
  });

  it('renders without crashing copy all', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsMultiple} />
      </AppContext.Provider>,
    );
    expect(component).toMatchSnapshot();
  });

  it('handles country change', async () => {
    const { getByLabelText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsSingle} />
      </AppContext.Provider>,
    );
    const countryDropdown = getByLabelText(
      'polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.regionDropdown.ariaLabel',
    );

    fireEvent.change(countryDropdown, { target: { value: 'Germany' } });
    expect(countryDropdown).toHaveValue('Germany');
  });

  it('handles region change', async () => {
    const { getByLabelText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsSingle} />
      </AppContext.Provider>,
    );
    const regionDropdown = getByLabelText(
      'polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.regionDropdown.ariaLabel',
    );

    expect(regionDropdown).toHaveAttribute('aria-disabled', 'true');
  });

  it('Checks that crop is disabled', async () => {
    const { getByLabelText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsSingle} />
      </AppContext.Provider>,
    );
    const cropDropdown = getByLabelText(
      'polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup.cropDropdown.ariaLabel',
    );

    expect(cropDropdown).toHaveAttribute('aria-disabled', 'true');
  });

  it('handles radio change', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsSingle} />
      </AppContext.Provider>,
    );
    const radioButtonNo = getByTestId(
      'copy-analyses-nutrient-radio-btn-polaris.cnpdetails.soilanalysis.nutrientlist.nutrientpopup.no',
    );
    const radioButtonYes = getByTestId(
      'copy-analyses-nutrient-radio-btn-polaris.cnpdetails.soilanalysis.nutrientlist.nutrientpopup.yes',
    );
    fireEvent.click(radioButtonNo);
    fireEvent.click(radioButtonYes);
  });

  it('handles Save click', async () => {
    const { getByTestId, getByText } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <CopyNutrientPopup {...mockPropsSingle} />
      </AppContext.Provider>,
    );
    const saveButton = getByTestId('dialog-save-btn');

    fireEvent.click(saveButton);

    expect(getByText('polaris.error.isRequired')).toBeInTheDocument();
  });
});
