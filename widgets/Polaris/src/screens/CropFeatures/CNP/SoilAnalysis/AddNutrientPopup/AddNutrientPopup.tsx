import React, { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, <PERSON>, Dialog, IconButton } from '@yaradigitallabs/ahua-react';
import { Nutrient } from '@common/types';
import {
  ContentStyle,
  GridContainer,
  PopupHeader,
  TailWrapperStyle,
  TilesContainer,
} from './AddNutrientPopup.styled';
import NutrientTile from '../NutrientTile/NutrientTile';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { displaySnackbarMessage } from '@widgets/Polaris/utils/successSnackbar/successSnackbar';

interface AddNutrientPopupProps {
  showDialog: boolean;
  onOpenChange: (isOpen: boolean) => void;
  nutrientsData: Nutrient[];
  onSave: (nutrient: Nutrient[]) => void;
  alreadyAddedNutrients: string[] | undefined;
}

const AddNutrientPopup: FC<AddNutrientPopupProps> = ({
  showDialog,
  onOpenChange,
  nutrientsData,
  alreadyAddedNutrients,
  onSave,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cnpDetails.soilAnalysis.nutrientList.nutrientPopup',
  });
  const [selectedNutrients, setSelectedNutrients] = useState<Nutrient[]>([]);
  const { setDisplaySnackbar } = useSnackbar();

  const toggleNutrientSelection = (nutrient: Nutrient) => {
    setSelectedNutrients((prev) => {
      const isSelected = prev.find((n) => n.id === nutrient.id);
      if (isSelected) {
        return prev.filter((n) => n.id !== nutrient.id); // Deselect
      } else {
        return [...prev, nutrient]; // Select
      }
    });
  };

  // Categorize nutrients
  const soilNutrients = nutrientsData.filter((n) => n.isDemandNutrient);
  const soilCharacteristics = nutrientsData.filter((n) => !n.isDemandNutrient);

  const handleSave = () => {
    onSave(selectedNutrients);
    onOpenChange(false);
    setSelectedNutrients([]);
    if (selectedNutrients.length > 0) {
      displaySnackbarMessage(
        t('snackbarMessage', {
          selectedNutrientsLength: selectedNutrients.length,
        }),
        setDisplaySnackbar,
      );
    }
  };

  if (!showDialog) return null;

  return (
    <Dialog open={showDialog} onOpenChange={onOpenChange}>
      <Dialog.Content data-cy='nutrient-popup-content' css={ContentStyle}>
        <PopupHeader>
          <Dialog.Title data-cy='nutrient-popup'>{t('title')}</Dialog.Title>
          <Dialog.Close>
            <IconButton
              data-cy='nutrient-popup-close-btn'
              icon='Close'
              colorConcept={'brand'}
              size='xs'
            />
          </Dialog.Close>
        </PopupHeader>
        <Card.Divider />
        <TilesContainer>
          {/* Soil Nutrients Group */}
          <GridContainer>
            {soilNutrients.map((nutrient, index) => (
              <NutrientTile
                key={nutrient.id}
                nutrient={nutrient}
                onSelect={() => toggleNutrientSelection(nutrient)}
                selected={selectedNutrients.some((n) => n.id === nutrient.id)}
                disabled={alreadyAddedNutrients?.includes(nutrient.id)}
                variant='popup'
                dataCy={`soil-nutrient-tile-${index}`}
              />
            ))}
          </GridContainer>
          <Card.Divider />
          {/* Soil Characteristics Group */}
          <GridContainer>
            {soilCharacteristics.map((nutrient, index) => (
              <NutrientTile
                key={nutrient.id}
                nutrient={nutrient}
                onSelect={() => toggleNutrientSelection(nutrient)}
                selected={selectedNutrients.some((n) => n.id === nutrient.id)}
                disabled={alreadyAddedNutrients?.includes(nutrient.id)}
                variant='popup'
                dataCy={`soil-characteristics-tile-${index}`}
              />
            ))}
          </GridContainer>
        </TilesContainer>
        <Card.Divider />
        <TailWrapperStyle>
          <Button onClick={handleSave} data-cy='nutrient-popup-save-btn'>
            {t('saveButton')}
          </Button>
        </TailWrapperStyle>
      </Dialog.Content>
    </Dialog>
  );
};

export default AddNutrientPopup;
