import { styled } from '@yaradigitallabs/ahua-react';

export const ContentStyle = {
  height: '872px',
  maxHeight: '100%',
  maxWidth: '100%',
  width: '965px',
};

export const PopupHeader = styled('div', {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  width: '100%',
  padding: '$x3 $x4',
  boxSizing: 'border-box',
  '& h2': {
    fontWeight: 700,
    textAlign: 'left',
  },
});

export const TilesContainer = styled('div', {
  overflow: 'auto',
  padding: '$x1 $x24 $x3',
  width: '100%',
  boxSizing: 'border-box',
});

export const GridContainer = styled('div', {
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(90px, 1fr))',
  gap: '$x4',
  padding: '$x6 0',
});

export const TailWrapperStyle = styled('div', {
  display: 'flex',
  justifyContent: 'flex-end',
  padding: '12px 16px',
  boxSizing: 'border-box',
  width: '100%',
});
