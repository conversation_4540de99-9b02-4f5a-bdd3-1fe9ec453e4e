import React from 'react';
import AddNutrientPopup from '../AddNutrientPopup';
import { render, screen } from '@testing-library/react';
import { nutrientMock, nutrientsMock } from '@common/mocks';
import { userEvent } from '@testing-library/user-event';
import { SnackBarType } from '@common/types';
import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

describe('AddNutrientPopup Component', () => {
  const mockOnSave = jest.fn();
  const mockOnOpenChange = jest.fn();
  const mockSetDisplaySnackbar = jest.fn();
  const snackbarInitialState: SnackBarType = {
    open: false,
    colorConcept: 'successLight',
    duration: 3000,
    placement: 'bottomRight',
    description: '',
    useClose: true,
  };

  beforeEach(() => {
    mockSetDisplaySnackbar.mockReset();
    mockOnSave.mockReset();
    jest.clearAllMocks();
  });

  it('does not render when showDialog is false', () => {
    render(
      <AddNutrientPopup
        showDialog={false}
        onOpenChange={mockOnOpenChange}
        nutrientsData={[]}
        alreadyAddedNutrients={[]}
        onSave={mockOnSave}
      />,
    );
    expect(screen.queryByTestId('nutrient-popup-content')).not.toBeInTheDocument();
  });

  it('renders and displays nutrients when showDialog is true', () => {
    render(
      <AddNutrientPopup
        showDialog={true}
        onOpenChange={mockOnOpenChange}
        nutrientsData={nutrientsMock}
        alreadyAddedNutrients={[]}
        onSave={mockOnSave}
      />,
    );
    expect(screen.getByText('Sulphur')).toBeInTheDocument();
  });

  it('handles nutrient selection and triggers onSave with selected nutrients', async () => {
    const user = userEvent.setup();

    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: snackbarInitialState,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AddNutrientPopup
          showDialog={true}
          onOpenChange={mockOnOpenChange}
          nutrientsData={nutrientsMock}
          alreadyAddedNutrients={[]}
          onSave={mockOnSave}
        />
      </SnackbarContext.Provider>,
    );

    await user.click(screen.getByText('Sulphur'));
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));

    expect(mockOnSave).toHaveBeenCalledWith([nutrientMock]);
  });

  it('handles nutrient deselection and ensures deselected nutrients are not included in onSave', async () => {
    const user = userEvent.setup();

    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: snackbarInitialState,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AddNutrientPopup
          showDialog={true}
          onOpenChange={mockOnOpenChange}
          nutrientsData={nutrientsMock}
          alreadyAddedNutrients={[]}
          onSave={mockOnSave}
        />
      </SnackbarContext.Provider>,
    );

    // Simulate selecting Sulphur
    await user.click(screen.getByText('Sulphur'));
    // Simulate selecting Iron
    await user.click(screen.getByText('Iron'));
    // Simulate deselecting Sulphur
    await user.click(screen.getByText('Sulphur'));
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));

    expect(mockOnSave).toHaveBeenCalledWith([nutrientsMock[1]]);
  });

  it('only displays snackbar message when nutrients are selected', async () => {
    const user = userEvent.setup();
    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: snackbarInitialState,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AddNutrientPopup
          showDialog={true}
          onOpenChange={mockOnOpenChange}
          nutrientsData={nutrientsMock}
          alreadyAddedNutrients={[]}
          onSave={mockOnSave}
        />
      </SnackbarContext.Provider>,
    );

    // No nutrients selected, simulate save action
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));
    expect(mockSetDisplaySnackbar).not.toHaveBeenCalled();

    // Now simulate selecting a nutrient and saving again
    await user.click(screen.getByText('Sulphur'));
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));

    expect(mockSetDisplaySnackbar).toHaveBeenCalled();
    expect(mockSetDisplaySnackbar.mock.calls[0][0]).toMatchObject({
      colorConcept: 'successLight',
      duration: 3000,
      icon: 'Check',
      open: true,
      placement: 'bottomRight',
      title: 'snackbarMessage',
    });
  });

  // Test for Soil characteristic
  it('handles selection and deselection of soil characteristics nutrients', async () => {
    const user = userEvent.setup();
    render(
      <SnackbarContext.Provider
        value={{
          displaySnackbar: snackbarInitialState,
          setDisplaySnackbar: mockSetDisplaySnackbar,
        }}
      >
        <AddNutrientPopup
          showDialog={true}
          onOpenChange={mockOnOpenChange}
          nutrientsData={nutrientsMock}
          alreadyAddedNutrients={[]}
          onSave={mockOnSave}
        />
      </SnackbarContext.Provider>,
    );

    // Select "Buffer pH"
    await user.click(screen.getByText('Buffer pH'));
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));

    // Expect onSave to have been called with "Buffer pH" selected
    expect(mockOnSave).toHaveBeenCalledWith([expect.objectContaining({ name: 'Buffer pH' })]);

    // Reset mock functions before next interaction
    mockOnSave.mockReset();

    // Deselect "Buffer pH"
    await user.click(screen.getByText('Buffer pH'));
    await user.click(screen.getByTestId('nutrient-popup-save-btn'));
  });
});
