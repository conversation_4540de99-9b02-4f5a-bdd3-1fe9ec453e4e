import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  useCropDemandAnalysisNutrients,
  useBulkDeleteCropDemandAnalysisNutrients,
  filterUnitsByNutrientElementName,
  useGetAllElementUnits,
  useBulkDeleteCropDemandAnalyses,
  useCropDemandAnalyses,
  useAnalysisMethods,
} from '@polaris-hooks/index';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { ComboboxOption } from '@yaradigitallabs/ahua-react';
import { METHOD } from '@common/constants';
import { FilterType, Translation } from '@widgets/Polaris/src/types';

const SoilAnalysisLogic = ({ keyPrefix, t }: Translation) => {
  const [showDeleteMethodDialog, setShowDeleteMethodDialog] = useState<boolean>(false);
  const [showDeleteMethodAlertDialog, setShowDeleteMethodAlertDialog] = useState<boolean>(false);
  const {
    selectedNutrient,
    selectedCountryUnits,
    cropRegion,
    cropDemandAnalyses: cropDemandAnalysesData,
    methods: { filterCropDemandAnalyses, setCropDemandAnalyses },
  } = useAppContext();

  const nutrientID = selectedNutrient?.id;
  const nutrientElement = selectedNutrient?.elementalName;
  const { trigger: triggerGetCropDemandAnalyses } = useCropDemandAnalyses();
  const selectedCropDemandAnalysesData = cropDemandAnalysesData?.filter(
    (analysis) => analysis.nutrientId === nutrientID,
  );
  const selectedCropDemandAnalysisIds = useMemo(
    () => selectedCropDemandAnalysesData?.map((item) => item.analysisMethodId),
    [selectedCropDemandAnalysesData?.length],
  );
  const selectedAnalysesMethodIds = selectedCropDemandAnalysesData?.map(
    (item) => item.analysisMethodId,
  );

  const elementUnits = useGetAllElementUnits(nutrientElement, selectedCountryUnits);
  const baseUnits = filterUnitsByNutrientElementName(selectedCountryUnits, nutrientElement);
  const { analysisMethodsData, trigger: triggerGetAnalysisMethods } = useAnalysisMethods();

  const { trigger: triggerGetCropDemandAnalysisNutrients } = useCropDemandAnalysisNutrients();
  const { triggerBulkDeleteCropDemandAnalyses } = useBulkDeleteCropDemandAnalyses();
  const { triggerBulkPermDeleteCropDemandAnalysisNutrients } =
    useBulkDeleteCropDemandAnalysisNutrients();
  const disableDeleteSelection =
    selectedCropDemandAnalysesData && selectedCropDemandAnalysesData.length <= 1;

  const handleOpenDeleteMethodDialog = () => {
    setShowDeleteMethodDialog(true);
  };
  const { setDisplaySnackbar } = useSnackbar();

  const addDeleteMenuItemList = [
    {
      icon: 'Plus',
      title: t(`${keyPrefix}.dropdownItems.addTitle`),
      dataCy: 'add-analysis-method-btn',
      onClick: () => console.log('TODO: click add'),
    },
    {
      icon: 'Delete',
      title: t(`${keyPrefix}.dropdownItems.deleteTitle`),
      subTitle: t(`${keyPrefix}.dropdownItems.deleteSubtitle`),
      dataCy: 'delete-analysis-method-btn',
      disabled: disableDeleteSelection,
      onClick: handleOpenDeleteMethodDialog,
    },
  ];

  const handleDeleteAnalysisMethod = useCallback(
    (selectedAnalysisMethodsToDelete: ComboboxOption[]): void => {
      if (selectedAnalysisMethodsToDelete.length === selectedAnalysesMethodIds?.length) {
        setShowDeleteMethodAlertDialog(true);
      } else {
        deleteSelectedAnalysisMethod(selectedAnalysisMethodsToDelete);
      }
    },
    [selectedAnalysesMethodIds?.length],
  );

  const deleteSelectedAnalysisMethod = useCallback(
    async (selectedAnalysisMethodsToDelete: ComboboxOption[]): Promise<void> => {
      // Get demand analysis methods base on selected analysis methods
      const analysisMethodIds = selectedAnalysisMethodsToDelete.map((el) => {
        return el.value;
      });
      const analysesDemandMethodsToDelete = selectedCropDemandAnalysesData?.filter((item) =>
        analysisMethodIds.includes(item.analysisMethodId),
      );
      // Delete demand analysis method nutrients
      const response = await triggerGetCropDemandAnalysisNutrients({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'cropDemandAnalysisId',
              value: analysesDemandMethodsToDelete.map((analysis) => analysis.id).join(','),
              type: FilterTypeß.IN,
            },
          ],
        }),
      });

      const nutrientsToDelete = response?.entities.flat(1);
      await triggerBulkPermDeleteCropDemandAnalysisNutrients({
        method: METHOD.DELETE,
        body: JSON.stringify({
          ids: nutrientsToDelete?.map((el) => el.id),
        }),
      });

      // Delete demand analysis method
      const deletedCropDemandAnalyses = await triggerBulkDeleteCropDemandAnalyses(
        analysesDemandMethodsToDelete?.map((el) => el.id),
      );

      if (deletedCropDemandAnalyses && deletedCropDemandAnalyses.length > 0) {
        setDisplaySnackbar({
          title:
            deletedCropDemandAnalyses.length > 1
              ? t(`${keyPrefix}.deleteAnalysisMethodPopup.snackbarMessagePlural`, {
                  count: deletedCropDemandAnalyses.length,
                })
              : t(`${keyPrefix}.deleteAnalysisMethodPopup.snackbarMessageSingular`, {
                  count: deletedCropDemandAnalyses.length,
                }),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          // duration: 3000,
          open: true,
        });
        filterCropDemandAnalyses(deletedCropDemandAnalyses);
      }
    },
    [selectedAnalysesMethodIds?.length],
  );

  useEffect(() => {
    if (selectedCropDemandAnalysisIds && selectedCropDemandAnalysisIds?.length) {
      triggerGetAnalysisMethods({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'id',
              value: selectedCropDemandAnalysisIds.join(','),
              type: FilterType.IN,
            },
          ],
        }),
      });
    }
  }, [selectedCropDemandAnalysisIds?.length]);

  useEffect(() => {
    if (cropRegion) {
      triggerGetCropDemandAnalyses({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'cropRegionId',
              value: cropRegion?.id,
              type: FilterType.EQ,
            },
          ],
        }),
      }).then((data) => {
        setCropDemandAnalyses(data?.entities || []);
      });
    }
  }, [cropRegion]);

  return {
    showDeleteMethodDialog,
    setShowDeleteMethodDialog,
    showDeleteMethodAlertDialog,
    setShowDeleteMethodAlertDialog,
    nutrientElement,
    baseUnits,
    elementUnits,
    analysisMethodsData,
    selectedCropDemandAnalysesData,
    selectedAnalysesMethodIds,
    handleOpenDeleteMethodDialog,
    handleDeleteAnalysisMethod,
    deleteSelectedAnalysisMethod,
    addDeleteMenuItemList,
  };
};

export default SoilAnalysisLogic;
