import React from 'react';
import NutrientsList from './NutrientsList/NutrientsList';
import { AddContainer } from './SoilAnalysis.styled';
import { useTranslation } from 'react-i18next';
import { Subtitle } from '@yaradigitallabs/ahua-react';
import { AnalysisMethodCollapsible } from '@widgets/Polaris/src/components/AnalysisMethodCollapsible/AnalysisMethodCollapsible';
import { ConfirmationDialog, MeatballsMenu } from '@widgets/Polaris/src/components';
import SoilAnalysisLogic from './SoilAnalysisLogic';
import { DeleteAnalysisMethodPopup } from '../../shared/DeleteAnalysisMethodPopup';

const SoilAnalysis = () => {
  const { t } = useTranslation();
  const keyPrefix = 'polaris.cnpDetails.planConfiguration.soilAnalysis';

  const {
    showDeleteMethodDialog,
    setShowDeleteMethodDialog,
    showDeleteMethodAlertDialog,
    setShowDeleteMethodAlertDialog,
    nutrientElement,
    baseUnits,
    elementUnits,
    analysisMethodsData,
    selectedCropDemandAnalysesData,
    handleDeleteAnalysisMethod,
    addDeleteMenuItemList,
  } = SoilAnalysisLogic({ keyPrefix, t });

  return (
    <>
      <NutrientsList />
      {nutrientElement && selectedCropDemandAnalysesData?.length > 0 && (
        <AddContainer>
          <Subtitle size='s'>
            {`${nutrientElement}${t(`${keyPrefix}.addTitle`, {
              count: selectedCropDemandAnalysesData?.length,
            })}`}
          </Subtitle>
          <MeatballsMenu
            itemList={addDeleteMenuItemList}
            triggerDataCy={'analysis-method-edit-menu-btn'}
          />
        </AddContainer>
      )}
      <DeleteAnalysisMethodPopup
        showDialog={showDeleteMethodDialog}
        onOpenChange={setShowDeleteMethodDialog}
        analysisMethods={analysisMethodsData?.entities}
        onSave={(selectedAnalysisMethodsToDelete) => {
          handleDeleteAnalysisMethod(selectedAnalysisMethodsToDelete);
        }}
      />
      <ConfirmationDialog
        open={showDeleteMethodAlertDialog}
        title={t(`polaris.deleteAnalysisMethodPopup.deleteAlertDialog.title`)}
        description={t(`polaris.deleteAnalysisMethodPopup.deleteAlertDialog.description`)}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={t('polaris.common.ok')}
        onOk={() => setShowDeleteMethodAlertDialog(false)}
        hideCancelButton
      />
      {selectedCropDemandAnalysesData?.map((nutrientAnalysis, index) => (
        <AnalysisMethodCollapsible
          key={nutrientAnalysis.id}
          index={index}
          cropDemandAnalysis={nutrientAnalysis}
          elementUnits={elementUnits}
          baseUnits={baseUnits}
          keyPrefix={keyPrefix}
        />
      ))}
    </>
  );
};

export default SoilAnalysis;
