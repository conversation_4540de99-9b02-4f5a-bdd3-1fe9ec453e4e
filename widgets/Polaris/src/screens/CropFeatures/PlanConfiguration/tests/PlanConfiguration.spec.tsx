import React from 'react';
import { act, render, screen } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { BrowserRouter as Router } from 'react-router-dom';
import { NavbarProvider } from '@libs/nav-context';
import PlanConfiguration from '../PlanConfiguration';
import {
  mockCNPAppProviderValue,
  mockCMMMAppProviderValue,
  mockFertigationAppProviderValue,
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
} from '@common/mocks';

import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import {
  CMMM_PLAN_CONFIGURATION_TABS,
  CNP_PLAN_CONFIGURATION_TABS,
  FERTIGATION_PLAN_CONFIGURATION_TABS,
} from '../../shared/constants';
import { getTranslationKeyFromPath } from '@widgets/Polaris/utils';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  cnpPartnersHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

jest.mock('@polaris-hooks/useURLPath/useUrlPath', () => ({
  useUpdatePlanConfigUrlPath: jest.fn(),
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

const mockStateToLocalStorageValue = ['plan-name', jest.fn()];
jest.mock('uuid', () => ({
  v4: () => 'mocked-uuid',
}));

jest.mock('@widgets/Polaris/utils/stateToLocalStorage/useStateToLocalStorage', () => ({
  useStateToLocalStorage: jest.fn().mockImplementation(() => {
    return mockStateToLocalStorageValue;
  }),
}));

describe('CNP, plan configuration renders', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });

  it('should initialize the context with default values and render the CNP Details page', async () => {
    mockStateToLocalStorageValue[0] = 'soil-based-nutrition-plan';
    let component;
    await act(async () => {
      component = render(
        <AppContext.Provider value={mockCNPAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <PlanConfiguration />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(component).toMatchSnapshot();

    expect(screen.getByTestId('plan-configuration-content')).toBeInTheDocument();
    expect(screen.getByTestId('plan-configuration-title')).toBeInTheDocument();
    const parametersKey = CNP_PLAN_CONFIGURATION_TABS?.PARAMETERS;
    if (!parametersKey) {
      throw new Error('PARAMETERS key is not defined');
    }

    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(parametersKey)}`,
      ),
    ).toBeInTheDocument();

    const soilAnalysisKey = CNP_PLAN_CONFIGURATION_TABS.SOIL_ANALYSIS;
    if (!soilAnalysisKey) {
      throw new Error('SOIL_ANALYSIS key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(soilAnalysisKey)}`,
      ),
    ).toBeInTheDocument();

    const demandCalcKey = CNP_PLAN_CONFIGURATION_TABS.DEMAND_CALC;
    if (!demandCalcKey) {
      throw new Error('DEMAND_CALC key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(demandCalcKey)}`,
      ),
    ).toBeInTheDocument();

    const productRecKey = CNP_PLAN_CONFIGURATION_TABS.PRODUCT_REC;
    if (!productRecKey) {
      throw new Error('PRODUCT_REC key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(productRecKey)}`,
      ),
    ).toBeInTheDocument();
  });

  it('should initialize the context with default values and render the Cereal MMM Details page', async () => {
    mockStateToLocalStorageValue[0] = 'cereals-mmm';
    let component;
    await act(async () => {
      component = render(
        <AppContext.Provider value={mockCMMMAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <PlanConfiguration />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(component).toMatchSnapshot();

    expect(screen.getByTestId('plan-configuration-content')).toBeInTheDocument();
    expect(screen.getByTestId('plan-configuration-title')).toBeInTheDocument();
    const parametersKey = CMMM_PLAN_CONFIGURATION_TABS?.SOIL_ANALYSIS;
    if (!parametersKey) {
      throw new Error('SOIL_ANALYSIS key is not defined');
    }

    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(parametersKey)}`,
      ),
    ).toBeInTheDocument();

    const demandCalcKey = CMMM_PLAN_CONFIGURATION_TABS.DEMAND_CALC;
    if (!demandCalcKey) {
      throw new Error('DEMAND_CALC key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(demandCalcKey)}`,
      ),
    ).toBeInTheDocument();

    const organicFertKey = CMMM_PLAN_CONFIGURATION_TABS.ORGANIC_FERTILISERS;
    if (!organicFertKey) {
      throw new Error('ORGANIC_FERTILISERS key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(organicFertKey)}`,
      ),
    ).toBeInTheDocument();

    const nSplittingKey = CMMM_PLAN_CONFIGURATION_TABS.N_SPLITTING_SCHEDULE;
    if (!nSplittingKey) {
      throw new Error('N_SPLITTING_SCHEDULE key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(nSplittingKey)}`,
      ),
    ).toBeInTheDocument();

    const productRecKey = CMMM_PLAN_CONFIGURATION_TABS.PRODUCT_REC;
    if (!productRecKey) {
      throw new Error('PRODUCT_REC key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(productRecKey)}`,
      ),
    ).toBeInTheDocument();
  });

  it('should initialize the context with default values and render the Fertigation Details page', async () => {
    mockStateToLocalStorageValue[0] = 'fertigation-plan';
    let component;
    await act(async () => {
      component = render(
        <AppContext.Provider value={mockFertigationAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <PlanConfiguration />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(component).toMatchSnapshot();

    expect(screen.getByTestId('plan-configuration-content')).toBeInTheDocument();
    expect(screen.getByTestId('plan-configuration-title')).toBeInTheDocument();

    const soilAnalysisKey = FERTIGATION_PLAN_CONFIGURATION_TABS?.SOIL_ANALYSIS;
    if (!soilAnalysisKey) {
      throw new Error('SOIL_ANALYSIS key is not defined');
    }

    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(soilAnalysisKey)}`,
      ),
    ).toBeInTheDocument();

    const leafAnalysisKey = FERTIGATION_PLAN_CONFIGURATION_TABS?.LEAF_ANALYSIS;
    if (!leafAnalysisKey) {
      throw new Error('LEAF_ANALYSIS key is not defined');
    }

    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(leafAnalysisKey)}`,
      ),
    ).toBeInTheDocument();

    const waterAnalysisKey = FERTIGATION_PLAN_CONFIGURATION_TABS?.WATER_ANALYSIS;
    if (!waterAnalysisKey) {
      throw new Error('WATER_ANALYSIS key is not defined');
    }

    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(waterAnalysisKey)}`,
      ),
    ).toBeInTheDocument();

    const demandCalcKey = FERTIGATION_PLAN_CONFIGURATION_TABS.DEMAND_CALC;
    if (!demandCalcKey) {
      throw new Error('DEMAND_CALC key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(demandCalcKey)}`,
      ),
    ).toBeInTheDocument();

    const splittingKey = FERTIGATION_PLAN_CONFIGURATION_TABS.SPLITTING_SCHEDULE;
    if (!splittingKey) {
      throw new Error('SPLITTING_SCHEDULE key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(splittingKey)}`,
      ),
    ).toBeInTheDocument();

    const productRecKey = FERTIGATION_PLAN_CONFIGURATION_TABS.PRODUCT_REC;
    if (!productRecKey) {
      throw new Error('PRODUCT_REC key is not defined');
    }
    expect(
      screen.getByTestId(
        `plan-configuration-tab-button-${getTranslationKeyFromPath(productRecKey)}`,
      ),
    ).toBeInTheDocument();

    const organicFertKey = FERTIGATION_PLAN_CONFIGURATION_TABS.ORGANIC_FERTILISERS ?? undefined;
    function assertKeyExists(key: string | undefined, keyName: string) {
      if (!key) {
        throw new Error(`${keyName} key is not defined`);
      }
    }
    expect(() => {
      assertKeyExists(organicFertKey, 'ORGANIC_FERTILISERS');
    }).toThrow('ORGANIC_FERTILISERS key is not defined');
  });
});
