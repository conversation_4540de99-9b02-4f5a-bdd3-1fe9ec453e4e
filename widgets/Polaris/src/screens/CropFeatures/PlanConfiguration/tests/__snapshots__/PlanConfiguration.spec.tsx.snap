// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CNP, plan configuration renders should initialize the context with default values and render the CNP Details page 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-evOjxA"
        data-cy="plan-configuration-content"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ihsbtnx-css"
          data-cy="plan-configuration-title"
        >
          cnpDetails.title
        </h1>
        <div
          class="c-jtiEzY"
          data-orientation="horizontal"
          dir="ltr"
        >
          <div
            aria-orientation="horizontal"
            class="c-bVBcpu c-bVBcpu-ihqLmXl-css"
            data-orientation="horizontal"
            role="tablist"
            style="outline: none;"
            tabindex="0"
          >
            <button
              aria-controls="radix-:r0:-content-Parameters"
              aria-selected="true"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-parameters"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="active"
              id="radix-:r0:-trigger-Parameters"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cnpDetails.planConfiguration.tabs.parameters
            </button>
            <button
              aria-controls="radix-:r0:-content-Soil analysis"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-soilAnalysis"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r0:-trigger-Soil analysis"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cnpDetails.planConfiguration.tabs.soilAnalysis
            </button>
            <button
              aria-controls="radix-:r0:-content-Demand calculations"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-demandCalculations"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r0:-trigger-Demand calculations"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cnpDetails.planConfiguration.tabs.demandCalculations
            </button>
            <button
              aria-controls="radix-:r0:-content-Product recommendations"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-productRecommendations"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r0:-trigger-Product recommendations"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cnpDetails.planConfiguration.tabs.productRecommendations
            </button>
          </div>
          <div
            aria-labelledby="radix-:r0:-trigger-Parameters"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-parameters"
            data-orientation="horizontal"
            data-state="active"
            hidden=""
            id="radix-:r0:-content-Parameters"
            role="tabpanel"
            style="animation-duration: 0s;"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r0:-trigger-Soil analysis"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-soilAnalysis"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r0:-content-Soil analysis"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r0:-trigger-Demand calculations"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-demandCalculations"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r0:-content-Demand calculations"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r0:-trigger-Product recommendations"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-productRecommendations"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r0:-content-Product recommendations"
            role="tabpanel"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-evOjxA"
      data-cy="plan-configuration-content"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ihsbtnx-css"
        data-cy="plan-configuration-title"
      >
        cnpDetails.title
      </h1>
      <div
        class="c-jtiEzY"
        data-orientation="horizontal"
        dir="ltr"
      >
        <div
          aria-orientation="horizontal"
          class="c-bVBcpu c-bVBcpu-ihqLmXl-css"
          data-orientation="horizontal"
          role="tablist"
          style="outline: none;"
          tabindex="0"
        >
          <button
            aria-controls="radix-:r0:-content-Parameters"
            aria-selected="true"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-parameters"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="active"
            id="radix-:r0:-trigger-Parameters"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cnpDetails.planConfiguration.tabs.parameters
          </button>
          <button
            aria-controls="radix-:r0:-content-Soil analysis"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-soilAnalysis"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r0:-trigger-Soil analysis"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cnpDetails.planConfiguration.tabs.soilAnalysis
          </button>
          <button
            aria-controls="radix-:r0:-content-Demand calculations"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-demandCalculations"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r0:-trigger-Demand calculations"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cnpDetails.planConfiguration.tabs.demandCalculations
          </button>
          <button
            aria-controls="radix-:r0:-content-Product recommendations"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-productRecommendations"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r0:-trigger-Product recommendations"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cnpDetails.planConfiguration.tabs.productRecommendations
          </button>
        </div>
        <div
          aria-labelledby="radix-:r0:-trigger-Parameters"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-parameters"
          data-orientation="horizontal"
          data-state="active"
          hidden=""
          id="radix-:r0:-content-Parameters"
          role="tabpanel"
          style="animation-duration: 0s;"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r0:-trigger-Soil analysis"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-soilAnalysis"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r0:-content-Soil analysis"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r0:-trigger-Demand calculations"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-demandCalculations"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r0:-content-Demand calculations"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r0:-trigger-Product recommendations"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-productRecommendations"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r0:-content-Product recommendations"
          role="tabpanel"
          tabindex="0"
        />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`CNP, plan configuration renders should initialize the context with default values and render the Cereal MMM Details page 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-evOjxA"
        data-cy="plan-configuration-content"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ihsbtnx-css"
          data-cy="plan-configuration-title"
        >
          cmmmDetails.title
        </h1>
        <div
          class="c-jtiEzY"
          data-orientation="horizontal"
          dir="ltr"
        >
          <div
            aria-orientation="horizontal"
            class="c-bVBcpu c-bVBcpu-ihqLmXl-css"
            data-orientation="horizontal"
            role="tablist"
            style="outline: none;"
            tabindex="0"
          >
            <button
              aria-controls="radix-:r5:-content-Soil analysis"
              aria-selected="true"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-soilAnalysis"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="active"
              id="radix-:r5:-trigger-Soil analysis"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cmmmDetails.planConfiguration.tabs.soilAnalysis
            </button>
            <button
              aria-controls="radix-:r5:-content-Demand calculations"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-demandCalculations"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r5:-trigger-Demand calculations"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cmmmDetails.planConfiguration.tabs.demandCalculations
            </button>
            <button
              aria-controls="radix-:r5:-content-Organic fertilisers"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-organicFertilisers"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r5:-trigger-Organic fertilisers"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cmmmDetails.planConfiguration.tabs.organicFertilisers
            </button>
            <button
              aria-controls="radix-:r5:-content-N splitting schedule"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-nSplittingSchedule"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r5:-trigger-N splitting schedule"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cmmmDetails.planConfiguration.tabs.nSplittingSchedule
            </button>
            <button
              aria-controls="radix-:r5:-content-Product recommendations"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-productRecommendations"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:r5:-trigger-Product recommendations"
              role="tab"
              tabindex="-1"
              type="button"
            >
              cmmmDetails.planConfiguration.tabs.productRecommendations
            </button>
          </div>
          <div
            aria-labelledby="radix-:r5:-trigger-Soil analysis"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-soilAnalysis"
            data-orientation="horizontal"
            data-state="active"
            hidden=""
            id="radix-:r5:-content-Soil analysis"
            role="tabpanel"
            style="animation-duration: 0s;"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r5:-trigger-Demand calculations"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-demandCalculations"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r5:-content-Demand calculations"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r5:-trigger-Organic fertilisers"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-organicFertilisers"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r5:-content-Organic fertilisers"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r5:-trigger-N splitting schedule"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-nSplittingSchedule"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r5:-content-N splitting schedule"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:r5:-trigger-Product recommendations"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-productRecommendations"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:r5:-content-Product recommendations"
            role="tabpanel"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-evOjxA"
      data-cy="plan-configuration-content"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ihsbtnx-css"
        data-cy="plan-configuration-title"
      >
        cmmmDetails.title
      </h1>
      <div
        class="c-jtiEzY"
        data-orientation="horizontal"
        dir="ltr"
      >
        <div
          aria-orientation="horizontal"
          class="c-bVBcpu c-bVBcpu-ihqLmXl-css"
          data-orientation="horizontal"
          role="tablist"
          style="outline: none;"
          tabindex="0"
        >
          <button
            aria-controls="radix-:r5:-content-Soil analysis"
            aria-selected="true"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-soilAnalysis"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="active"
            id="radix-:r5:-trigger-Soil analysis"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cmmmDetails.planConfiguration.tabs.soilAnalysis
          </button>
          <button
            aria-controls="radix-:r5:-content-Demand calculations"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-demandCalculations"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r5:-trigger-Demand calculations"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cmmmDetails.planConfiguration.tabs.demandCalculations
          </button>
          <button
            aria-controls="radix-:r5:-content-Organic fertilisers"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-organicFertilisers"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r5:-trigger-Organic fertilisers"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cmmmDetails.planConfiguration.tabs.organicFertilisers
          </button>
          <button
            aria-controls="radix-:r5:-content-N splitting schedule"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-nSplittingSchedule"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r5:-trigger-N splitting schedule"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cmmmDetails.planConfiguration.tabs.nSplittingSchedule
          </button>
          <button
            aria-controls="radix-:r5:-content-Product recommendations"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-productRecommendations"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:r5:-trigger-Product recommendations"
            role="tab"
            tabindex="-1"
            type="button"
          >
            cmmmDetails.planConfiguration.tabs.productRecommendations
          </button>
        </div>
        <div
          aria-labelledby="radix-:r5:-trigger-Soil analysis"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-soilAnalysis"
          data-orientation="horizontal"
          data-state="active"
          hidden=""
          id="radix-:r5:-content-Soil analysis"
          role="tabpanel"
          style="animation-duration: 0s;"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r5:-trigger-Demand calculations"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-demandCalculations"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r5:-content-Demand calculations"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r5:-trigger-Organic fertilisers"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-organicFertilisers"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r5:-content-Organic fertilisers"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r5:-trigger-N splitting schedule"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-nSplittingSchedule"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r5:-content-N splitting schedule"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:r5:-trigger-Product recommendations"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-productRecommendations"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:r5:-content-Product recommendations"
          role="tabpanel"
          tabindex="0"
        />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`CNP, plan configuration renders should initialize the context with default values and render the Fertigation Details page 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-evOjxA"
        data-cy="plan-configuration-content"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ihsbtnx-css"
          data-cy="plan-configuration-title"
        >
          fpDetails.title
        </h1>
        <div
          class="c-jtiEzY"
          data-orientation="horizontal"
          dir="ltr"
        >
          <div
            aria-orientation="horizontal"
            class="c-bVBcpu c-bVBcpu-ihqLmXl-css"
            data-orientation="horizontal"
            role="tablist"
            style="outline: none;"
            tabindex="0"
          >
            <button
              aria-controls="radix-:rb:-content-Soil analysis"
              aria-selected="true"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-soilAnalysis"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="active"
              id="radix-:rb:-trigger-Soil analysis"
              role="tab"
              tabindex="-1"
              type="button"
            >
              fpDetails.planConfiguration.tabs.soilAnalysis
            </button>
            <button
              aria-controls="radix-:rb:-content-Leaf analysis"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-leafAnalysis"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:rb:-trigger-Leaf analysis"
              role="tab"
              tabindex="-1"
              type="button"
            >
              fpDetails.planConfiguration.tabs.leafAnalysis
            </button>
            <button
              aria-controls="radix-:rb:-content-Water analysis"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-waterAnalysis"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:rb:-trigger-Water analysis"
              role="tab"
              tabindex="-1"
              type="button"
            >
              fpDetails.planConfiguration.tabs.waterAnalysis
            </button>
            <button
              aria-controls="radix-:rb:-content-Demand calculations"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-demandCalculations"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:rb:-trigger-Demand calculations"
              role="tab"
              tabindex="-1"
              type="button"
            >
              fpDetails.planConfiguration.tabs.demandCalculations
            </button>
            <button
              aria-controls="radix-:rb:-content-Splitting schedule"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-splittingSchedule"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:rb:-trigger-Splitting schedule"
              role="tab"
              tabindex="-1"
              type="button"
            >
              fpDetails.planConfiguration.tabs.splittingSchedule
            </button>
            <button
              aria-controls="radix-:rb:-content-Product recommendations"
              aria-selected="false"
              class="c-jTsjiB c-esaikO"
              data-cy="plan-configuration-tab-button-productRecommendations"
              data-orientation="horizontal"
              data-radix-collection-item=""
              data-state="inactive"
              id="radix-:rb:-trigger-Product recommendations"
              role="tab"
              tabindex="-1"
              type="button"
            >
              fpDetails.planConfiguration.tabs.productRecommendations
            </button>
          </div>
          <div
            aria-labelledby="radix-:rb:-trigger-Soil analysis"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-soilAnalysis"
            data-orientation="horizontal"
            data-state="active"
            hidden=""
            id="radix-:rb:-content-Soil analysis"
            role="tabpanel"
            style="animation-duration: 0s;"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:rb:-trigger-Leaf analysis"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-leafAnalysis"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:rb:-content-Leaf analysis"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:rb:-trigger-Water analysis"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-waterAnalysis"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:rb:-content-Water analysis"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:rb:-trigger-Demand calculations"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-demandCalculations"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:rb:-content-Demand calculations"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:rb:-trigger-Splitting schedule"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-splittingSchedule"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:rb:-content-Splitting schedule"
            role="tabpanel"
            tabindex="0"
          />
          <div
            aria-labelledby="radix-:rb:-trigger-Product recommendations"
            class="c-gaCPoV c-gaCPoV-iVDfaI-css"
            data-cy="plan-configuration-tab-content-productRecommendations"
            data-orientation="horizontal"
            data-state="inactive"
            hidden=""
            id="radix-:rb:-content-Product recommendations"
            role="tabpanel"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-evOjxA"
      data-cy="plan-configuration-content"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ihsbtnx-css"
        data-cy="plan-configuration-title"
      >
        fpDetails.title
      </h1>
      <div
        class="c-jtiEzY"
        data-orientation="horizontal"
        dir="ltr"
      >
        <div
          aria-orientation="horizontal"
          class="c-bVBcpu c-bVBcpu-ihqLmXl-css"
          data-orientation="horizontal"
          role="tablist"
          style="outline: none;"
          tabindex="0"
        >
          <button
            aria-controls="radix-:rb:-content-Soil analysis"
            aria-selected="true"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-soilAnalysis"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="active"
            id="radix-:rb:-trigger-Soil analysis"
            role="tab"
            tabindex="-1"
            type="button"
          >
            fpDetails.planConfiguration.tabs.soilAnalysis
          </button>
          <button
            aria-controls="radix-:rb:-content-Leaf analysis"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-leafAnalysis"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:rb:-trigger-Leaf analysis"
            role="tab"
            tabindex="-1"
            type="button"
          >
            fpDetails.planConfiguration.tabs.leafAnalysis
          </button>
          <button
            aria-controls="radix-:rb:-content-Water analysis"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-waterAnalysis"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:rb:-trigger-Water analysis"
            role="tab"
            tabindex="-1"
            type="button"
          >
            fpDetails.planConfiguration.tabs.waterAnalysis
          </button>
          <button
            aria-controls="radix-:rb:-content-Demand calculations"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-demandCalculations"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:rb:-trigger-Demand calculations"
            role="tab"
            tabindex="-1"
            type="button"
          >
            fpDetails.planConfiguration.tabs.demandCalculations
          </button>
          <button
            aria-controls="radix-:rb:-content-Splitting schedule"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-splittingSchedule"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:rb:-trigger-Splitting schedule"
            role="tab"
            tabindex="-1"
            type="button"
          >
            fpDetails.planConfiguration.tabs.splittingSchedule
          </button>
          <button
            aria-controls="radix-:rb:-content-Product recommendations"
            aria-selected="false"
            class="c-jTsjiB c-esaikO"
            data-cy="plan-configuration-tab-button-productRecommendations"
            data-orientation="horizontal"
            data-radix-collection-item=""
            data-state="inactive"
            id="radix-:rb:-trigger-Product recommendations"
            role="tab"
            tabindex="-1"
            type="button"
          >
            fpDetails.planConfiguration.tabs.productRecommendations
          </button>
        </div>
        <div
          aria-labelledby="radix-:rb:-trigger-Soil analysis"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-soilAnalysis"
          data-orientation="horizontal"
          data-state="active"
          hidden=""
          id="radix-:rb:-content-Soil analysis"
          role="tabpanel"
          style="animation-duration: 0s;"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:rb:-trigger-Leaf analysis"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-leafAnalysis"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:rb:-content-Leaf analysis"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:rb:-trigger-Water analysis"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-waterAnalysis"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:rb:-content-Water analysis"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:rb:-trigger-Demand calculations"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-demandCalculations"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:rb:-content-Demand calculations"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:rb:-trigger-Splitting schedule"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-splittingSchedule"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:rb:-content-Splitting schedule"
          role="tabpanel"
          tabindex="0"
        />
        <div
          aria-labelledby="radix-:rb:-trigger-Product recommendations"
          class="c-gaCPoV c-gaCPoV-iVDfaI-css"
          data-cy="plan-configuration-tab-content-productRecommendations"
          data-orientation="horizontal"
          data-state="inactive"
          hidden=""
          id="radix-:rb:-content-Product recommendations"
          role="tabpanel"
          tabindex="0"
        />
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
