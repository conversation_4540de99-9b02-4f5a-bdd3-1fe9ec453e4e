import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, useLocation, useParams } from 'react-router';
import { Tabs, Title } from '@yaradigitallabs/ahua-react';
import { cloneDeep } from 'lodash';

import { ROUTES } from '@src/routes';
import { useNavbar } from '@libs/nav-context';
import {
  FeatureConfigOptions,
  ConfigurationType,
  CMMMPlanConfigurationTabs,
  CNPPlanConfigurationTabs,
  FertigationPlanConfigurationTabs,
} from '@common/types';
import { FEATURE_IDS, URLParamKey } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  configTabInParamsToConfigTabName,
  featureInParamsToFeatureId,
  getValueByKeyFromUrlParams,
  useStateToLocalStorage,
  getTranslationKeyFromPath,
} from '@widgets/Polaris/utils';
import { useUpdatePlanConfigUrlPath } from '@polaris-hooks/index';
import { PlanConfigurationContainer, StyledTabsTrigger } from './PlanConfiguration.styled';
import NSplittingSchedule from '../CMMM/NSplittingSchedule/NSplittingSchedule';
import SoilAnalysisRenderer from '../shared/SoilAnalysisRenderer/SoilAnalysisRenderer';
import DemandCalculationsRenderer from '../shared/DemandCalculationsRenderer/DemandCalculationsRenderer';
import ProductRecommendationsRenderer from '../shared/ProductRecommendationsRenderer/ProductRecommendationsRenderer';

import {
  CMMM_PLAN_CONFIGURATION_TABS,
  CNP_PLAN_CONFIGURATION_TABS,
  FERTIGATION_PLAN_CONFIGURATION_TABS,
  NUTRIENT_IDS_BY_TAB,
} from '../shared/constants';
import OrganicFertilisers from '../CMMM/OrganicFertilisers/OrganicFertilisers';
import SplittingSchedule from '../Fertigation/SplittingSchedule/SplittingSchedule';
import LeafAnalysis from '../Fertigation/LeafAnalysis/LeafAnalysis';
import { WaterAnalysis } from '../Fertigation/WaterAnalysis/WaterAnalysis';
import CnpParameters from '../CNP/Parameters/Parameters';

const PlanConfiguration: () => JSX.Element = () => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const { pathname, state } = useLocation();
  const params = useParams();

  const configTabFromURLParams = useMemo(() => params.configTab, []);

  const [planName, setPlanName] = useStateToLocalStorage('', 'planName');

  const {
    navbarTranslationKey,
    activeRoutePath,
    setActiveRoutePath,
    setIsSubPageNavbar,
    setNavbarTranslationKey,
  } = useNavbar();

  const {
    selectedFeature,
    selectedCropDescription,
    selectedCrop,
    selectedRegion,
    selectedCountry,
    cropRegion,
    selectedPlanConfigTab,
    selectedFeatureNutrients,
    methods: { setSelectedPlanConfigTab, setSelectedFeatureNutrients },
  } = useAppContext();

  const [tabsConfig, setTabsConfig] = useState<
    CNPPlanConfigurationTabs | CMMMPlanConfigurationTabs | FertigationPlanConfigurationTabs
  >();

  const [activeTab, setActiveTab] = useState<string | null>(selectedPlanConfigTab);

  // Handling case when user pastes URL into browser tab where they previously had some other config page open.
  useEffect(() => {
    const featureIdFromURLParams = featureInParamsToFeatureId(
      getValueByKeyFromUrlParams(pathname, URLParamKey.CROP_FEATURES),
    );
    const configTabFromURL = configTabInParamsToConfigTabName(configTabFromURLParams);
    const nutrientIdFromURL = params.nutrientId;
    const planNameFromUrl = params.planName;

    if (!configTabFromURL || !featureIdFromURLParams) return;

    setActiveTab(configTabFromURL);
    setSelectedPlanConfigTab(configTabFromURL);
    planNameFromUrl && planNameFromUrl !== planName && setPlanName(planNameFromUrl);

    if (selectedFeatureNutrients && nutrientIdFromURL) {
      const isNutrientAllowedInConfigTab =
        NUTRIENT_IDS_BY_TAB[featureIdFromURLParams][configTabFromURL].includes(nutrientIdFromURL);
      if (!isNutrientAllowedInConfigTab) return;

      if (selectedFeatureNutrients[configTabFromURL] !== nutrientIdFromURL) {
        const newFeatureNutrients =
          selectedFeatureNutrients &&
          // we only keep nutrients-by-tab if we are on the same feature
          featureIdFromURLParams === selectedFeature?.id
            ? {
                ...cloneDeep(selectedFeatureNutrients),
                [configTabFromURL]: nutrientIdFromURL,
              }
            : {
                [configTabFromURL]: nutrientIdFromURL,
              };
        setSelectedFeatureNutrients(newFeatureNutrients);
      }
    }
  }, []);

  useEffect(() => {
    if (state) setPlanName(state.planName);
    const activeRouteConfig =
      selectedFeature?.id === FEATURE_IDS.CNP
        ? ROUTES.nutritionPlanConfiguration
        : selectedFeature?.id === FEATURE_IDS.CMMM
        ? ROUTES.cerealsMMMConfiguration
        : ROUTES.fertigationPlanConfiguration;
    const path = generatePath(`${ROUTES.cropFeatures}/${activeRouteConfig}`, {
      planName: planName || state?.planName,
      cropRegionId: cropRegion?.id || '',
    });
    setActiveRoutePath(path);
    setIsSubPageNavbar(true);

    setNavbarTranslationKey(
      `cnpDetails.${
        planName ? getTranslationKeyFromPath(planName) : getTranslationKeyFromPath(state?.planName)
      }`,
    );
  }, [state, planName, navbarTranslationKey, activeRoutePath]);

  useEffect(() => {
    const configTabs =
      selectedFeature?.id === FEATURE_IDS.CNP
        ? CNP_PLAN_CONFIGURATION_TABS
        : selectedFeature?.id === FEATURE_IDS.CMMM
        ? CMMM_PLAN_CONFIGURATION_TABS
        : FERTIGATION_PLAN_CONFIGURATION_TABS;
    setTabsConfig(configTabs);

    if (!activeTab) {
      const selectedTab = configTabInParamsToConfigTabName(configTabFromURLParams);
      const activeTabValue = selectedTab || Object.values(configTabs)[0];
      setActiveTab(activeTabValue);
      setSelectedPlanConfigTab(activeTabValue);
    }
  }, [selectedFeature, activeTab]);

  const prefixDetails = selectedFeature?.name.toLowerCase();

  const analysisConfigType = useMemo(() => {
    if (!selectedFeature || !selectedFeature.displayName) return;

    if (selectedFeature.displayName.includes(ConfigurationType.Fertigation))
      return FeatureConfigOptions.FERTIGATION;
    if (selectedFeature.displayName.includes(ConfigurationType.Cereal))
      return FeatureConfigOptions.CEREAL;
  }, [selectedFeature?.displayName]);

  // Hook that updates the URL path depending on the change of selected nutrient and/or tab config
  useUpdatePlanConfigUrlPath(selectedFeature);

  return (
    <PlanConfigurationContainer data-cy='plan-configuration-content'>
      <Title data-cy='plan-configuration-title' size='n' css={{ paddingBottom: '15px' }}>
        {t(`${prefixDetails}Details.title`, {
          defaultValue: 'Polaris',
          selectedCropSubclassName: selectedCrop?.name,
          selectedCropDescriptionName: selectedCropDescription?.name,
          selectedFeatureName:
            selectedFeature?.id === FEATURE_IDS.CNP
              ? selectedFeature?.name
              : selectedFeature?.displayName,
          selectedRegionName: selectedRegion?.name,
          selectedCountryName: selectedCountry?.name,
          interpolation: { escapeValue: false },
        })}
      </Title>
      {tabsConfig && (
        <Tabs
          defaultValue={activeTab}
          onValueChange={(tab: string) => {
            setSelectedPlanConfigTab(tab);
            setActiveTab(tab);
          }}
          value={activeTab}
        >
          <Tabs.List
            css={{
              height: '$x14',
            }}
          >
            {Object.values(tabsConfig).map((tab) => {
              const translationKey = getTranslationKeyFromPath(tab);
              return (
                <StyledTabsTrigger
                  key={tab + '-tabButton'}
                  value={tab}
                  data-cy={`plan-configuration-tab-button-${translationKey}`}
                >
                  {t(
                    `${selectedFeature?.name.toLowerCase()}Details.planConfiguration.tabs.${translationKey}`,
                  )}
                </StyledTabsTrigger>
              );
            })}
          </Tabs.List>
          {Object.values(tabsConfig).map((tab) => (
            <Tabs.Content
              key={tab + '-tabContent'}
              value={tab}
              data-cy={`plan-configuration-tab-content-${getTranslationKeyFromPath(tab)}`}
              css={{ padding: '$x6 0' }}
            >
              {'PARAMETERS' in tabsConfig && tab === tabsConfig.PARAMETERS && (
                <CnpParameters cropRegion={cropRegion} />
              )}
              {tab === tabsConfig.SOIL_ANALYSIS && <SoilAnalysisRenderer />}
              {'LEAF_ANALYSIS' in tabsConfig &&
                tab === tabsConfig.LEAF_ANALYSIS &&
                analysisConfigType && <LeafAnalysis configType={analysisConfigType} />}
              {'WATER_ANALYSIS' in tabsConfig && tab === tabsConfig.WATER_ANALYSIS && (
                <WaterAnalysis />
              )}
              {tab === tabsConfig.DEMAND_CALC && (
                <DemandCalculationsRenderer setActiveTab={setActiveTab} />
              )}
              {'ORGANIC_FERTILISERS' in tabsConfig && tab === tabsConfig.ORGANIC_FERTILISERS && (
                <OrganicFertilisers selectedCountry={selectedCountry} />
              )}
              {'SPLITTING_SCHEDULE' in tabsConfig && tab === tabsConfig.SPLITTING_SCHEDULE && (
                <SplittingSchedule />
              )}
              {'N_SPLITTING_SCHEDULE' in tabsConfig && tab === tabsConfig.N_SPLITTING_SCHEDULE && (
                <NSplittingSchedule />
              )}
              {tab === tabsConfig.PRODUCT_REC && <ProductRecommendationsRenderer />}
            </Tabs.Content>
          ))}
        </Tabs>
      )}
    </PlanConfigurationContainer>
  );
};

export default PlanConfiguration;
