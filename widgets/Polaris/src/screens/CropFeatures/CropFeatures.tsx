import { useNavbar } from '@libs/nav-context';
import React, { useEffect, useMemo } from 'react';
import { Outlet, useLocation, useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@src/routes';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { featureInParamsToFeatureId, getValueByKeyFromUrlParams } from '@widgets/Polaris/utils';
import { METHOD, URLParamKey } from '@common/constants';
import { useFeatureService, useGetCropDescription, useGetCropSubClass } from '@polaris-hooks/index';
import { FilterType } from '../../types';

const CropFeatures = (): JSX.Element => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const {
    cropRegion,
    selectedFeature,
    methods: { setFeature, setCrop, setCropDescription },
  } = useAppContext();
  const { activeRoutePath, setNavbarTranslationKey, setIsSubPageNavbar, setActiveRoutePath } =
    useNavbar();
  const { data: cropDescriptionData } = useGetCropDescription(cropRegion?.cropDescriptionId);
  const { data: cropSubClassData } = useGetCropSubClass(cropDescriptionData?.cropSubClassId);
  const { trigger: fetchFeatureData } = useFeatureService();

  // selected feature is set from URL params if none is stored in App Context
  const featureNameParamFromUrl = useMemo(() => {
    return getValueByKeyFromUrlParams(location.pathname, URLParamKey.CROP_FEATURES);
  }, []);

  const featureIdFromURLParams = featureInParamsToFeatureId(featureNameParamFromUrl);
  const cropRegionIdFromURLParams = params.cropRegionId;

  // Handles overriding state with values from URL params:
  // if feature from URL is different, we nullify the feature part of state and set it in the useEffect below
  useEffect(() => {
    if (selectedFeature && featureIdFromURLParams !== selectedFeature?.id) {
      setFeature(null);
    }
  }, []);

  useEffect(() => {
    if (!cropRegion || !featureIdFromURLParams || cropRegionIdFromURLParams !== cropRegion.id)
      return;

    // If feature tag is not added to this crop region, navigate back to Homepage
    const containsFeatureTag = cropRegion?.tagsConfiguration?.featureTags?.find(
      (fTag) => fTag.id === featureIdFromURLParams,
    );
    if (!containsFeatureTag) {
      navigate(ROUTES.home);
      setActiveRoutePath('');
    }
  }, [cropRegion, featureIdFromURLParams]);

  useEffect(() => {
    cropDescriptionData && setCropDescription(cropDescriptionData);
  }, [cropDescriptionData]);

  useEffect(() => {
    cropSubClassData && setCrop(cropSubClassData);
  }, [cropSubClassData]);

  useEffect(() => {
    if (activeRoutePath === ROUTES.cropFeatures) {
      setNavbarTranslationKey('cropFeatures');
      setIsSubPageNavbar(true);
    }
  }, [activeRoutePath]);

  useEffect(() => {
    const fetchFeatureById = async () => {
      try {
        const currentFeature = await fetchFeatureData({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'id',
                value: featureIdFromURLParams,
                type: FilterType.EQ,
              },
            ],
          }),
        });
        setFeature(currentFeature?.entities[0] || null);
      } catch (error) {
        console.error('Error fetching feature', error);
      }
    };
    if (!selectedFeature && featureIdFromURLParams) {
      fetchFeatureById();
    }
  }, [selectedFeature?.id, featureIdFromURLParams]);

  return (
    <div>
      <Outlet />
    </div>
  );
};

export default CropFeatures;
