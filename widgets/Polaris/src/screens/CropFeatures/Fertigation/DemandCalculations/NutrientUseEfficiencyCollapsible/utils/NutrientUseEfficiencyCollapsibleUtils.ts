import { FertigationUseEfficiency } from '@common/types';

export const getFertigationUseEfficiency = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any,
): FertigationUseEfficiency | undefined => {
  if (
    'maximumNUE' in data &&
    'dripNUE' in data &&
    'microSprinklerNUE' in data &&
    'sprinklerNUE' in data &&
    'pivotNUE' in data &&
    'dryApplicationNUE' in data &&
    'unitIdNUE' in data
  ) {
    return data;
  }
};

export const getFertigationUseEfficiencyKey = (
  value: string,
): keyof FertigationUseEfficiency | undefined => {
  if (
    value === 'maximumNUE' ||
    value === 'dripNUE' ||
    value === 'microSprinklerNUE' ||
    value === 'sprinklerNUE' ||
    value === 'pivotNUE' ||
    value === 'dryApplicationNUE' ||
    value === 'unitIdNUE'
  ) {
    return value;
  }
};
