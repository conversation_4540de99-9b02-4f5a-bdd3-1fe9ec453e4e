import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { NavbarProvider } from '@libs/nav-context';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import {
  getFertigationDemandCalculations,
  updateFertigationDemandCalculationHandler,
  allUnitsHandler,
  updateFertigationDemandCalculationModuleHandler,
  mockUseEfficiencyModule,
  mockAppProviderValue,
} from '@common/mocks';
import { NutrientUseEfficiencyCollapsible } from '../NutrientUseEfficiencyCollapsible';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import userEvent from '@testing-library/user-event';

const server = setupServer(
  updateFertigationDemandCalculationModuleHandler,
  getFertigationDemandCalculations,
  updateFertigationDemandCalculationHandler,
  allUnitsHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

describe('NutrientUseEfficiencyCollapsible Component', () => {
  const user = userEvent.setup();

  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => (
                <NutrientUseEfficiencyCollapsible
                  useEfficiencyModule={mockUseEfficiencyModule}
                  isModuleExpanded={true}
                  setCurrentModulesAccordionState={jest.fn}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders the NutrientUseEfficiencyCollapsible component', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => (
                <NutrientUseEfficiencyCollapsible
                  useEfficiencyModule={mockUseEfficiencyModule}
                  isModuleExpanded={true}
                  setCurrentModulesAccordionState={jest.fn}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(getByTestId('dc-nutrient-use-efficiency-collapsible')).toBeInTheDocument();
  });

  it('displays the equation text correctly', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <NutrientUseEfficiencyCollapsible
              useEfficiencyModule={mockUseEfficiencyModule}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
            />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const collapsibleSection = getByTestId('dc-nutrient-use-efficiency-collapsible');
    expect(collapsibleSection).toHaveAttribute('data-state', 'open');
    expect(getByTestId('fertigation-dc-nutrient-use-efficiency-equation')).toBeInTheDocument();
  });

  it('renders the input fields with correct data attributes', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <NutrientUseEfficiencyCollapsible
              useEfficiencyModule={mockUseEfficiencyModule}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
            />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const collapsibleSection = getByTestId('dc-nutrient-use-efficiency-collapsible');
    expect(collapsibleSection).toHaveAttribute('data-state', 'open');

    const input = getByTestId('maximumNUE-input');
    expect(input).toBeInTheDocument();

    await waitFor(async () => {
      await user.type(input, '50');
      fireEvent.blur(input);
    });
    expect(input).toHaveValue('50');
  });

  it('triggers onChange and onBlur events correctly for input fields', async () => {
    const { getAllByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <NutrientUseEfficiencyCollapsible
              useEfficiencyModule={mockUseEfficiencyModule}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
            />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const resultCells = getAllByTestId('calculated-result-cell');
    expect(resultCells.length).toEqual(5);
  });
});
