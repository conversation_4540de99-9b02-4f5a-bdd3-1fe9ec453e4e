import React, { Dispatch, FC, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TableCell, TableContainer, InnerContainer } from '..';

import { AhuaIcon, Input, Subtitle, Table, Title } from '@yaradigitallabs/ahua-react';
import {
  DemandCalculationModule,
  FertigationUseEfficiency,
  ModuleNameToAccordionNutrientState,
  FeatureConfigOptions,
} from '@common/types';
import { useUpdateDemandCalculationModule, filterUnitsById } from '@polaris-hooks/index';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { MAX_PERCENT_VALUE, MIN_PERCENT_VALUE } from '../DemandCalculations.constant';
import { METHOD } from '@common/constants';
import { Equation, Bold, CollapsibleNoPaddingContainer } from '../../../shared';
import {
  getFertigationUseEfficiency,
  getFertigationUseEfficiencyKey,
} from './utils/NutrientUseEfficiencyCollapsibleUtils';

interface NutrientUseEfficiencyCollapsibleProps {
  useEfficiencyModule: DemandCalculationModule;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const NutrientUseEfficiencyCollapsible: FC<NutrientUseEfficiencyCollapsibleProps> = ({
  useEfficiencyModule,
  isModuleExpanded,
  setCurrentModulesAccordionState,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.demandCalculations.nutrientUseEfficiency',
  });

  const {
    selectedCountryUnits,
    methods: { updateDemandCalculationModule },
  } = useAppContext();

  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    FeatureConfigOptions.FERTIGATION,
    useEfficiencyModule.id || '',
  );

  const configurationData: FertigationUseEfficiency | undefined = useMemo(
    () => getFertigationUseEfficiency(useEfficiencyModule.configuration?.data),
    [useEfficiencyModule],
  );

  const [nueValues, setNueValues] = useState(() => ({
    maximumNUE: configurationData?.maximumNUE ?? MIN_PERCENT_VALUE,
    dripNUE: configurationData?.dripNUE ?? MAX_PERCENT_VALUE,
    microSprinklerNUE: configurationData?.microSprinklerNUE ?? MAX_PERCENT_VALUE,
    sprinklerNUE: configurationData?.sprinklerNUE ?? MAX_PERCENT_VALUE,
    pivotNUE: configurationData?.pivotNUE ?? MAX_PERCENT_VALUE,
    dryApplicationNUE: configurationData?.dryApplicationNUE ?? MAX_PERCENT_VALUE,
  }));

  useEffect(() => {
    setNueValues({
      maximumNUE: configurationData?.maximumNUE ?? MIN_PERCENT_VALUE,
      dripNUE: configurationData?.dripNUE ?? MAX_PERCENT_VALUE,
      microSprinklerNUE: configurationData?.microSprinklerNUE ?? MAX_PERCENT_VALUE,
      sprinklerNUE: configurationData?.sprinklerNUE ?? MAX_PERCENT_VALUE,
      pivotNUE: configurationData?.pivotNUE ?? MAX_PERCENT_VALUE,
      dryApplicationNUE: configurationData?.dryApplicationNUE ?? MAX_PERCENT_VALUE,
    });
  }, [configurationData]);

  const calculatedResults = useMemo(() => {
    const maximumNUEValue = configurationData?.maximumNUE ?? Number(MIN_PERCENT_VALUE);
    const dripNUEValue = configurationData?.dripNUE ?? Number(MIN_PERCENT_VALUE);
    const microSprinklerNUEValue =
      configurationData?.microSprinklerNUE ?? Number(MIN_PERCENT_VALUE);
    const sprinklerNUEValue = configurationData?.sprinklerNUE ?? Number(MIN_PERCENT_VALUE);
    const pivotNUEValue = configurationData?.pivotNUE ?? Number(MIN_PERCENT_VALUE);
    const dryApplicationNUEValue =
      configurationData?.dryApplicationNUE ?? Number(MIN_PERCENT_VALUE);
    return {
      dripNUECalculated: (maximumNUEValue * dripNUEValue) / 100,
      microSprinklerNUECalculated: (maximumNUEValue * microSprinklerNUEValue) / 100,
      sprinklerNUECalculated: (maximumNUEValue * sprinklerNUEValue) / 100,
      pivotNUECalculated: (maximumNUEValue * pivotNUEValue) / 100,
      dryApplicationNUECalculated: (maximumNUEValue * dryApplicationNUEValue) / 100,
    };
  }, [configurationData]);

  const nueUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, configurationData?.unitIdNUE),
    [selectedCountryUnits, configurationData],
  );

  const handleValueUpdate = async (value: string, prop: string) => {
    const data = getFertigationUseEfficiency(useEfficiencyModule.configuration.data);
    const efficiencyKey = getFertigationUseEfficiencyKey(prop);
    const nueValue = Number(value) === 0 ? MIN_PERCENT_VALUE : value;

    if (efficiencyKey) {
      if (data?.[efficiencyKey] === Number(nueValue)) {
        return setNueValues((prev) => ({
          ...prev,
          [efficiencyKey]: nueValue,
        }));
      }

      useEfficiencyModule.configuration.data = {
        ...useEfficiencyModule.configuration.data,
        [efficiencyKey]: Number(nueValue),
      };

      try {
        const updatedModule = await triggerUpdate({
          method: METHOD.PUT,
          body: JSON.stringify(useEfficiencyModule),
        });
        updatedModule && updateDemandCalculationModule(updatedModule);
      } catch (error) {
        console.error('Failed to update Nutrient Use Efficiency:', error);
      }
    }
  };

  const getValidatedValue = (value: string) => {
    const validatedValue = value === '' ? value : parseInt(value, 10);
    if (
      value !== '' &&
      (Number(validatedValue) < Number(MIN_PERCENT_VALUE) ||
        Number(validatedValue) > Number(MAX_PERCENT_VALUE) ||
        isNaN(Number(validatedValue)))
    )
      return;

    return validatedValue;
  };

  const tableCellHeaders = [
    t('drip'),
    t('microSprinkler'),
    t('sprinkler'),
    t('pivot'),
    t('dryApplication'),
  ];

  const HeaderTitle = (): JSX.Element => (
    <div>
      <Title size='s'>{t('headerTitle')}</Title>
      <Subtitle className='info-subtitle'>{t('headerSubtitle')}</Subtitle>
    </div>
  );

  return (
    <>
      <CollapsibleNoPaddingContainer
        open={isModuleExpanded}
        data-cy='dc-nutrient-use-efficiency-collapsible'
        header={<HeaderTitle />}
        className='collapsible-section'
        onOpenChange={() =>
          setCurrentModulesAccordionState((prev) => {
            const modules = {
              ...prev.accordionState,
              [useEfficiencyModule.name]: !isModuleExpanded,
            };
            return {
              nutrientId: prev.nutrientId,
              accordionState: modules,
            };
          })
        }
      >
        <InnerContainer>
          <Equation
            title={t('equationText')}
            equation={
              <span>
                {t('formulaNue')} <Bold>{t('maximumNue')}</Bold> x <Bold>{t('fertSystem')}</Bold>
              </span>
            }
            dataCy='fertigation-dc-nutrient-use-efficiency-equation'
          />
          <Subtitle size='s' css={{ paddingTop: '$x5' }}>
            {t('enterFields')}
          </Subtitle>
          <TableContainer>
            <tbody>
              <Table.Row>
                <TableCell rowSpan={2}>
                  {t('maximumNue')} ({nueUnit?.name})
                </TableCell>
                <TableCell rowSpan={2}></TableCell>
                <TableCell colSpan={5} css={{ backgroundColor: '$blue0' }}>
                  {t('fertSystem')} ({nueUnit?.name})
                </TableCell>
              </Table.Row>
              <Table.Row>
                {tableCellHeaders.map((text, i) => (
                  <TableCell key={text + i}>{text}</TableCell>
                ))}
              </Table.Row>
              <Table.Row>
                {Object.entries(nueValues).map(([name, value], index) => (
                  <React.Fragment key={name}>
                    {index === 1 && (
                      <TableCell css={{ padding: '$x4 $x6', textAlign: 'center' }}>
                        <AhuaIcon
                          colorConcept='neutral'
                          icon='Close'
                          iconSize='x3'
                          css={{ verticalAlign: 'middle' }}
                        />
                      </TableCell>
                    )}
                    <TableCell>
                      <Input
                        value={value}
                        onChange={({ target: { value } }) => {
                          const validatedValue = getValidatedValue(value);
                          if (validatedValue !== undefined) {
                            setNueValues((prev) => ({
                              ...prev,
                              [name]: validatedValue,
                            }));
                          }
                        }}
                        onBlur={({ target: { value } }) => {
                          handleValueUpdate(value, name);
                        }}
                        data-cy={`${name}-input`}
                        size='xs'
                      />
                    </TableCell>
                  </React.Fragment>
                ))}
              </Table.Row>

              <Table.Row>
                <TableCell>{`${t('nue')} (${nueUnit?.name})`}</TableCell>
                <TableCell></TableCell>
                {Object.values(calculatedResults).map((item, i) => (
                  <TableCell key={i} data-cy='calculated-result-cell'>
                    {item}
                  </TableCell>
                ))}
              </Table.Row>
            </tbody>
          </TableContainer>
        </InnerContainer>
      </CollapsibleNoPaddingContainer>
    </>
  );
};
