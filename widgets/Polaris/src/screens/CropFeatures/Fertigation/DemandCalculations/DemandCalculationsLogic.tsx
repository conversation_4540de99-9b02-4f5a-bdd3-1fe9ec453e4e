import { useEffect, useMemo, useRef, useState } from 'react';
import { isEmpty, isEqual, sortBy } from 'lodash';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS,
  FERTIGATION_PLAN_CONFIGURATION_TABS,
  FERTIGATION_MODULES_TO_STATES_INITIAL_MAP,
} from '../../shared/constants';
import {
  ConfigurationType,
  DemandCalculationModule,
  FeatureConfigOptions,
  FertigationNutrientWithNutrientForms,
  FertigationDemandCalculationModule,
} from '@common/types';
import {
  useFetchDemandCalculations,
  useFetchNutrientsFromPolaris,
  useUpdateDemandCalculationNutrientForm,
  useFetchMMMValidations,
} from '@polaris-hooks/index';
import { GenericFilter, FilterType } from '@widgets/Polaris/src/types';
import { METHOD, UNIT_TAGS } from '@common/constants';
import { mapFertigationNutrientIdsToNutrientForms } from '../../shared/helpers/nutrientsListHelpers/nutrientsListHelpers';

const FertigationDemandCalculationsLogic = () => {
  const {
    selectedFeatureNutrients,
    selectedPlanConfigTab,
    selectedCountry,
    selectedCountryUnits,
    cropRegion,
    demandCalculations,
    demandCalculationAccordion,
    methods: {
      setDemandCalculations,
      setDemandCalculationAccordion,
      setFertigationDemandCalculationNutrientsWithNutrientForms,
    },
  } = useAppContext();

  const nutrientID = useMemo(
    () => selectedFeatureNutrients?.[selectedPlanConfigTab ?? ''],
    [selectedPlanConfigTab, selectedFeatureNutrients],
  );
  const nutrientsData = useFetchNutrientsFromPolaris([
    {
      key: 'id',
      value: FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.join(','),
      type: FilterType.IN,
    },
  ]);
  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);
  const nutrientsRef = useRef(sortedNutrients);
  if (!isEqual(nutrientsRef.current, sortedNutrients)) {
    nutrientsRef.current = sortedNutrients;
  }
  const selectedNutrient = useMemo(
    () => sortedNutrients && sortedNutrients.find(({ id }) => id === nutrientID),
    [nutrientsRef.current, nutrientID],
  );

  const demandCalculationFilter: GenericFilter[] | undefined = useMemo(() => {
    if (!selectedCountry?.id || !cropRegion?.id) return;
    return [
      {
        key: 'countryId',
        value: selectedCountry.id,
        type: FilterType.EQ,
      },
      {
        key: 'cropRegionId',
        value: cropRegion.id,
        type: FilterType.EQ,
      },
    ];
  }, [selectedCountry?.id, cropRegion?.id]);

  const demandCalculationData = useFetchDemandCalculations(
    FeatureConfigOptions.FERTIGATION,
    demandCalculationFilter,
    Boolean(selectedCountry?.id && cropRegion?.id),
  );

  const selectedDemandCalculationConfiguration = useMemo(() => {
    if (
      !selectedNutrient ||
      isEmpty(selectedNutrient) ||
      !demandCalculations ||
      isEmpty(demandCalculations)
    ) {
      return;
    }
    return demandCalculations.find(
      (demandCalculation) => demandCalculation.nutrientId === selectedNutrient.id,
    );
  }, [selectedNutrient, demandCalculations]);

  const { updateDemandCalculationNutrientForm } = useUpdateDemandCalculationNutrientForm(
    FeatureConfigOptions.FERTIGATION,
    selectedDemandCalculationConfiguration?.id,
  );

  const validationsFilter: GenericFilter[] | undefined = useMemo(() => {
    if (!cropRegion?.id) return;
    return [
      {
        key: 'cropRegionId',
        value: cropRegion.id,
        type: FilterType.EQ,
      },
      {
        key: 'configurationType',
        value: ConfigurationType.Fertigation,
        type: FilterType.EQ,
      },
    ];
  }, [cropRegion?.id]);
  const fertigationPlanValidations = useFetchMMMValidations(
    validationsFilter,
    Boolean(cropRegion?.id),
  );
  const currentPlanValidationStatus = useMemo(() => {
    return fertigationPlanValidations?.[0]?.validationStatus;
  }, [fertigationPlanValidations]);

  // Get nutrient forms for the Demand calculation nutrients
  const demandCalculationNutrientsAndNutrientForms:
    | FertigationNutrientWithNutrientForms
    | undefined = useMemo(() => {
    return mapFertigationNutrientIdsToNutrientForms({
      nutrientList: sortedNutrients,
      unitList: selectedCountryUnits ?? undefined,
      demandCalculationConfigs: demandCalculations,
    });
  }, [sortedNutrients, selectedCountryUnits, demandCalculations, selectedNutrient]);

  useEffect(() => {
    if (!isEmpty(demandCalculationNutrientsAndNutrientForms)) {
      setFertigationDemandCalculationNutrientsWithNutrientForms(
        demandCalculationNutrientsAndNutrientForms,
      );
    }
  }, [demandCalculationNutrientsAndNutrientForms]);

  const currentNutrientFormsOptions = useMemo(() => {
    return selectedNutrient && !isEmpty(demandCalculationNutrientsAndNutrientForms)
      ? demandCalculationNutrientsAndNutrientForms[selectedNutrient.id].map((nForm) => ({
          text: nForm.nutrientFormName,
          value: nForm.nutrientFormId,
          selected: nForm.selected,
        }))
      : [];
  }, [selectedNutrient, demandCalculationNutrientsAndNutrientForms]);

  const [currentUseEfficiencyModule, setCurrentUseEfficiencyModule] =
    useState<DemandCalculationModule | null>();
  const [currentSoilCorrectionFactorModule, setCurrentSoilCorrectionFactorModule] =
    useState<DemandCalculationModule | null>();
  const [currentNutrientDemandModule, setCurrentNutrientDemandModule] =
    useState<DemandCalculationModule | null>();
  const [currentIrrigationWaterNutrientModule, setCurrentIrrigationWaterNutrientModule] =
    useState<DemandCalculationModule | null>();
  const [currentModulesAccordionState, setCurrentModulesAccordionState] = useState(
    demandCalculationAccordion ?? {
      nutrientId: selectedNutrient?.id || '',
      accordionState: FERTIGATION_MODULES_TO_STATES_INITIAL_MAP,
    },
  );

  useEffect(() => {
    if (!demandCalculationData) return;
    setDemandCalculations(demandCalculationData);
  }, [demandCalculationData]);

  // Whenever nutrient changes, we reset local accordion state to default
  useEffect(() => {
    const noNutrient =
      !selectedNutrient ||
      !selectedNutrient?.id ||
      !selectedFeatureNutrients ||
      !selectedFeatureNutrients?.[FERTIGATION_PLAN_CONFIGURATION_TABS.DEMAND_CALC];
    if (noNutrient) return;

    const demandCalcFeatureNutrientId =
      selectedFeatureNutrients[FERTIGATION_PLAN_CONFIGURATION_TABS.DEMAND_CALC];
    // The state should not reset when user gets back from a different config tab.
    // It could happen that the nutrient has not updated yet (because it comes from a child component)
    const shouldNotResetState =
      selectedNutrient.id !== demandCalcFeatureNutrientId ||
      currentModulesAccordionState.nutrientId === demandCalcFeatureNutrientId;
    if (shouldNotResetState) return;

    const newState = {
      nutrientId: selectedNutrient?.id || '',
      accordionState: FERTIGATION_MODULES_TO_STATES_INITIAL_MAP,
    };
    setCurrentModulesAccordionState(newState);
  }, [selectedNutrient?.id]);

  // Setting the global state when a collapsible expands or closes
  useEffect(() => {
    selectedNutrient &&
      selectedNutrient.id &&
      setDemandCalculationAccordion(currentModulesAccordionState);
  }, [currentModulesAccordionState]);

  useEffect(() => {
    if (!demandCalculations || !selectedNutrient) return;

    const selectedDemandCalculation = demandCalculations.find(
      (demandCalculation) => demandCalculation.nutrientId === selectedNutrient.id,
    );

    // when there is no configuration for a newly selected nutrient,
    // the modules are set to null to avoid showing the modules for the previously selected nutrient
    if (!selectedDemandCalculation) {
      setCurrentNutrientDemandModule(null);
      setCurrentUseEfficiencyModule(null);
      setCurrentSoilCorrectionFactorModule(null);
      setCurrentIrrigationWaterNutrientModule(null);
      return;
    }
    const moduleMap = selectedDemandCalculation.demandCalculationModules.reduce<
      Record<string, DemandCalculationModule>
    >((acc, module) => {
      acc[module.name] = module;
      return acc;
    }, {});

    setCurrentNutrientDemandModule(
      moduleMap[FertigationDemandCalculationModule.CROP_DEMAND] || null,
    );
    setCurrentUseEfficiencyModule(
      moduleMap[FertigationDemandCalculationModule.USE_EFFICIENCY] || null,
    );
    setCurrentSoilCorrectionFactorModule(
      moduleMap[FertigationDemandCalculationModule.SOIL_CORRECTION_FACTOR] || null,
    );
    setCurrentIrrigationWaterNutrientModule(
      moduleMap[FertigationDemandCalculationModule.IRRIGATION_WATER_NUTRIENT] || null,
    );
  }, [selectedNutrient, demandCalculations]);

  const cropNutrientDemandUnits = useMemo(() => {
    if (selectedCountryUnits) {
      return selectedCountryUnits.filter(
        ({ tags }) =>
          tags.includes(UNIT_TAGS.NUTRIENT_REMOVAL_UNIT) ||
          tags.includes(UNIT_TAGS.FERTIGATION_NUTRIENT_DEMAND_UNIT),
      );
    }
  }, [selectedCountryUnits]);

  // When we change nutrient form ID, we send API request to update the demand calculation configuration
  // and we also set the new global state for the demand calculations
  const handleNutrientFormChange = async (elementFormId: string) => {
    if (!selectedNutrient) {
      return;
    }
    try {
      const demandCalculationConfigurationResponse = await updateDemandCalculationNutrientForm({
        method: METHOD.PUT,
        body: JSON.stringify({
          elementFormId,
        }),
      });
      if (!demandCalculationConfigurationResponse) {
        return;
      }

      const updatedDemandCalculations = demandCalculations
        .map((demandCalculation) => {
          if (demandCalculation.nutrientId === selectedNutrient.id) {
            return demandCalculationConfigurationResponse;
          }
        })
        .filter((data) => !!data);

      setDemandCalculations(updatedDemandCalculations);
    } catch (error) {
      console.error('Error updating demand calculation configuration:', error);
    }
  };

  return {
    selectedNutrient,
    nutrientsData: sortedNutrients,
    currentNutrientFormsOptions,
    handleNutrientFormChange,
    currentNutrientDemandModule,
    currentUseEfficiencyModule,
    currentSoilCorrectionFactorModule,
    currentIrrigationWaterNutrientModule,
    cropNutrientDemandUnits,
    currentModulesAccordionState,
    setCurrentModulesAccordionState,
    currentPlanValidationStatus,
  };
};

export default FertigationDemandCalculationsLogic;
