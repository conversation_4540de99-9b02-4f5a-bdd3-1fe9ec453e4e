import React from 'react';
import { act, render, screen } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import {
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  getFertigationDemandCalculations,
  allPolarisNutrientsHandler,
  fertigationMMMValidationHandler,
  fertigationNutrientFormsMock,
  mockFertigationDemandCalcAppProviderValue,
} from '@common/mocks';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import FertigationDemandCalculations from '../DemandCalculations';
import { POLARIS_API_GW_ENDPOINTS } from '@common/constants';
import { FertigationDemandCalculationModule } from '@common/types';
import { http, HttpResponse } from 'msw';

export const getAllUnitsHandler = http.post(
  `${POLARIS_API_GW_ENDPOINTS.UNIT_API}/units/filter`,
  () => {
    return HttpResponse.json({
      entities: fertigationNutrientFormsMock,
    });
  },
);

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  getFertigationDemandCalculations,
  getAllUnitsHandler,
  allPolarisNutrientsHandler,
  fertigationMMMValidationHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

describe('FertigationDemandCalculations Component', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders equation block correctly', async () => {
    const component = render(
      <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(
      await component.findByTestId('fertigation-demand-calculations-main-equation'),
    ).toBeInTheDocument();
  });

  it('renders static list of nutrients correctly and a nutrient form select', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(await screen.findByTestId('nutrients-static-list')).toBeInTheDocument();
    expect(
      await screen.findByTestId('fertigation-demand-calculations-nutrient-forms'),
    ).toBeInTheDocument();
  });

  it('renders nutrient form select with the correct nutrient form name displayed', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    const nutrientFormSelect = await screen.findByTestId(
      'fertigation-demand-calculations-nutrient-forms',
    );
    expect(nutrientFormSelect).toBeInTheDocument();

    const nutrientFormSelectBtn = await screen.findByRole('combobox');
    expect(nutrientFormSelectBtn).toBeInTheDocument();

    const nutrientFormName = fertigationNutrientFormsMock.find(
      (nForm) =>
        nForm.id === mockFertigationDemandCalcAppProviderValue.demandCalculations[0].elementFormId,
    )?.name;

    if (nutrientFormName) {
      expect(await screen.findByText(nutrientFormName)).toBeInTheDocument();
    } else {
      throw new Error('Nutrient form name is not shown in the document!');
    }
  });

  /* Checking Demand Calculation modules */

  const checkIfModuleRendersWhenItShould = (moduleName: string, testId: string) => {
    const module = screen.queryByTestId(testId);
    if (
      mockFertigationDemandCalcAppProviderValue.demandCalculations[0].demandCalculationModules.find(
        (module) => module.name === moduleName,
      )
    ) {
      return Boolean(module) === true;
    } else {
      return Boolean(module) === false;
    }
  };

  it('renders Crop Nutrient Demand module when currentNutrientDemandModule is available', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(
      checkIfModuleRendersWhenItShould(
        FertigationDemandCalculationModule.CROP_DEMAND,
        'crop-nutrient-demand-collapsible',
      ),
    ).toBe(true);
  });

  it('renders Nutrient Use Efficiency module when currentUseEfficiencyModule is available', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(
      checkIfModuleRendersWhenItShould(
        FertigationDemandCalculationModule.USE_EFFICIENCY,
        'dc-nutrient-use-efficiency-collapsible',
      ),
    ).toBe(true);
  });

  it('renders Soil Correction Factor module when currentSoilCorrectionFactorModule is available', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(
      checkIfModuleRendersWhenItShould(
        FertigationDemandCalculationModule.SOIL_CORRECTION_FACTOR,
        'fertigation-soil-correction-factor-collapsible-section-component',
      ),
    ).toBe(true);
  });

  it('renders Irrigation Water Nutrient module when currentIrrigationWaterNutrientModule is available', async () => {
    await act(async () => {
      render(
        <AppContext.Provider value={mockFertigationDemandCalcAppProviderValue}>
          <NavbarProvider>
            <Router>
              <AppContext.Consumer>{() => <FertigationDemandCalculations />}</AppContext.Consumer>
            </Router>
          </NavbarProvider>
        </AppContext.Provider>,
      );
    });

    expect(
      checkIfModuleRendersWhenItShould(
        FertigationDemandCalculationModule.IRRIGATION_WATER_NUTRIENT,
        'dc-irrigation-water-nutrient-collapsible',
      ),
    ).toBe(true);
  });
});
