import React from 'react';

import { useTranslation } from 'react-i18next';
import { Select } from '@yaradigitallabs/ahua-react';
import { FertigationDemandCalculationsWrapper } from '.';
import { NutrientsStaticList, TooltipWrapper } from '@widgets/Polaris/src/components';
import FertigationDemandCalculationsLogic from './DemandCalculationsLogic';
import { NutrientUseEfficiencyCollapsible } from './NutrientUseEfficiencyCollapsible';
import { IrrigationWaterNutrientCollapsible } from './IrrigationWaterNutrientCollapsible/IrrigationWaterNutrientCollapsible';
import {
  FeatureConfigOptions,
  FertigationDemandCalculationModule,
  ValidationStatus,
} from '@common/types';

import {
  CropNutrientDemandCollapsible,
  Equation,
  SoilCorrectionFactorCollapsible,
  FERTIGATION_MODULES_TO_STATES_INITIAL_MAP,
} from '../../shared';

const FertigationDemandCalculations = () => {
  const keyPrefix = 'polaris.fpDetails.demandCalculations';
  const { t } = useTranslation('polaris', { keyPrefix });
  const {
    selectedNutrient,
    nutrientsData,
    currentUseEfficiencyModule,
    currentIrrigationWaterNutrientModule,
    currentSoilCorrectionFactorModule,
    cropNutrientDemandUnits,
    currentNutrientDemandModule,
    currentModulesAccordionState,
    setCurrentModulesAccordionState,
    currentNutrientFormsOptions,
    handleNutrientFormChange,
    currentPlanValidationStatus,
  } = FertigationDemandCalculationsLogic();

  return (
    <FertigationDemandCalculationsWrapper data-cy='fertigation-demand-calculations-wrapper'>
      <Equation
        title={t('mainEquation.title')}
        description={t('mainEquation.description')}
        equation={t('mainEquation.equation')}
        dataCy='fertigation-demand-calculations-main-equation'
      />
      <NutrientsStaticList
        selectedNutrient={selectedNutrient}
        nutrientParams={nutrientsData}
        title={t('nutrientList.title')}
        emptyStateText={t('nutrientList.emptyStateText')}
      />
      <TooltipWrapper
        showTooltip={currentPlanValidationStatus === ValidationStatus.VALIDATED}
        tooltipText={t('nutrientForm.tooltipText')}
        concept='inverse'
        dataCy='fertigation-demand-calculations-nutrient-forms-tooltip'
        maxWidth={160}
        position='right'
      >
        <div style={{ width: '420px' }} data-cy='fertigation-demand-calculations-nutrient-forms'>
          <Select
            ariaLabel={t('nutrientForm.areaLabel')}
            cover='outline'
            css={{ width: '420px', margin: '$x4 0 $x4' }}
            helper-text={t('nutrientForm.description')}
            items={currentNutrientFormsOptions}
            disabled={currentPlanValidationStatus === ValidationStatus.VALIDATED}
            label={t('nutrientForm.title')}
            position='popper'
            size='s'
            variant='default'
            value={currentNutrientFormsOptions.find((option) => option.selected === true)?.value}
            loading={!nutrientsData}
            onFocus={null}
            onBlur={null}
            onChange={(val) => {
              handleNutrientFormChange(val);
            }}
          />
        </div>
      </TooltipWrapper>

      {currentNutrientDemandModule && (
        <CropNutrientDemandCollapsible
          units={cropNutrientDemandUnits}
          nutrientDemandModule={currentNutrientDemandModule}
          isModuleExpanded={
            currentModulesAccordionState.accordionState[
              FertigationDemandCalculationModule.CROP_DEMAND
            ] ??
            FERTIGATION_MODULES_TO_STATES_INITIAL_MAP[
              FertigationDemandCalculationModule.CROP_DEMAND
            ]
          }
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
          configType={FeatureConfigOptions.FERTIGATION}
        />
      )}
      {currentUseEfficiencyModule && (
        <NutrientUseEfficiencyCollapsible
          useEfficiencyModule={currentUseEfficiencyModule}
          isModuleExpanded={
            currentModulesAccordionState.accordionState[
              FertigationDemandCalculationModule.USE_EFFICIENCY
            ] ??
            FERTIGATION_MODULES_TO_STATES_INITIAL_MAP[
              FertigationDemandCalculationModule.USE_EFFICIENCY
            ]
          }
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
        />
      )}
      {currentSoilCorrectionFactorModule && (
        <SoilCorrectionFactorCollapsible
          keyPrefix={keyPrefix}
          soilCorrectionFactorModule={currentSoilCorrectionFactorModule}
          isModuleExpanded={
            currentModulesAccordionState.accordionState[
              FertigationDemandCalculationModule.SOIL_CORRECTION_FACTOR
            ] ??
            FERTIGATION_MODULES_TO_STATES_INITIAL_MAP[
              FertigationDemandCalculationModule.SOIL_CORRECTION_FACTOR
            ]
          }
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
          configType={FeatureConfigOptions.FERTIGATION}
        />
      )}
      {currentIrrigationWaterNutrientModule && (
        <IrrigationWaterNutrientCollapsible
          irrigationWaterNutrientModule={currentIrrigationWaterNutrientModule}
          isModuleExpanded={
            FertigationDemandCalculationModule.IRRIGATION_WATER_NUTRIENT in
              currentModulesAccordionState.accordionState &&
            currentModulesAccordionState.accordionState[
              FertigationDemandCalculationModule.IRRIGATION_WATER_NUTRIENT
            ]
          }
          setCurrentModulesAccordionState={setCurrentModulesAccordionState}
        />
      )}
    </FertigationDemandCalculationsWrapper>
  );
};

export default FertigationDemandCalculations;
