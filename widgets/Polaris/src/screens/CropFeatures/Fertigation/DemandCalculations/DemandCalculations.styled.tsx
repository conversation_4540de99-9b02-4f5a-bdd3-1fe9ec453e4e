import { Caption, styled, Table } from '@yaradigitallabs/ahua-react';

export const FertigationDemandCalculationsWrapper = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  gap: '$x4',
  width: '100%',
});

export const TableContainer = styled(Table, {
  border: '1px solid gray5',
  width: '100%',
  marginTop: '$x2',
});

export const TableCell = styled(Table.Cell, {
  border: '1px solid $black10',
  backgroundColor: '$white100',
  fontWeight: '$semiBold',
  alignItems: 'left',
  padding: '$x2 $x4 $x2 $x3',
  '&:last-child': {
    textAlign: 'left',
  },
});

export const TitleStyle = {
  fontWeight: '$medium',
};

export const CheckboxWrapper = styled('div', {
  display: 'flex',
  paddingTop: '$x3',
});

export const CaptionStyled = styled(Caption, {
  alignContent: 'center',
  paddingLeft: '$x1',
});

export const InnerContainer = styled('div', {
  padding: '$x6 $x4 $x4 $x4',
});
