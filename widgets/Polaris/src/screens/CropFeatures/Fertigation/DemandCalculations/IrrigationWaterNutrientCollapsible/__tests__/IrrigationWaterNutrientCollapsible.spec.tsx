import React from 'react';
import { render } from '@testing-library/react';
import { NavbarProvider } from '@libs/nav-context';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import {
  getFertigationDemandCalculations,
  updateFertigationDemandCalculationHandler,
  updateFertigationDemandCalculationModuleHandler,
  allUnitsHandler,
  mockIrrigationWaterNutrientModule,
  mockAppProviderValue,
} from '@common/mocks';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import userEvent from '@testing-library/user-event';
import { IrrigationWaterNutrientCollapsible } from '../IrrigationWaterNutrientCollapsible';

const server = setupServer(
  getFertigationDemandCalculations,
  updateFertigationDemandCalculationHandler,
  updateFertigationDemandCalculationModuleHandler,
  allUnitsHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

describe('IrrigationWaterNutrientCollapsible Component', () => {
  const user = userEvent.setup();

  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => (
                <IrrigationWaterNutrientCollapsible
                  irrigationWaterNutrientModule={mockIrrigationWaterNutrientModule}
                  isModuleExpanded={true}
                  setCurrentModulesAccordionState={jest.fn}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders the IrrigationWaterNutrientCollapsible component', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => (
                <IrrigationWaterNutrientCollapsible
                  irrigationWaterNutrientModule={mockIrrigationWaterNutrientModule}
                  isModuleExpanded={true}
                  setCurrentModulesAccordionState={jest.fn}
                />
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(getByTestId('dc-irrigation-water-nutrient-collapsible')).toBeInTheDocument();
  });

  it('displays the preset data in Irrigation water nutrient section', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <IrrigationWaterNutrientCollapsible
              irrigationWaterNutrientModule={mockIrrigationWaterNutrientModule}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
            />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    const collapsibleSection = getByTestId('dc-irrigation-water-nutrient-collapsible');

    expect(collapsibleSection).toHaveAttribute('data-state', 'open');
    expect(getByTestId('fertigation-dc-irrigation-water-nutrient-equation')).toBeInTheDocument();
    expect(getByTestId('dripIWN-input')).toBeInTheDocument();
    expect(getByTestId('dripIWN-input')).toHaveValue('100');
    expect(getByTestId('microSprinklerIWN-input')).toHaveValue('100');
    expect(getByTestId('sprinklerIWN-input')).toHaveValue('100');
    expect(getByTestId('pivotIWN-input')).toHaveValue('100');
  });

  it('triggers onChange and onBlur events correctly for input fields', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <IrrigationWaterNutrientCollapsible
              irrigationWaterNutrientModule={mockIrrigationWaterNutrientModule}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
            />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const input = getByTestId('dripIWN-input');
    await userEvent.clear(input);
    await user.type(input, '50');
    await userEvent.tab();

    expect(input).toHaveValue('50');
  });

  it('toggles isEnabled property correctly will change its value', async () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <IrrigationWaterNutrientCollapsible
              irrigationWaterNutrientModule={{
                ...mockIrrigationWaterNutrientModule,
                isEnabled: false,
              }}
              isModuleExpanded={true}
              setCurrentModulesAccordionState={jest.fn}
            />
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const collapsibleSection = getByTestId('dc-irrigation-water-nutrient-collapsible');

    await userEvent.click(collapsibleSection).then(() => {
      expect(getByTestId('isEnabled-toggle')).toHaveAttribute('aria-checked', 'false');
    });
  });
});
