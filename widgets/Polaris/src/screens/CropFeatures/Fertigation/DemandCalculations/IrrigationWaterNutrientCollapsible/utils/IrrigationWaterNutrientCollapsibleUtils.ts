import { FertigationIrrigationWaterNutrient } from '@common/types';

export const getFertigationIrrigationWaterNutrient = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any,
): FertigationIrrigationWaterNutrient | undefined => {
  if (
    'dripIWN' in data &&
    'microSprinklerIWN' in data &&
    'sprinklerIWN' in data &&
    'pivotIWN' in data &&
    'unitIdIWN' in data
  ) {
    return data;
  }
};

export const getFertigationIrrigationWaterNutrientKey = (
  value: string,
): keyof FertigationIrrigationWaterNutrient | undefined => {
  if (
    value === 'dripIWN' ||
    value === 'microSprinklerIWN' ||
    value === 'sprinklerIWN' ||
    value === 'pivotIWN' ||
    value === 'unitIdIWN'
  ) {
    return value;
  }
};
