import React, { Dispatch, FC, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TableCell, CheckboxWrapper, TableContainer, CaptionStyled, InnerContainer } from '..';

import { CheckBox, Input, Subtitle, Table } from '@yaradigitallabs/ahua-react';
import {
  DemandCalculationModule,
  FertigationIrrigationWaterNutrient,
  ModuleNameToAccordionNutrientState,
  FeatureConfigOptions,
} from '@common/types';
import { useUpdateDemandCalculationModule, filterUnitsById } from '@polaris-hooks/index';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { MAX_PERCENT_VALUE, MIN_PERCENT_VALUE } from '../DemandCalculations.constant';
import { METHOD } from '@common/constants';

import {
  CollapsibleNoPaddingContainer,
  Equation,
  HeaderForCollapsible,
  Bold,
} from '../../../shared';
import {
  getFertigationIrrigationWaterNutrient,
  getFertigationIrrigationWaterNutrientKey,
} from './utils/IrrigationWaterNutrientCollapsibleUtils';

interface IrrigationWaterNutrientCollapsibleProps {
  irrigationWaterNutrientModule: DemandCalculationModule;
  isModuleExpanded: boolean;
  setCurrentModulesAccordionState: Dispatch<SetStateAction<ModuleNameToAccordionNutrientState>>;
}

export const IrrigationWaterNutrientCollapsible: FC<IrrigationWaterNutrientCollapsibleProps> = ({
  irrigationWaterNutrientModule,
  isModuleExpanded,
  setCurrentModulesAccordionState,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.demandCalculations.irrigationWaterNutrient',
  });

  const {
    selectedCountryUnits,
    methods: { updateDemandCalculationModule },
  } = useAppContext();

  const { trigger: triggerUpdate } = useUpdateDemandCalculationModule(
    FeatureConfigOptions.FERTIGATION,
    irrigationWaterNutrientModule.id || '',
  );

  const configurationData: FertigationIrrigationWaterNutrient | undefined = useMemo(
    () => getFertigationIrrigationWaterNutrient(irrigationWaterNutrientModule.configuration.data),
    [irrigationWaterNutrientModule],
  );

  const [tableValues, setTableValues] = useState<FertigationIrrigationWaterNutrient>(() => ({
    dripIWN: configurationData?.dripIWN ?? Number(MAX_PERCENT_VALUE),
    microSprinklerIWN: configurationData?.microSprinklerIWN ?? Number(MAX_PERCENT_VALUE),
    sprinklerIWN: configurationData?.sprinklerIWN ?? Number(MAX_PERCENT_VALUE),
    pivotIWN: configurationData?.pivotIWN ?? Number(MAX_PERCENT_VALUE),
  }));

  useEffect(() => {
    setTableValues({
      dripIWN: configurationData?.dripIWN ?? Number(MAX_PERCENT_VALUE),
      microSprinklerIWN: configurationData?.microSprinklerIWN ?? Number(MAX_PERCENT_VALUE),
      sprinklerIWN: configurationData?.sprinklerIWN ?? Number(MAX_PERCENT_VALUE),
      pivotIWN: configurationData?.pivotIWN ?? Number(MAX_PERCENT_VALUE),
    });
  }, [configurationData]);

  const iwnUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, configurationData?.unitIdIWN),
    [selectedCountryUnits, configurationData],
  );

  const sendUpdateRequest = async (module: DemandCalculationModule) => {
    try {
      const updatedModule = await triggerUpdate({
        method: METHOD.PUT,
        body: JSON.stringify(module),
      });
      updatedModule && updateDemandCalculationModule(updatedModule);
    } catch (error) {
      console.error('Failed to update Irrigation Water Nutrient module:', error);
    }
  };

  const handleValueUpdate = async (value: string, prop: string) => {
    const data = getFertigationIrrigationWaterNutrient(
      irrigationWaterNutrientModule.configuration.data,
    );
    const iwnValue = Number(value) === 0 ? MIN_PERCENT_VALUE : value;
    const nutrientKey = getFertigationIrrigationWaterNutrientKey(prop);
    if (nutrientKey) {
      if (data?.[nutrientKey] === Number(iwnValue)) {
        return setTableValues((prev) => ({
          ...prev,
          [nutrientKey]: iwnValue,
        }));
      }

      irrigationWaterNutrientModule.configuration.data = {
        ...irrigationWaterNutrientModule.configuration.data,
        [nutrientKey]: Number(iwnValue),
      };

      sendUpdateRequest(irrigationWaterNutrientModule);
    }
  };

  const handleCheckboxUpdate = async () => {
    irrigationWaterNutrientModule = {
      ...irrigationWaterNutrientModule,
      isEnabled: !irrigationWaterNutrientModule.isEnabled,
    };

    sendUpdateRequest(irrigationWaterNutrientModule);
  };

  const getValidatedValue = (value: string) => {
    const validatedValue = value === '' ? value : parseInt(value, 10);
    if (
      value !== '' &&
      (Number(validatedValue) < Number(MIN_PERCENT_VALUE) ||
        Number(validatedValue) > Number(MAX_PERCENT_VALUE) ||
        isNaN(Number(validatedValue)))
    )
      return;

    return validatedValue;
  };

  const tableCellHeaders = [t('drip'), t('microSprinkler'), t('sprinkler'), t('pivot')];

  return (
    <>
      <CollapsibleNoPaddingContainer
        open={isModuleExpanded}
        data-cy='dc-irrigation-water-nutrient-collapsible'
        header={
          <HeaderForCollapsible
            title={t('headerTitle')}
            subtitle={t('headerSubtitle')}
            isStatusEnabled={irrigationWaterNutrientModule.isEnabled}
          />
        }
        className='collapsible-section'
        onOpenChange={() =>
          setCurrentModulesAccordionState((prev) => {
            const modules = {
              ...prev.accordionState,
              [irrigationWaterNutrientModule.name]: !isModuleExpanded,
            };
            return {
              nutrientId: prev.nutrientId,
              accordionState: modules,
            };
          })
        }
      >
        <CheckboxWrapper>
          <CheckBox
            ariaLabel='isEnabledText'
            checked={irrigationWaterNutrientModule.isEnabled}
            onClick={handleCheckboxUpdate}
            data-cy='isEnabled-toggle'
          />
          <CaptionStyled size='s'>{t('isEnabledText')}</CaptionStyled>
        </CheckboxWrapper>
        <InnerContainer>
          <Equation
            title={t('equationText')}
            equation={
              <span>
                {t('formulaIWN')}
                <Bold>{t('boldText')}</Bold> / 100)
              </span>
            }
            dataCy='fertigation-dc-irrigation-water-nutrient-equation'
          />
          <Subtitle size='s' css={{ paddingTop: '$x5' }}>
            {t('enterFields')}
          </Subtitle>
          <TableContainer>
            <tbody>
              <Table.Row>
                <TableCell colSpan={4} css={{ backgroundColor: '$blue0' }}>
                  {t('tableHeadText')} ({iwnUnit?.name})
                </TableCell>
              </Table.Row>
              <Table.Row>
                {tableCellHeaders.map((text, i) => (
                  <TableCell key={text + i}>{text}</TableCell>
                ))}
              </Table.Row>
              <Table.Row>
                {Object.entries(tableValues).map(([name, value], index) => (
                  <TableCell key={name + index}>
                    <Input
                      value={value}
                      onChange={({ target: { value } }) => {
                        const validatedValue = getValidatedValue(value);
                        if (validatedValue !== undefined) {
                          setTableValues((prev) => ({
                            ...prev,
                            [name]: validatedValue,
                          }));
                        }
                      }}
                      onBlur={({ target: { value } }) => {
                        handleValueUpdate(value, name);
                      }}
                      data-cy={`${name}-input`}
                      size='xs'
                    />
                  </TableCell>
                ))}
              </Table.Row>
            </tbody>
          </TableContainer>
        </InnerContainer>
      </CollapsibleNoPaddingContainer>
    </>
  );
};
