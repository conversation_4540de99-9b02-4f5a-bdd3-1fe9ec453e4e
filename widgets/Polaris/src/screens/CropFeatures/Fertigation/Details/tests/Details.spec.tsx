/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { fireEvent, render, waitFor, screen, within } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  countriesHandler,
  regionsHandler,
  allUnitsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  allPartnerTagsHandler,
  planValidationHandler,
  cnpValidationsHandler,
  updatedPlanValidationsHandler,
  yieldSolidUnitsHandler,
  updateCropRegionHandler,
  canModifyCropRegionGrowthScaleHandler,
  unitCountriesHandler,
  fertigationCropSettingsHandler,
  fertigationMMMValidationHandler,
  mockFertigationAppProviderValue,
  snackbarInitialStateMock,
  mockAppProviderValue,
  fertigationUnitSettingsResponse,
  fertigationMMMValidationRespone,
  fertigationUnitSettingsResponseValid,
} from '@common/mocks';

import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { BrowserRouter as Router, MemoryRouter, Routes, Route } from 'react-router-dom';
import { NavbarProvider } from '@libs/nav-context';
import FertigationPlanDetails from '../Details';
import {
  ConfigurationType,
  CropNutritionPlans,
  MMMValidation,
  CropRegion,
  ValidationStatus,
} from '@common/types';
import * as useCropSettings from '@widgets/Polaris/src/hooks/masterMindMap/useCropSettings/useCropSettings';
import * as useMMMValidations from '@widgets/Polaris/src/hooks/masterMindMap/useMMMValidations/useMMMValidations';
import { mmmValidations } from '../../../../Home/mock-data/MockData';
import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';

const server = setupServer(
  countriesHandler,
  regionsHandler,
  allUnitsHandler,
  cropSubclassesHandler,
  cropDescriptionsHandler,
  featuresHandler,
  cropRegionsHandler,
  allPartnerTagsHandler,
  planValidationHandler,
  cnpValidationsHandler,
  updatedPlanValidationsHandler,
  yieldSolidUnitsHandler,
  updateCropRegionHandler,
  canModifyCropRegionGrowthScaleHandler,
  unitCountriesHandler,
  fertigationCropSettingsHandler,
  fertigationMMMValidationHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useReducer: jest.fn().mockReturnValue([{}, jest.fn()]),
}));

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

jest.mock('@polaris-hooks/masterMindMap/useMMMValidations/useMMMValidations', () => ({
  useFetchMMMValidations: jest.fn(),
  useUpdateMMMValidation: jest.fn(() => ({
    trigger: jest.fn(),
  })),
}));

const mock = () => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});

describe('Fertigation Plan Homepage', () => {
  beforeEach(() => {
    window.IntersectionObserver = jest.fn().mockImplementation(mock);
    window.ResizeObserver = jest.fn().mockImplementation(mock);
  });
  const mockSetDisplaySnackbar = jest.fn();

  it('should initialize the context with default values and render the Fertigation Plan Details page', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();

    expect(component.getByTestId('mmm-details-content')).toBeInTheDocument();
    expect(component.getByTestId('crop-settings-card')).toBeInTheDocument();
    expect(component.getByTestId('mmm-configuration-card')).toBeInTheDocument();
  });

  it('should allow user to navigate to the Fertigation Plan configuration screen when plan is not validated', () => {
    const updatedValidation = {
      ...fertigationMMMValidationRespone,
      validationStatus: ValidationStatus.INVALIDATED,
    };
    const component = render(
      <AppContext.Provider
        value={{
          ...mockAppProviderValue,
          selectedMMMValidation: updatedValidation,
          selectedFeatureUnitSettings: fertigationUnitSettingsResponseValid,
        }}
      >
        <NavbarProvider>
          <AppContext.Consumer>
            {(_context) => (
              <MemoryRouter initialEntries={['/fertigation-plan']}>
                <Routes>
                  <Route path='fertigation-plan' element={<FertigationPlanDetails />} />
                  <Route
                    path={`/crop-features/fertigation-plan/crop-region/5e4750da-1ff4-4044-beb3-4275e0f994de/configuration/fertigation-plan/edit-configuration`}
                    element={
                      <div>
                        <h1>{CropNutritionPlans.FERTIGATION}</h1>
                      </div>
                    }
                  />
                </Routes>
              </MemoryRouter>
            )}
          </AppContext.Consumer>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const configureButton = component.getByTestId('configuration-card-configure-button');
    expect(configureButton).toBeInTheDocument();
    expect(configureButton).not.toBeDisabled();
    fireEvent.click(configureButton);
    expect(component.getByText(CropNutritionPlans.FERTIGATION)).toBeInTheDocument();
  });

  it('should forbid user to navigate to the Fertigation Plan configuration screen when plan is not validated and settings units are not all set', () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockAppProviderValue,
          selectedMMMValidation: {
            ...fertigationMMMValidationRespone,
            validationStatus: ValidationStatus.INVALIDATED,
          },
          selectedFeatureUnitSettings: fertigationUnitSettingsResponse,
        }}
      >
        <NavbarProvider>
          <AppContext.Consumer>
            {(_context) => (
              <MemoryRouter initialEntries={['/fertigation-plan']}>
                <Routes>
                  <Route path='fertigation-plan' element={<FertigationPlanDetails />} />
                  <Route
                    path={`/crop-features/fertigation-plan/crop-region/5e4750da-1ff4-4044-beb3-4275e0f994de/configuration/fertigation-plan/edit-configuration`}
                    element={
                      <div>
                        <h1>{CropNutritionPlans.FERTIGATION}</h1>
                      </div>
                    }
                  />
                </Routes>
              </MemoryRouter>
            )}
          </AppContext.Consumer>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const configureButton = component.getByTestId('configuration-card-configure-button');
    expect(configureButton).toBeInTheDocument();
    expect(configureButton).toBeDisabled();
  });

  it('should forbid user to navigate to the Fertigation Plan configuration screen when plan is validated', () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockAppProviderValue,
          selectedMMMValidation: { ...fertigationMMMValidationRespone },
        }}
      >
        <NavbarProvider>
          <AppContext.Consumer>
            {(_context) => (
              <MemoryRouter initialEntries={['/fertigation-plan']}>
                <Routes>
                  <Route path='fertigation-plan' element={<FertigationPlanDetails />} />
                  <Route
                    path={`/crop-features/fertigation-plan/crop-region/5e4750da-1ff4-4044-beb3-4275e0f994de/configuration/fertigation-plan/edit-configuration`}
                    element={
                      <div>
                        <h1>{CropNutritionPlans.FERTIGATION}</h1>
                      </div>
                    }
                  />
                </Routes>
              </MemoryRouter>
            )}
          </AppContext.Consumer>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const configureButton = component.getByTestId('configuration-card-configure-button');
    expect(configureButton).toBeInTheDocument();
    expect(configureButton).toBeDisabled();
  });

  it('should display Default yield with numeric value when it is available', () => {
    jest
      .spyOn(useCropSettings, 'useFetchCropSettings')
      .mockImplementation(() => fertigationUnitSettingsResponse);

    const defaultTargetYieldUnit = '1';

    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    expect(component.getByTestId('crop-settings-card')).toBeInTheDocument();

    const yieldSelect = component.getByTestId('default-target-yield-unit-select');
    expect(yieldSelect).toBeInTheDocument();

    const yieldValueLabel = yieldSelect.querySelector('label:last-of-type');
    expect(yieldValueLabel?.innerHTML).toEqual(defaultTargetYieldUnit);
  });

  it('should show a disabled button in the Fertigation Plan card', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const fertigationPlanCard = component.getByTestId('mmm-configuration-card');
    const buttons = within(fertigationPlanCard).getAllByRole('button');
    const disabledButton = buttons[0];

    expect(disabledButton).toBeInTheDocument();
    expect(disabledButton).toHaveTextContent('buttons.test');
    expect(disabledButton).toHaveAttribute('disabled');
  });

  it('should show checked validation switch in the Fertigation Plan card when soilAnalysisStatus of a plan validation is validated', () => {
    (useMMMValidations.useFetchMMMValidations as jest.Mock).mockReturnValue(
      mmmValidations.entities,
    );

    render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedMMMValidation: fertigationMMMValidationRespone,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const validationSwitch = screen.getByTestId('soil-validation-switch-btn');
    expect(validationSwitch.getAttribute('data-state')).toBe('checked');
  });

  it('should show unchecked validation switch in the Fertigation Plan card when soilAnalysisStatus of a plan validation is NOT validated', () => {
    const planValidationsWithUncheckedStatus: MMMValidation = {
      id: '59a3a562-a4c7-4084-9dc2-5fe3e43361b2',
      countryId: '9473c999-f14c-4a2f-9f95-531aa9a1cf4e',
      cropRegionId: '2963d361-a089-4bd8-ba0b-ffd019799587',
      validationStatus: ValidationStatus.NOT_SET,
      configurationType: ConfigurationType.Cereal,
      created: '2024-02-08T19:54:51.221Z',
      modified: '2024-02-08T19:54:51.221Z',
      modifiedBy: '<EMAIL>',
      deleted: null,
    };

    jest
      .spyOn(useMMMValidations, 'useFetchMMMValidations')
      .mockImplementation(() => [planValidationsWithUncheckedStatus]);

    render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const validationSwitch = screen.getByTestId('soil-validation-switch-btn');
    expect(validationSwitch.getAttribute('data-state')).toBe('unchecked');
  });

  it('should show disabled Edit button in the Crop settings card if Fertigation Plan has been validated', async () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedMMMValidation: fertigationMMMValidationRespone,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const editButton = component.getByTestId('crop-settings-edit-button');
    expect(editButton).toBeDisabled();
  });

  it('should show enabled clickable Edit button in the Crop settings card if Fertigation Plan has NOT been validated', () => {
    jest.spyOn(useMMMValidations, 'useFetchMMMValidations').mockImplementation(() => [
      {
        ...fertigationMMMValidationRespone,
        validationStatus: ValidationStatus.INVALIDATED,
      },
    ]);

    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          cropRegion: {
            ...mockFertigationAppProviderValue.cropRegion,
            id: '111',
          } as CropRegion,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{(_context) => <FertigationPlanDetails />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const editButton = component.getByTestId('crop-settings-edit-button');
    expect(editButton).toBeInTheDocument();
    expect(editButton).not.toHaveAttribute('disabled');
  });

  it('should switch state when FP plan validate switch is clicked', async () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {(_context) => (
                <SnackbarContext.Provider
                  value={{
                    displaySnackbar: snackbarInitialStateMock,
                    setDisplaySnackbar: mockSetDisplaySnackbar,
                  }}
                >
                  <FertigationPlanDetails />
                </SnackbarContext.Provider>
              )}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const switchBtn = component.getByTestId('soil-validation-switch-btn');
    expect(switchBtn).toHaveAttribute('data-state', 'unchecked');
    fireEvent.click(switchBtn);
    waitFor(() => {
      expect(switchBtn).toHaveAttribute('data-state', 'checked');
    });
  });
});
