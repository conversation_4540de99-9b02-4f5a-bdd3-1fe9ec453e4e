// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Fertigation Plan Homepage should initialize the context with default values and render the Fertigation Plan Details page 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-hAzCDl"
        data-cy="mmm-details-content"
      >
        <h1
          class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ijZBIvl-css"
          data-cy="mmm-details-title"
        >
          polaris.cmmmDetails.title
        </h1>
        <a
          class="c-wzBoY"
          data-cy="crop-settings-card"
        >
          <div
            class="card-header"
          >
            <div
              class="c-jCarvd c-jCarvd-ikfQhrm-css"
            >
              <div
                class="c-cVKzzi"
              >
                <div
                  class="c-cXFqtJ"
                >
                  <h1
                    class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                  >
                    cropSettings.settingsTitle
                  </h1>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
                />
              </div>
              <div
                class="c-hcUxto"
              >
                <div
                  data-state="closed"
                >
                  <button
                    class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-sXzsQ-cv c-kkaoVB"
                    data-cy="crop-settings-edit-button"
                    disabled=""
                    title="cropSettings.editButton"
                    type="button"
                  >
                    <svg
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                      />
                    </svg>
                    <span
                      class="c-iepcqn"
                    >
                      cropSettings.editButton
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
            data-cy="crop-settings-card-body"
          >
            <div
              data-cy="yield-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.yieldUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r1:"
                    aria-expanded="false"
                    aria-label="Yield unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="default-target-yield-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.defaultTargetYieldUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    1
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r2:"
                    aria-expanded="false"
                    aria-label="Default target yield unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="nutrient-removal-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.nutrientRemovalUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r3:"
                    aria-expanded="false"
                    aria-label="Nutrient removal unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M14.405 17.29a6.83 6.83 0 0 1-4.786 1.564 6.88 6.88 0 0 1-4.152-1.765M15 11.583a5.346 5.346 0 0 1-2.007 3.141 4.88 4.88 0 0 1-3.485.947 4.987 4.987 0 0 1-3.203-1.725 5.423 5.423 0 0 1-1.174-2.363m5.933-.026c-.012.143-.05.282-.115.409a1.05 1.05 0 0 1-.258.328.96.96 0 0 1-1.147.086 1.03 1.03 0 0 1-.3-.286 1.087 1.087 0 0 1-.178-.813m1.022-7.82S7.647 5.936 7.063 6.624c-.585.689-1.444 1.807-1.616 2.115-.172.307-.312 1.324-.344 1.876C5.07 11.197 5 14.293 5 15.05c0 .757.172 2.131.24 2.922.07.79.344 2.097.551 2.474.207.377.55.55.413.756-.138.206-.582.165-.55.481.032.316.412.317.55.317h11.448s.473.004.618-.093c.206-.137.138-.326-.069-.429a1.263 1.263 0 0 1-.378-.343c.217-.246.412-.51.582-.79.24-.413.447-1.376.582-2.475.135-1.1.068-3.747.068-4.985 0-1.238 0-2.029-.068-2.785a4.983 4.983 0 0 0-.275-1.306c-.378-1.272-4.538-5.226-4.538-5.226m-1.262-1.395c-.86-.413-1.528.02-1.87.206-.326.176-1.224.893-1.224.893l.955.64s.31 1.169.413 1.41c.212.493.549.791 1.017.791.469 0 .7-.379.906-.791.206-.413.48-1.376.48-1.376l.819-.632s-.635-.732-1.496-1.141z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="nutrient-demand-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.nutrientDemandUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r4:"
                    aria-expanded="false"
                    aria-label="Nutrient demand unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M14.405 17.29a6.83 6.83 0 0 1-4.786 1.564 6.88 6.88 0 0 1-4.152-1.765M15 11.583a5.346 5.346 0 0 1-2.007 3.141 4.88 4.88 0 0 1-3.485.947 4.987 4.987 0 0 1-3.203-1.725 5.423 5.423 0 0 1-1.174-2.363m5.933-.026c-.012.143-.05.282-.115.409a1.05 1.05 0 0 1-.258.328.96.96 0 0 1-1.147.086 1.03 1.03 0 0 1-.3-.286 1.087 1.087 0 0 1-.178-.813m1.022-7.82S7.647 5.936 7.063 6.624c-.585.689-1.444 1.807-1.616 2.115-.172.307-.312 1.324-.344 1.876C5.07 11.197 5 14.293 5 15.05c0 .757.172 2.131.24 2.922.07.79.344 2.097.551 2.474.207.377.55.55.413.756-.138.206-.582.165-.55.481.032.316.412.317.55.317h11.448s.473.004.618-.093c.206-.137.138-.326-.069-.429a1.263 1.263 0 0 1-.378-.343c.217-.246.412-.51.582-.79.24-.413.447-1.376.582-2.475.135-1.1.068-3.747.068-4.985 0-1.238 0-2.029-.068-2.785a4.983 4.983 0 0 0-.275-1.306c-.378-1.272-4.538-5.226-4.538-5.226m-1.262-1.395c-.86-.413-1.528.02-1.87.206-.326.176-1.224.893-1.224.893l.955.64s.31 1.169.413 1.41c.212.493.549.791 1.017.791.469 0 .7-.379.906-.791.206-.413.48-1.376.48-1.376l.819-.632s-.635-.732-1.496-1.141z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
          </div>
          <div
            class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
            data-cy="crop-settings-card-body"
          >
            <div
              data-cy="recommended-solid-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r5:"
                    aria-expanded="false"
                    aria-label="Recommendation solid unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.9874 16.6176C12.6275 17.7362 10.9248 18.2922 9.20137 18.1805C7.66701 18.0811 6.21084 17.4585 5.04963 16.4159M14.5824 10.9095C14.3018 12.1796 13.5877 13.2973 12.5755 14.0509C11.5634 14.8044 10.3234 15.1414 9.09064 14.9979C7.85789 14.8544 6.71804 14.2405 5.88712 13.2725C5.30766 12.5975 4.90653 11.7829 4.71355 10.9095M10.6462 10.8841C10.6347 11.027 10.5957 11.166 10.5317 11.2926C10.4676 11.4193 10.3798 11.5309 10.2735 11.6208C10.1673 11.7108 10.0447 11.7771 9.91338 11.8158C9.78203 11.8545 9.64457 11.8649 9.50932 11.8462C9.37406 11.8274 9.24383 11.7801 9.12648 11.7069C9.00913 11.6338 8.90711 11.5364 8.82659 11.4206C8.74607 11.3049 8.68872 11.1731 8.65801 11.0334C8.6273 10.8937 8.62387 10.7488 8.64792 10.6077M8.65213 5.90767H8.75731M11.4138 5.90767H15.1987M5.81192 19.9071C5.81192 19.4638 4.71332 19.4508 4.71332 18.177V4.45107C4.71332 4.45107 3.92993 4.04 3.92993 3.33584C3.92993 2.70342 4.69651 2.40002 5.25828 2.40002H18.6292C19.1433 2.40002 19.8999 2.71207 19.8999 3.33584C19.8999 4.04199 19.1154 4.45107 19.1154 4.45107V18.177C19.1154 19.4924 18.1979 19.4685 18.1979 19.9071C18.1979 20.3457 20.0032 21.1292 19.1154 22.0374C18.2277 22.9456 16.7804 21.3894 16.7804 21.3894H7.24847C7.24847 21.3894 5.42397 22.8822 4.71332 22.0374C3.93724 21.1149 5.81192 20.3504 5.81192 19.9071Z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="recommended-liquids-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerArea
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r6:"
                    aria-expanded="false"
                    aria-label="Recommendation liquids unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.4545 4.72727H13.1818C13.6839 4.72727 14.0909 4.32026 14.0909 3.81818V2.90909C14.0909 2.40701 13.6839 2 13.1818 2H10.4545C9.95247 2 9.54545 2.40701 9.54545 2.90909V3.81818C9.54545 4.32026 9.95247 4.72727 10.4545 4.72727ZM10.4545 4.72727H5.90909M8.18182 12L8.24297 12.0902C8.73974 12.8226 9.74906 12.9889 10.4545 12.4545M5 13.8182L5.36238 14.1369C7.39041 15.9203 10.2832 16.3307 12.7273 15.1818M14.5455 17.9091L12.8724 18.4509C10.2307 19.3063 7.34529 18.9411 5 17.4545M11.4616 6.16222L6.46161 7.16222C5.61174 7.3322 5 8.07841 5 8.9451V20.1818C5 21.186 5.81403 22 6.81818 22H16.8182C17.8223 22 18.6364 21.186 18.6364 20.1818V8.9451C18.6364 8.07841 18.0246 7.3322 17.1748 7.16222L12.1748 6.16222C11.9394 6.11515 11.697 6.11515 11.4616 6.16222Z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="recommended-solid-per-plant-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.recommendationSolidsUnitPerPlant
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r7:"
                    aria-expanded="false"
                    aria-label="Recommendation solid unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.9874 16.6176C12.6275 17.7362 10.9248 18.2922 9.20137 18.1805C7.66701 18.0811 6.21084 17.4585 5.04963 16.4159M14.5824 10.9095C14.3018 12.1796 13.5877 13.2973 12.5755 14.0509C11.5634 14.8044 10.3234 15.1414 9.09064 14.9979C7.85789 14.8544 6.71804 14.2405 5.88712 13.2725C5.30766 12.5975 4.90653 11.7829 4.71355 10.9095M10.6462 10.8841C10.6347 11.027 10.5957 11.166 10.5317 11.2926C10.4676 11.4193 10.3798 11.5309 10.2735 11.6208C10.1673 11.7108 10.0447 11.7771 9.91338 11.8158C9.78203 11.8545 9.64457 11.8649 9.50932 11.8462C9.37406 11.8274 9.24383 11.7801 9.12648 11.7069C9.00913 11.6338 8.90711 11.5364 8.82659 11.4206C8.74607 11.3049 8.68872 11.1731 8.65801 11.0334C8.6273 10.8937 8.62387 10.7488 8.64792 10.6077M8.65213 5.90767H8.75731M11.4138 5.90767H15.1987M5.81192 19.9071C5.81192 19.4638 4.71332 19.4508 4.71332 18.177V4.45107C4.71332 4.45107 3.92993 4.04 3.92993 3.33584C3.92993 2.70342 4.69651 2.40002 5.25828 2.40002H18.6292C19.1433 2.40002 19.8999 2.71207 19.8999 3.33584C19.8999 4.04199 19.1154 4.45107 19.1154 4.45107V18.177C19.1154 19.4924 18.1979 19.4685 18.1979 19.9071C18.1979 20.3457 20.0032 21.1292 19.1154 22.0374C18.2277 22.9456 16.7804 21.3894 16.7804 21.3894H7.24847C7.24847 21.3894 5.42397 22.8822 4.71332 22.0374C3.93724 21.1149 5.81192 20.3504 5.81192 19.9071Z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="recommended-liquids-per-plant-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerPlant
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r8:"
                    aria-expanded="false"
                    aria-label="Recommendation liquids unit select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.4545 4.72727H13.1818C13.6839 4.72727 14.0909 4.32026 14.0909 3.81818V2.90909C14.0909 2.40701 13.6839 2 13.1818 2H10.4545C9.95247 2 9.54545 2.40701 9.54545 2.90909V3.81818C9.54545 4.32026 9.95247 4.72727 10.4545 4.72727ZM10.4545 4.72727H5.90909M8.18182 12L8.24297 12.0902C8.73974 12.8226 9.74906 12.9889 10.4545 12.4545M5 13.8182L5.36238 14.1369C7.39041 15.9203 10.2832 16.3307 12.7273 15.1818M14.5455 17.9091L12.8724 18.4509C10.2307 19.3063 7.34529 18.9411 5 17.4545M11.4616 6.16222L6.46161 7.16222C5.61174 7.3322 5 8.07841 5 8.9451V20.1818C5 21.186 5.81403 22 6.81818 22H16.8182C17.8223 22 18.6364 21.186 18.6364 20.1818V8.9451C18.6364 8.07841 18.0246 7.3322 17.1748 7.16222L12.1748 6.16222C11.9394 6.11515 11.697 6.11515 11.4616 6.16222Z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
          </div>
          <div
            class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
            data-cy="crop-settings-card-body"
          >
            <div
              data-cy="total-irrigation-water-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.totalIrrigationWaterUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:r9:"
                    aria-expanded="false"
                    aria-label="Total irrigation water select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M4 10.768c.924.198 1.508.763 2.147 1.24m11.945 1.087c.712-.147 1.391-.418 1.908-.849M4 17.904c1.61.345 2.187 1.804 3.829 1.971 1.914.195 2.718-1.478 4.644-1.478 1.925 0 2.31 1.97 4.236 1.971 1.151 0 2.44-.277 3.291-.986M4 3.304c1.61.345 2.187 1.804 3.829 1.971 1.914.195 2.718-1.478 4.644-1.478 1.925 0 2.31 1.97 4.236 1.97 1.151 0 2.44-.276 3.291-.985m-6.96 5.708c-.3-.197-1.036-.654-1.036-.654s-.745.457-1.04.654a1.817 1.817 0 0 0-.818 1.518c0 1.013.832 1.828 1.854 1.828 1.022 0 1.854-.82 1.854-1.828a1.813 1.813 0 0 0-.814-1.518z"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
            <div
              data-cy="plant-density-unit-select"
              style="display: inline-block; width: 100%;"
            >
              <div
                class="c-jGFTiO c-jGFTiO-ubosY-state-default"
              >
                <div
                  class="c-kFLrJl"
                >
                  <label
                    class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                  >
                    polaris.fpDetails.cropSettings.plantDensityUnit
                  </label>
                  <label
                    class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                  >
                    -
                  </label>
                  <button
                    aria-autocomplete="none"
                    aria-controls="radix-:ra:"
                    aria-expanded="false"
                    aria-label="Plant density select element"
                    class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                    data-state="closed"
                    dir="ltr"
                    role="combobox"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      style="pointer-events: none;"
                    >
                      <div
                        class="c-fSebPZ"
                      >
                        <svg
                          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                          />
                        </svg>
                      </div>
                    </span>
                    <svg
                      aria-hidden="true"
                      class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18 9.75l-5 6h-2l-5-6"
                      />
                    </svg>
                  </button>
                </div>
                <p
                  class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
                />
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-iTKOFX-orientation-vertical c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-kdEBiP"
          >
            <label
              class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-ibPNjhd-css partners-title"
              data-cy="crop-setting-partners-title"
            >
              cropSettings.partnersTitle
            </label>
            <div
              class="c-bfPkPS c-bfPkPS-iczWuCV-css stack"
            />
          </div>
        </a>
        <a
          class="c-wzBoY c-wzBoY-ikclSel-css"
          data-cy="mmm-configuration-card"
        >
          <div
            class="c-hPgkjm"
          >
            <div
              class="c-iQKjxI"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-kVDIWi"
              >
                title
              </h1>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iiffMpN-css c-fIVxPx"
                data-state="closed"
              >
                polaris.sharedText.validatedStatus
              </p>
            </div>
            <div
              class="validate-switch"
            >
              <div
                class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-icOKMlW-css"
                >
                  switchLabel
                </label>
                <button
                  aria-checked="true"
                  class="c-dyBzGm c-dyBzGm-iiQcApQ-css"
                  data-cy="soil-validation-switch-btn"
                  data-state="checked"
                  role="switch"
                  type="button"
                  value="on"
                >
                  <span
                    class="c-bILIWL"
                    data-state="checked"
                  />
                </button>
              </div>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical plan-details-configure-nav-content"
          >
            <div
              class="c-hPExTj c-hPExTj-ibUIiAd-css"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
              >
                content.title
              </h1>
              <p
                class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
              >
                content.description
              </p>
            </div>
          </div>
          <div
            class="c-fZEhOm"
          />
          <div
            class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
          >
            <div
              data-state="closed"
              style="flex: 1 1 0%;"
            >
              <button
                class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv"
                data-cy="configuration-card-test-harness-button"
                disabled=""
              >
                <span
                  class="c-iepcqn"
                >
                  buttons.test
                </span>
              </button>
            </div>
            <div
              style="flex: 1 1 0%;"
            >
              <button
                class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-bETQVM-variant-primary c-hRrCwb-evtOnb-cv"
                data-cy="configuration-card-configure-button"
                disabled=""
              >
                <span
                  class="c-iepcqn"
                >
                  buttons.configure
                </span>
              </button>
            </div>
          </div>
        </a>
        <div
          class="crop-settings-edit-popup-root"
        />
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-hAzCDl"
      data-cy="mmm-details-content"
    >
      <h1
        class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-ijZBIvl-css"
        data-cy="mmm-details-title"
      >
        polaris.cmmmDetails.title
      </h1>
      <a
        class="c-wzBoY"
        data-cy="crop-settings-card"
      >
        <div
          class="card-header"
        >
          <div
            class="c-jCarvd c-jCarvd-ikfQhrm-css"
          >
            <div
              class="c-cVKzzi"
            >
              <div
                class="c-cXFqtJ"
              >
                <h1
                  class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
                >
                  cropSettings.settingsTitle
                </h1>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
              />
            </div>
            <div
              class="c-hcUxto"
            >
              <div
                data-state="closed"
              >
                <button
                  class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-coREMZ-variant-ghost c-hRrCwb-sXzsQ-cv c-kkaoVB"
                  data-cy="crop-settings-edit-button"
                  disabled=""
                  title="cropSettings.editButton"
                  type="button"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18.67 9.084l1.841-1.841a1 1 0 0 0 0-1.414L18.39 3.707a1 1 0 0 0-1.414 0l-1.841 1.841m3.535 3.536L7.875 19.878a1 1 0 0 1-.58.285l-2.432.31a1 1 0 0 1-1.118-1.118l.31-2.432a1 1 0 0 1 .285-.58L15.135 5.548m3.535 3.536l-3.535-3.536"
                    />
                  </svg>
                  <span
                    class="c-iepcqn"
                  >
                    cropSettings.editButton
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
          data-cy="crop-settings-card-body"
        >
          <div
            data-cy="yield-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.yieldUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r1:"
                  aria-expanded="false"
                  aria-label="Yield unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="default-target-yield-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.defaultTargetYieldUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  1
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r2:"
                  aria-expanded="false"
                  aria-label="Default target yield unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="nutrient-removal-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.nutrientRemovalUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r3:"
                  aria-expanded="false"
                  aria-label="Nutrient removal unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.405 17.29a6.83 6.83 0 0 1-4.786 1.564 6.88 6.88 0 0 1-4.152-1.765M15 11.583a5.346 5.346 0 0 1-2.007 3.141 4.88 4.88 0 0 1-3.485.947 4.987 4.987 0 0 1-3.203-1.725 5.423 5.423 0 0 1-1.174-2.363m5.933-.026c-.012.143-.05.282-.115.409a1.05 1.05 0 0 1-.258.328.96.96 0 0 1-1.147.086 1.03 1.03 0 0 1-.3-.286 1.087 1.087 0 0 1-.178-.813m1.022-7.82S7.647 5.936 7.063 6.624c-.585.689-1.444 1.807-1.616 2.115-.172.307-.312 1.324-.344 1.876C5.07 11.197 5 14.293 5 15.05c0 .757.172 2.131.24 2.922.07.79.344 2.097.551 2.474.207.377.55.55.413.756-.138.206-.582.165-.55.481.032.316.412.317.55.317h11.448s.473.004.618-.093c.206-.137.138-.326-.069-.429a1.263 1.263 0 0 1-.378-.343c.217-.246.412-.51.582-.79.24-.413.447-1.376.582-2.475.135-1.1.068-3.747.068-4.985 0-1.238 0-2.029-.068-2.785a4.983 4.983 0 0 0-.275-1.306c-.378-1.272-4.538-5.226-4.538-5.226m-1.262-1.395c-.86-.413-1.528.02-1.87.206-.326.176-1.224.893-1.224.893l.955.64s.31 1.169.413 1.41c.212.493.549.791 1.017.791.469 0 .7-.379.906-.791.206-.413.48-1.376.48-1.376l.819-.632s-.635-.732-1.496-1.141z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="nutrient-demand-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.nutrientDemandUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r4:"
                  aria-expanded="false"
                  aria-label="Nutrient demand unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.405 17.29a6.83 6.83 0 0 1-4.786 1.564 6.88 6.88 0 0 1-4.152-1.765M15 11.583a5.346 5.346 0 0 1-2.007 3.141 4.88 4.88 0 0 1-3.485.947 4.987 4.987 0 0 1-3.203-1.725 5.423 5.423 0 0 1-1.174-2.363m5.933-.026c-.012.143-.05.282-.115.409a1.05 1.05 0 0 1-.258.328.96.96 0 0 1-1.147.086 1.03 1.03 0 0 1-.3-.286 1.087 1.087 0 0 1-.178-.813m1.022-7.82S7.647 5.936 7.063 6.624c-.585.689-1.444 1.807-1.616 2.115-.172.307-.312 1.324-.344 1.876C5.07 11.197 5 14.293 5 15.05c0 .757.172 2.131.24 2.922.07.79.344 2.097.551 2.474.207.377.55.55.413.756-.138.206-.582.165-.55.481.032.316.412.317.55.317h11.448s.473.004.618-.093c.206-.137.138-.326-.069-.429a1.263 1.263 0 0 1-.378-.343c.217-.246.412-.51.582-.79.24-.413.447-1.376.582-2.475.135-1.1.068-3.747.068-4.985 0-1.238 0-2.029-.068-2.785a4.983 4.983 0 0 0-.275-1.306c-.378-1.272-4.538-5.226-4.538-5.226m-1.262-1.395c-.86-.413-1.528.02-1.87.206-.326.176-1.224.893-1.224.893l.955.64s.31 1.169.413 1.41c.212.493.549.791 1.017.791.469 0 .7-.379.906-.791.206-.413.48-1.376.48-1.376l.819-.632s-.635-.732-1.496-1.141z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
        </div>
        <div
          class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
          data-cy="crop-settings-card-body"
        >
          <div
            data-cy="recommended-solid-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r5:"
                  aria-expanded="false"
                  aria-label="Recommendation solid unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.9874 16.6176C12.6275 17.7362 10.9248 18.2922 9.20137 18.1805C7.66701 18.0811 6.21084 17.4585 5.04963 16.4159M14.5824 10.9095C14.3018 12.1796 13.5877 13.2973 12.5755 14.0509C11.5634 14.8044 10.3234 15.1414 9.09064 14.9979C7.85789 14.8544 6.71804 14.2405 5.88712 13.2725C5.30766 12.5975 4.90653 11.7829 4.71355 10.9095M10.6462 10.8841C10.6347 11.027 10.5957 11.166 10.5317 11.2926C10.4676 11.4193 10.3798 11.5309 10.2735 11.6208C10.1673 11.7108 10.0447 11.7771 9.91338 11.8158C9.78203 11.8545 9.64457 11.8649 9.50932 11.8462C9.37406 11.8274 9.24383 11.7801 9.12648 11.7069C9.00913 11.6338 8.90711 11.5364 8.82659 11.4206C8.74607 11.3049 8.68872 11.1731 8.65801 11.0334C8.6273 10.8937 8.62387 10.7488 8.64792 10.6077M8.65213 5.90767H8.75731M11.4138 5.90767H15.1987M5.81192 19.9071C5.81192 19.4638 4.71332 19.4508 4.71332 18.177V4.45107C4.71332 4.45107 3.92993 4.04 3.92993 3.33584C3.92993 2.70342 4.69651 2.40002 5.25828 2.40002H18.6292C19.1433 2.40002 19.8999 2.71207 19.8999 3.33584C19.8999 4.04199 19.1154 4.45107 19.1154 4.45107V18.177C19.1154 19.4924 18.1979 19.4685 18.1979 19.9071C18.1979 20.3457 20.0032 21.1292 19.1154 22.0374C18.2277 22.9456 16.7804 21.3894 16.7804 21.3894H7.24847C7.24847 21.3894 5.42397 22.8822 4.71332 22.0374C3.93724 21.1149 5.81192 20.3504 5.81192 19.9071Z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="recommended-liquids-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerArea
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r6:"
                  aria-expanded="false"
                  aria-label="Recommendation liquids unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.4545 4.72727H13.1818C13.6839 4.72727 14.0909 4.32026 14.0909 3.81818V2.90909C14.0909 2.40701 13.6839 2 13.1818 2H10.4545C9.95247 2 9.54545 2.40701 9.54545 2.90909V3.81818C9.54545 4.32026 9.95247 4.72727 10.4545 4.72727ZM10.4545 4.72727H5.90909M8.18182 12L8.24297 12.0902C8.73974 12.8226 9.74906 12.9889 10.4545 12.4545M5 13.8182L5.36238 14.1369C7.39041 15.9203 10.2832 16.3307 12.7273 15.1818M14.5455 17.9091L12.8724 18.4509C10.2307 19.3063 7.34529 18.9411 5 17.4545M11.4616 6.16222L6.46161 7.16222C5.61174 7.3322 5 8.07841 5 8.9451V20.1818C5 21.186 5.81403 22 6.81818 22H16.8182C17.8223 22 18.6364 21.186 18.6364 20.1818V8.9451C18.6364 8.07841 18.0246 7.3322 17.1748 7.16222L12.1748 6.16222C11.9394 6.11515 11.697 6.11515 11.4616 6.16222Z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="recommended-solid-per-plant-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.recommendationSolidsUnitPerPlant
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r7:"
                  aria-expanded="false"
                  aria-label="Recommendation solid unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.9874 16.6176C12.6275 17.7362 10.9248 18.2922 9.20137 18.1805C7.66701 18.0811 6.21084 17.4585 5.04963 16.4159M14.5824 10.9095C14.3018 12.1796 13.5877 13.2973 12.5755 14.0509C11.5634 14.8044 10.3234 15.1414 9.09064 14.9979C7.85789 14.8544 6.71804 14.2405 5.88712 13.2725C5.30766 12.5975 4.90653 11.7829 4.71355 10.9095M10.6462 10.8841C10.6347 11.027 10.5957 11.166 10.5317 11.2926C10.4676 11.4193 10.3798 11.5309 10.2735 11.6208C10.1673 11.7108 10.0447 11.7771 9.91338 11.8158C9.78203 11.8545 9.64457 11.8649 9.50932 11.8462C9.37406 11.8274 9.24383 11.7801 9.12648 11.7069C9.00913 11.6338 8.90711 11.5364 8.82659 11.4206C8.74607 11.3049 8.68872 11.1731 8.65801 11.0334C8.6273 10.8937 8.62387 10.7488 8.64792 10.6077M8.65213 5.90767H8.75731M11.4138 5.90767H15.1987M5.81192 19.9071C5.81192 19.4638 4.71332 19.4508 4.71332 18.177V4.45107C4.71332 4.45107 3.92993 4.04 3.92993 3.33584C3.92993 2.70342 4.69651 2.40002 5.25828 2.40002H18.6292C19.1433 2.40002 19.8999 2.71207 19.8999 3.33584C19.8999 4.04199 19.1154 4.45107 19.1154 4.45107V18.177C19.1154 19.4924 18.1979 19.4685 18.1979 19.9071C18.1979 20.3457 20.0032 21.1292 19.1154 22.0374C18.2277 22.9456 16.7804 21.3894 16.7804 21.3894H7.24847C7.24847 21.3894 5.42397 22.8822 4.71332 22.0374C3.93724 21.1149 5.81192 20.3504 5.81192 19.9071Z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="recommended-liquids-per-plant-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerPlant
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r8:"
                  aria-expanded="false"
                  aria-label="Recommendation liquids unit select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.4545 4.72727H13.1818C13.6839 4.72727 14.0909 4.32026 14.0909 3.81818V2.90909C14.0909 2.40701 13.6839 2 13.1818 2H10.4545C9.95247 2 9.54545 2.40701 9.54545 2.90909V3.81818C9.54545 4.32026 9.95247 4.72727 10.4545 4.72727ZM10.4545 4.72727H5.90909M8.18182 12L8.24297 12.0902C8.73974 12.8226 9.74906 12.9889 10.4545 12.4545M5 13.8182L5.36238 14.1369C7.39041 15.9203 10.2832 16.3307 12.7273 15.1818M14.5455 17.9091L12.8724 18.4509C10.2307 19.3063 7.34529 18.9411 5 17.4545M11.4616 6.16222L6.46161 7.16222C5.61174 7.3322 5 8.07841 5 8.9451V20.1818C5 21.186 5.81403 22 6.81818 22H16.8182C17.8223 22 18.6364 21.186 18.6364 20.1818V8.9451C18.6364 8.07841 18.0246 7.3322 17.1748 7.16222L12.1748 6.16222C11.9394 6.11515 11.697 6.11515 11.4616 6.16222Z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
        </div>
        <div
          class="c-kVBBIh c-jVVrXY c-kVBBIh-eJHswK-orientation-horizontal c-jVVrXY-iQvIAm-state-contentFromStart"
          data-cy="crop-settings-card-body"
        >
          <div
            data-cy="total-irrigation-water-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.totalIrrigationWaterUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:r9:"
                  aria-expanded="false"
                  aria-label="Total irrigation water select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M4 10.768c.924.198 1.508.763 2.147 1.24m11.945 1.087c.712-.147 1.391-.418 1.908-.849M4 17.904c1.61.345 2.187 1.804 3.829 1.971 1.914.195 2.718-1.478 4.644-1.478 1.925 0 2.31 1.97 4.236 1.971 1.151 0 2.44-.277 3.291-.986M4 3.304c1.61.345 2.187 1.804 3.829 1.971 1.914.195 2.718-1.478 4.644-1.478 1.925 0 2.31 1.97 4.236 1.97 1.151 0 2.44-.276 3.291-.985m-6.96 5.708c-.3-.197-1.036-.654-1.036-.654s-.745.457-1.04.654a1.817 1.817 0 0 0-.818 1.518c0 1.013.832 1.828 1.854 1.828 1.022 0 1.854-.82 1.854-1.828a1.813 1.813 0 0 0-.814-1.518z"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
          <div
            data-cy="plant-density-unit-select"
            style="display: inline-block; width: 100%;"
          >
            <div
              class="c-jGFTiO c-jGFTiO-ubosY-state-default"
            >
              <div
                class="c-kFLrJl"
              >
                <label
                  class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-iLfdtR c-iLfdtR-fteRNr-selectSize-s c-iLfdtR-hIzKkk-iconPresent-true"
                >
                  polaris.fpDetails.cropSettings.plantDensityUnit
                </label>
                <label
                  class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-WRIat c-PJLV c-WRIat-fiMQNM-iconPresent-true c-WRIat-jqMIxA-textType-item c-PJLV-hDbASB-selectSize-s"
                >
                  -
                </label>
                <button
                  aria-autocomplete="none"
                  aria-controls="radix-:ra:"
                  aria-expanded="false"
                  aria-label="Plant density select element"
                  class="c-fExIjO c-fExIjO-dbIciD-size-s c-fExIjO-kRNjEX-variant-default c-fExIjO-fMHODY-readOnly-true c-fExIjO-inBkMG-cover-outline c-fExIjO-iPJLV-css"
                  data-state="closed"
                  dir="ltr"
                  role="combobox"
                  tabindex="0"
                  type="button"
                >
                  <span
                    style="pointer-events: none;"
                  >
                    <div
                      class="c-fSebPZ"
                    >
                      <svg
                        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10.99 16.88c-1.984.032-2.368 1.914-2.47 1.968-.1.053-1.585-.962-3.241 0-1.657.962-1.581 2.7-1.581 2.7h14.7s-.011-1.736-1.547-2.724c-1.537-.988-3.174.093-3.34.047-.164-.047-.385-1.78-2.52-1.99zm0 0c.065-2.185.165-5.08.165-5.08m.02-3.16s4.268.667 6.532-1.013c2.458-1.825 2.99-5.171 2.636-5.171-.355 0-4.38-.352-6.16.836-1.78 1.188-3.008 2.738-3.008 5.349zm0 0v1.993m0 0s-.96-2.725-3.405-3.31c-1.438-.345-3.194-.431-3.675-.431-.376 0 .328 2.99 1.926 4.055 1.734 1.156 5.135.853 5.135.853m.019-1.167l-.02 1.167"
                        />
                      </svg>
                    </div>
                  </span>
                  <svg
                    aria-hidden="true"
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-icKbjjX-css"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M18 9.75l-5 6h-2l-5-6"
                    />
                  </svg>
                </button>
              </div>
              <p
                class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-coqULC"
              />
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-cvAFBk c-cvAFBk-iTKOFX-orientation-vertical c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-kdEBiP"
        >
          <label
            class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-ibPNjhd-css partners-title"
            data-cy="crop-setting-partners-title"
          >
            cropSettings.partnersTitle
          </label>
          <div
            class="c-bfPkPS c-bfPkPS-iczWuCV-css stack"
          />
        </div>
      </a>
      <a
        class="c-wzBoY c-wzBoY-ikclSel-css"
        data-cy="mmm-configuration-card"
      >
        <div
          class="c-hPgkjm"
        >
          <div
            class="c-iQKjxI"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-kVDIWi"
            >
              title
            </h1>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iiffMpN-css c-fIVxPx"
              data-state="closed"
            >
              polaris.sharedText.validatedStatus
            </p>
          </div>
          <div
            class="validate-switch"
          >
            <div
              class="c-UazGY c-UazGY-ejCoEP-labelPosition-left"
            >
              <label
                class="c-jAwvtv c-jAwvtv-fADHcj-size-s c-jAwvtv-iPJLV-css c-leHWbT c-leHWbT-fwMXZD-labelPosition-left c-leHWbT-icOKMlW-css"
              >
                switchLabel
              </label>
              <button
                aria-checked="true"
                class="c-dyBzGm c-dyBzGm-iiQcApQ-css"
                data-cy="soil-validation-switch-btn"
                data-state="checked"
                role="switch"
                type="button"
                value="on"
              >
                <span
                  class="c-bILIWL"
                  data-state="checked"
                />
              </button>
            </div>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-kVBBIh c-kVBBIh-hakyQ-orientation-vertical plan-details-configure-nav-content"
        >
          <div
            class="c-hPExTj c-hPExTj-ibUIiAd-css"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-ekmpeO-size-n c-iFoEyZ-iPJLV-css c-jqDidP"
            >
              content.title
            </h1>
            <p
              class="c-gIhYmC c-gIhYmC-gVuvhK-size-n c-gIhYmC-iPJLV-css c-BXaoL"
            >
              content.description
            </p>
          </div>
        </div>
        <div
          class="c-fZEhOm"
        />
        <div
          class="c-cvAFBk c-cvAFBk-ejCoEP-orientation-horizontal c-cvAFBk-fGHEql-buttonWidth-block c-cvAFBk-ijTNCvQ-css c-jCABAd"
        >
          <div
            data-state="closed"
            style="flex: 1 1 0%;"
          >
            <button
              class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-qswFQ-variant-outline c-hRrCwb-egrcxB-cv"
              data-cy="configuration-card-test-harness-button"
              disabled=""
            >
              <span
                class="c-iepcqn"
              >
                buttons.test
              </span>
            </button>
          </div>
          <div
            style="flex: 1 1 0%;"
          >
            <button
              class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-hWMCax-inactive-true c-hRrCwb-bETQVM-variant-primary c-hRrCwb-evtOnb-cv"
              data-cy="configuration-card-configure-button"
              disabled=""
            >
              <span
                class="c-iepcqn"
              >
                buttons.configure
              </span>
            </button>
          </div>
        </div>
      </a>
      <div
        class="crop-settings-edit-popup-root"
      />
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
