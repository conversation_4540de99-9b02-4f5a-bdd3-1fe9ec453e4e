import { Input, Select } from '@yaradigitallabs/ahua-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { FertigationSettingsFieldKeys } from './CropSettings.types';
import { SelectOptions } from '@widgets/Polaris/src/types';
import { FertigationCropSettingsConfigData } from '@common/types';
import { SelectWrapper } from '@widgets/Polaris/src/components';
import { MAX_NUM_INPUT_LENGTH } from '@widgets/Polaris/src/components/EditCropSettings/constants';
import { truncateToLimitedNumberOfDecimals } from '@widgets/Polaris/utils';

interface FertigationCropSettingsDropdown {
  dropdownOptions: {
    recommendedSolidPerAreaUnitOptions: SelectOptions<string> | undefined;
    recommendedLiquidsPerAreaUnitOptions: SelectOptions<string> | undefined;
    recommendedSolidPerPlantUnitOptions: SelectOptions<string> | undefined;
    recommendedLiquidsPerPlantUnitOptions: SelectOptions<string> | undefined;
    nutrientRemovalUnitOptions: SelectOptions<string> | undefined;
    yieldUnitOptions: SelectOptions<string> | undefined;
    nutrientDemandUnitOptions: SelectOptions<string> | undefined;
    totalIrrigationWaterUnitOptions: SelectOptions<string> | undefined;
    plantDensityUnitOptions: SelectOptions<string> | undefined;
  };
  onSettingsChange: (key: string, value: string | number | null) => void;
  unitSettings: FertigationCropSettingsConfigData | null | undefined;
}

const FertigationEditCropSettingsDropdownArea: React.FC<FertigationCropSettingsDropdown> = ({
  dropdownOptions,
  onSettingsChange,
  unitSettings,
}) => {
  const { t } = useTranslation();
  const {
    recommendedSolidPerAreaUnitOptions,
    recommendedLiquidsPerAreaUnitOptions,
    recommendedSolidPerPlantUnitOptions,
    recommendedLiquidsPerPlantUnitOptions,
    nutrientRemovalUnitOptions,
    yieldUnitOptions,
    nutrientDemandUnitOptions,
    totalIrrigationWaterUnitOptions,
    plantDensityUnitOptions,
  } = dropdownOptions;

  const isYieldUnitListValid = yieldUnitOptions && yieldUnitOptions.length > 0;
  const isRecommendedSolidsPerAreaUnitListValid =
    recommendedSolidPerAreaUnitOptions && recommendedSolidPerAreaUnitOptions.length > 0;
  return (
    <>
      <SelectWrapper dataCy='yield-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.yieldUnit`)}
          css={{ width: '100%' }}
          value={unitSettings?.yieldUnitId ?? ''}
          items={yieldUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.yieldUnit`)}
          position='popper'
          variant={!isYieldUnitListValid ? 'error' : 'default'}
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.YieldUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.yieldUnit`)}
          helper-text={
            !isYieldUnitListValid
              ? t(`polaris.fpDetails.cropSettings.yieldUnitErrorText`)
              : t(`polaris.fpDetails.cropSettings.yieldUnitHelperText`)
          }
          onBlur={null}
          onFocus={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='default-target-yield-input'>
        <Input
          size='n'
          type='number'
          value={
            unitSettings?.defaultTargetYield || unitSettings?.defaultTargetYield === 0
              ? unitSettings?.defaultTargetYield
              : ''
          }
          onKeyDown={(e) =>
            ['e', 'E', '-', '+', 'ArrowUp', 'ArrowDown'].includes(e.key) && e.preventDefault()
          }
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;

            if (value === '') {
              onSettingsChange(FertigationSettingsFieldKeys.DefaultTargetYield, null);

              return;
            }

            const shortenedValue =
              value.length >= MAX_NUM_INPUT_LENGTH ? value.slice(0, MAX_NUM_INPUT_LENGTH) : value;
            const maxTwoDecimals = truncateToLimitedNumberOfDecimals(shortenedValue, 2);

            onSettingsChange(
              FertigationSettingsFieldKeys.DefaultTargetYield,
              Math.abs(Number(maxTwoDecimals)),
            );
          }}
          label={t(`polaris.fpDetails.cropSettings.defaultTargetYieldUnitOpt`)}
          aria-label={t(`polaris.fpDetails.cropSettings.defaultTargetYieldUnitOpt`)}
          helperText={t(`polaris.fpDetails.cropSettings.defaultTargetYieldHelperText`)}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='nutrient-removal-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.nutrientRemovalUnit`)}
          css={{ width: '100%' }}
          value={unitSettings?.nutrientRemovalUnitId ?? ''}
          items={nutrientRemovalUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.nutrientRemovalUnit`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.NutrientRemovalUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.nutrientRemovalUnit`)}
          helper-text={t(`polaris.fpDetails.cropSettings.nutrientRemovalUnitHelperText`)}
          disabled={!unitSettings?.recommendedSolidPerAreaUnitId}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='nutrient-demand-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`)}
          css={{ width: '100%' }}
          value={unitSettings?.nutrientDemandUnitId ?? ''}
          items={nutrientDemandUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.NutrientDemandUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`)}
          helper-text={t(`polaris.fpDetails.cropSettings.nutrientDemandUnitHelperText`)}
          disabled={!unitSettings?.recommendedSolidPerAreaUnitId}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='recommended-solids-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`)}
          css={{ width: '100%' }}
          value={unitSettings?.recommendedSolidPerAreaUnitId ?? ''}
          items={recommendedSolidPerAreaUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`)}
          position='popper'
          variant={
            unitSettings?.yieldUnitId && !isRecommendedSolidsPerAreaUnitListValid
              ? 'error'
              : 'default'
          }
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.RecSolidPerAreaUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`)}
          disabled={!unitSettings?.yieldUnitId}
          helper-text={
            unitSettings?.yieldUnitId && !isRecommendedSolidsPerAreaUnitListValid
              ? t(`polaris.fpDetails.cropSettings.recommendedSolidPerAreaUnitErrorText`)
              : ''
          }
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='recommended-liquids-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerArea`)}
          css={{ width: '100%' }}
          value={unitSettings?.recommendedLiquidPerAreaUnitId ?? ''}
          items={recommendedLiquidsPerAreaUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerArea`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.RecLiquidPerAreaUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerArea`)}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='recommended-solids-unit-per-plant-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerPlant`)}
          css={{ width: '100%' }}
          value={unitSettings?.recommendedSolidPerPlantUnitId ?? ''}
          items={recommendedSolidPerPlantUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerPlant`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.RecSolidPerPlantUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerPlant`)}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='recommended-liquids-unit-per-plant-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerPlant`)}
          css={{ width: '100%' }}
          value={unitSettings?.recommendedLiquidPerPlantUnitId ?? ''}
          items={recommendedLiquidsPerPlantUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerPlant`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.RecLiquidPerPlantUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerPlant`)}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='total-irrigation-water-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.totalIrrigationWaterUnit`)}
          css={{ width: '100%' }}
          value={unitSettings?.totalIrrigationWaterUnitId ?? ''}
          items={totalIrrigationWaterUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.totalIrrigationWaterUnit`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.TotalIrrigationWaterUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.totalIrrigationWaterUnit`)}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
      <SelectWrapper dataCy='plant-density-unit-dropdown'>
        <Select
          ariaLabel={t(`polaris.fpDetails.cropSettings.plantDensityUnit`)}
          css={{ width: '100%' }}
          value={unitSettings?.plantDensityUnitId ?? ''}
          items={plantDensityUnitOptions || []}
          placeholder={t(`polaris.fpDetails.cropSettings.plantDensityUnit`)}
          position='popper'
          size='n'
          onChange={(value: string) =>
            onSettingsChange(FertigationSettingsFieldKeys.PlantDensityUnitId, value)
          }
          label={t(`polaris.fpDetails.cropSettings.plantDensityUnit`)}
          onFocus={null}
          onBlur={null}
        />
      </SelectWrapper>
    </>
  );
};

export default FertigationEditCropSettingsDropdownArea;
