import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { filterUnitsByTag, useFetchCropSettings } from '@polaris-hooks/index';
import { ValidationStatus, FeatureConfigOptions, ConfigurationType } from '@common/types';
import { ROUTES } from '@src/routes';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { PLAN_NAME_PARAM, UNIT_TAGS } from '@common/constants';
import FertigationCropSettingsCard from './CropSettingsCard';
import { EditCropSettings } from '@widgets/Polaris/src/components';
import ConfigurationCard from '../../shared/ConfigurationCard/ConfigurationCard';
import Details from '../../shared/Details/Details';
import '../../../../../styles/Polaris/screens/NPDetails/_page-details-configure-nav.scss';

const FertigationPlanDetails = () => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.fertigationPlan',
  });

  const [isEditCropSettingOpened, setIsEditCropSettingOpened] = useState<boolean>(false);

  const {
    selectedFeature,
    selectedCropDescription,
    selectedCrop,
    selectedRegion,
    selectedCountry,
    selectedCountryUnits,
    selectedMMMValidation,
    selectedFeatureUnitSettings,
    cropRegion,
    methods: { setCropRegion, setSelectedFeatureUnitSettings },
  } = useAppContext();

  const settings = useFetchCropSettings(
    selectedCountry?.id,
    cropRegion?.id,
    FeatureConfigOptions.FERTIGATION,
  );

  useEffect(() => {
    if (settings) {
      setSelectedFeatureUnitSettings(settings);
    }
  }, [settings]);

  useEffect(() => {
    if (cropRegion) {
      setSelectedFeatureUnitSettings(null);
    }
  }, [cropRegion]);

  // Edit function lists
  const filteredYieldUnits = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_YIELD_UNIT,
  );

  const filteredRecommendedSolidUnitsPerArea = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_MIN_SOL_PROD_PER_AREA_UNIT,
  );

  const filteredRecommendedLiquidsUnitsPerArea = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_MIN_LIQ_PROD_PER_AREA_UNIT,
  );

  const filteredNutrientRemovalUnits = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.NUTRIENT_REMOVAL_UNIT,
  );

  const filteredRecommendedSolidUnitsPerPlant = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_MIN_SOL_PROD_PER_PLANT_UNIT,
  );

  const filteredRecommendedLiquidsUnitsPerPlant = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_MIN_LIQ_PROD_PER_PLANT_UNIT,
  );

  const filteredNutrientDemandUnits = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_NUTRIENT_DEMAND_UNIT,
  );

  const filteredTotalIrrigationWaterUnits = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_TOTAL_IRRIGATION_WATER_UNIT,
  );

  const plantDensityUnits = filterUnitsByTag(
    selectedCountryUnits,
    UNIT_TAGS.FERTIGATION_PLANT_DENSITY_UNIT,
  );

  const isMMMValidated = selectedMMMValidation?.validationStatus === ValidationStatus.VALIDATED;

  return (
    <Details route={ROUTES.fertigationPlan} featureDetails={'fpDetails'}>
      <FertigationCropSettingsCard
        cropRegion={cropRegion}
        isEditingDisabled={isMMMValidated}
        setIsEditCropSettingOpened={setIsEditCropSettingOpened}
      />
      <ConfigurationCard
        titles={{
          header: t(`title`),
          content: t(`content.title`),
        }}
        tooltips={{
          testHarnessButton: t('buttons.testTooltipMessage'),
          configureButton: '',
        }}
        labels={{
          testHarnessAction: t(`buttons.test`),
          configureButton: t(`buttons.configure`),
        }}
        configurationType={ConfigurationType.Fertigation}
        contentDescription={t(`content.description`)}
        featureTranslationKeyPrefix={'fpDetails.fertigationPlan.validationSwitch'}
        showConfigureButtonTooltip={false}
        isConfigureButtonDisable={
          (!isMMMValidated &&
            Object.entries(selectedFeatureUnitSettings?.configuration.data || {})
              .filter(([key]) => key !== 'defaultTargetYield') // Ignore optional field
              .some(([_, value]) => !value)) ||
          isMMMValidated
        }
        featurePlanConfigurationRoute={ROUTES.fertigationPlanConfiguration}
        featurePlanName={PLAN_NAME_PARAM.FP}
      />
      <EditCropSettings
        dataPrefix='fertigation-plan'
        selectedFeature={selectedFeature}
        selectedCropRegion={cropRegion}
        selectedCrop={selectedCrop}
        selectedCountry={selectedCountry}
        selectedRegion={selectedRegion}
        setCropRegion={setCropRegion}
        selectedCropDescription={selectedCropDescription}
        isEditCropSettingOpened={isEditCropSettingOpened}
        setIsEditCropSettingOpened={setIsEditCropSettingOpened}
        fertigationYieldUnitsData={filteredYieldUnits}
        recommendedSolidUnitsPerAreaData={filteredRecommendedSolidUnitsPerArea}
        recommendedLiquidsUnitsPerAreaData={filteredRecommendedLiquidsUnitsPerArea}
        recommendedSolidUnitsPerPlantData={filteredRecommendedSolidUnitsPerPlant}
        recommendedLiquidsUnitsPerPlantData={filteredRecommendedLiquidsUnitsPerPlant}
        nutrientRemovalUnitsData={filteredNutrientRemovalUnits}
        nutrientDemandUnitsData={filteredNutrientDemandUnits}
        totalIrrigationWaterUnitsData={filteredTotalIrrigationWaterUnits}
        plantDensityUnitsData={plantDensityUnits}
      />
    </Details>
  );
};

export default FertigationPlanDetails;
