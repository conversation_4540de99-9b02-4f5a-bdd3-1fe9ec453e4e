import { ConfigurationType } from '@common/types';
import { CropSettingsCardConfigurationData, CropSettingsCardConfigurationInitial } from '../../shared/CropSettingsCard';

export enum FertigationSettingsFieldKeys {
	YieldUnitId = 'yieldUnitId',
	DefaultTargetYield = 'defaultTargetYield',
	RecSolidPerAreaUnitId = 'recommendedSolidPerAreaUnitId',
	RecLiquidPerAreaUnitId = 'recommendedLiquidPerAreaUnitId',
	RecSolidPerPlantUnitId = 'recommendedSolidPerPlantUnitId',
	RecLiquidPerPlantUnitId = 'recommendedLiquidPerPlantUnitId',
	NutrientRemovalUnitId = 'nutrientRemovalUnitId',
	NutrientDemandUnitId = 'nutrientDemandUnitId',
	TotalIrrigationWaterUnitId = 'totalIrrigationWaterUnitId',
	PlantDensityUnitId = 'plantDensityUnitId',
}

type FertigationCropSettingsCardConfigurationName = {
	name: FertigationSettingsFieldKeys;
};

export type FertigationCropSettingsCardInitial = FertigationCropSettingsCardConfigurationName & CropSettingsCardConfigurationInitial;

export type FertigationCropSettingsCardData = FertigationCropSettingsCardConfigurationName & CropSettingsCardConfigurationData;

export type FertigationCropSettingsCardConfig = FertigationCropSettingsCardInitial & FertigationCropSettingsCardData;

export type FertigationCropSettingsCard = {
	configurationType: ConfigurationType.Fertigation;
	config: FertigationCropSettingsCardConfig[][];
};
