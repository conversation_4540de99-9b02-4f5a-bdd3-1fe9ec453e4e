import React from 'react';
import { CropRegion } from '@common/types';
import { CropSettingsFertigationCardLogic } from './CropSettingsCardLogic';
import { CropSettingsCard } from '../../shared/CropSettingsCard';
import { useTranslation } from 'react-i18next';

interface FertigationCropSettingsCardProps {
  cropRegion: CropRegion | null;
  setIsEditCropSettingOpened: React.Dispatch<boolean>;
  isEditingDisabled: boolean;
}

const FertigationCropSettingsCard: React.FC<FertigationCropSettingsCardProps> = ({
  cropRegion,
  setIsEditCropSettingOpened,
  isEditingDisabled,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails'
  });

  const { fertigationCropSettingsConfig, filteredPartnerTags } = CropSettingsFertigationCardLogic();

  return (
    <CropSettingsCard
      cropSettings={fertigationCropSettingsConfig}
      cropRegion={cropRegion}
      setIsEditCropSettingOpened={setIsEditCropSettingOpened}
      isEditingDisabled={isEditingDisabled}
      filteredPartnerTags={filteredPartnerTags}
      titles={{
        head: t(`cropSettings.settingsTitle`),
        editButton: t(`cropSettings.editButton`),
        partner: t(`cropSettings.partnersTitle`),
      }}
      texts={{
        headActionTooltip: t('fertigationPlan.buttons.editTooltipMessage'),
        partnerCaption: t(`cropSettings.partnersCaption`),
      }}
    />
  );
};

export default FertigationCropSettingsCard;
