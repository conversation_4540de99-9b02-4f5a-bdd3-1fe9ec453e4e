import { useMemo } from 'react';
import { CropSettingsCardPrep, SelectItem } from '../../shared/CropSettingsCard';
import { AhuaIconProps } from '@yaradigitallabs/ahua-react';
import {
  FertigationCropSettingsCard,
  FertigationCropSettingsCardConfig,
  FertigationCropSettingsCardData,
  FertigationSettingsFieldKeys,
} from './CropSettings.types';
import { fertigationCropSettingsConfigInitial } from './CropSetting.constants';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { ConfigurationType, FertigationCropSettingsConfigData } from '@common/types';
import { filterUnitsById, useGetFilteredPartnersData } from '@widgets/Polaris/src/hooks';

/**
 * Hook to get crop settings units for Fertigation Plan homepage
 * @param {CropRegion | null} cropRegion - The crop region object for the selected country.
 * @returns {Object} Object containing Fertigation Plan crop settings units
 */
export const CropSettingsFertigationCardLogic = () => {
  const { selectedFeatureUnitSettings, cropRegion, selectedCountryUnits } = useAppContext();
  const { getItems, getValue } = CropSettingsCardPrep();
  const unitSettings: FertigationCropSettingsConfigData | undefined =
    selectedFeatureUnitSettings?.configuration.data;
  const filteredPartnerTags = useGetFilteredPartnersData(cropRegion);

  const defaultTargetYieldUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.yieldUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const hasDefaultTargetYieldDefaultValue = useMemo(
    () => !!(unitSettings?.defaultTargetYield || unitSettings?.defaultTargetYield === 0),
    [unitSettings],
  );

  const recommendedSolidUnitPerArea = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.recommendedSolidPerAreaUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const recommendedLiquidsUnitPerArea = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.recommendedLiquidPerAreaUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const nutrientRemovalUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.nutrientRemovalUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const recommendedSolidUnitPerPlant = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.recommendedSolidPerPlantUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const recommendedLiquidsUnitPerPlant = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.recommendedLiquidPerPlantUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const nutrientDemandUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.nutrientDemandUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const totalIrrigationWaterUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.totalIrrigationWaterUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const plantDensityUnit = useMemo(
    () => filterUnitsById(selectedCountryUnits, unitSettings?.plantDensityUnitId),
    [selectedCountryUnits, unitSettings],
  );

  const yieldItems = useMemo(() => {
    return getItems(defaultTargetYieldUnit, 'Crop');
  }, [defaultTargetYieldUnit]);

  const yieldValue = useMemo(() => {
    return getValue(defaultTargetYieldUnit);
  }, [defaultTargetYieldUnit]);

  const defaultTargetYieldItems = useMemo((): SelectItem[] => {
    const cropIconName: AhuaIconProps['icon'] = 'Crop';
    const item = !hasDefaultTargetYieldDefaultValue
      ? {
        icon: cropIconName,
        value: 'error',
        text: '-',
      }
      : {
        icon: cropIconName,
        value: hasDefaultTargetYieldDefaultValue ? String(unitSettings?.defaultTargetYield) : '',
        text: String(unitSettings?.defaultTargetYield) ?? '-',
      };

    return [item];
  }, [unitSettings, hasDefaultTargetYieldDefaultValue]);

  const defaultTargetYieldValue = useMemo(() => {
    return !hasDefaultTargetYieldDefaultValue ? 'error' : String(unitSettings?.defaultTargetYield);
  }, [unitSettings, hasDefaultTargetYieldDefaultValue]);

  const recommendedSolidItemsPerArea = useMemo(() => {
    return getItems(recommendedSolidUnitPerArea, 'FertilizerSolid');
  }, [recommendedSolidUnitPerArea]);

  const recommendedSolidValuePerArea = useMemo(() => {
    return getValue(recommendedSolidUnitPerArea);
  }, [recommendedSolidUnitPerArea]);

  const recommendedLiquidItemsPerArea = useMemo(() => {
    return getItems(recommendedLiquidsUnitPerArea, 'FertilizerLiquid');
  }, [recommendedLiquidsUnitPerArea]);

  const recommendedLiquidValuePerArea = useMemo(() => {
    return getValue(recommendedLiquidsUnitPerArea);
  }, [recommendedLiquidsUnitPerArea]);

  const nutrientRemovalItems = useMemo(() => {
    return getItems(nutrientRemovalUnit, 'Fertillizer');
  }, [nutrientRemovalUnit]);

  const nutrientRemovalValue = useMemo(() => {
    return getValue(nutrientRemovalUnit);
  }, [nutrientRemovalUnit]);

  const recommendedSolidItemsPerPlant = useMemo(() => {
    return getItems(recommendedSolidUnitPerPlant, 'FertilizerSolid');
  }, [recommendedSolidUnitPerPlant]);

  const recommendedSolidValuePerPlant = useMemo(() => {
    return getValue(recommendedSolidUnitPerPlant);
  }, [recommendedSolidUnitPerPlant]);

  const recommendedLiquidItemsPerPlant = useMemo(() => {
    return getItems(recommendedLiquidsUnitPerPlant, 'FertilizerLiquid');
  }, [recommendedLiquidsUnitPerPlant]);

  const recommendedLiquidValuePerPlant = useMemo(() => {
    return getValue(recommendedLiquidsUnitPerPlant);
  }, [recommendedLiquidsUnitPerPlant]);

  const nutrientDemandItems = useMemo(() => {
    return getItems(nutrientDemandUnit, 'Fertillizer');
  }, [nutrientDemandUnit]);

  const nutrientDemandValue = useMemo(() => {
    return getValue(nutrientDemandUnit);
  }, [nutrientDemandUnit]);

  const totalIrrigationWaterItems = useMemo(() => {
    return getItems(totalIrrigationWaterUnit, 'Humidity');
  }, [totalIrrigationWaterUnit]);

  const totalIrrigationWaterValue = useMemo(() => {
    return getValue(totalIrrigationWaterUnit);
  }, [totalIrrigationWaterUnit]);

  const plantDensityItems = useMemo(() => {
    return getItems(plantDensityUnit, 'Crop');
  }, [plantDensityUnit]);

  const plantDensityValue = useMemo(() => {
    return getValue(plantDensityUnit);
  }, [plantDensityUnit]);

  // Each row is represented as an array of elements
  const cerealsCropSettingsConfigData: FertigationCropSettingsCardData[][] = [
    [
      {
        name: FertigationSettingsFieldKeys.YieldUnitId,
        data: { items: yieldItems, value: yieldValue },
      },
      {
        name: FertigationSettingsFieldKeys.DefaultTargetYield,
        data: { items: defaultTargetYieldItems, value: defaultTargetYieldValue },
      },
      {
        name: FertigationSettingsFieldKeys.NutrientRemovalUnitId,
        data: { items: nutrientRemovalItems, value: nutrientRemovalValue },
      },
      {
        name: FertigationSettingsFieldKeys.NutrientDemandUnitId,
        data: { items: nutrientDemandItems, value: nutrientDemandValue },
      },
    ],
    [
      {
        name: FertigationSettingsFieldKeys.RecSolidPerAreaUnitId,
        data: { items: recommendedSolidItemsPerArea, value: recommendedSolidValuePerArea },
      },
      {
        name: FertigationSettingsFieldKeys.RecLiquidPerAreaUnitId,
        data: { items: recommendedLiquidItemsPerArea, value: recommendedLiquidValuePerArea },
      },
      {
        name: FertigationSettingsFieldKeys.RecSolidPerPlantUnitId,
        data: { items: recommendedSolidItemsPerPlant, value: recommendedSolidValuePerPlant },
      },
      {
        name: FertigationSettingsFieldKeys.RecLiquidPerPlantUnitId,
        data: { items: recommendedLiquidItemsPerPlant, value: recommendedLiquidValuePerPlant },
      },
    ],
    [
      {
        name: FertigationSettingsFieldKeys.TotalIrrigationWaterUnitId,
        data: { items: totalIrrigationWaterItems, value: totalIrrigationWaterValue },
      },
      {
        name: FertigationSettingsFieldKeys.PlantDensityUnitId,
        data: { items: plantDensityItems, value: plantDensityValue },
      },
    ],
  ];

  const structuredConfig: FertigationCropSettingsCardConfig[][] = cerealsCropSettingsConfigData.map(
    (configRow) => {
      const structuredConfigData = configRow.reduce<FertigationCropSettingsCardConfig[]>(
        (acc, curr) => {
          const initialData = fertigationCropSettingsConfigInitial.find(
            ({ name }) => name === curr.name,
          );

          if (initialData) {
            const row: FertigationCropSettingsCardConfig = {
              ...curr,
              ...initialData,
              ...configRow,
            };
            const newConfigData = [...acc, row];

            return newConfigData;
          }

          return acc;
        },
        [],
      );

      return structuredConfigData;
    },
  );

  const fertigationCropSettingsConfig: FertigationCropSettingsCard = {
    configurationType: ConfigurationType.Fertigation,
    config: structuredConfig,
  };

  return {
    yieldItems,
    defaultTargetYieldItems,
    recommendedSolidItemsPerArea,
    recommendedLiquidItemsPerArea,
    nutrientRemovalItems,
    filteredPartnerTags,
    recommendedSolidItemsPerPlant,
    recommendedLiquidItemsPerPlant,
    nutrientDemandItems,
    totalIrrigationWaterItems,
    plantDensityItems,

    yieldValue,
    defaultTargetYieldValue,
    defaultTargetYieldUnit,
    recommendedSolidValuePerArea,
    recommendedLiquidValuePerArea,
    nutrientRemovalValue,
    recommendedSolidValuePerPlant,
    recommendedLiquidValuePerPlant,
    nutrientDemandValue,
    totalIrrigationWaterValue,
    plantDensityValue,

    fertigationCropSettingsConfig,
  };
};
