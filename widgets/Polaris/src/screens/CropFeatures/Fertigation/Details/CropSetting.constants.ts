import {
  FertigationCropSettingsCardInitial,
  FertigationSettingsFieldKeys,
} from './CropSettings.types';

export const fertigationCropSettingsConfigInitial: FertigationCropSettingsCardInitial[] = [
  {
    name: FertigationSettingsFieldKeys.YieldUnitId,
    label: 'polaris.fpDetails.cropSettings.yieldUnit',
    ariaLabel: 'Yield unit select element',
    dataCy: 'yield-unit',
  },
  {
    name: FertigationSettingsFieldKeys.DefaultTargetYield,
    label: 'polaris.fpDetails.cropSettings.defaultTargetYieldUnit',
    ariaLabel: 'Default target yield unit select element',
    dataCy: 'default-target-yield-unit',
  },
  {
    name: FertigationSettingsFieldKeys.NutrientRemovalUnitId,
    label: 'polaris.fpDetails.cropSettings.nutrientRemovalUnit',
    ariaLabel: 'Nutrient removal unit select element',
    dataCy: 'nutrient-removal-unit',
  },
  {
    name: FertigationSettingsFieldKeys.NutrientDemandUnitId,
    label: 'polaris.fpDetails.cropSettings.nutrientDemandUnit',
    ariaLabel: 'Nutrient demand unit select element',
    dataCy: 'nutrient-demand-unit',
  },
  {
    name: FertigationSettingsFieldKeys.RecSolidPerAreaUnitId,
    label: 'polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea',
    ariaLabel: 'Recommendation solid unit select element',
    dataCy: 'recommended-solid-unit',
  },
  {
    name: FertigationSettingsFieldKeys.RecLiquidPerAreaUnitId,
    label: 'polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerArea',
    ariaLabel: 'Recommendation liquids unit select element',
    dataCy: 'recommended-liquids-unit',
  },
  {
    name: FertigationSettingsFieldKeys.RecSolidPerPlantUnitId,
    label: 'polaris.fpDetails.cropSettings.recommendationSolidsUnitPerPlant',
    ariaLabel: 'Recommendation solid unit select element',
    dataCy: 'recommended-solid-per-plant-unit',
  },
  {
    name: FertigationSettingsFieldKeys.RecLiquidPerPlantUnitId,
    label: 'polaris.fpDetails.cropSettings.recommendationLiquidsUnitPerPlant',
    ariaLabel: 'Recommendation liquids unit select element',
    dataCy: 'recommended-liquids-per-plant-unit',
  },
  {
    name: FertigationSettingsFieldKeys.TotalIrrigationWaterUnitId,
    label: 'polaris.fpDetails.cropSettings.totalIrrigationWaterUnit',
    ariaLabel: 'Total irrigation water select element',
    dataCy: 'total-irrigation-water-unit',
  },
  {
    name: FertigationSettingsFieldKeys.PlantDensityUnitId,
    label: 'polaris.fpDetails.cropSettings.plantDensityUnit',
    ariaLabel: 'Plant density select element',
    dataCy: 'plant-density-unit',
  },
];
