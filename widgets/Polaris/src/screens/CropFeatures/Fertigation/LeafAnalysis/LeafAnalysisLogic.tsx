import { useEffect, useMemo, useRef, useState } from 'react';
import { isEqual, sortBy } from 'lodash';
import { METHOD } from '@common/constants';
import { FeatureConfig, AnalysisTypeOptions } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  filterUnitsByNutrientElementName,
  useFetchNutrientsFromPolaris,
  useGetAnalysisConfigurations,
} from '@polaris-hooks/index';
import { FilterType } from '@widgets/Polaris/src/types';
import { FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS } from '../../shared/constants';

const FertigationLeafAnalysisLogic = (configType: FeatureConfig) => {
  const {
    selectedPlanConfigTab,
    selectedFeatureNutrients,
    selectedCountryUnits,
    cropRegion,
    analysisConfigurations: leafAnalysisConfigurationsData,
    methods: { setAnalysisConfigurations },
  } = useAppContext();
  const [configurationUpdated, setConfigurationUpdated] = useState(false);

  const nutrientID = useMemo(
    () => selectedPlanConfigTab && selectedFeatureNutrients?.[selectedPlanConfigTab],
    [selectedPlanConfigTab, selectedFeatureNutrients],
  );
  const nutrientsData = useFetchNutrientsFromPolaris([
    {
      key: 'id',
      value: FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.join(','),
      type: FilterType.IN,
    },
  ]);
  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);
  const nutrientsRef = useRef(sortedNutrients);
  if (!isEqual(nutrientsRef.current, sortedNutrients)) {
    nutrientsRef.current = sortedNutrients;
  }
  const selectedNutrient = useMemo(
    () => sortedNutrients && sortedNutrients.find(({ id }) => id === nutrientID),
    [nutrientsRef.current, nutrientID],
  );
  const nutrientElement = selectedNutrient?.elementalName;

  const baseUnits = filterUnitsByNutrientElementName(selectedCountryUnits, nutrientElement);

  const { trigger: triggerGetLeafAnalysisConfigurations } = useGetAnalysisConfigurations(
    configType,
    AnalysisTypeOptions.LEAF,
  );
  const selectedLeafAnalysisConfigurationData = useMemo(
    () =>
      leafAnalysisConfigurationsData?.filter(
        (configuration) => configuration?.nutrientId === nutrientID,
      ),
    [leafAnalysisConfigurationsData, nutrientID],
  );

  useEffect(() => {
    if (cropRegion) {
      triggerGetLeafAnalysisConfigurations({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'cropRegionId',
              value: cropRegion.id,
              type: FilterType.EQ,
            },
          ],
        }),
      })
        .then((data) => {
          setAnalysisConfigurations(data?.entities || []);
          setConfigurationUpdated(false);
        })
        .catch((err) => console.error(err));
    }
  }, [cropRegion, configurationUpdated]);

  return {
    nutrientsData: sortedNutrients,
    baseUnits,
    selectedNutrient,
    selectedLeafAnalysisConfigurationData,
  };
};

export default FertigationLeafAnalysisLogic;
