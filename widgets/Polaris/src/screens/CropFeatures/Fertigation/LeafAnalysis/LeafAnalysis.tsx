import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  ConfigurationsAnalysisMethodCollapsible,
  NutrientsStaticList,
} from '@widgets/Polaris/src/components';
import {
  FeatureConfig,
  AnalysisTypeOptions,
  AnalysisConfiguration,
  ValidationStatus,
} from '@common/types';
import LeafAnalysisLogic from './LeafAnalysisLogic';
import { NutrientsListWrapper } from './LeafAnalysis.styled';
import { UNIT_IDS } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers';

const LeafAnalysis = ({ configType }: { configType: FeatureConfig }) => {
  const keyPrefix = 'polaris.fpDetails.leafAnalysis';
  const { t } = useTranslation('polaris');

  const { selectedMMMValidation } = useAppContext();
  const { selectedNutrient, selectedLeafAnalysisConfigurationData, baseUnits, nutrientsData } =
    LeafAnalysisLogic(configType);

  return (
    <>
      {' '}
      <NutrientsListWrapper>
        <NutrientsStaticList
          selectedNutrient={selectedNutrient}
          nutrientParams={nutrientsData}
          title={t(`${keyPrefix}.leafParamsList.title`)}
          emptyStateText={t(`${keyPrefix}.leafParamsList.emptyStateText`)}
        />
      </NutrientsListWrapper>
      {selectedLeafAnalysisConfigurationData?.map(
        (configuration: AnalysisConfiguration, index: number) => (
          <ConfigurationsAnalysisMethodCollapsible
            key={`${index}_${configuration.id}`}
            index={index}
            data={configuration}
            baseUnits={baseUnits}
            keyPrefix={keyPrefix}
            configType={configType}
            analysisType={AnalysisTypeOptions.LEAF}
            considerSecondaryParameters={false}
            triggerErrorState={
              configuration.analysisBaseUnitId === UNIT_IDS.DEFAULT &&
              selectedMMMValidation?.validationStatus === ValidationStatus.FAILED
            }
          />
        ),
      )}
    </>
  );
};

export default LeafAnalysis;
