import React from 'react';
import { render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { mockFertigationAppProviderValue, leafAnalysisConfigurationsMock } from '@common/mocks';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';

import LeafAnalysis from '../LeafAnalysis';
import * as useNutrientsService from '@polaris-hooks/polarisMockService/useNutrientsService/useNutrientsService';
import {
  nutrientsFertigationMock,
  unitCountriesHandler,
  allUnitsHandler,
  fertigationLeafAnalysisConfigurationHandler,
  updateFertigationLeafAnalysisNutrientClassificationLowHandler,
  getAnalysisMethodsHandler,
  allPolarisNutrientsHandler,
} from '@common/mocks';
import { FeatureConfigOptions } from '@common/types';

const server = setupServer(
  getAnalysisMethods<PERSON>andler,
  allUnitsHandler,
  unitCountriesHandler,
  fertigationLeafAnalysisConfigurationHandler,
  updateFertigationLeafAnalysisNutrientClassificationLowHandler,
  allPolarisNutrientsHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest
  .spyOn(useNutrientsService, 'useFetchNutrientsFromPolaris')
  .mockImplementation(() => nutrientsFertigationMock)
  .mockImplementationOnce(() => nutrientsFertigationMock)
  .mockImplementationOnce(() => []);

jest.mock('@widgets/Polaris/src/components/NutrientsStaticList/NutrientsStaticList', () => ({
  NutrientsStaticList: () => <div>Nutrients list</div>,
}));

describe('LeafAnalysis Component', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => <LeafAnalysis configType={FeatureConfigOptions.FERTIGATION} />}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders the list', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => <LeafAnalysis configType={FeatureConfigOptions.FERTIGATION} />}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(component.getByText('Nutrients list')).toBeInTheDocument();
  });

  it('renders ConfigurationsAnalysisMethodCollapsible when crop demand analyses are available for the selected nutrient', () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          analysisConfigurations: leafAnalysisConfigurationsMock?.entities,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>
              {() => <LeafAnalysis configType={FeatureConfigOptions.FERTIGATION} />}
            </AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const collapsible = component.queryByTestId('analyses-collapsible');
    expect(collapsible).toBeInTheDocument();
  });
});
