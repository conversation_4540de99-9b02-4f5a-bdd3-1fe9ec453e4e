import { useEffect, useMemo, useRef, useState } from 'react';
import { isEqual, sortBy } from 'lodash';
import { METHOD } from '@common/constants';
import { AnalysisTypeOptions, FeatureConfigOptions } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  filterUnitsByNutrientElementName,
  useFetchNutrientsFromPolaris,
  useGetAnalysisConfigurations,
} from '@polaris-hooks/index';
import { FilterType } from '@widgets/Polaris/src/types';
import { FERTIGATION_WATER_ANALYSIS_NUTRIENTS } from '../../shared/constants';

const WaterAnalysisLogic = () => {
  const {
    selectedPlanConfigTab,
    selectedFeatureNutrients,
    selectedCountryUnits,
    cropRegion,
    analysisConfigurations: waterAnalysisConfigurationsData,
    methods: { setAnalysisConfigurations },
  } = useAppContext();

  const [configurationUpdated, setConfigurationUpdated] = useState(false);

  const nutrientID = useMemo(
    () => selectedPlanConfigTab && selectedFeatureNutrients?.[selectedPlanConfigTab],
    [selectedPlanConfigTab, selectedFeatureNutrients],
  );

  const nutrientsData = useFetchNutrientsFromPolaris([
    {
      key: 'id',
      value: FERTIGATION_WATER_ANALYSIS_NUTRIENTS.join(','),
      type: FilterType.IN,
    },
  ]);

  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      FERTIGATION_WATER_ANALYSIS_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);

  const nutrientsRef = useRef(sortedNutrients);
  if (!isEqual(nutrientsRef.current, sortedNutrients)) {
    nutrientsRef.current = sortedNutrients;
  }
  const selectedNutrient = useMemo(
    () => sortedNutrients && sortedNutrients.find(({ id }) => id === nutrientID),
    [nutrientsRef.current, nutrientID],
  );

  const nutrientElement = selectedNutrient?.elementalName;
  const baseUnits = filterUnitsByNutrientElementName(selectedCountryUnits, nutrientElement);

  const selectedWaterAnalysisConfigurationData = useMemo(
    () =>
      waterAnalysisConfigurationsData?.filter(
        (configuration) => configuration?.nutrientId === nutrientID,
      ),
    [waterAnalysisConfigurationsData, nutrientID],
  );

  const { trigger: triggerGetWaterAnalysisConfigurations } = useGetAnalysisConfigurations(
    FeatureConfigOptions.FERTIGATION,
    AnalysisTypeOptions.WATER,
  );

  useEffect(() => {
    if (cropRegion) {
      triggerGetWaterAnalysisConfigurations({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'cropRegionId',
              value: cropRegion.id,
              type: FilterType.EQ,
            },
          ],
        }),
      })
        .then((data) => {
          setAnalysisConfigurations(data?.entities || []);
          setConfigurationUpdated(false);
        })
        .catch((err) => console.error(err));
    }
  }, [cropRegion, configurationUpdated]);

  return {
    nutrientsData: sortedNutrients,
    baseUnits,
    selectedNutrient,
    selectedWaterAnalysisConfigurationData,
  };
};

export default WaterAnalysisLogic;
