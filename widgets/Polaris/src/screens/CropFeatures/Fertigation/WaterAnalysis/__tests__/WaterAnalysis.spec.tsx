import React from 'react';
import { render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { mockFertigationAppProviderValue, waterAnalysisConfigurationsMock } from '@common/mocks';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import { WaterAnalysis } from '../WaterAnalysis';
import * as useNutrientsService from '@polaris-hooks/polarisMockService/useNutrientsService/useNutrientsService';
import {
  unitCountriesHandler,
  allUnitsHandler,
  getFertigationWaterAnalysisConfigurationHandler,
  updateFertigationWaterAnalysisNutrientClassificationLowHandler,
  getAnalysisMethodsHandler,
  allPolarisNutrientsHandler,
  nutrientsFertigationMock,
} from '@common/mocks';

const server = setupServer(
  getAnalysisMethodsHandler,
  allUnitsHandler,
  unitCountriesHandler,
  getFertigationWaterAnalysisConfigurationHandler,
  updateFertigationWaterAnalysisNutrientClassificationLowHandler,
  allPolarisNutrientsHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest
  .spyOn(useNutrientsService, 'useFetchNutrientsFromPolaris')
  .mockImplementation(() => nutrientsFertigationMock)
  .mockImplementationOnce(() => nutrientsFertigationMock)
  .mockImplementationOnce(() => []);

jest.mock('@widgets/Polaris/src/components/NutrientsStaticList/NutrientsStaticList', () => ({
  NutrientsStaticList: () => <div>Nutrients list</div>,
}));

describe('WaterAnalysis Component', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <WaterAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders the list', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <WaterAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(component.getByText('Nutrients list')).toBeInTheDocument();
  });

  it('renders ConfigurationsAnalysisMethodCollapsible when crop demand analyses are available for the selected nutrient', () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          analysisConfigurations: waterAnalysisConfigurationsMock?.entities,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <WaterAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const collapsible = component.queryByTestId('analyses-collapsible');
    expect(collapsible).toBeInTheDocument();
  });
});
