/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { NutrientListWrapper } from './WaterAnalysis.styled';
import {
  ConfigurationsAnalysisMethodCollapsible,
  NutrientsStaticList,
} from '@widgets/Polaris/src/components';
import {
  AnalysisConfiguration,
  AnalysisTypeOptions,
  FeatureConfigOptions,
  ValidationStatus,
} from '@common/types';
import WaterAnalysisLogic from './WaterAnalysisLogic';
import { UNIT_IDS } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers';

export const WaterAnalysis = () => {
  const keyPrefix = 'polaris.fpDetails.waterAnalysis';
  const { t } = useTranslation('polaris', {
    keyPrefix: keyPrefix,
  });

  const { selectedMMMValidation } = useAppContext();
  const { selectedNutrient, selectedWaterAnalysisConfigurationData, baseUnits, nutrientsData } =
    WaterAnalysisLogic();

  return (
    <>
      <NutrientListWrapper>
        <NutrientsStaticList
          selectedNutrient={selectedNutrient}
          nutrientParams={nutrientsData}
          title={t(`nutrients.title`)}
          emptyStateText={t(`nutrients.emptyStateText`)}
        />
      </NutrientListWrapper>

      {selectedWaterAnalysisConfigurationData?.map(
        (configuration: AnalysisConfiguration, index: number) => (
          <ConfigurationsAnalysisMethodCollapsible
            key={`${index}_${configuration.id}`}
            index={index}
            data={configuration}
            baseUnits={baseUnits}
            keyPrefix={keyPrefix}
            configType={FeatureConfigOptions.FERTIGATION}
            analysisType={AnalysisTypeOptions.WATER}
            considerSecondaryParameters={false}
            triggerErrorState={
              configuration.analysisBaseUnitId === UNIT_IDS.DEFAULT &&
              selectedMMMValidation?.validationStatus === ValidationStatus.FAILED
            }
          />
        ),
      )}
    </>
  );
};
