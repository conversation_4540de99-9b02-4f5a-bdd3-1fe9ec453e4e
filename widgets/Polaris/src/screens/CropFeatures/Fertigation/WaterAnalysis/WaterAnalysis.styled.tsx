import { styled, Subtitle } from '@yaradigitallabs/ahua-react';

export const collapsibleStyles = {
  '&.collapsible-section > div': {
    padding: '$x6 0',
  },
  '&.collapsible-section > button': {
    fontSize: '$scale4',
    lineHeight: '$scale5',
  },
};

export const InputContainer = styled('div', {
  display: 'flex',
  flexDirection: 'row',
  paddingRight: '$x4',
  paddingLeft: '$x4',
  paddingBottom: '$x4',
  gap: '$x4',
});

export const FirstRowInput = styled('div', {
  flexGrow: 1,
  width: '33%',
});

export const StyledSubtitle = styled(Subtitle, {
  marginBottom: '$x4',
  color: '$black70',
  padding: 0,
  lineHeight: '$scale5',
});

export const NutrientListWrapper = styled('div', {
  marginBottom: '$x6',
});
