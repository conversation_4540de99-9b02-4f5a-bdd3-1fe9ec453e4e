import React from 'react';
import { render } from '@testing-library/react';
import { DynamicMethodSelect } from '../DynamicMethodSelect';
import { DynamicPRecommendationApplicationRate } from '@common/types';
import { mockSoilPhase } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

describe('DynamicMethodSelect Component', () => {
  const mockOnValueChanged = jest.fn();

  const renderComponent = (phase = mockSoilPhase) =>
    render(
      <DynamicMethodSelect
        phase={phase}
        onValueChanged={mockOnValueChanged}
        triggerErrorState={false}
        disabled={false}
      />,
    );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId, getByRole } = renderComponent();

    expect(getByTestId('dynamic-app-rate-method-dropdown')).toBeInTheDocument();
    expect(getByRole('combobox')).toHaveAttribute('aria-label', 'selectRateMethodLabel');
  });

  it('selects the correct initial rate method based on the phase', () => {
    const phaseWithRateMethod = {
      ...mockSoilPhase,
      dynamicApplicationRate: {
        dynamicApplicationRateMethod: DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS,
        minRate: 0,
        maxRate: 0,
        rateUnits: [],
      },
    };

    const { getByText } = renderComponent(phaseWithRateMethod);

    // Use getByText to find the option by its displayed text
    expect(getByText('nutrientSplitsOption')).toBeInTheDocument();
  });

  it('updates the selected rate method when phase changes', () => {
    const { rerender, getByText } = renderComponent();

    const updatedPhase = {
      ...mockSoilPhase,
      dynamicApplicationRate: {
        dynamicApplicationRateMethod: DynamicPRecommendationApplicationRate.PRODUCT_PLANT,
        minRate: 0,
        maxRate: 0,
        rateUnits: [],
      },
    };

    rerender(
      <DynamicMethodSelect
        phase={updatedPhase}
        onValueChanged={mockOnValueChanged}
        triggerErrorState={false}
        disabled={false}
      />,
    );

    // Use getByText for the updated selection
    expect(getByText('appOfProductPlantOption')).toBeInTheDocument();
  });
});
