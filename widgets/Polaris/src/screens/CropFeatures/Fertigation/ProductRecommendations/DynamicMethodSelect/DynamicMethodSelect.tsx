import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SelectWrapper } from '../../../../../components/Select/SelectWrapper';
import { Select, SelectOption } from '@yaradigitallabs/ahua-react';
import {
  DynamicPRecommendationApplicationRate,
  FertigationPRecommendationGrowthPhase,
} from '@common/types';
import { cloneDeep } from 'lodash';
import { ConfirmationDialog } from '@widgets/Polaris/src/components';

interface ComponentProps {
  phase: FertigationPRecommendationGrowthPhase;
  disabled: boolean;
  triggerErrorState: boolean;
  onValueChanged: (phase: FertigationPRecommendationGrowthPhase) => void;
}

export const DynamicMethodSelect: React.FC<ComponentProps> = ({
  phase,
  disabled,
  triggerErrorState,
  onValueChanged,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations.productRecommendationsTable',
  });

  const [selectedRateMethod, setSelectedRateMethod] = useState<SelectOption<string> | null>(null);
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const newRateMethodRef = useRef<DynamicPRecommendationApplicationRate | null>(null);

  const rateMethodArray = [
    {
      value: DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS,
      text: t('nutrientSplitsOption'),
    },
    {
      value: DynamicPRecommendationApplicationRate.PRODUCT_PLANT,
      text: t('appOfProductPlantOption'),
    },
  ];

  useEffect(() => {
    const newRateMethod = rateMethodArray.find(
      (rate) => rate.value === phase?.dynamicApplicationRate?.dynamicApplicationRateMethod,
    );
    setSelectedRateMethod(newRateMethod ? newRateMethod : null);
  }, [phase, phase.dynamicApplicationRate]);

  const updateRateMethod = () => {
    const { current: newRateMethod } = newRateMethodRef;
    if (!newRateMethod) return;

    let newPhase = cloneDeep(phase);
    const { dynamicApplicationRate } = newPhase;
    const dynamicApplicationRateConfig = {
      minRate:
        newRateMethod === DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS
          ? dynamicApplicationRate?.minRate ?? null
          : null,
      maxRate:
        newRateMethod === DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS
          ? dynamicApplicationRate?.maxRate ?? null
          : null,
      rateDefiningNutrients:
        newRateMethod === DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS
          ? dynamicApplicationRate?.rateDefiningNutrients ?? []
          : [],
      productPlant:
        newRateMethod === DynamicPRecommendationApplicationRate.PRODUCT_PLANT
          ? dynamicApplicationRate?.productPlant ?? null
          : null,
      dynamicApplicationRateMethod: newRateMethod,
      rateUnits: dynamicApplicationRate?.rateUnits ?? [], // kg/ha or l/ha
      productPlantUnits: dynamicApplicationRate?.productPlantUnits ?? [], // g/plant or ml/plant
    };

    newPhase = {
      ...newPhase,
      dynamicApplicationRate: dynamicApplicationRateConfig,
      recommendedProducts:
        newPhase.recommendedProducts.length > 1 &&
        newRateMethod === DynamicPRecommendationApplicationRate.PRODUCT_PLANT
          ? []
          : phase.recommendedProducts,
    };
    onValueChanged && onValueChanged(newPhase);
  };

  const onRateMethodChange = (value: DynamicPRecommendationApplicationRate) => {
    if (
      value === DynamicPRecommendationApplicationRate.PRODUCT_PLANT &&
      phase.recommendedProducts.length > 1
    ) {
      return setShowDialog(true);
    } else {
      updateRateMethod();
    }
  };

  return (
    <>
      <ConfirmationDialog
        open={showDialog}
        title={t('resetProductDialog.title')}
        description={t('resetProductDialog.description')}
        icon='Info'
        iconColorConcept='brand'
        okButton={t('resetProductDialog.yes')}
        cancelButton={t('resetProductDialog.cancel')}
        onOk={() => {
          updateRateMethod();
          setShowDialog(false);
        }}
        onCancel={() => setShowDialog(false)}
      />
      <SelectWrapper dataCy='dynamic-app-rate-method-dropdown'>
        <Select
          ariaLabel={t('selectRateMethodLabel')}
          css={{ width: '100%' }}
          value={selectedRateMethod ? selectedRateMethod.value : null}
          items={rateMethodArray || []}
          placeholder={t('selectRateMethodLabel')}
          position='popper'
          size='xs'
          disabled={disabled}
          onChange={(value: DynamicPRecommendationApplicationRate) => {
            newRateMethodRef.current = value;
            onRateMethodChange(value);
          }}
          variant={triggerErrorState ? 'error' : 'default'}
        />
      </SelectWrapper>
    </>
  );
};
