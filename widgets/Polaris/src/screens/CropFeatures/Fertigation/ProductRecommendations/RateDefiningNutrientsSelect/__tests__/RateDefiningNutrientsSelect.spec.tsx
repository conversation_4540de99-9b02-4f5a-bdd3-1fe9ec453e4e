import React from 'react';
import { render } from '@testing-library/react';
import { RateDefiningNutrientsSelect } from '../RateDefiningNutrientsSelect';
import { mockSoilPhase } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

const mockNutrientOptions = [
  { value: '880abcda-5ee5-4068-879c-94489be314d5', label: 'N' },
  { value: 'f2c275c4-1522-4524-8747-08ace254b155', label: 'P' },
  { value: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5', label: 'K' },
  { value: '5fe008ca-d2d7-48a6-921b-8b53c781a4ab', label: 'Ca' },
  { value: 'd764880a-c5d8-4a9a-ab49-a2b3ba59970d', label: 'Mg' },
];
const mockNutrients = [
  '880abcda-5ee5-4068-879c-94489be314d5',
  'f2c275c4-1522-4524-8747-08ace254b155',
  '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
  '5fe008ca-d2d7-48a6-921b-8b53c781a4ab',
  'd764880a-c5d8-4a9a-ab49-a2b3ba59970d',
];

describe('RateDefiningNutrientsSelect Component', () => {
  const mockOnValueChanged = jest.fn();

  const renderComponent = (
    phase = {
      ...mockSoilPhase,
      dynamicApplicationRate: {
        ...mockSoilPhase.dynamicApplicationRate,
        rateDefiningNutrients: mockNutrients,
      },
    },
  ) =>
    render(
      <RateDefiningNutrientsSelect
        phase={phase}
        onValueChanged={mockOnValueChanged}
        rateNutrientsArray={mockNutrientOptions}
        triggerErrorState={false}
      />,
    );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId, getByRole } = renderComponent();

    expect(getByTestId('rate-defining-nutrients-combobox')).toBeInTheDocument();
    expect(getByRole('combobox')).toBeInTheDocument();
  });
});
