import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComboboxComponent } from '../ProductRecommendations.styled';
import {
  ComboBoxOption,
  DynamicPRecommendationApplicationRate,
  DynamicApplicationRate,
  FertigationPRecommendationGrowthPhase,
} from '@common/types';
import { cloneDeep } from 'lodash';
import { SelectWrapper } from '@widgets/Polaris/src/components';

interface ComponentProps {
  phase: FertigationPRecommendationGrowthPhase;
  rateNutrientsArray: ComboBoxOption[];
  triggerErrorState: boolean;
  onValueChanged: (phase: FertigationPRecommendationGrowthPhase) => void;
}

export const RateDefiningNutrientsSelect: React.FC<ComponentProps> = ({
  phase,
  rateNutrientsArray,
  triggerErrorState,
  onValueChanged,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations.productRecommendationsTable',
  });
  const [selectedOptions, setSelectedOptions] = useState<ComboBoxOption[] | null>(null);

  useEffect(() => {
    if (!phase || !rateNutrientsArray) return;
    const rateDefiningNutrients = phase.dynamicApplicationRate?.rateDefiningNutrients || [];

    const newRateNutrients = rateDefiningNutrients
      .map((id) => rateNutrientsArray.find((option) => option.value === id))
      .filter((option) => option !== undefined);

    setSelectedOptions(newRateNutrients.length > 0 ? newRateNutrients : null);
  }, [phase.dynamicApplicationRate, rateNutrientsArray]);

  const onNutrientsSelectChange = (selectedOptions: ComboBoxOption[]) => {
    setSelectedOptions(selectedOptions);

    let newPhase = cloneDeep(phase);
    const { dynamicApplicationRate } = newPhase;

    const defaultDynamicApplicationRate: DynamicApplicationRate = {
      rateDefiningNutrients: [],
      minRate: null,
      maxRate: null,
      rateUnits: [],
      dynamicApplicationRateMethod: null,
      productPlant: null,
      productPlantUnits: [],
    };

    newPhase = {
      ...newPhase,
      dynamicApplicationRate: {
        ...defaultDynamicApplicationRate,
        ...(dynamicApplicationRate || {}),
        rateDefiningNutrients: selectedOptions
          .map((option) => option.value)
          .filter((value): value is string => value !== undefined),
      },
    };

    onValueChanged && onValueChanged(newPhase);
  };

  return (
    <SelectWrapper dataCy='rate-defining-nutrients-combobox'>
      <ComboboxComponent
        cover='outline'
        value={selectedOptions ?? null}
        className='styled-combo-box'
        options={rateNutrientsArray}
        placeholder={t('selectNutrientsLabel')}
        size='xs'
        isMulti={true}
        isDisabled={
          phase.dynamicApplicationRate?.dynamicApplicationRateMethod !==
          DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS
        }
        onChange={(selectedOptions: ComboBoxOption[]) => onNutrientsSelectChange(selectedOptions)}
        ariaLabel={t('selectNutrientsLabel')}
        variant={triggerErrorState ? 'error' : 'default'}
      />
    </SelectWrapper>
  );
};
