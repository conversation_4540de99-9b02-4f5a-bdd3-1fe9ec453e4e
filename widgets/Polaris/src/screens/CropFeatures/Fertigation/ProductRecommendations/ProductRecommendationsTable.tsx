import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { AhuaIcon, Image, Table, Tooltip } from '@yaradigitallabs/ahua-react';
import {
  StyledTable,
  TableWrapper,
  TableHead,
  TableCellStatic,
  TBody,
  StyledTableHeaderCell,
  StyledTableCell,
  ImageFrame,
} from './ProductRecommendations.styled';
import { RateRuleSelect } from './RateRuleSelect/RateRuleSelect';
import { AppMethodSelect } from './AppMethodSelect/AppMethodSelect';
import { AddProducts } from './AddProducts/AddProducts';
import { DynamicMethodSelect } from './DynamicMethodSelect/DynamicMethodSelect';
import { RateDefiningNutrientsSelect } from './RateDefiningNutrientsSelect/RateDefiningNutrientsSelect';
import { InputNumberComponent } from './InputNumberComponent/InputNumberComponent';
import { AppCondition } from './AppCondition';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  ComboBoxOption,
  ProductRegion,
  ValidationStatus,
  DynamicPRecommendationApplicationRate,
  FertigationPRecommendationGrowthPhase,
  InputFieldType,
  ProductRecommendationApplicationTypes,
  ProductRecommendationRateRules,
  ProductRecommendationDynamicUnitNames,
} from '@common/types';
import { MAX_PHASE_LENGTH, MIN_PHASE_LENGTH } from '../../shared/constants';

interface ProductRecommendationsProps {
  applicationData: FertigationPRecommendationGrowthPhase[];
  productsRegion: ProductRegion[];
  rateNutrientsArray: ComboBoxOption[];
  dynamicUnitNames: ProductRecommendationDynamicUnitNames;
  useCloneGrowthPhase: (data: FertigationPRecommendationGrowthPhase) => void;
  useDeleteGrowthPhase: (data: FertigationPRecommendationGrowthPhase) => void;
  useUpdateGrowthPhase: (data: FertigationPRecommendationGrowthPhase) => void;
  handleOpenAddProductRecommendationsPopup: (data: FertigationPRecommendationGrowthPhase) => void;
  handleUpdateRecommendedProducts: (
    data: string[],
    phase: FertigationPRecommendationGrowthPhase,
  ) => void;
  handleOpenAppConditionBuilder: (
    phase: FertigationPRecommendationGrowthPhase,
    displayedCondition: string,
  ) => void;
}

type AppRow = {
  tHeadCell: string;
  tBodyCell:
    | ((phase: FertigationPRecommendationGrowthPhase) => string | React.JSX.Element)
    | keyof FertigationPRecommendationGrowthPhase;
  tHeadCellToolTip?: string;
  tCellStyle?: string;
};

export const ProductRecommendationsTable: React.FC<ProductRecommendationsProps> = ({
  applicationData,
  productsRegion,
  rateNutrientsArray,
  dynamicUnitNames,
  useCloneGrowthPhase,
  useDeleteGrowthPhase,
  useUpdateGrowthPhase,
  handleOpenAddProductRecommendationsPopup,
  handleUpdateRecommendedProducts,
  handleOpenAppConditionBuilder,
}) => {
  const keyTablePrefix = 'polaris.fpDetails.productRecommendations.productRecommendationsTable';
  const { t } = useTranslation('polaris', {
    keyPrefix: keyTablePrefix,
  });

  const [selectedAppConfig, setSelectedAppConfig] = useState<AppRow[]>();

  const { selectedAppType, selectedMMMValidation } = useAppContext();

  const getShouldTriggerErrorState = (condition: boolean) =>
    condition && selectedMMMValidation?.validationStatus === ValidationStatus.FAILED;
  const soilAppConfig: AppRow[] = useMemo(
    () => [
      {
        tHeadCell: t('grPhaseName'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) =>
          phase.phaseName ? phase.phaseName : `${t('growthPhase')} ${phase.phaseNumber ?? ''}`,
      },
      {
        tHeadCell: t('grPhaseImg'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) =>
          phase && (
            <ImageFrame>
              {phase.phaseImage?.[0].value && (
                <Image
                  src={phase?.phaseImage[0].value}
                  data-cy='soil-growth-stage-image'
                  width={100}
                  ratio={1.38}
                />
              )}
            </ImageFrame>
          ),
      },
      {
        tHeadCell: t('recommendedProducts'),
        tCellStyle: 'add-product-cell',
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <AddProducts
            phase={phase}
            productsRegion={productsRegion}
            handleOpenAddProductRecommendationsPopup={handleOpenAddProductRecommendationsPopup}
            handleUpdateRecommendedProducts={handleUpdateRecommendedProducts}
          />
        ),
      },
      {
        tHeadCell: t('appMethod'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <AppMethodSelect
            phase={phase}
            onValueChanged={useUpdateGrowthPhase}
            triggerErrorState={getShouldTriggerErrorState(!phase.applicationMethod)}
          />
        ),
      },
      {
        tHeadCell: t('appCondition'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <AppCondition
            phase={phase}
            keyTablePrefix={keyTablePrefix}
            onOpenAppConditionBuilder={handleOpenAppConditionBuilder}
          />
        ),
      },
      {
        tHeadCell: t('rateRule'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <RateRuleSelect
            phase={phase}
            onValueChanged={useUpdateGrowthPhase}
            triggerErrorState={getShouldTriggerErrorState(!phase.rateRule)}
          />
        ),
      },
      {
        tHeadCell: t('fixedAppRate', {
          massUnit: dynamicUnitNames?.recommendedSolidUnitPerArea,
          volumeUnit: dynamicUnitNames?.recommendedLiquidsUnitPerArea,
          interpolation: { escapeValue: false },
        }),
        tHeadCellToolTip: t('fixedAppRateTooltip'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <InputNumberComponent
            phase={phase}
            field={InputFieldType.FixedApplicationRate}
            disabled={phase.rateRule !== ProductRecommendationRateRules.FIXED}
            triggerErrorState={getShouldTriggerErrorState(
              phase.rateRule === ProductRecommendationRateRules.FIXED &&
                phase.fixedApplicationRate?.applicationValue == null,
            )}
            onValueBlur={useUpdateGrowthPhase}
          />
        ),
      },
      {
        tHeadCell: t('dynamicMethod'),
        tBodyCell: (phase) => (
          <DynamicMethodSelect
            phase={phase}
            disabled={phase.rateRule !== ProductRecommendationRateRules.DYNAMIC}
            triggerErrorState={getShouldTriggerErrorState(
              phase.rateRule === ProductRecommendationRateRules.DYNAMIC &&
                !phase.dynamicApplicationRate,
            )}
            onValueChanged={useUpdateGrowthPhase}
          />
        ),
      },
      {
        tHeadCell: t('rateDefNutrients'),
        tHeadCellToolTip: t('rateDefNutrientsTooltip'),
        tBodyCell: (phase) => (
          <RateDefiningNutrientsSelect
            phase={phase}
            rateNutrientsArray={rateNutrientsArray}
            triggerErrorState={getShouldTriggerErrorState(
              phase.rateRule === ProductRecommendationRateRules.DYNAMIC &&
                phase.dynamicApplicationRate?.dynamicApplicationRateMethod ===
                  DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS &&
                phase.dynamicApplicationRate?.rateDefiningNutrients.length < 1,
            )}
            onValueChanged={useUpdateGrowthPhase}
          />
        ),
      },
      {
        tHeadCell: t('maxAppRate', {
          massUnit: dynamicUnitNames?.recommendedSolidUnitPerArea,
          volumeUnit: dynamicUnitNames?.recommendedLiquidsUnitPerArea,
          interpolation: { escapeValue: false },
        }),
        tBodyCell: (phase) => (
          <InputNumberComponent
            phase={phase}
            field={InputFieldType.MaxAppRate}
            disabled={
              phase.dynamicApplicationRate?.dynamicApplicationRateMethod !==
              DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS
            }
            onValueBlur={useUpdateGrowthPhase}
          />
        ),
      },
      {
        tHeadCell: t('minAppRate', {
          massUnit: dynamicUnitNames?.recommendedSolidUnitPerArea,
          volumeUnit: dynamicUnitNames?.recommendedLiquidsUnitPerArea,
          interpolation: { escapeValue: false },
        }),
        tBodyCell: (phase) => (
          <InputNumberComponent
            phase={phase}
            field={InputFieldType.MinAppRate}
            disabled={
              phase.dynamicApplicationRate?.dynamicApplicationRateMethod !==
              DynamicPRecommendationApplicationRate.NUTRIENT_SPLITS
            }
            onValueBlur={useUpdateGrowthPhase}
          />
        ),
      },
      {
        tHeadCell: t('appOfProduct', {
          massUnit: dynamicUnitNames?.recommendedSolidsUnitPerPlant,
          volumeUnit: dynamicUnitNames?.recommendedLiquidsUnitPerPlant,
          interpolation: { escapeValue: false },
        }),
        tBodyCell: (phase) => (
          <InputNumberComponent
            phase={phase}
            field={InputFieldType.AppOfProductPlant}
            disabled={
              !(
                phase.rateRule === ProductRecommendationRateRules.DYNAMIC &&
                phase.dynamicApplicationRate?.dynamicApplicationRateMethod ===
                  DynamicPRecommendationApplicationRate.PRODUCT_PLANT
              )
            }
            triggerErrorState={getShouldTriggerErrorState(
              phase.rateRule === ProductRecommendationRateRules.DYNAMIC &&
                phase.dynamicApplicationRate?.dynamicApplicationRateMethod ===
                  DynamicPRecommendationApplicationRate.PRODUCT_PLANT &&
                phase.dynamicApplicationRate?.productPlant == null,
            )}
            onValueBlur={useUpdateGrowthPhase}
          />
        ),
      },
    ],
    [productsRegion, dynamicUnitNames],
  );

  const foliarAppConfig: AppRow[] = useMemo(
    () => [
      {
        tHeadCell: t('grPhaseName'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) =>
          phase.phaseName ? phase.phaseName : `${t('growthPhase')} ${phase.phaseNumber ?? ''}`,
      },
      {
        tHeadCell: t('grPhaseImg'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) =>
          phase && (
            <ImageFrame>
              {phase.phaseImage?.[0].value && (
                <Image
                  src={phase?.phaseImage[0].value}
                  data-cy='foliar-growth-stage-image'
                  width={100}
                  ratio={1.38}
                />
              )}
            </ImageFrame>
          ),
      },
      {
        tHeadCell: t('recommendedProducts'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <AddProducts
            phase={phase}
            productsRegion={productsRegion}
            handleOpenAddProductRecommendationsPopup={handleOpenAddProductRecommendationsPopup}
            handleUpdateRecommendedProducts={handleUpdateRecommendedProducts}
          />
        ),
      },
      {
        tHeadCell: t('appCondition'),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <AppCondition
            phase={phase}
            keyTablePrefix={keyTablePrefix}
            onOpenAppConditionBuilder={handleOpenAppConditionBuilder}
          />
        ),
      },
      {
        tHeadCell: t('rateRule'),
        tBodyCell: () => t('fixedAppRateDescription'),
      },
      {
        tHeadCell: t('fixedAppRate', {
          massUnit: dynamicUnitNames?.recommendedSolidUnitPerArea,
          volumeUnit: dynamicUnitNames?.recommendedLiquidsUnitPerArea,
          interpolation: { escapeValue: false },
        }),
        tBodyCell: (phase: FertigationPRecommendationGrowthPhase) => (
          <InputNumberComponent
            phase={phase}
            field={InputFieldType.FixedApplicationRate}
            disabled={false}
            triggerErrorState={getShouldTriggerErrorState(
              phase.fixedApplicationRate?.applicationValue == null,
            )}
            onValueBlur={useUpdateGrowthPhase}
          />
        ),
      },
    ],
    [productsRegion, dynamicUnitNames],
  );

  useEffect(() => {
    const selectedConfig =
      selectedAppType === ProductRecommendationApplicationTypes.SOIL
        ? soilAppConfig
        : foliarAppConfig;
    setSelectedAppConfig(selectedConfig);
  }, [selectedAppType, soilAppConfig, foliarAppConfig]);

  const renderTableCell = (AppRow: AppRow, phase: FertigationPRecommendationGrowthPhase) => {
    if (typeof AppRow.tBodyCell === 'string') {
      const value = phase[AppRow.tBodyCell];
      return value != null ? String(value) : '';
    }
    return AppRow.tBodyCell(phase);
  };

  return (
    <TableWrapper data-cy='fertigation-product-recommendations-table'>
      <StyledTable>
        <TBody data-cy='fertigation-product-recommendations-table-body'>
          <Table.Row key='fertigation-product-recommendations-table-head'>
            <TableCellStatic>
              <TableHead>{t('grPhaseNo')}</TableHead>
            </TableCellStatic>
            {applicationData &&
              applicationData.map((phase) => {
                const phaseCount = applicationData.filter(
                  (item) => item.phaseNumber === phase.phaseNumber,
                ).length;
                return (
                  <StyledTableCell key={phase.id + phase.phaseName} className='hovered-btn'>
                    <TableHead>
                      {phase.phaseNumber}{' '}
                      <Table.ActionMenu title=''>
                        <Table.ActionMenuItem
                          icon='Copy'
                          disabled={phaseCount >= MAX_PHASE_LENGTH}
                          data-cy='duplicate-action'
                          onClick={() =>
                            phaseCount < MAX_PHASE_LENGTH && useCloneGrowthPhase(phase)
                          }
                        >
                          {t('duplicate')}
                        </Table.ActionMenuItem>
                        <Table.ActionMenuItem
                          icon='Delete'
                          disabled={phaseCount === MIN_PHASE_LENGTH}
                          data-cy='delete-action'
                          onClick={() => {
                            phaseCount >= MIN_PHASE_LENGTH && useDeleteGrowthPhase(phase);
                          }}
                        >
                          {t('delete')}
                        </Table.ActionMenuItem>
                      </Table.ActionMenu>
                    </TableHead>
                  </StyledTableCell>
                );
              })}
          </Table.Row>
          {selectedAppConfig?.map((AppRow, i) => (
            <Table.Row key={AppRow.tHeadCell + i}>
              <StyledTableHeaderCell key={`header-${AppRow.tHeadCell}-${i}`}>
                <TableHead>
                  <span>{AppRow.tHeadCell}</span>
                  {AppRow.tHeadCellToolTip && (
                    <Tooltip
                      data-cy='default-value-tooltip'
                      className='default-tooltip'
                      concept='inverse'
                      maxWidth={225}
                      minWidth={225}
                      position='right'
                      text={AppRow.tHeadCellToolTip}
                      tipVisibility
                    >
                      <AhuaIcon colorConcept='neutral' icon='Info' iconSize='x4' />
                    </Tooltip>
                  )}{' '}
                </TableHead>
              </StyledTableHeaderCell>

              {applicationData &&
                applicationData.map((phase, i) => (
                  <StyledTableCell
                    className={AppRow.tCellStyle ?? ''}
                    key={`body-${phase.phaseName}-${phase.id}-${i}`}
                  >
                    {renderTableCell(AppRow, phase)}
                  </StyledTableCell>
                ))}
            </Table.Row>
          ))}
        </TBody>
      </StyledTable>
    </TableWrapper>
  );
};
