import { Stack, Status, styled, Table } from '@yaradigitallabs/ahua-react';

// Table
export const TableWrapperStyled = styled('div', {
  width: '1080px',
  height: '460px',
  // Remove pagination absolute position
  [`& div`]: {
    position: 'relative',
  },

  // Table wrapper div
  [`& > div:first-child`]: {
    padding: '0 $x4',
  },
  ['& thead, & tbody']: {
    verticalAlign: 'middle',
  },

  // Table head rows
  [`& thead > tr`]: {
    verticalAlign: 'middle',
    display: 'inline-block',
    width: '1048px',
  },
  [`& thead > tr:nth-child(1)`]: {
    height: '53px',
    borderTop: '1px solid var(--colors-black10)',
  },
  [`& thead > tr:nth-child(2)`]: {
    borderTop: 'none !important',
    height: '55px',
  },

  // Table body rows
  [`& tbody > tr`]: {
    verticalAlign: 'middle',
    display: 'inline-block',
    width: '1048px',
  },
  [`& tbody > tr:nth-child(even)`]: {
    borderTop: 'none !important',
    height: '54px',
  },
  [`& tbody > tr:nth-child(odd)`]: {
    height: '55px',
  },

  // Pagination wrapper
  [`& > div > div`]: {
    padding: '14px 0',
  },

  // Pagination buttons
  [`& > div > div > div > div > div > button`]: {
    padding: '$x2',
  },

  // Pagination input
  [`& > div > div > div > div > div > div > input`]: {
    height: '$x10',
  },
});

export const StyledTableHeaderRow = styled(Table.Row, {
  [`& th`]: {
    display: 'inline-block',
    height: '$x14',
    alignContent: 'center',
  },
  [`& th:nth-child(1)`]: {
    width: '69px',
    padding: 0,
  },
  [`& th:nth-child(2)`]: {
    width: '256px',
    padding: '0 9px 0 14px',
    [`&:before`]: {
      content: 'none',
    },
  },
  [`& th:nth-child(3)`]: {
    width: '258px',
    padding: '0 9px 0 $x3',
  },
  [`& th:nth-child(4)`]: {
    width: '400px',
    padding: '0 9px 0 $x3',
  },
});

export const StyledTableSearchRow = styled(Table.Row, {
  [`& td:nth-child(1)`]: {
    height: '$x14',
    width: '69px',
    padding: 0,
  },
  [`& td:nth-child(n+2):nth-child(-n+3)`]: {
    width: '242px',
    padding: '$x2 $x4 $x2 $x3',
  },
});

export const StyledTableSortHead = styled(Table.SortHead, {
  svg: {
    color: '$brand-contrast',
    stroke: '$brand-contrast',
  },
});

export const StyledTableRow = styled(Table.Row, {
  ['& td']: {
    verticalAlign: 'middle',
  },
  [`& td:nth-child(1)`]: {
    width: '69px',
    padding: 0,
  },
  [`& td:nth-child(2)`]: {
    width: '242px',
    padding: '14px $x4 18px $x3',
  },
  [`& td:nth-child(3)`]: {
    width: '242px',
    height: '20px',
    padding: '$x4 $x4 $x5 $x3',
  },
  [`& td:nth-child(4)`]: {
    width: '425px',
    height: '$x14',
    padding: '0 0 0 $x3',
  },
});

export const StyledStatus = styled(Status, {
  height: '14px',
  wordBreak: 'normal',
  minWidth: 'fit-content',
  border: '1px solid $blue70',
  backgroundColor: 'unset !important',
  variants: {
    state: {
      brown: {
        color: '$brown50',
        borderColor: '$brown50 !important',
      },
    },
  },
});

export const StyledStack = styled(Stack, {
  height: '$x14',
  padding: 0,
  alignItems: 'center',
  overflow: 'auto hidden',
  '&::-webkit-scrollbar': {
    width: '$x1',
    height: '6px',
  },
  '&::-webkit-scrollbar-track': {
    margin: '$x4 0',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '$black20',
    borderRadius: '$s',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: '$black40',
  },
});
