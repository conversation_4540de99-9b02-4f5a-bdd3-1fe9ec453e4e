import React, { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTableConfig } from '@polaris-hooks/index';
import {
  FertigationPRecommendationGrowthPhase,
  Partner,
  ProductFamily,
  ProductRegion,
} from '@common/types';
import TableSection from '@widgets/Polaris/src/components/Table/Table';
import { getColumnDefs } from '../utils/addProductsPopup';
import { EmptyStateComponent } from '@widgets/Polaris/src/components';
import { TableWrapperStyled } from './AddProductsTable.styled';
import { TableHeader, TableBody } from '.';
import { ProductsListByPhase } from '../AddProductsPopup.type';
import { LIMIT, PRODUCT_FAMILY_MEDIA_URI_KEYS } from '@common/constants';
import { ImagePreview } from '../../../../CMMM/OrganicFertilisers/ProductsAvailable/ProductsAvailable.styled';

interface AddProductsTableProps {
  tPrefix: string;
  productsRegion: ProductRegion[] | undefined;
  productsFamily: ProductFamily[] | undefined;
  partnerTags: Partner[];
  selectedProducts: ProductsListByPhase;
  selectedPhase: FertigationPRecommendationGrowthPhase | undefined;
  setSelectedProducts: React.Dispatch<React.SetStateAction<ProductsListByPhase>>;
}

export const AddProductsTable: FC<AddProductsTableProps> = ({
  tPrefix,
  productsRegion,
  productsFamily,
  partnerTags,
  selectedProducts,
  selectedPhase,
  setSelectedProducts,
}) => {
  const { t } = useTranslation();
  const [resetSearch, setResetSearch] = useState<boolean>(false);

  const productNameColCustomFilter = (productRegionId: string, filterValue: string) => {
    const productRegion = productsRegion?.find((x) => x.id == productRegionId);
    const productName = productRegion?.localizedName
      ? productRegion?.localizedName
      : productRegion?.name;
    return productName?.toLowerCase().includes(filterValue.toLowerCase()) ?? false;
  };

  const productFamilyColCustomFilter = (productFamilyId: string, filterValue: string) => {
    const productFamily = productsFamily?.find((x) => x.id == productFamilyId);

    return productFamily?.name.toLowerCase().includes(filterValue.toLowerCase()) ?? false;
  };

  const getProductFamilyUri = (productFamilyId: string) => {
    const productFamily = productsFamily?.find((x) => x.id == productFamilyId);

    if (productFamily) {
      const uri =
        productFamily.mediaUri &&
        productFamily.mediaUri.find((item) => item.key == PRODUCT_FAMILY_MEDIA_URI_KEYS.DEFAULT);

      return uri ? <ImagePreview src={uri.value} title={productFamily.name} /> : productFamily.name;
    }
    return null;
  };
  const colDefs = getColumnDefs({
    t,
    keyPrefix: tPrefix,
    productNameColCustomFilter,
    productFamilyColCustomFilter,
  });

  const { table, paginationState, autoResetPageIndexRef } = useTableConfig<ProductRegion>(
    productsRegion || [],
    () => colDefs,
    {
      pageIndex: LIMIT.ZERO,
      pageSize: LIMIT.FIVE,
    },
  );

  return (
    <TableWrapperStyled>
      <TableSection
        showPagination={table.getRowCount() ? table.getRowCount() > 5 : false}
        tableHeader={
          <TableHeader
            table={table}
            resetSearch={resetSearch}
            autoResetPageIndexRef={autoResetPageIndexRef}
          />
        }
        tableBody={
          productsRegion &&
          selectedPhase && (
            <TableBody
              table={table}
              partnerTags={partnerTags}
              productsRegion={productsRegion}
              selectedProducts={selectedProducts}
              selectedPhase={selectedPhase}
              getProductFamilyUri={getProductFamilyUri}
              setSelectedProducts={setSelectedProducts}
            />
          )
        }
        pageCount={table.getPageCount()}
        pageIndex={paginationState.pageIndex}
        canNextPage={table.getCanNextPage()}
        canPreviousPage={table.getCanPreviousPage()}
        onNextPage={() => table.nextPage()}
        onPrevPage={() => table.previousPage()}
        onLastPage={() => table.lastPage()}
        onFirstPage={() => table.firstPage()}
        variant={'dynamic'}
      />

      {table.getFilteredRowModel().rows.length === 0 && (
        <EmptyStateComponent
          dataCy='add-products-empty-search-state'
          message={t(`polaris.common.emptySearchState.text`)}
          searchTerm={` ${table
            .getState()
            .columnFilters.map((filter) => filter.value)
            .join(' ')}`}
          actionText={t(`polaris.common.emptySearchState.button`)}
          onActionClick={() => {
            setResetSearch(!resetSearch);
            table.resetColumnFilters(true);
          }}
        />
      )}
    </TableWrapperStyled>
  );
};
