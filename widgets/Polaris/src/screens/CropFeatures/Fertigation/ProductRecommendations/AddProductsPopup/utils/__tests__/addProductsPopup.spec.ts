import { t } from 'i18next';
import { getColumnDefs, validateMaxProductsSelected } from '../addProductsPopup';
import { ColumnDef } from '@tanstack/react-table';
import { ProductRegion } from '@common/types';

describe('addProductsPopup Utils', () => {
  it('getColumnDefs', () => {
    const filterFn1 = jest.fn();
    const filterFn2 = jest.fn();
    const expectedColDefs: ColumnDef<ProductRegion>[] = [
      {
        header: undefined,
        accessorKey: 'checkbox',
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: 'Product family',
        accessorKey: 'productFamilyId',
        enableColumnFilter: true,
        enableSorting: true,
        filterFn: filterFn1,
      },
      {
        header: 'Product name',
        accessorKey: 'name',
        enableSorting: false,
        enableColumnFilter: true,
        filterFn: filterFn2,
      },
      {
        header: 'Partners',
        accessorKey: 'partners',
        enableColumnFilter: false,
        enableSorting: false,
      },
    ];
    const columnDefs = getColumnDefs({
      t,
      keyPrefix: 'key',
      productNameColCustomFilter: filterFn1,
      productFamilyColCustomFilter: filterFn2,
    });
    columnDefs.forEach((column, index) => {
      expect(column.accessorKey).toEqual(expectedColDefs[index].accessorKey);
      expect(column.enableSorting).toEqual(expectedColDefs[index].enableSorting);
      expect(column.enableColumnFilter).toEqual(expectedColDefs[index].enableColumnFilter);
    });
  });

  it('validateMaxProductsSelected, number is less than max', () => {
    const arrayWithLessThanMaxEntries = new Array(19);
    const validatedNumber = validateMaxProductsSelected(arrayWithLessThanMaxEntries);
    expect(validatedNumber).toEqual(false);
  });
  it('validateMaxProductsSelected, number is more than the max', () => {
    const arrayWithMoreThanMaxEntries = new Array(21);
    const validatedNumber = validateMaxProductsSelected(arrayWithMoreThanMaxEntries);
    expect(validatedNumber).toEqual(true);
  });
});
