import React from 'react';
import { render, within } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockAppProviderValue, partnerTagsResponseMock } from '@common/mocks';
import {
  mockSoilPhase,
  useProductsByRegionMultipleResponse,
  useProductsByRegionResponse,
  useProductsFamilyResponse,
} from '../../../../../Home/mock-data/MockData';
import { AddProductsPopup, AddProductsPopupProps } from '../AddProductsPopup';
import userEvent from '@testing-library/user-event';

describe('Fertigation Product Recommendation, Add Products Popup', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const addProductsPopupProps: AddProductsPopupProps = {
    productsRegion: useProductsByRegionResponse,
    productsFamily: useProductsFamilyResponse,
    partnerTags: partnerTagsResponseMock,
    selectedProducts: {},
    selectedPhase: mockSoilPhase,
    showDialog: true,
    setSelectedProducts: jest.fn(),
    setShowDialog: jest.fn(),
    handleSave: jest.fn(),
  };
  const renderComponent = (props: AddProductsPopupProps) =>
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AddProductsPopup {...props} />,
      </AppContext.Provider>,
    );

  it('render table and match snapshot', () => {
    const component = renderComponent(addProductsPopupProps);
    expect(component).toMatchSnapshot();
  });

  it('render table when there are products available', async () => {
    const component = renderComponent(addProductsPopupProps);

    expect(component.getByTestId('table-section')).toBeInTheDocument();
    expect(component.queryByTestId('add-products-empty-search-state')).not.toBeInTheDocument();
    expect(component.queryByLabelText('Go to next page')).not.toBeInTheDocument();

    const searchBar = component.getByTestId('add-products-table-search-bar-name');
    expect(searchBar).toBeInTheDocument();

    await userEvent.type(searchBar, 'test');
    expect(searchBar).toHaveValue('test');

    const saveButton = component.getByTestId('add-products-save-button');
    await userEvent.click(saveButton);
    expect(addProductsPopupProps.handleSave).toHaveBeenCalled();
  });

  it('selectedProducts empty, select product', async () => {
    const component = renderComponent(addProductsPopupProps);

    const checkbox = component.getAllByRole('checkbox')[0];

    expect(checkbox).not.toBeChecked();

    await userEvent.click(checkbox);

    expect(checkbox).toBeChecked();
    expect(addProductsPopupProps.setSelectedProducts).toHaveBeenCalledTimes(1);
  });

  // Not working for some reason
  xit('unselect a selected product', async () => {
    const propsWithSelectedProduct = {
      ...addProductsPopupProps,
      selectedProducts: {
        [`${addProductsPopupProps.selectedPhase.id}`]: [addProductsPopupProps.productsRegion[0].id],
      },
    };
    const component = renderComponent(propsWithSelectedProduct);

    const checkbox = component.getAllByRole('checkbox')[0];

    expect(checkbox).toBeChecked();

    await userEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
    expect(addProductsPopupProps.setSelectedProducts).toHaveBeenCalledTimes(1);
  });

  it('render empty table state when there are no products available', async () => {
    const newProps = {
      ...addProductsPopupProps,
      productsRegion: [],
    };
    const component = renderComponent(newProps);

    const emptyStateComp = component.getByTestId('add-products-empty-search-state');
    expect(emptyStateComp).toBeInTheDocument();

    const emptyStateIcon = within(emptyStateComp).getAllByTestId('empty-state-info-icon');
    expect(emptyStateIcon[0]).toBeInTheDocument();

    const actBtn = within(emptyStateComp).getByTestId(
      'add-products-empty-search-state-action-button',
    );
    expect(actBtn).toBeInTheDocument();

    expect(component.queryByLabelText('Go to next page')).not.toBeInTheDocument();
    const closeButton = component.getByTestId('add-products-close-btn');
    await userEvent.click(closeButton);
    expect(addProductsPopupProps.setShowDialog).toHaveBeenCalled();
  });

  it('render table pagination when there are more then 5 products available', () => {
    const newState = {
      ...addProductsPopupProps,
      productsRegion: useProductsByRegionMultipleResponse,
    };
    const component = renderComponent(newState);
    expect(component.getByLabelText('Go to next page')).toBeInTheDocument();
    expect(component.getByLabelText('Go to previous page')).toBeInTheDocument();
    expect(component.getByLabelText('Go to first page')).toBeInTheDocument();
    expect(component.getByLabelText('Go to last page')).toBeInTheDocument();
  });
});
