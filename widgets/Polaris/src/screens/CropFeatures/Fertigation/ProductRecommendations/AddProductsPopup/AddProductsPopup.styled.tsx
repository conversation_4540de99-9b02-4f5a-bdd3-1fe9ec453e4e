import { Button, Dialog, styled } from '@yaradigitallabs/ahua-react';

export const StyledDialogContent = styled(Dialog.Content, {
  '&&': {
    maxHeight: '100%',
    maxWidth: '1080px',
    height: '684px',
    width: '1080px',
    minHeight: 'unset',
    overflow: 'unset',
  },
});

export const HeaderWrapper = styled('div', {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '$x14',
  width: '100%',
  boxSizing: 'border-box',
  padding: '$x1 $x4',
});
export const StyledDialogHeader = styled(Dialog.Header, {
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  height: '$x12',
  width: '100%',
  margin: '$x1 0',
  overflow: 'hidden',
  gap: '$xhalf',
});

export const StyledDialogTitle = styled(Dialog.Title, {
  fontWeight: 'var(--fontWeights-black)',
  lineHeight: '$scale3',
});

export const subtitleStyles = {
  color: '$black70',
  padding: 0,
  fontSize: '12px',
  lineHeight: '$scale2',
};

export const StyledContentWrapper = styled('div', {
  boxSizing: 'border-box',
  height: '$x18',
  minHeight: '$x18',
  width: '100%',
  marginTop: '$x4',
  padding: '0 $x4',
  alignContent: 'center',
});

export const StyledGrowthPhaseText = styled('div', {
  boxSizing: 'border-box',
  height: '53px',
  marginTop: '19px',
  marginLeft: '3px',
  padding: '$x3 0 $x4 $x4',
  alignContent: 'center',
  fontWeight: 'var(--fontWeights-bold)',
  fontSize: '$scale5',
});

export const StyledButtonWrapper = styled('div', {
  width: '100%',
  height: '$x20',
  display: 'flex',
  justifyContent: 'end',
  alignItems: 'center',
});

export const StyledButton = styled(Button, {
  marginRight: '$x4',
});
