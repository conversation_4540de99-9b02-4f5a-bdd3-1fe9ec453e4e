import { ProductRegion } from '@common/types';
import { ColumnDef } from '@tanstack/react-table';
import { TFunction } from 'i18next';

export interface TableConfigProps {
  t: TFunction;
  keyPrefix: string;
  productNameColCustomFilter: (productRegionId: string, filterValue: string) => boolean;
  productFamilyColCustomFilter: (productFamilyId: string, filterValue: string) => boolean;
}

export function getColumnDefs({
  t,
  keyPrefix,
  productNameColCustomFilter,
  productFamilyColCustomFilter,
}: TableConfigProps): ColumnDef<ProductRegion>[] {
  return [
    {
      header: t(`col1`, { keyPrefix }),
      accessorKey: 'checkbox',
      enableColumnFilter: false,
      enableSorting: false,
    },
    {
      header: t(`col2`, { keyPrefix }),
      accessorKey: 'productFamilyId',
      enableColumnFilter: true,
      enableSorting: true,
      filterFn: (row, columnId, filterValue) =>
        productFamilyColCustomFilter(row.getValue(columnId), filterValue),
    },
    {
      header: t(`col3`, { keyPrefix }),
      accessorKey: 'name',
      enableSorting: false,
      enableColumnFilter: true,
      filterFn: (row, _, filterValue) => {
        return productNameColCustomFilter(row.original.id, filterValue);
      },
    },
    {
      header: t(`col4`, { keyPrefix }),
      accessorKey: 'partners',
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];
}

const MAX_PRODUCTS_NUMBER = 20;

export const validateMaxProductsSelected = (selectedProducts: string[]): boolean =>
  selectedProducts?.length >= MAX_PRODUCTS_NUMBER;
