import React, { Fragment } from 'react';
import { ProductRegion } from '@common/types';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { Label, Table } from '@yaradigitallabs/ahua-react';
import {
  StyledTableSortHead,
  StyledTableHeaderRow,
  StyledTableSearchRow,
} from './AddProductsTable.styled';
import { flexRender } from '@tanstack/react-table';
import { Searchbar } from '@widgets/Polaris/src/components/Searchbar/Searchbar';

export type TableHeaderProps = {
  table: TanstackTable<ProductRegion>;
  resetSearch: boolean;
  autoResetPageIndexRef: React.MutableRefObject<boolean>;
};

export function TableHeader({
  table,
  resetSearch,
  autoResetPageIndexRef,
}: TableHeaderProps): JSX.Element {
  return (
    <>
      {table.getHeaderGroups().map((headerGroup, index) => {
        const key = headerGroup.headers[index].id;
        return (
          <Fragment key={key}>
            <StyledTableHeaderRow>
              {headerGroup.headers.map((header) => (
                <StyledTableSortHead
                  direction={header.column.getIsSorted()}
                  active={Boolean(header.column.getIsSorted())}
                  onClick={header.column.getToggleSortingHandler()}
                  key={`${header.id}-${header.column.columnDef.header}`}
                  color={'primary'}
                  colSpan={header.colSpan}
                >
                  <Label size={'n'}>
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </Label>
                </StyledTableSortHead>
              ))}
            </StyledTableHeaderRow>

            <StyledTableSearchRow>
              {headerGroup.headers.map((header) => {
                return (
                  <Table.Cell key={`${header.id}-${headerGroup.id}`}>
                    {header.column.getCanFilter() ? (
                      <Searchbar
                        dataCy={`add-products-table-search-bar-${header.id}`}
                        column={header.column}
                        resetSearch={resetSearch}
                        autoResetPageIndexRef={autoResetPageIndexRef}
                      />
                    ) : null}
                  </Table.Cell>
                );
              })}
            </StyledTableSearchRow>
          </Fragment>
        );
      })}
    </>
  );
}
