import React, { FC, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StyledContentWrapper,
  StyledDialogContent,
  StyledDialogHeader,
  subtitleStyles,
  StyledGrowthPhaseText,
  StyledDialogTitle,
  StyledButtonWrapper,
  StyledButton,
} from './AddProductsPopup.styled';
import { Card, Dialog, IconButton, Subtitle } from '@yaradigitallabs/ahua-react';
import {
  ProductFamily,
  ProductRegion,
  FertigationPRecommendationGrowthPhase,
  Partner,
} from '@common/types';
import { AddProductsTable } from './AddProductsTable';
import { useTranslation } from 'react-i18next';
import { ProductsListByPhase } from './AddProductsPopup.type';

export interface AddProductsPopupProps {
  productsRegion: ProductRegion[];
  productsFamily: ProductFamily[];
  partnerTags: Partner[];
  selectedProducts: ProductsListByPhase;
  selectedPhase: FertigationPRecommendationGrowthPhase | undefined;
  showDialog: boolean;
  setSelectedProducts: React.Dispatch<React.SetStateAction<ProductsListByPhase>>;
  setShowDialog: (value: boolean) => void;
  handleSave: (data: string[], selectedPhase: FertigationPRecommendationGrowthPhase) => void;
}

const keyPrefix =
  'polaris.fpDetails.productRecommendations.productRecommendationsTable.addProductsPopup';

export const AddProductsPopup: FC<AddProductsPopupProps> = ({
  productsRegion,
  productsFamily,
  partnerTags,
  selectedProducts,
  selectedPhase,
  showDialog,
  setSelectedProducts,
  setShowDialog,
  handleSave,
}) => {
  const { t } = useTranslation();

  const onSaveButtonClick = useCallback(() => {
    if (selectedPhase) {
      handleSave(selectedProducts[selectedPhase.id], selectedPhase);
    }
  }, [selectedProducts, selectedPhase]);

  const handleClose = () => {
    setShowDialog(false);
  };

  return (
    <Dialog open={showDialog} onOpenChange={handleClose}>
      <StyledDialogContent data-cy='add-product-popup-content'>
        <HeaderWrapper>
          <StyledDialogHeader>
            <StyledDialogTitle data-cy='add-product-title'>
              {t(`${keyPrefix}.headerTitle`)}
            </StyledDialogTitle>
            <Subtitle css={subtitleStyles} size='s'>
              {t(`${keyPrefix}.headerSubTitle`)}
            </Subtitle>
          </StyledDialogHeader>
          <Dialog.Close>
            <IconButton
              data-cy='add-products-close-btn'
              icon='Close'
              colorConcept='brand'
              size='xs'
            />
          </Dialog.Close>
        </HeaderWrapper>
        <Card.Divider />
        <StyledContentWrapper>
          {/*TODO: add growth phase image*/}
          <StyledGrowthPhaseText>
            {t(`${keyPrefix}.growthPhase`, {
              growthPhaseNo: selectedPhase?.phaseNumber,
            })}
          </StyledGrowthPhaseText>
        </StyledContentWrapper>
        <AddProductsTable
          tPrefix={`${keyPrefix}.table`}
          productsRegion={productsRegion}
          productsFamily={productsFamily}
          partnerTags={partnerTags}
          selectedProducts={selectedProducts}
          selectedPhase={selectedPhase}
          setSelectedProducts={setSelectedProducts}
        />
        <Card.Divider />
        <StyledButtonWrapper>
          <StyledButton onClick={onSaveButtonClick} data-cy='add-products-save-button'>
            {t('polaris.common.saveButton')}
          </StyledButton>
        </StyledButtonWrapper>
      </StyledDialogContent>
    </Dialog>
  );
};
