// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Fertigation Product Recommendation, Add Products Popup render table and match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body
    style="pointer-events: none;"
  >
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
    <div
      aria-hidden="true"
      data-aria-hidden="true"
    >
      ,
    </div>
    <div
      aria-hidden="true"
      class="c-bLiRqv"
      data-aria-hidden="true"
      data-state="open"
      style="pointer-events: auto;"
    />
    <div
      aria-describedby="radix-:r2:"
      aria-labelledby="radix-:r1:"
      class="c-cPoUYR c-bIcROm"
      data-cy="add-product-popup-content"
      data-state="open"
      id="radix-:r0:"
      role="dialog"
      style="pointer-events: auto;"
      tabindex="-1"
    >
      <div
        class="c-fpuJfD"
      >
        <div
          class="c-cVIuYM c-kevDQS"
        >
          <h2
            class="c-bALNxX c-bDsVHi"
            data-cy="add-product-title"
            id="radix-:r1:"
          >
            polaris.fpDetails.productRecommendations.productRecommendationsTable.addProductsPopup.headerTitle
          </h2>
          <h2
            class="c-iAVmsd c-iAVmsd-gVuvhK-size-s c-iAVmsd-iidfNdP-css"
          >
            polaris.fpDetails.productRecommendations.productRecommendationsTable.addProductsPopup.headerSubTitle
          </h2>
        </div>
        <button
          class="c-dexIdH c-kAXHSi c-kAXHSi-blUiqD-colorConcept-brand c-kAXHSi-dxftns-size-xs"
          data-cy="add-products-close-btn"
          type="button"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-kvTanc-colorConcept-brand c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17.6 18.1l-4.8-6h-1.6l-4.8 6M17.6 5.9l-4.8 6h-1.6l-4.8-6"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-hmfbBV"
      >
        <div
          class="c-iMfnDb"
        >
          polaris.fpDetails.productRecommendations.productRecommendationsTable.addProductsPopup.growthPhase
        </div>
      </div>
      <div
        class="c-YKbkd"
      >
        <div
          class="c-jqtSSh c-jqtSSh-hihSaP-variant-dynamic table-bottom-padding"
          data-cy="table-section"
        >
          <table
            class="c-kwAGqj table"
          >
            <thead>
              <tr
                class="c-eDGYZe c-heWkyW"
              >
                <th
                  class="c-kxWgPf c-kxWgPf-itUYGR-css c-bunVcG"
                  color="primary"
                  colspan="1"
                >
                  <div
                    class="c-UazGY"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                    >
                      col1
                    </label>
                    <div
                      class="c-iWBsAU"
                    />
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-kxWgPf-itUYGR-css c-bunVcG"
                  color="primary"
                  colspan="1"
                >
                  <div
                    class="c-UazGY"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                    >
                      col2
                    </label>
                    <div
                      class="c-iWBsAU"
                    />
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-kxWgPf-itUYGR-css c-bunVcG"
                  color="primary"
                  colspan="1"
                >
                  <div
                    class="c-UazGY"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                    >
                      col3
                    </label>
                    <div
                      class="c-iWBsAU"
                    />
                  </div>
                </th>
                <th
                  class="c-kxWgPf c-kxWgPf-itUYGR-css c-bunVcG"
                  color="primary"
                  colspan="1"
                >
                  <div
                    class="c-UazGY"
                  >
                    <label
                      class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css"
                    >
                      col4
                    </label>
                    <div
                      class="c-iWBsAU"
                    />
                  </div>
                </th>
              </tr>
              <tr
                class="c-eDGYZe c-eKqpYO"
              >
                <td
                  class="c-doquzR"
                />
                <td
                  class="c-doquzR"
                >
                  <form
                    class="search-input"
                  >
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-hAPHBc-iconTrailingVisibility-true c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-cHWLgR-cv c-exiTqG-bTMGXY-cv"
                        data-cy="add-products-table-search-bar-productFamilyId"
                        placeholder="searchbarLabel"
                        value=""
                      />
                      <label
                        class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                      >
                        searchbarLabel
                      </label>
                      <span
                        class="c-fcBbhr"
                      >
                        <span
                          class="c-gsmDXe"
                        >
                          <svg
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-ZiURY c-ZiURY-hfDgku-size-xs"
                            data-testid="trailing-icon"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M14.67 13.569a6.067 6.067 0 0 1-.901.9m.9-.9a6 6 0 1 0-.9.9m.9-.9l5.555 5.1a.958.958 0 1 1-1.354 1.355l-5.101-5.555"
                            />
                          </svg>
                        </span>
                      </span>
                    </div>
                  </form>
                </td>
                <td
                  class="c-doquzR"
                >
                  <form
                    class="search-input"
                  >
                    <div
                      class="c-gJoajD c-gJoajD-ifGHEql-css"
                    >
                      <input
                        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-hAPHBc-iconTrailingVisibility-true c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-cHWLgR-cv c-exiTqG-bTMGXY-cv"
                        data-cy="add-products-table-search-bar-name"
                        placeholder="searchbarLabel"
                        value=""
                      />
                      <label
                        class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
                      >
                        searchbarLabel
                      </label>
                      <span
                        class="c-fcBbhr"
                      >
                        <span
                          class="c-gsmDXe"
                        >
                          <svg
                            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-ZiURY c-ZiURY-hfDgku-size-xs"
                            data-testid="trailing-icon"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M14.67 13.569a6.067 6.067 0 0 1-.901.9m.9-.9a6 6 0 1 0-.9.9m.9-.9l5.555 5.1a.958.958 0 1 1-1.354 1.355l-5.101-5.555"
                            />
                          </svg>
                        </span>
                      </span>
                    </div>
                  </form>
                </td>
                <td
                  class="c-doquzR"
                />
              </tr>
            </thead>
            <tbody>
              <tr
                class="c-eDGYZe c-jHpZGy"
              >
                <td
                  class="c-doquzR"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div
                      class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
                    >
                      <button
                        aria-checked="false"
                        class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                        data-state="unchecked"
                        role="checkbox"
                        type="button"
                        value="on"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <img
                      class="c-iwUXHn"
                      src="https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraMila.svg"
                      title="YaraMila"
                    />
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    YaraMila
                  </div>
                </td>
                <td
                  class="c-doquzR partners"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div
                      class="c-bfPkPS c-bfPkPS-iIYFd-css c-iXyxAo"
                    />
                  </div>
                </td>
              </tr>
              <tr
                class="c-eDGYZe c-jHpZGy"
              >
                <td
                  class="c-doquzR"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div
                      class="c-hcxFDL c-PJLV c-PJLV-hNhsYe-concept-brand"
                    >
                      <button
                        aria-checked="false"
                        class="c-ciFbLc c-ciFbLc-gsnlwY-concept-brand c-ciFbLc-ktuBcb-cv"
                        data-state="unchecked"
                        role="checkbox"
                        type="button"
                        value="on"
                      />
                    </div>
                  </div>
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  />
                </td>
                <td
                  class="c-doquzR"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    MAÍZ
                  </div>
                </td>
                <td
                  class="c-doquzR partners"
                >
                  <div
                    aria-label="label"
                    aria-required="false"
                    class="c-fixGjY"
                    dir="ltr"
                    role="radiogroup"
                    style="outline: none;"
                    tabindex="-1"
                  >
                    <div
                      class="c-bfPkPS c-bfPkPS-iIYFd-css c-iXyxAo"
                    >
                      <div
                        class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s c-diEgBM c-diEgBM-hbUyHW-state-brown"
                        data-cy="Memorandum-status"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                        >
                          Memorandum
                        </label>
                      </div>
                      <div
                        class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s c-diEgBM c-diEgBM-hbUyHW-state-brown"
                        data-cy="Ayra-status"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                        >
                          Ayra
                        </label>
                      </div>
                      <div
                        class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s c-diEgBM"
                        data-cy="MXP-status"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                        >
                          MXP
                        </label>
                      </div>
                      <div
                        class="c-cIigya c-cIigya-hYSgRq-colorType-blue c-cIigya-fYJylb-size-s c-diEgBM"
                        data-cy="AtFarm-status"
                      >
                        <label
                          class="c-jAwvtv c-jAwvtv-bAcCCY-size-xs c-jAwvtv-iPJLV-css c-fCHvHQ"
                        >
                          AtFarm
                        </label>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        class="c-fZEhOm"
      />
      <div
        class="c-dzxFCp"
      >
        <button
          class="c-hRrCwb c-hRrCwb-bhpjfB-size-n c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-bETQVM-variant-primary c-igXptd"
          data-cy="add-products-save-button"
        >
          <span
            class="c-iepcqn"
          >
            polaris.common.saveButton
          </span>
        </button>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-aria-hidden="true"
      data-radix-focus-guard=""
      style="outline: none; opacity: 0; position: fixed; pointer-events: none;"
      tabindex="0"
    />
  </body>,
  "container": <div
    aria-hidden="true"
    data-aria-hidden="true"
  >
    ,
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
