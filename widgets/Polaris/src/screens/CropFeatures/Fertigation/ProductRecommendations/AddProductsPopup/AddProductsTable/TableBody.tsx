import React, { useCallback } from 'react';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { CheckBox, RadioButton, RadioButtonGroup, Table } from '@yaradigitallabs/ahua-react';
import { flexRender } from '@tanstack/react-table';
import { StyledStack, StyledStatus, StyledTableRow } from './AddProductsTable.styled';
import {
  DynamicPRecommendationApplicationRate,
  FertigationPRecommendationGrowthPhase,
  Partner,
  ProductRecommendationRateRules,
  ProductRegion,
  Tag,
} from '@common/types';
import { validateMaxProductsSelected } from '../utils/addProductsPopup';
import { ProductsListByPhase, RenderConfig } from '../AddProductsPopup.type';
import _ from 'lodash';

export type TableBodyProps = {
  table: TanstackTable<ProductRegion>;
  partnerTags: Partner[];
  productsRegion: ProductRegion[];
  getProductFamilyUri: (productFamilyId: string) => string | React.JSX.Element | null;
  selectedProducts: ProductsListByPhase;
  selectedPhase: FertigationPRecommendationGrowthPhase;
  setSelectedProducts: React.Dispatch<React.SetStateAction<ProductsListByPhase>>;
};

export function TableBody({
  table,
  partnerTags,
  getProductFamilyUri,
  selectedProducts,
  selectedPhase,
  setSelectedProducts,
}: TableBodyProps) {
  const handleAddRemoveProducts = useCallback(
    (value: string) => {
      let updatedProductIds = [];
      let updatedSelectedProducts = { [selectedPhase.id]: [value] };

      if (!_.isEmpty(selectedProducts) && selectedProducts[selectedPhase.id]) {
        updatedProductIds = [...selectedProducts[selectedPhase.id]];
        const alreadySelected = updatedProductIds.includes(value);

        if (alreadySelected) {
          updatedProductIds = updatedProductIds.filter((product) => product !== value);
        } else if (!validateMaxProductsSelected(updatedProductIds)) {
          updatedProductIds = updatedProductIds.concat(value);
        }
        updatedSelectedProducts = {
          ...selectedProducts,
          [selectedPhase.id]: updatedProductIds,
        };
      }
      setSelectedProducts(updatedSelectedProducts);
    },
    [selectedProducts, selectedPhase],
  );

  const handleDisableCheckbox = useCallback(
    (id: string) =>
      validateMaxProductsSelected(selectedProducts[selectedPhase.id]) &&
      !selectedProducts[selectedPhase.id].includes(id),
    [selectedProducts, selectedPhase],
  );

  const handleSelectSingleProduct = useCallback(
    (value: string) => {
      setSelectedProducts({ [selectedPhase.id]: [value] });
    },
    [selectedProducts, selectedPhase],
  );

  return (
    <>
      {table.getRowModel().rows.map((row) => {
        return (
          <StyledTableRow key={row.id}>
            {row.getVisibleCells().map((cell) => {
              // Sort to have external partners be at the end of the list
              const partners = cell.row.original.tagsConfiguration.partnerTags
                ?.map((partnerTag: Tag) => {
                  const matchingPartnerTag = partnerTags.find((tag) => tag.id === partnerTag.id);
                  return {
                    name: partnerTag.name,
                    isInternal: matchingPartnerTag?.isInternal,
                  };
                })
                .sort((a, b) => Number(b?.isInternal) - Number(a?.isInternal));

              const renderConfig: RenderConfig = {
                productFamilyId: getProductFamilyUri(cell.getValue<string>()),
                partners: (
                  <StyledStack direction='horizontal' gap='$x2'>
                    {partners?.map((partner) => (
                      <StyledStatus
                        key={row.original.name + partner.name}
                        data-cy={`${partner.name}-status`}
                        size='s'
                        state={partner.isInternal ? undefined : 'brown'}
                      >
                        {partner.name}
                      </StyledStatus>
                    ))}
                  </StyledStack>
                ),
                name: row.original.localizedName ? row.original.localizedName : row.original.name,
                checkbox:
                  selectedPhase.rateRule === ProductRecommendationRateRules.FIXED ||
                  selectedPhase.dynamicApplicationRate?.dynamicApplicationRateMethod ===
                    DynamicPRecommendationApplicationRate.PRODUCT_PLANT ? (
                    <RadioButton
                      key={row.id}
                      id={row.id}
                      concept='success'
                      data-cy={`${row.id}-add-remove-radio-bitton`}
                      checked={selectedProducts[selectedPhase.id]?.includes(row.original.id)}
                      onClick={() => {
                        handleSelectSingleProduct(row.original.id);
                      }}
                    />
                  ) : (
                    <CheckBox
                      key={row.id}
                      disabled={handleDisableCheckbox(row.original.id)}
                      checked={selectedProducts[selectedPhase.id]?.includes(row.original.id)}
                      onClick={() => {
                        handleAddRemoveProducts(row.original.id);
                      }}
                    />
                  ),
              };
              return (
                <Table.Cell
                  key={`${row.id}-${cell.id}`}
                  className={cell.column.id === 'partners' ? 'partners' : ''}
                >
                  <RadioButtonGroup aria-label='label'>
                    {flexRender(renderConfig[cell.column.id], cell.getContext())}
                  </RadioButtonGroup>
                </Table.Cell>
              );
            })}
          </StyledTableRow>
        );
      })}
    </>
  );
}
