import {
  CerealsProductRecommendations,
  ProductRecommendations,
  FertigationPRecommendationGrowthPhase,
  CerealsSoilApplicationPRecommendation,
  CerealsFoliarApplicationPRecommendation,
} from '@common/types';

export const getFertigationPRecommendationConfigData = (
  growthPhase:
    | FertigationPRecommendationGrowthPhase[]
    | CerealsSoilApplicationPRecommendation[]
    | CerealsFoliarApplicationPRecommendation[],
): FertigationPRecommendationGrowthPhase[] | undefined => {
  const fertigationProductRecommendationGrowthPhaseArray = growthPhase
    .map(
      (phase) =>
        'id' in phase &&
        'phaseName' in phase &&
        'phaseImage' in phase &&
        'phaseNumber' in phase &&
        'ordinal' in phase &&
        'recommendedProducts' in phase &&
        'applicationMethod' in phase &&
        'applicationCondition' in phase &&
        'rateRule' in phase &&
        'fixedApplicationRate' in phase &&
        'dynamicApplicationRate' in phase &&
        phase,
    )
    .filter((item) => !!item);
  return fertigationProductRecommendationGrowthPhaseArray;
};

export const getFertigationProductRecommendationConfiguration = (
  productRecommendation: CerealsProductRecommendations | ProductRecommendations,
): ProductRecommendations | undefined => {
  const { soilApplications, foliarApplications } = productRecommendation.configuration.data;
  if (
    soilApplications &&
    soilApplications.length &&
    foliarApplications &&
    foliarApplications.length
  ) {
    const typedSoilApplications = getFertigationPRecommendationConfigData(soilApplications);
    const typedFoliarApplications = getFertigationPRecommendationConfigData(foliarApplications);
    return {
      ...productRecommendation,
      configuration: {
        data: {
          soilApplications: typedSoilApplications,
          foliarApplications: typedFoliarApplications,
        },
      },
    };
  }
};
