import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SelectWrapper } from '../../../../../components/Select/SelectWrapper';
import { Select } from '@yaradigitallabs/ahua-react';
import {
  FertigationPRecommendationGrowthPhase,
  ProductRecommendationRateRules,
} from '@common/types';
import { cloneDeep } from 'lodash';
import { ConfirmationDialog } from '@widgets/Polaris/src/components';

export type RateRuleSelectOptions = {
  value: ProductRecommendationRateRules;
  text: string;
};
export interface RateRuleProps {
  phase: FertigationPRecommendationGrowthPhase;
  triggerErrorState: boolean;
  onValueChanged?: (phase: FertigationPRecommendationGrowthPhase) => void;
}

export const RateRuleSelect: React.FC<RateRuleProps> = ({
  phase,
  triggerErrorState,
  onValueChanged,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations.productRecommendationsTable',
  });

  const [selectedRateRule, setSelectedRateRule] = useState<RateRuleSelectOptions>();

  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [refreshKey, setRefreshKey] = useState<number>(0);

  const newRateRuleRef = useRef<ProductRecommendationRateRules | undefined>(undefined);

  const rateRuleArray: RateRuleSelectOptions[] = [
    {
      value: ProductRecommendationRateRules.FIXED,
      text: t('fixedRateSelect'),
    },
    {
      value: ProductRecommendationRateRules.DYNAMIC,
      text: t('dynamicRateSelect'),
    },
  ];

  useEffect(() => {
    if (!phase) return;

    const newRateRule = rateRuleArray.find((rate) => rate.value === phase.rateRule);

    setSelectedRateRule(newRateRule);
  }, [phase]);

  const updateRateRule = () => {
    const { current: newRule } = newRateRuleRef;

    if (!newRule) return;

    let newPhase = cloneDeep(phase);
    newPhase = {
      ...newPhase,
      dynamicApplicationRate:
        newRule === ProductRecommendationRateRules.DYNAMIC ? phase.dynamicApplicationRate : null,
      fixedApplicationRate:
        newRule === ProductRecommendationRateRules.FIXED ? phase.fixedApplicationRate : null,
      rateRule: newRule,
      recommendedProducts:
        phase.recommendedProducts.length > 1 && newRule === ProductRecommendationRateRules.FIXED
          ? []
          : phase.recommendedProducts,
    };
    onValueChanged && onValueChanged(newPhase);
  };

  const onRateRuleChange = () => {
    const { current: newRule } = newRateRuleRef;
    if (
      phase.recommendedProducts.length > 1 &&
      newRule === ProductRecommendationRateRules.FIXED &&
      (phase.rateRule === ProductRecommendationRateRules.DYNAMIC || phase.rateRule === null)
    ) {
      return setShowDialog(true);
    } else {
      updateRateRule();
    }
  };

  return (
    <>
      <ConfirmationDialog
        open={showDialog}
        title={t('resetProductDialog.title')}
        description={t('resetProductDialog.description')}
        icon='Info'
        iconColorConcept='brand'
        okButton={t('resetProductDialog.yes')}
        cancelButton={t('resetProductDialog.cancel')}
        onOk={() => {
          updateRateRule();
          setShowDialog(false);
        }}
        onCancel={() => {
          setShowDialog(false);
          setRefreshKey((prevKey) => prevKey + 1);
        }}
      />
      <SelectWrapper dataCy='rate-rule-dropdown'>
        <Select
          key={`select${refreshKey}`}
          ariaLabel={t('selectRateRuleLabel')}
          css={{ width: '100%' }}
          value={selectedRateRule?.value}
          items={rateRuleArray || []}
          placeholder={t('selectRateRuleLabel')}
          position='popper'
          size='xs'
          onChange={(value: ProductRecommendationRateRules) => {
            if (value === phase.rateRule) return;
            newRateRuleRef.current = value;
            onRateRuleChange();
          }}
          variant={triggerErrorState ? 'error' : 'default'}
        />
      </SelectWrapper>
    </>
  );
};
