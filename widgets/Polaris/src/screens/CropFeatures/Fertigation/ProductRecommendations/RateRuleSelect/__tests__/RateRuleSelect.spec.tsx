import React from 'react';
import { render } from '@testing-library/react';
import { RateRuleSelect } from '../RateRuleSelect';
import { ProductRecommendationRateRules } from '@common/types';
import { mockSoilPhase } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

describe('DynamicMethodSelect Component', () => {
  const mockOnValueChanged = jest.fn();

  const renderComponent = (phase = mockSoilPhase) =>
    render(
      <RateRuleSelect
        phase={phase}
        onValueChanged={mockOnValueChanged}
        triggerErrorState={false}
      />,
    );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId, getByRole } = renderComponent();

    expect(getByTestId('rate-rule-dropdown')).toBeInTheDocument();
    expect(getByRole('combobox')).toHaveAttribute('aria-label', 'selectRateRuleLabel');
  });

  it('selects the correct initial rate rule based on the phase', () => {
    const phaseWithRateMethod = {
      ...mockSoilPhase,
      rateRule: ProductRecommendationRateRules.DYNAMIC,
    };

    const { getByText } = renderComponent(phaseWithRateMethod);

    // Use getByText to find the option by its displayed text
    expect(getByText('dynamicRateSelect')).toBeInTheDocument();
  });

  it('updates the selected rate method when phase changes', () => {
    const { rerender, getByText } = renderComponent();

    const updatedPhase = {
      ...mockSoilPhase,
      rateRule: ProductRecommendationRateRules.FIXED,
    };

    rerender(<RateRuleSelect phase={updatedPhase} onValueChanged={mockOnValueChanged} />);

    // Use getByText for the updated selection
    expect(getByText('fixedRateSelect')).toBeInTheDocument();
  });
});
