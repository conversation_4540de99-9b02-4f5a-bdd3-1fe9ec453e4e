import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import '@testing-library/jest-dom';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockFertigationAppProviderValue } from '@common/mocks';
import { AppCondition } from '../AppCondition';
import { mockSoilPhase } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';
import * as helpers from '../../../../shared/helpers/productRecommendationsHelpers';

const noConditionString = 'noConditionRequired';
const noConditionDisplayString = 'No condition required';
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === noConditionString) {
        return noConditionDisplayString;
      }
    },
  }),
}));

jest.mock('../../../../shared/helpers/productRecommendationsHelpers', () => {
  return {
    ...jest.requireActual('../../../../shared/helpers/productRecommendationsHelpers'),
    parseApplicationCondition: jest.fn(),
  };
});

describe('AppCondition component', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  const mockOpenConditionHandler = jest.fn();

  it('renders without crashing', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <AppCondition
          phase={mockSoilPhase}
          keyTablePrefix=''
          onOpenAppConditionBuilder={mockOpenConditionHandler}
        />
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });

  it('calls the helper method to parse application condition string', () => {
    const parseApplicationSpy = jest.spyOn(helpers, 'parseApplicationCondition');

    render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <AppCondition
          phase={mockSoilPhase}
          keyTablePrefix=''
          onOpenAppConditionBuilder={mockOpenConditionHandler}
        />
      </AppContext.Provider>,
    );

    expect(parseApplicationSpy).toHaveBeenCalled();
  });

  it('calls mock condition handler when clicked on the condition string', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <AppCondition
          phase={{ ...mockSoilPhase, applicationCondition: noConditionString }}
          keyTablePrefix=''
          onOpenAppConditionBuilder={mockOpenConditionHandler}
        />
      </AppContext.Provider>,
    );

    const condition = component.getByText(noConditionString);
    fireEvent.click(condition);
    expect(mockOpenConditionHandler).toHaveBeenCalled();
  });
});
