import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Caption } from '@yaradigitallabs/ahua-react';
import { FertigationPRecommendationGrowthPhase } from '@common/types';
import { parseApplicationCondition } from '../../../shared/helpers/productRecommendationsHelpers';
import { ApplicationConditionParameters } from '../../../shared/constants';

export interface AppConditionProps {
  phase: FertigationPRecommendationGrowthPhase;
  keyTablePrefix: string;
  onOpenAppConditionBuilder: (
    phase: FertigationPRecommendationGrowthPhase,
    displayedCondition: string,
  ) => void;
}

export const AppCondition: React.FC<AppConditionProps> = ({
  phase,
  keyTablePrefix,
  onOpenAppConditionBuilder,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: keyTablePrefix,
  });

  const parsedConditionObjectOrString = useMemo(
    () => parseApplicationCondition(phase.applicationCondition, t('noConditionRequired')),
    [phase.applicationCondition],
  );

  const displayedCondition =
    typeof parsedConditionObjectOrString === 'string'
      ? parsedConditionObjectOrString
      : phase.applicationCondition
          .replaceAll(
            ApplicationConditionParameters.SOIL_ORG_M,
            t('applicationConditionBuilder.conditionBuilder.parameter.orgM'),
          )
          .replaceAll(
            ApplicationConditionParameters.SOIL_PH,
            t('applicationConditionBuilder.conditionBuilder.parameter.soilPh'),
          )
          .replaceAll(
            ApplicationConditionParameters.WATER_PH,
            t('applicationConditionBuilder.conditionBuilder.parameter.waterPh'),
          );

  return (
    <Caption
      css={{
        color: '$blue60',
        fontWeight: '$regular',
        fontSize: '$scale3',
        cursor: 'pointer',
      }}
      onClick={() => onOpenAppConditionBuilder(phase, displayedCondition)}
    >
      {displayedCondition}
    </Caption>
  );
};
