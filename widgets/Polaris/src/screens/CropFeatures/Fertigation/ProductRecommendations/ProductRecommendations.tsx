import React from 'react';
import { useTranslation } from 'react-i18next';
import { ProductRecommendationsTable } from './ProductRecommendationsTable';
import FertigationProductRecommendationsLogic from './ProductRecommendationsLogic';
import { ProductRecommendationApplicationTypes } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { AddProductsPopup } from './AddProductsPopup';
import { ApplicationConditionBuilder } from './ApplicationConditionBuilder/ApplicationConditionBuilder';
import { BtnTypes, RadioButtonGroup } from '../../shared';

const FertigationProductRecommendations = () => {
  const keyPrefix = 'polaris.fpDetails.productRecommendations';
  const conditionBuilderPrefix = `${keyPrefix}.productRecommendationsTable.applicationConditionBuilder`;
  const { t } = useTranslation('polaris', { keyPrefix });

  const {
    selectedAppType,
    methods: { setSelectedAppType },
  } = useAppContext();

  if (!selectedAppType) setSelectedAppType(ProductRecommendationApplicationTypes.SOIL);

  const {
    soilApplications,
    foliarApplications,
    productsRegion,
    selectedProducts,
    setSelectedProducts,
    selectedPhase,
    productsFamilyData,
    partnerTags,
    showAddProductDialog,
    rateNutrientsArray,
    useCloneGrowthPhase,
    useDeleteGrowthPhase,
    useUpdateGrowthPhase,
    setShowAddProductDialog,
    handleUpdateRecommendedProducts,
    handleOpenAddProductRecommendationsPopup,
    isConditionBuilderOpen,
    applicationCondition,
    handleApplicationConditionChange,
    handleOpenAppConditionBuilder,
    handleCloseAppConditionBuilder,
    isConditionApplied,
    dynamicUnitNames,
  } = FertigationProductRecommendationsLogic();

  const onTypeChanged = (btnType: BtnTypes) => {
    if (btnType === BtnTypes.LeftType) {
      setSelectedAppType(ProductRecommendationApplicationTypes.SOIL);
    } else {
      setSelectedAppType(ProductRecommendationApplicationTypes.FOLIAR);
    }
  };

  return (
    <>
      <RadioButtonGroup
        title={t('title')}
        leftLabelText={t('soilApp')}
        rightLabelText={t('foliarApp')}
        leftDataCy='product-recommendations-soil-type-button'
        rightDataCy='product-recommendations-foliar-type-button'
        defaultType={
          selectedAppType === ProductRecommendationApplicationTypes.SOIL
            ? BtnTypes.LeftType
            : BtnTypes.RightType
        }
        onChange={(value: BtnTypes) => onTypeChanged(value)}
      />
      <ProductRecommendationsTable
        applicationData={
          selectedAppType === ProductRecommendationApplicationTypes.SOIL
            ? soilApplications
            : foliarApplications
        }
        productsRegion={productsRegion}
        rateNutrientsArray={rateNutrientsArray}
        dynamicUnitNames={dynamicUnitNames}
        useCloneGrowthPhase={useCloneGrowthPhase}
        useDeleteGrowthPhase={useDeleteGrowthPhase}
        useUpdateGrowthPhase={useUpdateGrowthPhase}
        handleOpenAddProductRecommendationsPopup={handleOpenAddProductRecommendationsPopup}
        handleUpdateRecommendedProducts={handleUpdateRecommendedProducts}
        handleOpenAppConditionBuilder={handleOpenAppConditionBuilder}
      />
      <AddProductsPopup
        productsRegion={productsRegion}
        productsFamily={productsFamilyData}
        partnerTags={partnerTags}
        selectedProducts={selectedProducts}
        selectedPhase={selectedPhase}
        showDialog={showAddProductDialog}
        setSelectedProducts={setSelectedProducts}
        setShowDialog={setShowAddProductDialog}
        handleSave={handleUpdateRecommendedProducts}
      />
      {selectedPhase && (
        <ApplicationConditionBuilder
          isOpen={isConditionBuilderOpen}
          keyPrefix={conditionBuilderPrefix}
          onClose={handleCloseAppConditionBuilder}
          condition={applicationCondition}
          onUpdateApplicationCondition={handleApplicationConditionChange}
          isConditionApplied={isConditionApplied}
        />
      )}
    </>
  );
};

export default FertigationProductRecommendations;
