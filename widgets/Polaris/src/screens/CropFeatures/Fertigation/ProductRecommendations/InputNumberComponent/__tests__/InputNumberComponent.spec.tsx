import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { InputNumberComponent } from '../InputNumberComponent';
import { FertigationPRecommendationGrowthPhase, InputFieldType } from '@common/types';
import {
  mockFoliarPhase,
  mockSoilPhase,
} from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

describe('InputNumberComponent', () => {
  const mockPhase: FertigationPRecommendationGrowthPhase = mockSoilPhase;

  const renderComponent = (
    phase: FertigationPRecommendationGrowthPhase,
    field: InputFieldType,
    disabled?: boolean,
    onValueBlur?: jest.Mock,
  ) =>
    render(
      <InputNumberComponent
        phase={phase}
        field={field}
        disabled={disabled}
        onValueBlur={onValueBlur}
        triggerErrorState={false}
      />,
    );

  it('renders the input field correctly', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockPhase, field, true);

    const inputElement = screen.getByTestId(`${field}-value-input`);
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('disabled');
  });

  it('shows correct label text from translation', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockPhase, field, true);

    const label = screen.getByLabelText('enterValueLabel');
    expect(label).toBeInTheDocument();
  });

  it('Allow changes when input is enabled', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockPhase, field, false);

    const inputElement = screen.getByTestId(`${field}-value-input`);

    fireEvent.change(inputElement, { target: { value: '123' } });

    expect(inputElement).toHaveValue('123');
  });

  it('initializes input value based on phase and field', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockFoliarPhase, field, false);

    const inputElement = screen.getByTestId(`${field}-value-input`);

    const expectedValue = '100';
    expect(inputElement).toHaveValue(expectedValue);
  });

  it('does not allow changes when input is disabled', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockPhase, field, true);

    const inputElement = screen.getByTestId(`${field}-value-input`);

    fireEvent.change(inputElement, { target: { value: '123' } });

    expect(inputElement).toHaveValue(mockPhase.fixedApplicationRate?.applicationValue);
  });

  it('formats value to two decimals', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockFoliarPhase, field, false);

    const inputElement = screen.getByTestId(`${field}-value-input`);

    fireEvent.change(inputElement, { target: { value: '123.456' } });

    expect(inputElement).toHaveValue('123.45');
  });

  it('shows the correct label based on field', () => {
    const field = InputFieldType.FixedApplicationRate;
    renderComponent(mockPhase, field, false);

    const label = screen.getByText('enterValueLabel');
    expect(label).toBeInTheDocument();
  });
});
