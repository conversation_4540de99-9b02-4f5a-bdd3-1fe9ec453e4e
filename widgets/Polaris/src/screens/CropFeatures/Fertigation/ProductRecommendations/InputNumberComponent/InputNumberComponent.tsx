import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '@yaradigitallabs/ahua-react';
import {
  DynamicApplicationRate,
  FertigationPRecommendationGrowthPhase,
  FixedApplicationRate,
  INPUT_FIELD_PATHS,
  InputFieldType,
} from '@common/types';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import { cloneDeep } from 'lodash';
import { MAX_LIMIT_NUMBER } from '../../../shared/constants';
import {
  getFertigationPRecommendationGrowthPhase,
  getDynamicApplicationRate,
  getFixedApplicationRate,
  isCorrectDynamicApplicationRateKey,
  isCorrectFixedApplicationRateKey,
  isCorrectGrowthPhaseKey,
} from './utils/InputNumberComponentUtils';
import { dynamicApplicationRateInitialState, fixedApplicationRateInitialState } from './constants';

interface ComponentProps {
  phase: FertigationPRecommendationGrowthPhase;
  field: InputFieldType;
  disabled?: boolean;
  triggerErrorState?: boolean;
  onValueBlur?: (phase: FertigationPRecommendationGrowthPhase) => void;
}

export const InputNumberComponent: React.FC<ComponentProps> = ({
  phase,
  field,
  disabled,
  triggerErrorState,
  onValueBlur,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations.productRecommendationsTable',
  });

  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    if (!phase) return;
    const value = getValueByPath(phase, INPUT_FIELD_PATHS[field]);
    setInputValue(value);
  }, [phase, field]);

  const getValueByPath = (obj: FertigationPRecommendationGrowthPhase, path: string[]): string => {
    let value: number | undefined | null;
    const [phaseKey, applicationRateKey] = path;

    switch (phaseKey) {
      case 'dynamicApplicationRate':
        if (isCorrectDynamicApplicationRateKey(applicationRateKey)) {
          const dynamicApplicationRate = obj?.[phaseKey];
          value = dynamicApplicationRate && dynamicApplicationRate[applicationRateKey];
        }
        break;
      case 'fixedApplicationRate':
        if (isCorrectFixedApplicationRateKey(applicationRateKey)) {
          const fixedApplicationRate = obj?.[phaseKey];
          value = fixedApplicationRate && fixedApplicationRate[applicationRateKey];
        }
        break;
    }

    return value !== undefined && value !== null ? String(value) : '';
  };

  const updatePhaseField = (
    phase: FertigationPRecommendationGrowthPhase,
    field: InputFieldType,
    value: number | null,
  ): FertigationPRecommendationGrowthPhase => {
    const updatedPhase = cloneDeep(phase);
    const path = INPUT_FIELD_PATHS[field];
    path.reduce<
      FertigationPRecommendationGrowthPhase | DynamicApplicationRate | FixedApplicationRate
    >((current, key, index) => {
      if (index === path.length - 1) {
        const dynamicApplicationRate = getDynamicApplicationRate(current);
        const fixedApplicationRate = getFixedApplicationRate(current);

        if (dynamicApplicationRate && isCorrectDynamicApplicationRateKey(key)) {
          dynamicApplicationRate[key] = value;
        }
        if (fixedApplicationRate && isCorrectFixedApplicationRateKey(key)) {
          fixedApplicationRate[key] = value;
        }
      } else {
        const fertigationGrowthPhase = getFertigationPRecommendationGrowthPhase(current);

        if (fertigationGrowthPhase && isCorrectGrowthPhaseKey(key)) {
          if (fertigationGrowthPhase[key]) {
            current = fertigationGrowthPhase[key];
          } else if (fertigationGrowthPhase[key] === null) {
            if (key === 'dynamicApplicationRate') {
              fertigationGrowthPhase[key] = dynamicApplicationRateInitialState;
            }
            if (key === 'fixedApplicationRate') {
              fertigationGrowthPhase[key] = fixedApplicationRateInitialState;
            }
          }
        }
      }

      return current;
    }, updatedPhase);

    return updatedPhase;
  };

  const handleValueChanged = (value: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;
    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;

    const maxTwoDecimals = formatValidDecimalsNumber(value, 2);

    setInputValue(maxTwoDecimals);
  };

  const handleValueBlur = (value: string) => {
    const isValid = validateParameterNumber(value);
    if (!isValid) return;
    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;
    const maxTwoDecimals = formatValidDecimalsNumber(value, 2);

    const updatedPhase = updatePhaseField(
      phase,
      field,
      value === '' ? null : Number(maxTwoDecimals),
    );
    const phaseValue = getValueByPath(phase, INPUT_FIELD_PATHS[field]);

    if (phaseValue !== value) onValueBlur?.(updatedPhase);
  };

  return (
    <>
      <Input
        size='xs'
        cover='outline'
        label={inputValue == '' ? t('enterValueLabel') : ''}
        value={inputValue}
        disabled={disabled}
        id={`value-input-${field}-${phase.id}`}
        onChange={({ target: { value } }) => {
          handleValueChanged(value);
        }}
        onBlur={({ target: { value } }) => {
          handleValueBlur(value);
        }}
        data-cy={`${field}-value-input`}
        variant={triggerErrorState ? 'error' : 'default'}
      />
    </>
  );
};
