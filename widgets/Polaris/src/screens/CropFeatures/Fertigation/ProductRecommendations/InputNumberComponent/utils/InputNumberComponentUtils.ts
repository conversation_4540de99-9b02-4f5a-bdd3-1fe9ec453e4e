import {
  FertigationPRecommendationGrowthPhase,
  DynamicApplicationRate,
  FixedApplicationRate,
} from '@common/types';

export const getFertigationPRecommendationGrowthPhaseKey = (
  value: string,
): keyof FertigationPRecommendationGrowthPhase | undefined => {
  if (
    value === 'id' ||
    value === 'phaseName' ||
    value === 'phaseImage' ||
    value === 'ordinal' ||
    value === 'recommendedProducts' ||
    value === 'applicationMethod' ||
    value === 'applicationCondition' ||
    value === 'rateRule' ||
    value === 'fixedApplicationRate' ||
    value === 'dynamicApplicationRate'
  ) {
    return value;
  }
};

export const getDynamicApplicationRateKey = (
  value: string,
): keyof DynamicApplicationRate | undefined => {
  if (value === 'minRate' || value === 'maxRate' || value === 'productPlant') {
    return value;
  }
};

export const getFixedApplicationRateKey = (
  value: string,
): keyof FixedApplicationRate | undefined => {
  if (value === 'applicationValue') {
    return value;
  }
};

export const getFertigationPRecommendationGrowthPhase = (
  data: FertigationPRecommendationGrowthPhase | DynamicApplicationRate | FixedApplicationRate,
): FertigationPRecommendationGrowthPhase | undefined => {
  if (
    'id' in data &&
    'phaseName' in data &&
    'phaseImage' in data &&
    'ordinal' in data &&
    'recommendedProducts' in data &&
    'applicationMethod' in data &&
    'applicationCondition' in data &&
    'rateRule' in data &&
    'fixedApplicationRate' in data &&
    'dynamicApplicationRate' in data
  ) {
    return data;
  }
};

export const getDynamicApplicationRate = (
  data: FertigationPRecommendationGrowthPhase | DynamicApplicationRate | FixedApplicationRate,
): DynamicApplicationRate | undefined => {
  if (
    'minRate' in data &&
    'maxRate' in data &&
    'rateUnits' in data &&
    'dynamicApplicationRateMethod' in data &&
    'rateDefiningNutrients' in data &&
    'productPlant' in data &&
    'productPlantUnits' in data
  ) {
    return data;
  }
};

export const getFixedApplicationRate = (
  data: FertigationPRecommendationGrowthPhase | DynamicApplicationRate | FixedApplicationRate,
): FixedApplicationRate | undefined => {
  if ('applicationValue' in data && 'applicationValueUnitsIds' in data) {
    return data;
  }
};

export const isCorrectDynamicApplicationRateKey = (key: string) =>
  key === 'maxRate' || key === 'minRate' || key === 'productPlant';

export const isCorrectFixedApplicationRateKey = (key: string) => key === 'applicationValue';

export const isCorrectGrowthPhaseKey = (key: string) =>
  key === 'dynamicApplicationRate' || key === 'fixedApplicationRate';
