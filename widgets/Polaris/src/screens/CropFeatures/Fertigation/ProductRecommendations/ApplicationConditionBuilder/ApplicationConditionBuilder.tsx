import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AdvancedDialog,
  Button,
  IconButton,
  Input,
  Label,
  RadioButton,
} from '@yaradigitallabs/ahua-react';
import { formatValidTreeDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import {
  ApplicationConditionOptions,
  ApplicationConditionBuilderProps,
  ConditionErrors,
  ApplicationConditionKeys,
} from './conditionBuilder.type';
import {
  conditionObjectInitialState,
  getLogicalOperatorItems,
  getOperatorItems,
  getParameterItems,
} from './conditionBuilder.utils';
import {
  ConditionContainer,
  ConditionWrapper,
  ParameterSelect,
  OperatorSelect,
  LogicalOperatorSelect,
  ValueInputContainer,
  ConditionRadioElementWrapper,
  DialogTitle,
  ConditionFirstSubtitle,
  ConditionSecondSubtitle,
  ConditionRadioButtonsWrapper,
} from './ApplicationConditionBuilder.styled';
import { ParsedApplicationCondition } from '../../../shared/helpers/productRecommendationsHelpers';
import { ApplicationConditionLogicalOperators, MAX_LIMIT_NUMBER } from '../../../shared/constants';
import { initialCondition } from './constants';

export const ApplicationConditionBuilder = ({
  isOpen,
  keyPrefix,
  onClose,
  condition,
  onUpdateApplicationCondition,
  isConditionApplied,
}: ApplicationConditionBuilderProps) => {
  const { t } = useTranslation('polaris', {
    keyPrefix,
  });

  const [showCondition, setShowCondition] = useState(isConditionApplied);
  const [logicalOperatorSelected, setLogicalOperatorSelected] = useState<
    ApplicationConditionLogicalOperators | ''
  >(condition?.logicalOperator || '');
  const [isConditionExpanded, setIsConditionExpanded] = useState<boolean>(
    Boolean(condition?.logicalOperator && condition?.logicalOperator.length),
  );

  const [conditionOne, setConditionOne] = useState<ParsedApplicationCondition>(
    condition?.conditions[0] || conditionObjectInitialState,
  );
  const [conditionTwo, setConditionTwo] = useState<ParsedApplicationCondition | undefined>(
    condition?.conditions[1],
  );

  const [conditionOneErrors, setConditionOneErrors] = useState<ConditionErrors>([]);
  const [conditionTwoErrors, setConditionTwoErrors] = useState<ConditionErrors>([]);

  useEffect(() => {
    if (!condition) {
      setLogicalOperatorSelected('');
      setConditionOne(conditionObjectInitialState);
      setConditionTwo(undefined);
      setIsConditionExpanded(false);
    } else {
      setLogicalOperatorSelected(condition.logicalOperator || '');
      setConditionOne(condition.conditions[0]);
      setConditionTwo(condition.conditions[1]);
      setIsConditionExpanded(
        Boolean(condition.logicalOperator && condition.logicalOperator.length),
      );
    }
  }, [condition]);

  const handleExpandShortenCondition = () => {
    // when expanding, we make sure to add the second condition initial state
    if (!isConditionExpanded) {
      setLogicalOperatorSelected(ApplicationConditionLogicalOperators.AND);
      setConditionTwo(conditionObjectInitialState);
    } else {
      setConditionTwo(undefined);
      setLogicalOperatorSelected('');
      setConditionTwoErrors([]);
    }
    setIsConditionExpanded((prev) => !prev);
  };

  const handleSelectChange = (
    val: string,
    conditionSetter:
      | Dispatch<SetStateAction<ParsedApplicationCondition>>
      | Dispatch<SetStateAction<ParsedApplicationCondition | undefined>>,
    errorsSetter: Dispatch<SetStateAction<ConditionErrors>>,
    propKey: ApplicationConditionKeys,
  ) => {
    conditionSetter((prev: ParsedApplicationCondition | undefined) => {
      if (prev) {
        return {
          ...prev,
          [propKey]: val,
        };
      }
      return {
        ...initialCondition,
        [propKey]: val,
      };
    });
    errorsSetter((prev) => (prev.length ? prev.filter((err) => err !== propKey) : []));
  };

  const handleChangeValues = (
    value: string,
    setter:
      | Dispatch<SetStateAction<ParsedApplicationCondition>>
      | Dispatch<SetStateAction<ParsedApplicationCondition | undefined>>,
    errorsSetter: Dispatch<SetStateAction<ConditionErrors>>,
  ) => {
    if (value.match(/\s/g)) return;
    const isValid = validateParameterNumber(value);
    if (!isValid) return;
    const validatedValue = formatValidTreeDecimalsNumber(value);
    const isExceedingMaxLimit = Boolean(Number(value) > MAX_LIMIT_NUMBER);
    if (isExceedingMaxLimit) return;

    setter((prev: ParsedApplicationCondition | undefined) => {
      const condition = prev ? prev : initialCondition;
      return {
        ...condition,
        value: validatedValue === '' ? '0' : validatedValue,
      };
    });
    errorsSetter((prev) =>
      prev.length ? prev.filter((err) => err !== ApplicationConditionKeys.VALUE) : [],
    );
  };

  const handleBuildApplicationCondition = () => {
    if (!showCondition) {
      onUpdateApplicationCondition(null);
      onClose();
    } else {
      // Validation
      // First part of condition
      const leftPartErrors = [];
      if (!conditionOne.parameter || conditionOne.parameter === '') {
        leftPartErrors.push(ApplicationConditionKeys.PARAMETER);
      }
      if (!conditionOne.operator || conditionOne.operator === '') {
        leftPartErrors.push(ApplicationConditionKeys.OPERATOR);
      }
      const isValueOneValid =
        conditionOne.value &&
        conditionOne.value !== '' &&
        validateParameterNumber(conditionOne.value);
      if (!isValueOneValid) {
        leftPartErrors.push(ApplicationConditionKeys.VALUE);
      }
      if (leftPartErrors.length) {
        setConditionOneErrors(leftPartErrors);
      }
      // Second part of condition
      const rightPartErrors = [];
      if (logicalOperatorSelected) {
        if (!conditionTwo?.parameter || conditionTwo.parameter === '') {
          rightPartErrors.push(ApplicationConditionKeys.PARAMETER);
        }
        if (!conditionTwo?.operator || conditionTwo.operator === '') {
          rightPartErrors.push(ApplicationConditionKeys.OPERATOR);
        }
        const isValueTwoValid =
          conditionTwo?.value &&
          conditionTwo?.value !== '' &&
          validateParameterNumber(conditionTwo.value);
        if (!isValueTwoValid) {
          rightPartErrors.push(ApplicationConditionKeys.VALUE);
        }
        if (rightPartErrors.length) {
          setConditionTwoErrors(rightPartErrors);
        }
      }

      // both parts are identical
      if (
        logicalOperatorSelected &&
        conditionOne.parameter === conditionTwo?.parameter &&
        conditionOne.operator === conditionTwo?.operator &&
        conditionOne.value === conditionTwo?.value
      ) {
        const errors = [
          ApplicationConditionKeys.OPERATOR,
          ApplicationConditionKeys.PARAMETER,
          ApplicationConditionKeys.VALUE,
        ];
        setConditionOneErrors(errors);
        setConditionTwoErrors(errors);
        return;
      }

      if ([...leftPartErrors, ...rightPartErrors].length) return;

      const newCondition = {
        logicalOperator: logicalOperatorSelected !== '' ? logicalOperatorSelected : null,
        conditions:
          logicalOperatorSelected !== '' && conditionTwo
            ? [conditionOne, conditionTwo]
            : [conditionOne],
      };

      onUpdateApplicationCondition(newCondition);
      onClose();
    }
  };

  const parameterItems = useMemo(() => getParameterItems(t), []);
  const operatorItems = useMemo(() => getOperatorItems(t), []);
  const logicalOperatorItems = useMemo(() => getLogicalOperatorItems(t), []);

  return (
    <AdvancedDialog open={isOpen} onOpenChange={onClose}>
      <AdvancedDialog.Content css={{ width: '1080px' }}>
        <AdvancedDialog.Start css={{ height: '50px' }}>
          <DialogTitle>{t('title')}</DialogTitle>
          <AdvancedDialog.Close>
            <IconButton
              data-cy='application-condition-popup-close-btn'
              icon='Close'
              colorConcept='brand'
              size='s'
            />
          </AdvancedDialog.Close>
        </AdvancedDialog.Start>

        <AdvancedDialog.Middle css={{ paddingTop: '$x6', gap: '$x4' }}>
          <ConditionFirstSubtitle size='s'>{t('shouldBeApplied.title')}</ConditionFirstSubtitle>

          <ConditionRadioButtonsWrapper
            value={
              showCondition
                ? ApplicationConditionOptions.ADD_CONDITION
                : ApplicationConditionOptions.NO_CONDITION
            }
          >
            <ConditionRadioElementWrapper
              key='add-condition'
              checked={showCondition}
              onClick={() => setShowCondition(true)}
              data-cy='application-condition-builder-add-condition-btn'
            >
              <Label htmlFor='addCondition' size='s'>
                {t('shouldBeApplied.yes')}
              </Label>
              <RadioButton value={ApplicationConditionOptions.ADD_CONDITION} id='addCondition' />
            </ConditionRadioElementWrapper>
            <ConditionRadioElementWrapper
              key='no-condition'
              checked={!showCondition}
              onClick={() => setShowCondition(false)}
              data-cy='application-condition-builder-no-condition-btn'
            >
              <Label htmlFor='noCondition' size='s'>
                {t('shouldBeApplied.no')}
              </Label>
              <RadioButton value={ApplicationConditionOptions.NO_CONDITION} id='noCondition' />
            </ConditionRadioElementWrapper>
          </ConditionRadioButtonsWrapper>

          {showCondition && (
            <>
              <ConditionSecondSubtitle size='s'>
                {t('conditionBuilder.title')}
              </ConditionSecondSubtitle>

              <ConditionWrapper data-cy='application-condition-builder-condition-wrapper'>
                <ConditionContainer>
                  <ParameterSelect
                    ariaLabel={t('conditionBuilder.parameter.ariaLabel')}
                    label={t('conditionBuilder.parameter.label')}
                    size='s'
                    items={parameterItems}
                    value={conditionOne.parameter}
                    onChange={(value) =>
                      handleSelectChange(
                        value,
                        setConditionOne,
                        setConditionOneErrors,
                        ApplicationConditionKeys.PARAMETER,
                      )
                    }
                    position='popper'
                    variant={
                      conditionOneErrors.includes(ApplicationConditionKeys.PARAMETER)
                        ? 'error'
                        : 'default'
                    }
                    onBlur={null} // Ahua Select component requires these props for some reason
                    onFocus={null}
                  />
                  <OperatorSelect
                    ariaLabel={t('conditionBuilder.operator.ariaLabel')}
                    size='s'
                    items={operatorItems}
                    value={conditionOne.operator}
                    onChange={(value) =>
                      handleSelectChange(
                        value,
                        setConditionOne,
                        setConditionOneErrors,
                        ApplicationConditionKeys.OPERATOR,
                      )
                    }
                    position='popper'
                    variant={
                      conditionOneErrors.includes(ApplicationConditionKeys.OPERATOR)
                        ? 'error'
                        : 'default'
                    }
                    onBlur={null}
                    onFocus={null}
                  />
                  <ValueInputContainer>
                    <Input
                      aria-label={t('conditionBuilder.value.ariaLabel')}
                      name={t('conditionBuilder.value.name')}
                      label={
                        !conditionOne.value || conditionOne.value === ''
                          ? t('conditionBuilder.value.label')
                          : undefined
                      }
                      size='s'
                      value={conditionOne.value}
                      onChange={({ target: { value } }) =>
                        handleChangeValues(value, setConditionOne, setConditionOneErrors)
                      }
                      variant={
                        conditionOneErrors.includes(ApplicationConditionKeys.VALUE)
                          ? 'error'
                          : 'default'
                      }
                      type='text'
                    />
                  </ValueInputContainer>
                </ConditionContainer>

                {isConditionExpanded && (
                  <LogicalOperatorSelect
                    ariaLabel={t('conditionBuilder.logicalOperator.ariaLabel')}
                    size='s'
                    items={logicalOperatorItems}
                    value={logicalOperatorSelected}
                    onChange={(value) => {
                      if (
                        value === ApplicationConditionLogicalOperators.AND ||
                        value === ApplicationConditionLogicalOperators.OR
                      ) {
                        setLogicalOperatorSelected(value);
                      }
                    }}
                    position='popper'
                    onBlur={null}
                    onFocus={null}
                  />
                )}

                {isConditionExpanded && conditionTwo && (
                  <ConditionContainer>
                    <ParameterSelect
                      ariaLabel={t('conditionBuilder.parameter.ariaLabel')}
                      label={t('conditionBuilder.parameter.label')}
                      size='s'
                      items={parameterItems}
                      value={conditionTwo.parameter}
                      onChange={(value) =>
                        handleSelectChange(
                          value,
                          setConditionTwo,
                          setConditionTwoErrors,
                          ApplicationConditionKeys.PARAMETER,
                        )
                      }
                      position='popper'
                      variant={
                        conditionTwoErrors.includes(ApplicationConditionKeys.PARAMETER)
                          ? 'error'
                          : 'default'
                      }
                      onBlur={null}
                      onFocus={null}
                    />
                    <OperatorSelect
                      ariaLabel={t('conditionBuilder.operator.ariaLabel')}
                      size='s'
                      items={operatorItems}
                      value={conditionTwo.operator}
                      onChange={(value) =>
                        handleSelectChange(
                          value,
                          setConditionTwo,
                          setConditionTwoErrors,
                          ApplicationConditionKeys.OPERATOR,
                        )
                      }
                      position='popper'
                      variant={
                        conditionTwoErrors.includes(ApplicationConditionKeys.OPERATOR)
                          ? 'error'
                          : 'default'
                      }
                      onBlur={null}
                      onFocus={null}
                    />
                    <ValueInputContainer>
                      <Input
                        aria-label={t('conditionBuilder.value.ariaLabel')}
                        name={t('conditionBuilder.value.name')}
                        label={
                          !conditionTwo.value || conditionTwo.value === ''
                            ? t('conditionBuilder.value.label')
                            : undefined
                        }
                        size='s'
                        value={conditionTwo.value}
                        onChange={({ target: { value } }) =>
                          handleChangeValues(value, setConditionTwo, setConditionTwoErrors)
                        }
                        variant={
                          conditionTwoErrors.includes(ApplicationConditionKeys.VALUE)
                            ? 'error'
                            : 'default'
                        }
                        type='text'
                      />
                    </ValueInputContainer>
                  </ConditionContainer>
                )}

                <Button
                  data-cy='application-condition-builder-expand-collapse-button'
                  css={{ fontSize: '$scale4' }}
                  colorConcept='inverse'
                  size='s'
                  variant='primary'
                  iconLeading={isConditionExpanded ? 'Minus' : 'Plus'}
                  onClick={handleExpandShortenCondition}
                >
                  {isConditionExpanded
                    ? t('conditionBuilder.shorten')
                    : t('conditionBuilder.expand')}
                </Button>
              </ConditionWrapper>
            </>
          )}
        </AdvancedDialog.Middle>

        <AdvancedDialog.End css={{ padding: '10px $x4' }}>
          <Button
            size='s'
            onClick={handleBuildApplicationCondition}
            data-cy='save-application-condition-btn'
          >
            {t('saveBtn')}
          </Button>
        </AdvancedDialog.End>
      </AdvancedDialog.Content>
    </AdvancedDialog>
  );
};
