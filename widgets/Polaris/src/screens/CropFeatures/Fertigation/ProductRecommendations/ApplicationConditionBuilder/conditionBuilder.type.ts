import { ParsedApplicationConditionsResult } from '../../helpers';

export enum ApplicationConditionOptions {
  ADD_CONDITION = 'ADD_CONDITION',
  NO_CONDITION = 'NO_CONDITION',
}

export enum ApplicationConditionKeys {
  PARAMETER = 'parameter',
  OPERATOR = 'operator',
  VALUE = 'value',
}

export type ConditionErrors = (ApplicationConditionKeys | undefined)[];

export interface ApplicationConditionBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  condition: ParsedApplicationConditionsResult | null;
  onUpdateApplicationCondition: (
    parsedApplicationCondition: ParsedApplicationConditionsResult | null,
  ) => void;
  keyPrefix: string;
  isConditionApplied: boolean;
}
