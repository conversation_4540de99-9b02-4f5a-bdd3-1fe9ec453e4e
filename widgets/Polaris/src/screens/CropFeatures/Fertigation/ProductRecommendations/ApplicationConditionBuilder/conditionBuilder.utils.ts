import { TFunction } from 'react-i18next';
import {
  ApplicationConditionLogicalOperators,
  ApplicationConditionOperators,
  ApplicationConditionParameters,
} from '../../../shared/constants';
import { ParsedApplicationCondition } from '../../../shared/helpers/productRecommendationsHelpers';

export const getParameterItems = (tFunc: TFunction) => [
  {
    text: tFunc('conditionBuilder.parameter.orgM'),
    value: ApplicationConditionParameters.SOIL_ORG_M,
  },
  {
    text: tFunc('conditionBuilder.parameter.soilPh'),
    value: ApplicationConditionParameters.SOIL_PH,
  },
  {
    text: tFunc('conditionBuilder.parameter.waterPh'),
    value: ApplicationConditionParameters.WATER_PH,
  },
];

export const getOperatorItems = (tFunc: TFunction) => [
  {
    text: tFunc('conditionBuilder.operator.greaterThan'),
    value: ApplicationConditionOperators.GREATER_THAN,
  },
  {
    text: tFunc('conditionBuilder.operator.lowerThan'),
    value: ApplicationConditionOperators.LOWER_THAN,
  },
  {
    text: tFunc('conditionBuilder.operator.greaterOrEqual'),
    value: ApplicationConditionOperators.GREATER_OR_EQUAL,
  },
  {
    text: tFunc('conditionBuilder.operator.lowerOrEqual'),
    value: ApplicationConditionOperators.LOWER_OR_EQUAL,
  },
];

export const getLogicalOperatorItems = (tFunc: TFunction) => [
  {
    text: tFunc('conditionBuilder.logicalOperator.and'),
    value: ApplicationConditionLogicalOperators.AND,
  },
  {
    text: tFunc('conditionBuilder.logicalOperator.or'),
    value: ApplicationConditionLogicalOperators.OR,
  },
];

export const conditionObjectInitialState: ParsedApplicationCondition = {
  parameter: '',
  operator: ApplicationConditionOperators.GREATER_THAN,
  value: '',
};
