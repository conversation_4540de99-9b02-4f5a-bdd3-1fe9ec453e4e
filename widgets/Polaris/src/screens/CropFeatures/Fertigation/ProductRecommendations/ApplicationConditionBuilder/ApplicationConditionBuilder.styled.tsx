import { RadioElementWrapper } from '@widgets/Polaris/src/components';
import {
  AdvancedDialog,
  RadioButtonGroup,
  Select,
  styled,
  Subtitle,
} from '@yaradigitallabs/ahua-react';

export const DialogTitle = styled(AdvancedDialog.Title, {
  fontWeight: '$black',
  lineHeight: '$scale4',
});

export const ConditionFirstSubtitle = styled(Subtitle, {
  lineHeight: '$scale5',
  fontWeight: '$bold',
});

export const ConditionRadioButtonsWrapper = styled(RadioButtonGroup, {
  flexDirection: 'row',
  gap: '$x4',
});

export const ConditionRadioElementWrapper = styled(RadioElementWrapper, {
  flexBasis: '264px',
  variants: {
    checked: {
      false: {
        '& label': {
          color: '$black100',
        },
      },
    },
  },
  '& label': {
    fontSize: '$scale3',
  },
});

export const ConditionSecondSubtitle = styled(Subtitle, {
  marginTop: '$x5',
  fontWeight: '$semiBold',
  lineHeight: '$scale4',
});

export const ConditionWrapper = styled('div', {
  display: 'flex',
  gap: '$x4',
  alignItems: 'center',
});

export const ConditionContainer = styled('div', {
  display: 'flex',
  gap: '$x4',
});

export const ParameterSelect = styled(Select, {
  width: '138px',
  '& button': {
    width: '138px',
  },
});

export const OperatorSelect = styled(Select, {
  width: '86px',
  '& button': {
    width: '86px',
  },
});

export const LogicalOperatorSelect = styled(Select, {
  width: '96px',
  marginLeft: '$x2',
  marginRight: '$x2',
});

export const ValueInputContainer = styled('div', {
  width: 'fit-content',
  '& input': { width: '86px', height: '50px' },
  '&& input + label': {
    color: '$neutral-contrast',
  },
});
