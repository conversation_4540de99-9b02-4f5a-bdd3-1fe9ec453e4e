import React from 'react';
import { act, fireEvent, render, screen } from '@testing-library/react';
import { within } from '@testing-library/dom';
import { ApplicationConditionBuilder } from '../ApplicationConditionBuilder';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockFertigationAppProviderValue } from '@common/mocks';
import userEvent from '@testing-library/user-event';
import * as paramUtils from '@widgets/Polaris/utils/parameterUtils/parameterUtils';
import {
  ApplicationConditionOperators,
  ApplicationConditionParameters,
} from '../../../../shared/constants';
describe('ApplicationConditionBuilder component', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  const mockUpdateApplicationCondition = jest.fn();
  const mockCloseConditionBuilder = jest.fn();

  const user = userEvent.setup();

  const renderComponent = () =>
    render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <ApplicationConditionBuilder
          isOpen={true}
          onClose={mockCloseConditionBuilder}
          condition={null}
          onUpdateApplicationCondition={mockUpdateApplicationCondition}
          keyPrefix=''
          isConditionApplied={false}
        />
      </AppContext.Provider>,
    );

  const showConditionWrapper = () => {
    const addConditionButton = screen.getByTestId(
      'application-condition-builder-add-condition-btn',
    );

    fireEvent.click(addConditionButton);

    const conditionWrapper = screen.getByTestId('application-condition-builder-condition-wrapper');
    expect(conditionWrapper).toBeInTheDocument();

    return conditionWrapper;
  };

  const expandCollapseCondition = () => {
    const conditionWrapper = screen.getByTestId('application-condition-builder-condition-wrapper');
    const expandCollapseButton = within(conditionWrapper).getByTestId(
      'application-condition-builder-expand-collapse-button',
    );
    fireEvent.click(expandCollapseButton);
  };

  it('renders without crashing', () => {
    const { container } = renderComponent();
    expect(container).toMatchSnapshot();
  });

  it('renders two radio elements with the second one (NO_CONDITION) selected by default', () => {
    const { getAllByRole } = renderComponent();
    const radios = getAllByRole('radio');

    expect(radios.length).toBe(2);

    expect(radios[0]).toHaveAttribute('data-state', 'unchecked');
    expect(radios[1]).toHaveAttribute('value', 'NO_CONDITION');
    expect(radios[1]).toHaveAttribute('data-state', 'checked');
  });

  it('shows condition builder when ADD_CONDITION is checked', () => {
    const { getByText } = renderComponent();

    showConditionWrapper();

    expect(getByText('conditionBuilder.title')).toBeInTheDocument();
  });

  it('shows two selects, one text input, and Expand button inside the condition builder when ADD_CONDITION is checked', () => {
    renderComponent();

    const conditionWrapper = showConditionWrapper();

    const selects = within(conditionWrapper).getAllByRole('combobox');
    const input = within(conditionWrapper).getAllByRole('textbox');
    const expandCollapseButton = within(conditionWrapper).getByTestId(
      'application-condition-builder-expand-collapse-button',
    );
    expect(selects).toHaveLength(2);
    expect(input).toHaveLength(1);
    expect(expandCollapseButton).toBeInTheDocument();
    expect(expandCollapseButton).toHaveTextContent('conditionBuilder.expand');
  });

  it('shows logical operator select inside the condition builder when the condition is expanded', () => {
    const { getByLabelText } = renderComponent();

    showConditionWrapper();

    expandCollapseCondition();

    const logicalOperatorSelect = getByLabelText('conditionBuilder.logicalOperator.ariaLabel');
    expect(logicalOperatorSelect).toBeInTheDocument();
  });

  it('shows 5 selects, 2 text inputs, and Collapse button inside the condition builder when the condition is expanded', () => {
    const { getByTestId } = renderComponent();

    const conditionWrapper = showConditionWrapper();

    expandCollapseCondition();

    const selects = within(conditionWrapper).getAllByRole('combobox');
    const input = within(conditionWrapper).getAllByRole('textbox');
    expect(selects).toHaveLength(5);
    expect(input).toHaveLength(2);
    expect(getByTestId('application-condition-builder-expand-collapse-button')).toHaveTextContent(
      'conditionBuilder.shorten',
    );
  });

  it('hides second part of the condition when clicked on Shorten', () => {
    const { getByLabelText } = renderComponent();

    showConditionWrapper();

    expandCollapseCondition();

    const logicalOperatorSelect = getByLabelText('conditionBuilder.logicalOperator.ariaLabel');
    expect(logicalOperatorSelect).toBeInTheDocument();

    expandCollapseCondition();

    expect(logicalOperatorSelect).not.toBeInTheDocument();
  });

  it('allows updating text input value and hides value label', async () => {
    const { queryByText } = renderComponent();

    const conditionWrapper = showConditionWrapper();

    const input = within(conditionWrapper).getByRole('textbox', {
      name: 'conditionBuilder.value.ariaLabel',
    });
    expect(queryByText('conditionBuilder.value.label')).toBeInTheDocument();

    await user.type(input, '5');
    expect(input).toHaveValue('5');

    expect(queryByText('conditionBuilder.value.label')).not.toBeInTheDocument();
  });

  it('calls validation helpers when changing the input value', async () => {
    const validateParameterSpy = jest
      .spyOn(paramUtils, 'validateParameterNumber')
      .mockImplementation(jest.fn().mockReturnValue(true));
    const validateThreeDecimalsSpy = jest
      .spyOn(paramUtils, 'formatValidTreeDecimalsNumber')
      .mockImplementation(jest.fn().mockReturnValue('5'));

    renderComponent();

    const conditionWrapper = showConditionWrapper();

    const input = within(conditionWrapper).getByRole('textbox', {
      name: 'conditionBuilder.value.ariaLabel',
    });

    await user.type(input, '5');

    expect(validateParameterSpy).toHaveBeenCalled();
    expect(validateThreeDecimalsSpy).toHaveBeenCalled();
    expect(input).toHaveValue('5');
  });

  it('does not call onUpdateApplicationCondition when the user clicks on the Save button with condition enabled and only value provided', async () => {
    const { getByTestId } = renderComponent();

    const conditionWrapper = showConditionWrapper();

    const input = within(conditionWrapper).getByRole('textbox', {
      name: 'conditionBuilder.value.ariaLabel',
    });

    await user.type(input, '5');
    expect(input).toHaveValue('5');

    const saveBtn = getByTestId('save-application-condition-btn');
    expect(saveBtn).toBeInTheDocument();
    fireEvent.click(saveBtn);

    expect(mockUpdateApplicationCondition).not.toHaveBeenCalled();
  });

  it('does not call onUpdateApplicationCondition when the two parts of the condition are identical', async () => {
    const { debug, getByTestId } = renderComponent();

    const conditionWrapper = showConditionWrapper();
    expandCollapseCondition();

    const inputs = within(conditionWrapper).getAllByRole('textbox', {
      name: 'conditionBuilder.value.ariaLabel',
    });
    const parameterSelects = within(conditionWrapper).getAllByRole('combobox', {
      name: 'conditionBuilder.parameter.ariaLabel',
    });
    const operatorSelects = within(conditionWrapper).getAllByRole('combobox', {
      name: 'conditionBuilder.operator.ariaLabel',
    });

    await act(async () => {
      for (const input of inputs) {
        await user.type(input, '5');
      }
      for (const paramSelect of parameterSelects) {
        fireEvent.change(paramSelect, {
          target: { value: ApplicationConditionParameters.SOIL_ORG_M },
        });
      }
      for (const operatorSelect of operatorSelects) {
        fireEvent.change(operatorSelect, {
          target: { value: ApplicationConditionOperators.LOWER_OR_EQUAL },
        });
      }
    });

    debug(conditionWrapper);
    const saveBtn = getByTestId('save-application-condition-btn');
    expect(saveBtn).toBeInTheDocument();
    fireEvent.click(saveBtn);

    expect(mockUpdateApplicationCondition).not.toHaveBeenCalled();
  });

  it('calls onUpdateApplicationCondition when the user clicks on the Save button', () => {
    const { getByTestId } = renderComponent();

    const saveBtn = getByTestId('save-application-condition-btn');
    expect(saveBtn).toBeInTheDocument();
    fireEvent.click(saveBtn);
    expect(mockUpdateApplicationCondition).toHaveBeenCalledTimes(1);
  });

  it('renders an input with the missing value with error variant when save attempt is made', () => {
    jest
      .spyOn(paramUtils, 'validateParameterNumber')
      .mockImplementation(jest.fn().mockReturnValue(false));
    const { getByTestId } = renderComponent();

    const conditionWrapper = showConditionWrapper();

    const input = within(conditionWrapper).getByRole('textbox', {
      name: 'conditionBuilder.value.ariaLabel',
    });
    expect(input.className.includes('variant-default')).toBe(true);

    const saveBtn = getByTestId('save-application-condition-btn');
    expect(saveBtn).toBeInTheDocument();
    fireEvent.click(saveBtn);

    expect(input.className.includes('variant-error')).toBe(true);
  });

  it('calls onClose when the user closes the dialog', () => {
    const { getByTestId } = renderComponent();

    const closeBtn = getByTestId('application-condition-popup-close-btn');
    expect(closeBtn).toBeInTheDocument();
    fireEvent.click(closeBtn);
    expect(mockCloseConditionBuilder).toHaveBeenCalled();
  });
});
