import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SelectWrapper } from '../../../../../components/Select/SelectWrapper';
import {
  FertigationPRecommendationGrowthPhase,
  ProductRecommendationSoilApplicationMethods,
} from '@common/types';
import { SelectOption } from '@widgets/Polaris/src/types';
import { cloneDeep } from 'lodash';
import { StyledSelect } from '../ProductRecommendations.styled';
import { getProductRecommendationSoilApplicationMethod } from './utils/AppMethodSelectUtil';

export interface AppMethodSelectProps {
  phase: FertigationPRecommendationGrowthPhase;
  triggerErrorState: boolean;
  onValueChanged: (phase: FertigationPRecommendationGrowthPhase) => void;
}

export const AppMethodSelect: React.FC<AppMethodSelectProps> = ({
  phase,
  triggerErrorState,
  onValueChanged,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations.productRecommendationsTable',
  });
  const [selectedMethod, setSelectedMethod] = useState<SelectOption<string> | undefined>();

  const { BASE_DRESSING, SIDE_DRESSING, LIQUID, DRENCH, FERTIGATION } =
    ProductRecommendationSoilApplicationMethods;
  const appMethods = [
    { value: BASE_DRESSING, text: t('baseDressing') },
    { value: SIDE_DRESSING, text: t('sideDressing') },
    { value: LIQUID, text: t('liquid') },
    { value: DRENCH, text: t('drench') },
    { value: FERTIGATION, text: t('fertigation') },
  ];

  useEffect(() => {
    if (!phase) return;

    const newMethod = appMethods.find((method) => method.value === phase.applicationMethod);

    setSelectedMethod(newMethod);
  }, [phase]);

  const onAppMethodChange = (value: string) => {
    const soilApplicationMethod = getProductRecommendationSoilApplicationMethod(value);
    const newMethod = appMethods.find((method) => method.value === value);

    newMethod && setSelectedMethod(newMethod);
    let newPhase = cloneDeep(phase);

    if (soilApplicationMethod) {
      newPhase = {
        ...newPhase,
        applicationMethod: soilApplicationMethod,
      };
      onValueChanged && onValueChanged(newPhase);
    }
  };

  return (
    <SelectWrapper dataCy='app-method-dropdown'>
      <StyledSelect
        ariaLabel={t('selectAppMethodLabel')}
        value={selectedMethod?.value}
        items={appMethods || []}
        placeholder={t('selectAppMethodLabel')}
        position='popper'
        size='xs'
        onChange={(value) => onAppMethodChange(value)}
        variant={triggerErrorState ? 'error' : 'default'}
      />
    </SelectWrapper>
  );
};
