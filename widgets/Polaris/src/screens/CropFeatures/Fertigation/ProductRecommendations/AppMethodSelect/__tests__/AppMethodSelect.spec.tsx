import React from 'react';
import { render } from '@testing-library/react';
import { AppMethodSelect } from '../AppMethodSelect';
import { ProductRecommendationSoilApplicationMethods } from '@common/types';
import { mockSoilPhase } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

describe('AppMethodSelect Component', () => {
  const mockOnValueChanged = jest.fn();

  const renderComponent = (phase = mockSoilPhase) =>
    render(
      <AppMethodSelect
        phase={phase}
        onValueChanged={mockOnValueChanged}
        triggerErrorState={false}
      />,
    );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId, getByRole } = renderComponent();

    const dropdown = getByTestId('app-method-dropdown');

    expect(dropdown).toBeInTheDocument();
    expect(getByRole('combobox')).toHaveAttribute('aria-label', 'selectAppMethodLabel');
  });

  it('updates the selected method when phase changes', () => {
    const { rerender, getByTestId } = renderComponent();

    const updatedPhase = {
      ...mockSoilPhase,
      applicationMethod: ProductRecommendationSoilApplicationMethods.LIQUID,
    };

    rerender(
      <AppMethodSelect
        phase={updatedPhase}
        onValueChanged={mockOnValueChanged}
        triggerErrorState={false}
      />,
    );

    const dropdown = getByTestId('app-method-dropdown');
    const options = dropdown.querySelectorAll('label');
    const getSelectedFeature = () =>
      Array.from(options).find((option) => option.textContent === 'liquid');

    const selectedFeature = getSelectedFeature();
    expect(selectedFeature).toBeDefined();
  });
});
