import { ProductRecommendationSoilApplicationMethods } from '@common/types';

export const getProductRecommendationSoilApplicationMethod = (
  value: string,
): ProductRecommendationSoilApplicationMethods | undefined => {
  if (
    value === ProductRecommendationSoilApplicationMethods.BASE_DRESSING ||
    value === ProductRecommendationSoilApplicationMethods.DRENCH ||
    value === ProductRecommendationSoilApplicationMethods.FERTIGATION ||
    value === ProductRecommendationSoilApplicationMethods.LIQUID ||
    value === ProductRecommendationSoilApplicationMethods.SIDE_DRESSING
  ) {
    return value;
  }
};
