import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  useCloneGrowthPhaseConfiguration,
  useDeleteGrowthPhaseConfiguration,
  useFetchProductRecommendations,
  useUpdateGrowthPhaseConfiguration,
  filterUnitsById,
  useCropSettings,
  useFetchNutrientsFromPolaris,
  useFetchProductFamilyDetails,
  usePartnersService,
  useProductsRegion,
} from '@polaris-hooks/index';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import { cloneDeep, sortBy } from 'lodash';
import { METHOD, PRODUCTS_TYPE } from '@common/constants';
import {
  ComboBoxOption,
  FeatureConfigOptions,
  GrowthPhaseType,
  GrowthPhaseTypeOptions,
  Partner,
  ProductRegion,
  FertigationPRecommendationGrowthPhase,
  ProductRecommendationApplicationTypes,
  ProductRecommendationFoliarApplicationMethods,
  ProductRecommendationRateRules,
  ProductRecommendationDynamicUnitNames,
  ProductRecommendations,
} from '@common/types';

import { ProductsListByPhase } from './AddProductsPopup/AddProductsPopup.type';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  buildConditionText,
  parseApplicationCondition,
  ParsedApplicationConditionsResult,
} from '../../shared/helpers/productRecommendationsHelpers';
import { FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS } from '../../shared/constants';
import { getFertigationProductRecommendationConfiguration } from './utils/ProductRecommendationsUtils';

const FertigationProductRecommendationsLogic = () => {
  const { t, i18n } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations',
  });
  const commonT = (key: string) => i18n.t(`polaris.common.${key}`);
  const { setDisplaySnackbar } = useSnackbar();

  const {
    selectedCountry,
    cropRegion,
    selectedAppType,
    selectedFeature,
    selectedFeatureUnitSettings,
    selectedCountryUnits,
    methods: { setSelectedFeatureUnitSettings },
  } = useAppContext();

  const { SOIL: soilType, FOLIAR: foliarType } = ProductRecommendationApplicationTypes;

  const productRegionFilters: GenericFilter[] | null = useMemo(() => {
    return selectedCountry?.id && selectedFeature?.id
      ? [
          {
            key: 'countryId',
            value: selectedCountry.id,
            type: FilterType.EQ,
          },
          {
            key: 'productTypeId',
            value: `${PRODUCTS_TYPE.LIQUID},${PRODUCTS_TYPE.SOLID},${PRODUCTS_TYPE.FOLIAR}`,
            type: FilterType.IN,
          },
          {
            key: 'tagsConfiguration.featureTags.id',
            value: selectedFeature.id,
            type: FilterType.EQ,
          },
        ]
      : null;
  }, [selectedCountry?.id, selectedFeature?.id]);

  const locationFilters: GenericFilter[] | undefined = useMemo(() => {
    if (selectedCountry?.id && cropRegion?.id)
      return [
        {
          key: 'countryId',
          value: selectedCountry.id,
          type: FilterType.EQ,
        },
        {
          key: 'cropRegionId',
          value: cropRegion.id,
          type: FilterType.EQ,
        },
      ];
  }, [selectedCountry?.id, cropRegion?.id]);

  const [showAddProductDialog, setShowAddProductDialog] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<ProductsListByPhase>({});
  const [selectedPhase, setSelectedPhase] = useState<FertigationPRecommendationGrowthPhase>();
  const [productsRegion, setProductsRegion] = useState<ProductRegion[]>([]);
  const [partnerTags, setPartnerTags] = useState<Partner[]>([]);

  const [soilApplications, setSoilApplications] = useState<
    FertigationPRecommendationGrowthPhase[] | []
  >([]);
  const [foliarApplications, setFoliarApplications] = useState<
    FertigationPRecommendationGrowthPhase[] | []
  >([]);
  const [productRecommendations, setProductRecommendations] = useState<ProductRecommendations>();
  const [selectedApps, setSelectedApps] = useState<FertigationPRecommendationGrowthPhase[] | []>(
    [],
  );

  const [isConditionApplied, setIsConditionApplied] = useState<boolean>(false);
  const [isConditionBuilderOpen, setIsConditionBuilderOpen] = useState(false);
  const [applicationCondition, setApplicationCondition] =
    useState<ParsedApplicationConditionsResult | null>(null);

  const [dynamicUnitNames, setDynamicUnitNames] = useState<ProductRecommendationDynamicUnitNames>({
    recommendedSolidUnitPerArea: '',
    recommendedLiquidsUnitPerArea: '',
    recommendedSolidsUnitPerPlant: '',
    recommendedLiquidsUnitPerPlant: '',
  });

  const applicationTypeToGrowthPhaseMap: Record<
    ProductRecommendationApplicationTypes,
    GrowthPhaseType
  > = {
    [soilType]: GrowthPhaseTypeOptions.SOIL,
    [foliarType]: GrowthPhaseTypeOptions.FOLIAR,
  };

  const { trigger: triggerGetPartnerTags } = usePartnersService();

  const { trigger: fetchProductsRegion } = useProductsRegion();

  const { trigger: fetchUnitSettings } = useCropSettings(FeatureConfigOptions.FERTIGATION);

  const productsFamilyData = useFetchProductFamilyDetails(productsRegion);

  const { data: productRecommendationData, trigger: fetchProductRecommendations } =
    useFetchProductRecommendations<FeatureConfigOptions.FERTIGATION>(
      FeatureConfigOptions.FERTIGATION,
      locationFilters,
      Boolean(selectedCountry?.id && cropRegion?.id),
    );

  const appType = useMemo(() => {
    return selectedAppType ?? soilType;
  }, [selectedAppType]);

  useEffect(() => {
    setSelectedApps(selectedAppType === soilType ? soilApplications : foliarApplications);
  }, [appType, selectedAppType]);

  const { trigger: triggerCloneConfiguration } = useCloneGrowthPhaseConfiguration();

  const { trigger: triggerDeleteConfiguration } = useDeleteGrowthPhaseConfiguration();

  const { trigger: triggerUpdateConfiguration } = useUpdateGrowthPhaseConfiguration();

  const nutrientsData = useFetchNutrientsFromPolaris([
    {
      key: 'id',
      value: FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.join(','),
      type: FilterType.IN,
    },
  ]);
  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);

  const stateRef = useRef({
    selectedApps,
    productRecommendations,
    soilApplications,
    appType,
    selectedPhase,
  });

  useEffect(() => {
    stateRef.current = {
      selectedApps,
      productRecommendations,
      soilApplications,
      appType,
      selectedPhase,
    };
  }, [selectedApps, productRecommendations, soilApplications, selectedPhase]);

  const rateNutrientsArray: ComboBoxOption[] = useMemo(() => {
    if (!sortedNutrients) return [];
    const nutrientOptions: ComboBoxOption[] =
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.reduce<ComboBoxOption[]>((acc, id) => {
        const nutrient = sortedNutrients.find((item) => item.id === id);
        if (nutrient) {
          acc.push({ label: nutrient.elementalName, value: nutrient.id });
        }
        return acc;
      }, []);
    return nutrientOptions;
  }, [sortedNutrients.length]);

  const sortGrowthPhases = (array: FertigationPRecommendationGrowthPhase[] | []) =>
    array.sort((a, b) => a.ordinal - b.ordinal);

  // Application condition is parsed into an object if it's valid and existent,
  // so it can be managed as a number of inputs
  const parsedConditionObject = useMemo(
    () =>
      parseApplicationCondition(
        selectedPhase?.applicationCondition || '',
        t('productRecommendationsTable.noConditionRequired'),
      ),
    [selectedPhase?.applicationCondition],
  );

  useEffect(() => {
    typeof parsedConditionObject !== 'string' && setApplicationCondition(parsedConditionObject);
  }, [parsedConditionObject]);

  useEffect(() => {
    const hasMandatorySelectedItems = selectedCountry?.id && selectedFeature?.id;
    hasMandatorySelectedItems &&
      productRegionFilters &&
      fetchProductsRegion({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: productRegionFilters,
        }),
      }).then((data) => {
        const productsRegionFilteredByFeature = data?.entities;
        productsRegionFilteredByFeature && setProductsRegion(productsRegionFilteredByFeature);
      });

    // Fetch unit settings if page navigation is fresh (new browser session) and direct (url to page is pasted in the browser)
    hasMandatorySelectedItems &&
      !selectedFeatureUnitSettings &&
      fetchUnitSettings({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: locationFilters,
        }),
      }).then((data) => {
        const unitSettings = data?.entities[0];
        unitSettings && setSelectedFeatureUnitSettings(unitSettings);
      });
  }, [selectedCountry?.id, selectedFeature?.id]);

  useEffect(() => {
    if (!productsRegion) return;
    getPartnerTags().then((partnerTags) => {
      partnerTags && setPartnerTags(partnerTags);
    });
  }, [productsRegion]);

  useEffect(() => {
    if (productRecommendationData) {
      const fertigationProductRecommendationData =
        getFertigationProductRecommendationConfiguration(productRecommendationData);
      if (fertigationProductRecommendationData) {
        setProductRecommendations(fertigationProductRecommendationData);
      }
    }
  }, [productRecommendationData, selectedAppType]);

  useEffect(() => {
    if (!productRecommendations) return;
    const { soilApplications = [], foliarApplications = [] } =
      productRecommendations.configuration.data;

    setSoilApplications(sortGrowthPhases(soilApplications));
    setFoliarApplications(sortGrowthPhases(foliarApplications));
    const selected = selectedAppType === soilType ? soilApplications : foliarApplications;
    setSelectedApps(sortGrowthPhases(selected));
    const selectedNewPhase = selected.find(({ id }) => id === selectedPhase?.id);
    selectedNewPhase && setSelectedPhase(selectedNewPhase);
  }, [productRecommendations]);

  useEffect(() => {
    if (selectedPhase) {
      const updatedSelectedProducts = {
        ...selectedProducts,
        [selectedPhase.id]: [...selectedPhase.recommendedProducts],
      };
      setSelectedProducts(updatedSelectedProducts);
    }
  }, [selectedPhase]);

  useEffect(() => {
    if (!showAddProductDialog && selectedPhase) {
      setSelectedProducts({
        [selectedPhase.id]: [...selectedPhase.recommendedProducts],
      });
    }
  }, [showAddProductDialog, selectedPhase]);

  useEffect(() => {
    if (selectedCountryUnits && selectedCountryUnits.length > 0 && selectedFeatureUnitSettings) {
      const unitsConfig = selectedFeatureUnitSettings?.configuration.data;
      const unitNames = {
        recommendedSolidUnitPerArea: filterUnitsById(
          selectedCountryUnits,
          unitsConfig?.recommendedSolidPerAreaUnitId,
        )?.name,
        recommendedLiquidsUnitPerArea: filterUnitsById(
          selectedCountryUnits,
          unitsConfig?.recommendedLiquidPerAreaUnitId,
        )?.name,
        recommendedSolidsUnitPerPlant: filterUnitsById(
          selectedCountryUnits,
          unitsConfig?.recommendedSolidPerPlantUnitId,
        )?.name,
        recommendedLiquidsUnitPerPlant: filterUnitsById(
          selectedCountryUnits,
          unitsConfig?.recommendedLiquidPerPlantUnitId,
        )?.name,
      };
      setDynamicUnitNames(unitNames);
    }
  }, [selectedCountryUnits, selectedFeatureUnitSettings]);

  const useCloneGrowthPhase = async (phase: FertigationPRecommendationGrowthPhase) => {
    const { productRecommendations, appType } = stateRef.current;
    if (!productRecommendations || !locationFilters) return;

    try {
      const response = await triggerCloneConfiguration(applicationTypeToGrowthPhaseMap[appType], {
        method: METHOD.POST,
        body: JSON.stringify({
          growthPhaseSourceId: phase.id,
          productRecommendationConfigId: productRecommendations?.id,
        }),
      });
      if (!response) {
        throw new Error('Unknown error');
      }
      await fetchProductRecommendations({
        method: METHOD.POST,
        body: JSON.stringify({ filter: locationFilters }),
      });
    } catch (error) {
      console.error('Error in product recommendations duplication', error);
    }
  };

  const useDeleteGrowthPhase = async (phase: FertigationPRecommendationGrowthPhase) => {
    const { productRecommendations, appType } = stateRef.current;

    if (!productRecommendations || !phase) return;

    try {
      const response = await triggerDeleteConfiguration(
        productRecommendations.id,
        applicationTypeToGrowthPhaseMap[appType],
        phase.id,
      );

      if (!response) {
        throw new Error('Unknown error');
      }

      await fetchProductRecommendations({
        method: METHOD.POST,
        body: JSON.stringify({ filter: locationFilters }),
      });
    } catch (error) {
      console.error('Error Delete product recommendations', error);
    }
  };

  const useUpdateGrowthPhase = useCallback(
    async (phase: FertigationPRecommendationGrowthPhase, showSnackMessage = false) => {
      const { productRecommendations, appType } = stateRef.current;

      if (!productRecommendations || !phase) return;

      const phaseMethod =
        appType === foliarType
          ? ProductRecommendationFoliarApplicationMethods.FOLIAR
          : phase.applicationMethod;

      const phaseRateRule =
        appType === foliarType ? ProductRecommendationRateRules.FIXED : phase.rateRule;

      const updateConf = {
        phaseDetails: {
          recommendedProducts: phase.recommendedProducts,
          applicationMethod: phaseMethod,
          applicationCondition: phase.applicationCondition,
          rateRule: phaseRateRule,
          fixedApplicationRate:
            phaseRateRule === ProductRecommendationRateRules.FIXED
              ? {
                  applicationValue:
                    phase.fixedApplicationRate?.applicationValue === undefined
                      ? null
                      : phase.fixedApplicationRate.applicationValue,
                  applicationValueUnitsIds:
                    phase.fixedApplicationRate?.applicationValueUnitsIds || [],
                }
              : null,
          dynamicApplicationRate: phase.dynamicApplicationRate,
        },
      };

      try {
        const response = await triggerUpdateConfiguration(
          productRecommendations.id,
          applicationTypeToGrowthPhaseMap[appType],
          phase.id,
          {
            method: METHOD.PUT,
            body: JSON.stringify(updateConf),
          },
        );

        if (!response) {
          throw new Error('Unknown error');
        } else {
          showSnackMessage &&
            setDisplaySnackbar({
              title: commonT('changesSaved'),
              colorConcept: 'successLight',
              icon: 'Check',
              placement: 'bottomRight',
              duration: 3000,
              open: true,
            });
        }
        await fetchProductRecommendations({
          method: METHOD.POST,
          body: JSON.stringify({ filter: locationFilters }),
        });
      } catch (error) {
        console.error('Error updating product recommendations', error);
      }
    },
    [selectedApps, productRecommendations, soilApplications],
  );

  const handleOpenAddProductRecommendationsPopup = (
    phase: FertigationPRecommendationGrowthPhase,
  ) => {
    setShowAddProductDialog(true);
    setSelectedPhase(phase);
  };

  const handleUpdateRecommendedProducts = (
    products: string[],
    phase: FertigationPRecommendationGrowthPhase,
  ) => {
    if (!phase || !selectedProducts) return;

    const clonedPhase = cloneDeep(phase);

    const updatedPhase = {
      ...clonedPhase,
      recommendedProducts: [...products],
    };

    useUpdateGrowthPhase(updatedPhase);
    setSelectedProducts((prevState) => ({
      ...prevState,
      [phase.id]: products,
    }));
    setShowAddProductDialog(false);
  };

  const getPartnerTags = useCallback(async () => {
    const ids = productsRegion
      ?.reduce<string[]>((acc, productRegion) => {
        acc = [
          ...acc,
          productRegion?.tagsConfiguration.partnerTags?.map((partnerTag) => partnerTag.id).join() ||
            '',
        ];
        return acc;
      }, [])
      .join();
    const productTags = await triggerGetPartnerTags({
      method: METHOD.POST,
      body: JSON.stringify({
        filter: [
          {
            key: 'id',
            type: FilterType.IN,
            value: ids,
          },
        ],
      }),
    });
    return productTags?.entities;
  }, [productsRegion]);

  const handleOpenAppConditionBuilder = (
    phase: FertigationPRecommendationGrowthPhase,
    displayedCondition: string,
  ) => {
    displayedCondition === t('productRecommendationsTable.noConditionRequired')
      ? setIsConditionApplied(false)
      : setIsConditionApplied(true);
    setSelectedPhase(phase);
    setIsConditionBuilderOpen(true);
  };

  const handleCloseAppConditionBuilder = () => {
    setSelectedPhase(undefined);
    setApplicationCondition(null);
    setIsConditionBuilderOpen(false);
  };

  const handleApplicationConditionChange = (
    parsedApplicationCondition: ParsedApplicationConditionsResult | null,
  ) => {
    if (!selectedPhase) return;

    const conditionText = buildConditionText(
      parsedApplicationCondition,
      t('productRecommendationsTable.noConditionRequired'),
    );

    const newPhase = {
      ...selectedPhase,
      applicationCondition: conditionText,
    };

    useUpdateGrowthPhase(newPhase, true);
  };

  return {
    productRecommendations,
    soilApplications,
    foliarApplications,
    productsRegion,
    selectedPhase,
    productsFamilyData,
    partnerTags,
    selectedProducts,
    showAddProductDialog,
    rateNutrientsArray,
    useCloneGrowthPhase,
    useDeleteGrowthPhase,
    useUpdateGrowthPhase,
    setSelectedProducts,
    setShowAddProductDialog,
    handleOpenAddProductRecommendationsPopup,
    handleUpdateRecommendedProducts,
    isConditionBuilderOpen,
    setIsConditionBuilderOpen,
    applicationCondition,
    handleApplicationConditionChange,
    handleOpenAppConditionBuilder,
    handleCloseAppConditionBuilder,
    isConditionApplied,
    dynamicUnitNames,
  };
};

export default FertigationProductRecommendationsLogic;
