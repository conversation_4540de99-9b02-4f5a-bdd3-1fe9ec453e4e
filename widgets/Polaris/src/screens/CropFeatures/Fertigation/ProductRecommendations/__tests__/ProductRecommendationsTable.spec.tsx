import React from 'react';
import { render, screen } from '@testing-library/react';

import { ProductRecommendationApplicationTypes } from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { ProductRecommendationsTable } from '../ProductRecommendationsTable';
import { mockSoilPhase } from '../../../../Home/mock-data/MockData';

jest.mock('@widgets/Polaris/src/providers/AppProvider', () => ({
  useAppContext: jest.fn(),
}));

const mockSoilApplicationData = [mockSoilPhase];

const mockUseCloneGrowthPhase = jest.fn();
const mockUseDeleteGrowthPhase = jest.fn();
const mockUseUpdateGrowthPhase = jest.fn();

describe('ProductRecommendationsTable', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    (useAppContext as jest.Mock).mockReturnValue({
      selectedAppType: ProductRecommendationApplicationTypes.SOIL,
      methods: {
        setSelectedAppType: jest.fn(),
      },
    });
  });
  it('renders without crashing', () => {
    const useCloneGrowthPhase = jest.fn();
    const useDeleteGrowthPhase = jest.fn();
    const useUpdateGrowthPhase = jest.fn();

    render(
      <ProductRecommendationsTable
        applicationData={mockSoilApplicationData}
        useCloneGrowthPhase={useCloneGrowthPhase}
        useDeleteGrowthPhase={useDeleteGrowthPhase}
        useUpdateGrowthPhase={useUpdateGrowthPhase}
      />,
    );

    expect(screen.getByTestId('fertigation-product-recommendations-table')).toBeInTheDocument();
  });

  test('renders table with correct headers and rows', () => {
    render(
      <ProductRecommendationsTable
        applicationData={mockSoilApplicationData}
        useCloneGrowthPhase={mockUseCloneGrowthPhase}
        useDeleteGrowthPhase={mockUseDeleteGrowthPhase}
        useUpdateGrowthPhase={mockUseUpdateGrowthPhase}
      />,
    );

    expect(screen.getByText(mockSoilPhase.phaseName)).toBeInTheDocument();
    expect(screen.getByText('recommendedProducts')).toBeInTheDocument();
  });
});
