import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import {
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  allPartnerTagsHandler,
  productRegionFilterHandler,
  fertigationProductRecommendationFilterHandler,
  mockAppProviderValue,
} from '@common/mocks';
import { setupServer } from 'msw/node';
import FertigationProductRecommendations from '../ProductRecommendations';
import { ProductRecommendationApplicationTypes } from '@common/types';
import {
  productRecommendationsMock,
  regionResponse,
  featureResponse,
  countryResponse,
} from '../../../../Home/mock-data/MockData';

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
  allPartnerTagsHandler,
  productRegionFilterHand<PERSON>,
  fertigationProductRecommendationFilterHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());
const useCloneGrowthPhaseMock = jest.fn();
const useDeleteGrowthPhaseMock = jest.fn();
const useUpdateGrowthPhaseMock = jest.fn();

const mockData = {
  productRecommendationData: productRecommendationsMock,
  soilApplications: productRecommendationsMock.configuration.data.soilApplications,
  foliarApplications: productRecommendationsMock.configuration.data.foliarApplications,
  selectedRegion: regionResponse,
  selectedFeature: featureResponse,
  selectedCountry: countryResponse,
  dynamicUnitNames: {
    recommendedSolidUnitPerArea: '',
    recommendedLiquidsUnitPerArea: '',
    recommendedSolidsUnitPerPlant: '',
    recommendedLiquidsUnitPerPlant: '',
  },
  useCloneGrowthPhase: useCloneGrowthPhaseMock,
  useDeleteGrowthPhase: useDeleteGrowthPhaseMock,
  useUpdateGrowthPhase: useUpdateGrowthPhaseMock,
};

export default function FertigationProductRecommendationsLogic() {
  return mockData;
}

describe('FertigationProductRecommendations Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const component = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <FertigationProductRecommendations />
      </AppContext.Provider>,
    );
    const tableComponent = component.getByTestId('fertigation-product-recommendations-table');
    const titleComponent = component.getByTestId('radio-btn-title');
    const soilRadioBtnComponent = component.getByTestId('product-recommendations-soil-type-button');
    const foliarRadioBtnComponent = component.getByTestId(
      'product-recommendations-foliar-type-button',
    );
    expect(titleComponent).toBeInTheDocument();
    expect(soilRadioBtnComponent).toBeInTheDocument();
    expect(foliarRadioBtnComponent).toBeInTheDocument();
    expect(tableComponent).toBeInTheDocument();
    expect(component).toMatchSnapshot();
  });

  it('passes correct handlers to ProductRecommendationsTable', () => {
    const { getByTestId } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <FertigationProductRecommendations />
      </AppContext.Provider>,
    );

    const tableComponent = getByTestId('fertigation-product-recommendations-table');

    fireEvent.click(tableComponent);

    expect(useCloneGrowthPhaseMock).not.toHaveBeenCalled();
    expect(useDeleteGrowthPhaseMock).not.toHaveBeenCalled();
    expect(useUpdateGrowthPhaseMock).not.toHaveBeenCalled();
  });

  it('sets default selectedAppType to SOIL if not defined', () => {
    const mockAppProviderValueWithoutSelectedAppType = {
      ...mockAppProviderValue,
      selectedAppType: null,
    };

    render(
      <AppContext.Provider value={mockAppProviderValueWithoutSelectedAppType}>
        <FertigationProductRecommendations />
      </AppContext.Provider>,
    );

    expect(
      mockAppProviderValueWithoutSelectedAppType.methods.setSelectedAppType,
    ).toHaveBeenCalledWith(ProductRecommendationApplicationTypes.SOIL);
  });

  it('matches snapshot for soil application type', () => {
    const { asFragment } = render(
      <AppContext.Provider value={mockAppProviderValue}>
        <FertigationProductRecommendations />
      </AppContext.Provider>,
    );

    expect(asFragment()).toMatchSnapshot();
  });
});
