import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AhuaIcon } from '@yaradigitallabs/ahua-react';
import {
  AddProductButton,
  StyledChip,
  ChipWrapperStyled,
  TimelineItemCounter,
  StyledImage,
  StyledButton,
  StyledLabel,
  StyledImageWrapper,
  AddProductButtonWithChips,
  AddProductsCellWrapper,
} from '../ProductRecommendations.styled';
import { ProductRegion, FertigationPRecommendationGrowthPhase } from '@common/types';
import { cloneDeep } from 'lodash';

export interface AddProductProps {
  phase: FertigationPRecommendationGrowthPhase;
  productsRegion: ProductRegion[];
  handleOpenAddProductRecommendationsPopup: (data: FertigationPRecommendationGrowthPhase) => void;
  handleUpdateRecommendedProducts: (
    data: string[],
    phase: FertigationPRecommendationGrowthPhase,
  ) => void;
}

export const AddProducts: React.FC<AddProductProps> = ({
  phase,
  productsRegion,
  handleOpenAddProductRecommendationsPopup,
  handleUpdateRecommendedProducts,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.fpDetails.productRecommendations.productRecommendationsTable',
  });

  const products = useMemo(
    () =>
      cloneDeep(productsRegion)?.filter((productRegion) =>
        phase.recommendedProducts.includes(productRegion.id),
      ),
    [phase, productsRegion],
  );

  const handleRemoveProduct = useCallback(
    (value: ProductRegion) => {
      const updatedProducts = products.reduce<string[]>((acc, product) => {
        const isProductToRemove = product.id === value.id;
        if (!isProductToRemove) {
          acc = [...acc, product.id];
        }
        return acc;
      }, []);
      handleUpdateRecommendedProducts(updatedProducts, phase);
    },
    [products],
  );
  return (
    <AddProductsCellWrapper>
      {products?.length > 0 ? (
        <>
          <AddProductButtonWithChips
            colorConcept='inverse'
            iconLeading='Plus'
            size={products?.length > 0 ? 'xs' : 's'}
            id={`add-products-${phase.id}}`}
            data-cy='add-product-button'
            onClick={() => handleOpenAddProductRecommendationsPopup(phase)}
          >
            {t('addProducts')}
          </AddProductButtonWithChips>
          {products?.length > 0 && (
            <>
              <TimelineItemCounter
                mode='dark'
                outline='false'
                value={products?.length}
                data-cy={`products-counter-${products?.length}`}
              />
              <ChipWrapperStyled>
                {products?.map((tag) => (
                  <StyledChip key={tag.name}>
                    <StyledImageWrapper data-cy='product'>
                      <StyledImage src={tag.productFamily.mediaUri?.[0].value} />
                    </StyledImageWrapper>
                    <StyledLabel>{`${
                      tag.localizedName ? tag.localizedName : tag.name
                    }`}</StyledLabel>
                    <StyledButton onClick={() => handleRemoveProduct(tag)} data-cy='product-remove'>
                      <AhuaIcon iconSize='x4' colorConcept='neutral' icon='Clear' />
                    </StyledButton>
                  </StyledChip>
                ))}
              </ChipWrapperStyled>
            </>
          )}
        </>
      ) : (
        <AddProductButton
          colorConcept='inverse'
          iconLeading='Plus'
          size={products?.length > 0 ? 'xs' : 's'}
          id={`add-products-${phase.id}}`}
          data-cy='add-product-button'
          onClick={() => handleOpenAddProductRecommendationsPopup(phase)}
        >
          {' '}
          {t('addProducts')}
        </AddProductButton>
      )}
    </AddProductsCellWrapper>
  );
};
