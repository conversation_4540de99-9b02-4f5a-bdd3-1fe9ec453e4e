import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AddProducts } from '../AddProducts';
import {
  mockSoilPhase,
  useProductsByRegionResponse,
} from '@widgets/Polaris/src/screens/Home/mock-data/MockData';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { mockAppProviderValue } from '@common/mocks';

const mockHandleOpenAddProductRecommendationsPopup = jest.fn;
const mockHandleUpdateRecommendedProducts = jest.fn;

describe('AddProducts Component', () => {
  const renderComponent = (phase = mockSoilPhase, productsRegion = useProductsByRegionResponse) =>
    render(
      <AppContext.Provider value={mockAppProviderValue}>
        <AddProducts
          phase={phase}
          productsRegion={productsRegion}
          handleOpenAddProductRecommendationsPopup={mockHandleOpenAddProductRecommendationsPopup}
          handleUpdateRecommendedProducts={mockHandleUpdateRecommendedProducts}
        />
      </AppContext.Provider>,
    );

  it('renders without crashing', () => {
    const { container } = renderComponent();

    const addProductBtn = screen.getByTestId('add-product-button');
    expect(addProductBtn).toBeInTheDocument();

    if (mockSoilPhase.recommendedProducts.length > 0) {
      const productsCounter = screen.getByTestId(
        `products-counter-${mockSoilPhase.recommendedProducts.length}`,
      );
      expect(productsCounter).toBeInTheDocument();

      mockSoilPhase.recommendedProducts.forEach((tag) => {
        const product = useProductsByRegionResponse.find((product) => product.id === tag);
        const productName = product?.localizedName ? product.localizedName : product?.name;
        const foundProductName = productName && screen.getByText(productName);
        expect(foundProductName).toBeInTheDocument();
      });

      const productTagCloseButtons = screen.getAllByTestId('product-remove');
      expect(productTagCloseButtons.length).toEqual(mockSoilPhase.recommendedProducts.length);
    }

    expect(container).toMatchSnapshot();
  });

  it('renders the correct number of tags', () => {
    renderComponent();

    const tags = mockSoilPhase.recommendedProducts;

    if (tags.length > 0) {
      tags.forEach((tag) => {
        const product = useProductsByRegionResponse.find((product) => product.id === tag);
        const productName = product?.localizedName ? product.localizedName : product?.name;
        const chip = productName && screen.getByText(productName);
        expect(chip).toBeInTheDocument();
      });
    } else {
      expect(screen.queryAllByTestId('product')).toHaveLength(0);
    }
  });

  it('renders correctly when no tags are present', () => {
    const emptyPhase = { ...mockSoilPhase, recommendedProducts: [] };

    renderComponent(emptyPhase);

    const addProductBtn = screen.getByTestId('add-product-button');
    expect(addProductBtn).toBeInTheDocument();

    expect(screen.queryByTestId('products-counter')).not.toBeInTheDocument();
    expect(screen.queryAllByTestId('product')).toHaveLength(0);
  });
});
