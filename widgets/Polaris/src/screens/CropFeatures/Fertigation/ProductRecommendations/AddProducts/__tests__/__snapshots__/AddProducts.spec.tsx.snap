// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddProducts Component renders without crashing 1`] = `
<div>
  <div
    class="c-hlWtMU"
  >
    <button
      class="c-hRrCwb c-hRrCwb-epMc-size-xs c-hRrCwb-gHPsgr-colorConcept-inverse c-hRrCwb-bETQVM-variant-primary c-gftcTy"
      data-cy="add-product-button"
      id="add-products-774f3d1f-31f2-4275-a8a3-45b4c75e0827}"
    >
      <svg
        class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.072 6v12M18 12H6"
        />
      </svg>
      <span
        class="c-iepcqn"
      >
        addProducts
      </span>
    </button>
    <div
      class="c-gxYARC c-gxYARC-iRQKPI-prominence-medium c-gxYARC-hGXxkE-size-n c-gxYARC-WnUxl-cv c-fNxlSP"
      data-cy="products-counter-2"
    >
      <span
        class="c-fgiZZE c-fgiZZE-bZYPwV-size-n"
      >
        2
      </span>
    </div>
    <div
      class="c-fzPpmE"
    >
      <div
        class="c-izrZVj"
      >
        <div
          class="c-fHKUug"
          data-cy="product"
        >
          <div
            class="c-PJLV c-PJLV-iDHMfl-css"
          >
            <div
              data-radix-aspect-ratio-wrapper=""
              style="position: relative; width: 100%; padding-bottom: 100%;"
            >
              <div
                class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
              >
                <img
                  class="c-hinyfY c-hinyfY-ihWXOvK-css c-kTxSNx"
                  src="https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraMila.svg"
                />
              </div>
            </div>
          </div>
        </div>
        <label
          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-eSnTGk"
        >
          YaraMila
        </label>
        <button
          class="c-kQSwbE"
          data-cy="product-remove"
        >
          <svg
            class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.407 16.8l-3.777-4.721h-1.26L7.595 16.8m8.813-9.6l-3.777 4.721h-1.26L7.595 7.2"
            />
          </svg>
        </button>
      </div>
      <div
        class="c-izrZVj"
      >
        <div
          class="c-fHKUug"
          data-cy="product"
        >
          <div
            class="c-PJLV c-PJLV-iDHMfl-css"
          >
            <div
              data-radix-aspect-ratio-wrapper=""
              style="position: relative; width: 100%; padding-bottom: 100%;"
            >
              <div
                class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
              >
                <img
                  class="c-hinyfY c-hinyfY-ihWXOvK-css c-kTxSNx"
                  src="https://polaris-axial-static-medias.yarapolaris.com/product/brand_logo/YaraVita.svg"
                />
              </div>
            </div>
          </div>
        </div>
        <label
          class="c-jAwvtv c-jAwvtv-gVuvhK-size-n c-jAwvtv-iPJLV-css c-eSnTGk"
        >
          MAÍZ
        </label>
        <button
          class="c-kQSwbE"
          data-cy="product-remove"
        >
          <svg
            class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.407 16.8l-3.777-4.721h-1.26L7.595 16.8m8.813-9.6l-3.777 4.721h-1.26L7.595 7.2"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;
