// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SplittingSchedule renders without crashing 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="c-gJoajD c-gJoajD-ikkVQhH-css"
      >
        <input
          aria-controls="radix-:r0:"
          aria-expanded="false"
          aria-haspopup="dialog"
          class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-krXxlc-iconLeadingVisibility-true c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-cHWLgR-cv c-exiTqG-bTMGXY-cv c-exiTqG-ikkVQhH-css"
          data-state="closed"
          data-testid="calendar-trigger-input-start"
          inputmode="none"
          placeholder="Start date"
          type="text"
          value="29 Dec"
        />
        <label
          class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
        >
          Start date
        </label>
        <span
          class="c-fcBbhr"
        >
          <svg
            class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-hhNXrr c-hhNXrr-hfDgku-size-xs"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 4.5H6a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-11a3 3 0 0 0-3-3h-3m-6 0h6m-6 0v-2m0 2V6m6-1.5v-2m0 2V6m-3.557 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"
            />
          </svg>
        </span>
        <p
          class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-kfEueE"
        >
          calendarHelperText
        </p>
      </div>
      <div
        class="c-eWFPgW"
        data-cy="fertigation-splitting-schedule-content"
      >
        <div
          class="c-jCarvd c-jCarvd-ikfQhrm-css"
        >
          <div
            class="c-cVKzzi"
          >
            <div
              class="c-cXFqtJ"
            >
              <h1
                class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
              >
                title
              </h1>
            </div>
            <p
              class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
            />
          </div>
          <div
            class="c-hcUxto"
          >
            <button
              class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-fnSKWs"
              data-cy="fertigation-splitting-add-split-button"
              title="addSplitButton"
              type="button"
            >
              <svg
                class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.072 6v12M18 12H6"
                />
              </svg>
              <span
                class="c-iepcqn"
              >
                addSplitButton
              </span>
            </button>
          </div>
        </div>
        <div
          class="c-fBKYpf"
        >
          <table
            class="c-kwAGqj c-gntvdH"
            data-cy="fertigation-splitting-schedule-table"
          >
            <tr
              class="c-eDGYZe c-kNreeo"
            >
              <th
                class="c-kxWgPf c-ffeoqz c-LfdHC"
                colspan="2"
              >
                growthPhaseNo
              </th>
              <td
                class="c-doquzR c-kXwEbX c-fkYseh"
              >
                <div
                  class="c-kjgAso"
                >
                  1
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe c-kNreeo"
            >
              <th
                class="c-kxWgPf c-ffeoqz c-LfdHC"
                colspan="2"
              >
                growthPhaseName
              </th>
              <td
                class="c-doquzR c-kXwEbX c-hHwvrh"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-jxCHSJ-cv c-exiTqG-bTMGXY-cv c-gaNWaj"
                    data-cy="fertigation-splitting-schedule-cell-name-4e92604c-03da-4b6a-ac9e-a94ea1723a57-input"
                    value=""
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe c-kNreeo"
            >
              <th
                class="c-kxWgPf c-ffeoqz c-SdKYq"
                colspan="1"
              >
                growthPhaseDuration
              </th>
              <th
                class="c-kxWgPf c-ffeoqz c-kpwYEs c-iqaido"
                colspan="1"
              >
                0 growthPhaseTotalDays
              </th>
              <td
                class="c-doquzR c-kXwEbX c-hHwvrh"
              >
                <div
                  class="c-gJoajD c-gJoajD-ifGHEql-css"
                >
                  <input
                    class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-jxCHSJ-cv c-exiTqG-bTMGXY-cv c-gaNWaj"
                    data-cy="fertigation-splitting-schedule-cell-daysDuration-4e92604c-03da-4b6a-ac9e-a94ea1723a57-input"
                    value="0"
                  />
                  <span
                    class="c-fcBbhr"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="c-eDGYZe c-kNreeo"
              style="height: 94px;"
            >
              <th
                class="c-kxWgPf c-ffeoqz c-LfdHC"
                colspan="2"
              >
                growthPhaseImage
              </th>
              <td
                class="c-doquzR c-kXwEbX c-doquzR-ieckCxl-css"
              >
                <div
                  class="c-fBZITu"
                >
                  <div
                    class="c-PJLV c-PJLV-ieBcQxc-css"
                  >
                    <div
                      data-radix-aspect-ratio-wrapper=""
                      style="position: relative; width: 100%; padding-bottom: 72.46376811594203%;"
                    >
                      <div
                        class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                        style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                      >
                        <img
                          class="c-hinyfY c-hinyfY-ihWXOvK-css"
                          data-cy="growth-stage-image"
                          src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/sugar_cane_second_year/6.svg"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-esFqum ghost"
                  data-cy="fertigation-splitting-add-image-button"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.072 6v12M18 12H6"
                    />
                  </svg>
                  <span
                    class="c-iepcqn"
                  >
                    addImageButton
                  </span>
                </button>
              </td>
            </tr>
            <tr
              class="c-eDGYZe c-kNreeo"
            >
              <th
                class="c-kxWgPf c-ffeoqz c-SdKYq"
                colspan="1"
              >
                growthPhaseNutrient
              </th>
              <th
                class="c-kxWgPf c-ffeoqz c-kpwYEs"
                colspan="1"
              >
                <div
                  class="c-UazGY"
                >
                  growthPhaseNutrientTotalPercent
                  <svg
                    class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css c-gkxyQj"
                    data-state="closed"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                    />
                  </svg>
                </div>
              </th>
              <td
                class="c-doquzR c-kXwEbX"
              >
                growthPhaseNutrientSplitPercent
              </td>
            </tr>
            <tr
              class="c-eDGYZe c-kNreeo"
            >
              <th
                class="c-kxWgPf c-ffeoqz c-LfdHC"
                colspan="2"
              >
                <button
                  class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost"
                  data-cy="fertigation-splitting-expand-button"
                  style="font-weight: var(--fontWeights-semiBold);"
                >
                  <svg
                    class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7.5 10l3.038 3.797a1.872 1.872 0 002.924 0L16.5 10"
                    />
                  </svg>
                  <span
                    class="c-iepcqn"
                  >
                    showMoreButton
                  </span>
                </button>
              </th>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="c-gJoajD c-gJoajD-ikkVQhH-css"
    >
      <input
        aria-controls="radix-:r0:"
        aria-expanded="false"
        aria-haspopup="dialog"
        class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-krXxlc-iconLeadingVisibility-true c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-cHWLgR-cv c-exiTqG-bTMGXY-cv c-exiTqG-ikkVQhH-css"
        data-state="closed"
        data-testid="calendar-trigger-input-start"
        inputmode="none"
        placeholder="Start date"
        type="text"
        value="29 Dec"
      />
      <label
        class="c-jAwvtv c-jAwvtv-dDOYgV-size-l c-jAwvtv-iiffMpN-css c-Oxnqk"
      >
        Start date
      </label>
      <span
        class="c-fcBbhr"
      >
        <svg
          class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-hhNXrr c-hhNXrr-hfDgku-size-xs"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 4.5H6a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-11a3 3 0 0 0-3-3h-3m-6 0h6m-6 0v-2m0 2V6m6-1.5v-2m0 2V6m-3.557 7a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"
          />
        </svg>
      </span>
      <p
        class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-kfEueE"
      >
        calendarHelperText
      </p>
    </div>
    <div
      class="c-eWFPgW"
      data-cy="fertigation-splitting-schedule-content"
    >
      <div
        class="c-jCarvd c-jCarvd-ikfQhrm-css"
      >
        <div
          class="c-cVKzzi"
        >
          <div
            class="c-cXFqtJ"
          >
            <h1
              class="c-iFoEyZ c-iFoEyZ-cnxGjA-size-xs c-iFoEyZ-iPJLV-css c-gPjxah"
            >
              title
            </h1>
          </div>
          <p
            class="c-gIhYmC c-gIhYmC-fADHcj-size-s c-gIhYmC-iPJLV-css c-faogdM"
          />
        </div>
        <div
          class="c-hcUxto"
        >
          <button
            class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-fnSKWs"
            data-cy="fertigation-splitting-add-split-button"
            title="addSplitButton"
            type="button"
          >
            <svg
              class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.072 6v12M18 12H6"
              />
            </svg>
            <span
              class="c-iepcqn"
            >
              addSplitButton
            </span>
          </button>
        </div>
      </div>
      <div
        class="c-fBKYpf"
      >
        <table
          class="c-kwAGqj c-gntvdH"
          data-cy="fertigation-splitting-schedule-table"
        >
          <tr
            class="c-eDGYZe c-kNreeo"
          >
            <th
              class="c-kxWgPf c-ffeoqz c-LfdHC"
              colspan="2"
            >
              growthPhaseNo
            </th>
            <td
              class="c-doquzR c-kXwEbX c-fkYseh"
            >
              <div
                class="c-kjgAso"
              >
                1
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe c-kNreeo"
          >
            <th
              class="c-kxWgPf c-ffeoqz c-LfdHC"
              colspan="2"
            >
              growthPhaseName
            </th>
            <td
              class="c-doquzR c-kXwEbX c-hHwvrh"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-jxCHSJ-cv c-exiTqG-bTMGXY-cv c-gaNWaj"
                  data-cy="fertigation-splitting-schedule-cell-name-4e92604c-03da-4b6a-ac9e-a94ea1723a57-input"
                  value=""
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe c-kNreeo"
          >
            <th
              class="c-kxWgPf c-ffeoqz c-SdKYq"
              colspan="1"
            >
              growthPhaseDuration
            </th>
            <th
              class="c-kxWgPf c-ffeoqz c-kpwYEs c-iqaido"
              colspan="1"
            >
              0 growthPhaseTotalDays
            </th>
            <td
              class="c-doquzR c-kXwEbX c-hHwvrh"
            >
              <div
                class="c-gJoajD c-gJoajD-ifGHEql-css"
              >
                <input
                  class="c-exiTqG c-exiTqG-fVkzA-variant-default c-exiTqG-emKYNC-iconLeadingVisibility-false c-exiTqG-bMBexh-iconTrailingVisibility-false c-exiTqG-inBkMG-cover-outline c-exiTqG-jhHFqm-readonly-false c-exiTqG-jUmNEq-cv c-exiTqG-jxCHSJ-cv c-exiTqG-bTMGXY-cv c-gaNWaj"
                  data-cy="fertigation-splitting-schedule-cell-daysDuration-4e92604c-03da-4b6a-ac9e-a94ea1723a57-input"
                  value="0"
                />
                <span
                  class="c-fcBbhr"
                />
              </div>
            </td>
          </tr>
          <tr
            class="c-eDGYZe c-kNreeo"
            style="height: 94px;"
          >
            <th
              class="c-kxWgPf c-ffeoqz c-LfdHC"
              colspan="2"
            >
              growthPhaseImage
            </th>
            <td
              class="c-doquzR c-kXwEbX c-doquzR-ieckCxl-css"
            >
              <div
                class="c-fBZITu"
              >
                <div
                  class="c-PJLV c-PJLV-ieBcQxc-css"
                >
                  <div
                    data-radix-aspect-ratio-wrapper=""
                    style="position: relative; width: 100%; padding-bottom: 72.46376811594203%;"
                  >
                    <div
                      class="c-fxrEBZ c-fxrEBZ-iPJLV-css"
                      style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;"
                    >
                      <img
                        class="c-hinyfY c-hinyfY-ihWXOvK-css"
                        data-cy="growth-stage-image"
                        src="https://polaris-axial-static-medias.yarapolaris.com/growth_scale/sugar_cane_second_year/6.svg"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <button
                class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost c-esFqum ghost"
                data-cy="fertigation-splitting-add-image-button"
              >
                <svg
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.072 6v12M18 12H6"
                  />
                </svg>
                <span
                  class="c-iepcqn"
                >
                  addImageButton
                </span>
              </button>
            </td>
          </tr>
          <tr
            class="c-eDGYZe c-kNreeo"
          >
            <th
              class="c-kxWgPf c-ffeoqz c-SdKYq"
              colspan="1"
            >
              growthPhaseNutrient
            </th>
            <th
              class="c-kxWgPf c-ffeoqz c-kpwYEs"
              colspan="1"
            >
              <div
                class="c-UazGY"
              >
                growthPhaseNutrientTotalPercent
                <svg
                  class="c-nJRoe c-nJRoe-fvmOlv-iconSize-x4 c-nJRoe-dCZJbc-colorConcept-neutral c-nJRoe-iPJLV-css c-gkxyQj"
                  data-state="closed"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16.4v-4.9h-1.556M12 7.556V8m0 14C6.475 22 2 17.525 2 12S6.475 2 12 2s10 4.475 10 10-4.475 10-10 10z"
                  />
                </svg>
              </div>
            </th>
            <td
              class="c-doquzR c-kXwEbX"
            >
              growthPhaseNutrientSplitPercent
            </td>
          </tr>
          <tr
            class="c-eDGYZe c-kNreeo"
          >
            <th
              class="c-kxWgPf c-ffeoqz c-LfdHC"
              colspan="2"
            >
              <button
                class="c-hRrCwb c-hRrCwb-dHCPxX-size-s c-hRrCwb-jdtELQ-colorConcept-brand c-hRrCwb-coREMZ-variant-ghost"
                data-cy="fertigation-splitting-expand-button"
                style="font-weight: var(--fontWeights-semiBold);"
              >
                <svg
                  class="c-nJRoe c-nJRoe-iFviFh-iconSize-x6 c-nJRoe-bHKkzJ-colorConcept-inherit c-nJRoe-iPJLV-css c-PJLV c-PJLV-fwMXZD-position-leading"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M7.5 10l3.038 3.797a1.872 1.872 0 002.924 0L16.5 10"
                  />
                </svg>
                <span
                  class="c-iepcqn"
                >
                  showMoreButton
                </span>
              </button>
            </th>
          </tr>
        </table>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
