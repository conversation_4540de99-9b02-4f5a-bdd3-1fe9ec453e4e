import React from 'react';
import { render } from '@testing-library/react';
import { setupServer } from 'msw/node';
import {
  unitCountries<PERSON>andler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
} from '@common/mocks';
import SplittingSchedule from '../SplittingSchedule';
import { useFetchSplittingConfigurationResponse } from '../../../../Home/mock-data/MockData';

const server = setupServer(
  unitCountriesHandler,
  cropDemandAnalysisNutrientsHandler,
  cropDemandAnalysisHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

const setSelectedFertigationSplittingSchedulesMock = jest.fn();
const useGetSplittingConfigurationsMock = jest.fn();
const refetchSplittingConfigurationsMock = jest.fn();
const useUpdateStartDateMock = jest.fn();
const useUpdateSplitMock = jest.fn();
const updateSelectedSplittingScheduleStateMock = jest.fn();
const useAddSplitMock = jest.fn();
const useDeleteSplitMock = jest.fn();
const mockData = {
  selectedFertigationSplittingSchedules: useFetchSplittingConfigurationResponse.entities[0],
  setSelectedFertigationSplittingSchedules: setSelectedFertigationSplittingSchedulesMock,
  useGetSplittingConfigurations: useGetSplittingConfigurationsMock,
  refetchSplittingConfigurations: refetchSplittingConfigurationsMock,
  useUpdateStartDate: useUpdateStartDateMock,
  useUpdateSplit: useUpdateSplitMock,
  updateSelectedSplittingScheduleState: updateSelectedSplittingScheduleStateMock,
  useAddSplit: useAddSplitMock,
  useDeleteSplit: useDeleteSplitMock,
};
jest.mock('../SplittingScheduleHooksLogic', () => ({
  SplittingScheduleHooksLogic: jest.fn(() => mockData),
}));

describe('SplittingSchedule', () => {
  it('renders without crashing', () => {
    const component = render(<SplittingSchedule />);

    const startDateComponent = component.getByDisplayValue('29 Dec');
    expect(startDateComponent).toBeInTheDocument();

    const tableComponent = component.getByTestId('fertigation-splitting-schedule-table');
    expect(tableComponent).toBeInTheDocument();

    expect(component).toMatchSnapshot();
  });
});
