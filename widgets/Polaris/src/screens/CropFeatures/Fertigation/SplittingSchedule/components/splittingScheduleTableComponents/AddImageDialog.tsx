import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  AdvancedDialog,
  Button,
  IconButton,
  Image,
  Label,
  RadioButton,
  RadioButtonGroup,
} from '@yaradigitallabs/ahua-react';
import { Image<PERSON>rameSmall, RadioElementWrapper } from '../../SplittingSchedule.styled';
import { GrowthScaleStage, DefaultNoImageStage, MediaUri } from '@common/types';

interface AddImageDialogProps {
  isOpen: boolean;
  keyPrefix: string;
  onClose: () => void;
  selectedMediaUri?: MediaUri[] | null;
  growthPhaseStages?: GrowthScaleStage[] | [];
  onSaveImage: (value: MediaUri[] | null) => void;
}

export const AddImageDialog = ({
  isOpen,
  keyPrefix,
  onClose,
  selectedMediaUri,
  growthPhaseStages,
  onSaveImage,
}: AddImageDialogProps) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: `${keyPrefix}.addImageDialog`,
  });

  const [selectedStage, setSelectedStage] = useState<GrowthScaleStage | DefaultNoImageStage>();
  const [stages, setStages] = useState<(GrowthScaleStage | DefaultNoImageStage)[]>([
    {
      id: '0cfc8237-2058-4773-8274-09f12cf7ebab',
      name: t('noImageName'),
      mediaUri: null,
    },
  ]);

  useEffect(() => {
    if (!growthPhaseStages || growthPhaseStages.length === 0) return;

    const sortedData = growthPhaseStages?.sort((a, b) => {
      return (a.ordinal || 0) - (b.ordinal || 0);
    });

    const updatedGrowthScaleStagesData = [...stages, ...sortedData];

    setStages(updatedGrowthScaleStagesData);
  }, [growthPhaseStages]);

  useEffect(() => {
    const stage = stages?.find(
      (stage) => stage?.mediaUri?.[0]?.value === selectedMediaUri?.[0]?.value,
    );
    setSelectedStage(stage ? stage : stages[0]);
  }, [selectedMediaUri, isOpen]);

  const handleSaveImageBtn = () => {
    if (selectedStage?.mediaUri?.[0].value !== selectedMediaUri?.[0].value) {
      onSaveImage(selectedStage?.mediaUri ?? null);
    }

    onClose();
  };

  return (
    <AdvancedDialog open={isOpen} onOpenChange={() => onClose()}>
      <AdvancedDialog.Content css={{ width: '560px', maxHeight: '652px' }}>
        <AdvancedDialog.Start>
          <AdvancedDialog.Title>{t('title')}</AdvancedDialog.Title>
          <AdvancedDialog.Close>
            <IconButton
              data-cy='add-image-popup-close-btn'
              icon='Close'
              colorConcept='brand'
              size='s'
              onClick={() => onClose()}
            />
          </AdvancedDialog.Close>
        </AdvancedDialog.Start>

        <AdvancedDialog.Middle>
          <RadioButtonGroup value={selectedStage?.id} css={{ gap: '$x4' }}>
            {stages?.map(
              (stage) =>
                stage && (
                  <RadioElementWrapper
                    key={stage.id}
                    data-cy={`wrapper-add-images-button-${stage.id}`}
                    checked={selectedStage?.id === stage.id}
                    onClick={() => {
                      setSelectedStage(stage);
                    }}
                  >
                    <div>
                      <ImageFrameSmall>
                        {stage?.mediaUri?.[0]?.value && (
                          <Image src={stage.mediaUri[0].value} width={70} ratio={1.38} />
                        )}
                      </ImageFrameSmall>
                      <Label htmlFor={stage.id} size='n'>
                        {stage.name}
                      </Label>
                    </div>
                    <RadioButton
                      id={stage.id}
                      value={stage.id}
                      checked={selectedStage?.id === stage.id}
                    />
                  </RadioElementWrapper>
                ),
            )}
          </RadioButtonGroup>
        </AdvancedDialog.Middle>

        <AdvancedDialog.End css={{ padding: '10px $x4' }}>
          <Button size='s' onClick={handleSaveImageBtn} data-cy='save-selected-phase-image-btn'>
            {t('saveBtn')}
          </Button>
        </AdvancedDialog.End>
      </AdvancedDialog.Content>
    </AdvancedDialog>
  );
};
