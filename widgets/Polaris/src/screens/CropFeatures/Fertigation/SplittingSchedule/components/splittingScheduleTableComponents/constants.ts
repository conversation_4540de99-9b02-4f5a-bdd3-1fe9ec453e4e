import {
  FertigationNutrientKeyAndFormData,
  GrowthScaleStage,
  FertigationConfigurationSingleSplit,
} from '@common/types';

export interface SplittingScheduleVerticalTableBodyProps {
  tKeyPrefix: string;
  selectedSplittingScheduleSplits: FertigationConfigurationSingleSplit[];
  growthScaleStagesData?: GrowthScaleStage[];
  useUpdateSplit: (splitId: string, payload: FertigationConfigurationSingleSplit) => Promise<void>;
  useDeleteSplit: (payload: FertigationConfigurationSingleSplit) => Promise<void>;
  refetchSplittingConfigurations: () => void;
  updateSelectedSplittingScheduleState: (
    splitId: string,
    result: FertigationConfigurationSingleSplit,
  ) => void;
  nutrientsAndForms?: (FertigationNutrientKeyAndFormData | undefined)[];
}

export type OnFertigationSplitChangeInput = {
  value: string;
  key: string;
  splitId: string | undefined;
  isNutrient: boolean;
};

export const totalsByNutrientsDefault = {
  n: 0,
  p: 0,
  k: 0,
  ca: 0,
  mg: 0,
  s: 0,
  b: 0,
  cu: 0,
  fe: 0,
  mn: 0,
  zn: 0,
  mo: 0,
};

export const NUTRIENTS_TABLE_COUNT = {
  ESSENTIAL: 6,
  ALL: 12,
};

export const FERTIGATION_SPLITS_THRESHOLDS = {
  MAX_DAYS: 365,
  MAX_PERCENTAGE: 100,
};

export const SPLIT_COL_KEYS = {
  GROWTH_PHASE_IMAGE: 'growthPhaseImage',
  NUTRIENT_SPLIT_PERCENT: 'growthPhaseNutrientSplitPercent',
  DAYS_DURATION: 'daysDuration',
  GROWTH_PHASE_NO: 'growthPhaseNo',
};

export const defaultEmptyFertigationSplittingScheduleSplit: FertigationConfigurationSingleSplit = {
  growthPhaseNo: 0,
  name: '',
  daysDuration: 0,
  mediaUri: null,
  nutrientsSplit: {
    n: {
      splitPercent: 0,
      nutrientId: '880abcda-5ee5-4068-879c-94489be314d5',
      nutrientFormId: '',
    },
    p: {
      splitPercent: 0,
      nutrientId: 'f2c275c4-1522-4524-8747-08ace254b155',
      nutrientFormId: '',
    },
    k: {
      splitPercent: 0,
      nutrientId: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
      nutrientFormId: '',
    },
    ca: {
      splitPercent: 0,
      nutrientId: '5fe008ca-d2d7-48a6-921b-8b53c781a4ab',
      nutrientFormId: '',
    },
    mg: {
      splitPercent: 0,
      nutrientId: 'd764880a-c5d8-4a9a-ab49-a2b3ba59970d',
      nutrientFormId: '',
    },
    s: {
      splitPercent: 0,
      nutrientId: '0163eeff-5d87-4749-bcf7-3e4be6732808',
      nutrientFormId: '',
    },
    b: {
      splitPercent: 0,
      nutrientId: '6990195e-b1a3-4218-90db-f38c9cfae633',
      nutrientFormId: '',
    },
    cu: {
      splitPercent: 0,
      nutrientId: '578e38c1-e641-4aa9-9a8e-6a428e55f1ec',
      nutrientFormId: '',
    },
    fe: {
      splitPercent: 0,
      nutrientId: '5c7eb818-06c1-4013-b6d2-2f02f1e54d44',
      nutrientFormId: '',
    },
    mn: {
      splitPercent: 0,
      nutrientId: '45b614a2-cce3-4c85-9c63-b459396e9aef',
      nutrientFormId: '',
    },
    zn: {
      splitPercent: 0,
      nutrientId: 'a85856aa-c3cf-433d-8357-4dbe0259d1f9',
      nutrientFormId: '',
    },
    mo: {
      splitPercent: 0,
      nutrientId: '953dcd7a-0de1-4997-8360-985a73af3cb6',
      nutrientFormId: '',
    },
  },
};
