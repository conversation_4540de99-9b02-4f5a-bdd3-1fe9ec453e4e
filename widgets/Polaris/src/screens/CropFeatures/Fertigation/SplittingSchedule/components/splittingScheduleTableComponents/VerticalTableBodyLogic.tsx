import React from 'react';
import { useMemo, useState } from 'react';
import { IconButton, Image } from '@yaradigitallabs/ahua-react';
import {
  FERTIGATION_SPLITS_THRESHOLDS,
  NUTRIENTS_TABLE_COUNT,
  OnFertigationSplitChangeInput,
  SPLIT_COL_KEYS,
  SplittingScheduleVerticalTableBodyProps,
} from './constants';
import {
  StyledDeleteIconOnHoverTableCell,
  StyledGrowthPhaseNoContentWrapper,
  StyledSplitInput,
  StyledTableCell,
  StyledTableCellWithErrorState,
  StyledTableHeadMain,
  StyledTableInputCell,
  StyledTableRow,
  StyledButton,
  ImageFrame,
} from '../../SplittingSchedule.styled';
import { useTranslation } from 'react-i18next';
import {
  calcTotalFertigationSplitsDays,
  calcTotalPercentagesByNutrient,
  isNutrientSplitKey,
  isCorrectSplitKey,
  getSingleSplitKey,
} from './utils';
import { formatValidDecimalsNumber, validateParameterNumber } from '@widgets/Polaris/utils';
import {
  ChangeEvent,
  MediaUri,
  ValidationStatus,
  FertigationConfigurationSingleSplit,
} from '@common/types';
import { useAppContext } from '@widgets/Polaris/src/providers';

const VerticalTableBodyLogic = ({
  tKeyPrefix,
  selectedSplittingScheduleSplits,
  useUpdateSplit,
  useDeleteSplit,
  refetchSplittingConfigurations,
  updateSelectedSplittingScheduleState,
  nutrientsAndForms,
}: SplittingScheduleVerticalTableBodyProps) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: `${tKeyPrefix}.headers`,
  });
  const { selectedMMMValidation } = useAppContext();
  const [expandTable, setExpandTable] = useState<boolean>(false);
  const [hasChange, setHasChange] = useState<boolean>(false);
  const [splitToDelete, setSplitToDelete] = useState<FertigationConfigurationSingleSplit | null>(
    null,
  );
  const [selectedSplit, setSelectedSplit] = useState<FertigationConfigurationSingleSplit | null>(
    null,
  );

  const [addImageDialogOpen, setAddImageDialogOpen] = useState<boolean>(false);
  const [selectedMediaUri, setSelectedMediaUri] = useState<MediaUri[] | null>();

  const calcTotalDays = useMemo(
    () => calcTotalFertigationSplitsDays(selectedSplittingScheduleSplits),
    [selectedSplittingScheduleSplits],
  );

  const calcTotalsByNutrient = useMemo(
    () => calcTotalPercentagesByNutrient(selectedSplittingScheduleSplits),
    [selectedSplittingScheduleSplits],
  );

  // Update state to trigger totals recalculation
  const onSplitChange = (payload: OnFertigationSplitChangeInput) => {
    const { key, value, splitId, isNutrient } = payload;

    let splitToUpdate = selectedSplittingScheduleSplits.find((split) => split.splitId === splitId);
    const formattedValue = formatValidDecimalsNumber(value, 3);
    const numericValue = Number(formattedValue);

    if (splitToUpdate && splitId) {
      if (isNutrient) {
        if (numericValue > FERTIGATION_SPLITS_THRESHOLDS.MAX_PERCENTAGE) {
          return;
        }

        const { nutrientsSplit } = splitToUpdate;
        if (isNutrientSplitKey(key)) {
          nutrientsSplit[key].splitPercent = formattedValue;
          splitToUpdate = { ...splitToUpdate, nutrientsSplit };
        }
      } else {
        const newValue: string | number =
          key === SPLIT_COL_KEYS.DAYS_DURATION ? numericValue : formattedValue;

        splitToUpdate = { ...splitToUpdate, [key]: newValue };
      }
      setHasChange(true);
      updateSelectedSplittingScheduleState(splitId, splitToUpdate);
    }
  };

  // Update BE if totals are in threshold
  const onSplitBlur = (splitId: string | undefined) => {
    if (!hasChange) return;
    const isDaysDurationValid = FERTIGATION_SPLITS_THRESHOLDS.MAX_DAYS >= calcTotalDays;
    const isNutrientPercentValid = !Object.values(calcTotalsByNutrient).some(
      (nutrientPercentage) =>
        typeof nutrientPercentage === 'number' &&
        nutrientPercentage > FERTIGATION_SPLITS_THRESHOLDS.MAX_PERCENTAGE,
    );

    if (!isDaysDurationValid || !isNutrientPercentValid) {
      return;
    }

    const splitToUpdate = selectedSplittingScheduleSplits.find(
      (split) => split.splitId === splitId,
    );

    if (splitToUpdate && splitId) {
      for (const splitKey of Object.keys(splitToUpdate.nutrientsSplit)) {
        if (isNutrientSplitKey(splitKey)) {
          splitToUpdate.nutrientsSplit[splitKey].splitPercent = Number(
            splitToUpdate.nutrientsSplit[splitKey].splitPercent,
          );
        }
      }

      setHasChange(false);
      useUpdateSplit(splitId, splitToUpdate);
    }
  };

  const onSplitDelete = async () => {
    if (splitToDelete) {
      await useDeleteSplit(splitToDelete);
      await refetchSplittingConfigurations();
    }
    setSplitToDelete(null);
  };

  const onSaveSplitImage = (mediaUri?: MediaUri[] | null) => {
    if (!selectedSplit) return;

    const splitToUpdate: FertigationConfigurationSingleSplit = {
      ...selectedSplit,
      mediaUri: mediaUri ?? null,
    };

    if (splitToUpdate && splitToUpdate.splitId) {
      useUpdateSplit(splitToUpdate.splitId, splitToUpdate);
    }
    setSelectedSplit(null);
  };

  const extractRowDataByProperty = (property: string, isNutrient = false) => {
    return selectedSplittingScheduleSplits.map((result) => {
      if (isNutrient) {
        return (
          <StyledTableInputCell key={result.splitId}>
            <StyledSplitInput
              size='s'
              value={
                isNutrientSplitKey(property) ? result.nutrientsSplit[property].splitPercent : ''
              }
              onChange={(event: ChangeEvent) => {
                const isValid = validateParameterNumber(event.target.value);
                if (!isValid) return;

                onSplitChange({
                  value: event.target.value,
                  key: property,
                  splitId: result.splitId,
                  isNutrient: true,
                });
              }}
              onBlur={() => onSplitBlur(result.splitId)}
              data-cy={`fertigation-splitting-schedule-nutrient-cell-${property}-${result.splitId}-input`}
            />
          </StyledTableInputCell>
        );
      }

      if (property === SPLIT_COL_KEYS.NUTRIENT_SPLIT_PERCENT) {
        return (
          <StyledTableCell key={result.splitId}>
            {t('growthPhaseNutrientSplitPercent')}
          </StyledTableCell>
        );
      }

      if (property === SPLIT_COL_KEYS.GROWTH_PHASE_IMAGE) {
        return (
          <StyledTableCell
            key={result.splitId}
            css={{
              position: 'relative',
              boxSizing: 'border-box',
              padding: 0,
              '&:hover': {
                border: '1px solid $blue60 !important',
              },
            }}
          >
            {result.mediaUri?.[0]?.value && (
              <ImageFrame>
                <Image
                  src={result.mediaUri[0].value}
                  data-cy='growth-stage-image'
                  width={100}
                  ratio={1.38}
                />
              </ImageFrame>
            )}
            <StyledButton
              data-cy='fertigation-splitting-add-image-button'
              label={t('addImageButton')}
              variant='ghost'
              className={result.mediaUri?.[0]?.value ? 'ghost' : 'visible'}
              size='s'
              onClick={() => {
                setSelectedSplit(result);
                setSelectedMediaUri(result.mediaUri);
                setAddImageDialogOpen(true);
              }}
              iconLeading='Plus'
            />
          </StyledTableCell>
        );
      }

      if (property === SPLIT_COL_KEYS.DAYS_DURATION) {
        return (
          <StyledTableInputCell key={result.splitId}>
            <StyledSplitInput
              size='s'
              value={result.daysDuration}
              onChange={(event: ChangeEvent) => {
                const isValid = validateParameterNumber(event.target.value);
                if (!isValid) return;

                onSplitChange({
                  value: event.target.value,
                  key: property,
                  splitId: result.splitId,
                  isNutrient: false,
                });
              }}
              onBlur={() => onSplitBlur(result.splitId)}
              data-cy={`fertigation-splitting-schedule-cell-${property}-${result.splitId}-input`}
              variant={
                result.daysDuration < 1 &&
                selectedMMMValidation?.validationStatus === ValidationStatus.FAILED
                  ? 'error'
                  : 'default'
              }
            />
          </StyledTableInputCell>
        );
      }

      if (property === SPLIT_COL_KEYS.GROWTH_PHASE_NO) {
        return (
          <StyledDeleteIconOnHoverTableCell
            className={selectedSplittingScheduleSplits.length > 1 ? 'delete-icon-on-hover' : ''}
            key={result.splitId}
          >
            <StyledGrowthPhaseNoContentWrapper>
              {result.growthPhaseNo}
              {selectedSplittingScheduleSplits.length > 1 && (
                <IconButton
                  className={'delete-split'}
                  onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    setSplitToDelete(result);
                  }}
                  colorConcept={'neutral'}
                  icon='Delete'
                  size='s'
                  data-cy='fertigation-split-delete-button'
                />
              )}
            </StyledGrowthPhaseNoContentWrapper>
          </StyledDeleteIconOnHoverTableCell>
        );
      }

      const singleSplitKey = getSingleSplitKey(property);
      return (
        <StyledTableInputCell key={result.splitId}>
          <StyledSplitInput
            size='s'
            value={
              singleSplitKey && isCorrectSplitKey(singleSplitKey) ? result[singleSplitKey] : ''
            }
            onChange={(event: ChangeEvent) =>
              onSplitChange({
                value: event.target.value,
                key: property,
                splitId: result.splitId,
                isNutrient: false,
              })
            }
            onBlur={() => onSplitBlur(result.splitId)}
            data-cy={`fertigation-splitting-schedule-cell-${property}-${result.splitId}-input`}
            variant={
              singleSplitKey &&
              !result[singleSplitKey] &&
              selectedMMMValidation?.validationStatus === ValidationStatus.FAILED
                ? 'error'
                : 'default'
            }
          />
        </StyledTableInputCell>
      );
    });
  };

  const nutrientsRows = useMemo(() => {
    if (!nutrientsAndForms || !nutrientsAndForms.every((item) => item !== undefined)) {
      return null;
    }
    const finalNutrients = nutrientsAndForms.slice(
      0,
      expandTable ? NUTRIENTS_TABLE_COUNT.ALL : NUTRIENTS_TABLE_COUNT.ESSENTIAL,
    );

    return finalNutrients.map((nutrientData, index) => (
      <StyledTableRow key={index}>
        <StyledTableHeadMain colSpan={1}>{nutrientData.nutrientFormName}</StyledTableHeadMain>

        <StyledTableCellWithErrorState
          className={
            calcTotalsByNutrient[nutrientData.nutrientKey] >
            FERTIGATION_SPLITS_THRESHOLDS.MAX_PERCENTAGE
              ? 'validation-error'
              : ''
          }
          colSpan={1}
        >
          {`${calcTotalsByNutrient[nutrientData.nutrientKey]}`}
        </StyledTableCellWithErrorState>

        {extractRowDataByProperty(nutrientData.nutrientKey, true)}
      </StyledTableRow>
    ));
  }, [expandTable, calcTotalsByNutrient, nutrientsAndForms]);

  return {
    calcTotalDays,
    nutrientsRows,
    expandTable,
    splitToDelete,
    setExpandTable,
    extractRowDataByProperty,
    onSplitDelete,
    setSplitToDelete,
    addImageDialogOpen,
    setAddImageDialogOpen,
    selectedMediaUri,
    onSaveSplitImage,
  };
};

export default VerticalTableBodyLogic;
