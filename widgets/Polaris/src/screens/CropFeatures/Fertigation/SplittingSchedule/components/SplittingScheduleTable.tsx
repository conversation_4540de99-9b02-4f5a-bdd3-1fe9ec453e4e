import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  SplittingScheduleTableWrapper,
  StyledDiv,
  AddButton,
  StyledTable,
} from '../SplittingSchedule.styled';
import { SplittingScheduleVerticalTableBody } from './splittingScheduleTableComponents/VerticalTableBody';
import { Card } from '@yaradigitallabs/ahua-react';
import { defaultEmptyFertigationSplittingScheduleSplit } from './splittingScheduleTableComponents/constants';
import {
  FertigationNutrientKeyAndFormData,
  GrowthScaleStage,
  FertigationConfigurationSingleSplit,
  Splitting,
} from '@common/types';
import { constructNewFertigationSplittingScheduleSplit } from './splittingScheduleTableComponents/utils';

interface SplittingScheduleProps {
  tKeyPrefix: string;
  selectedFertigationSplittingSchedules: Splitting | null | undefined;
  growthScaleStagesData: GrowthScaleStage[] | [];
  useUpdateSplit: (splitId: string, payload: FertigationConfigurationSingleSplit) => Promise<void>;
  useAddSplit: (payload: FertigationConfigurationSingleSplit) => Promise<void>;
  useDeleteSplit: (payload: FertigationConfigurationSingleSplit) => Promise<void>;
  refetchSplittingConfigurations: () => void;
  updateSelectedSplittingScheduleState: (
    splitId: string,
    result: FertigationConfigurationSingleSplit,
  ) => void;
  nutrientsAndForms?: FertigationNutrientKeyAndFormData[];
}

export const SplittingScheduleTable: React.FC<SplittingScheduleProps> = ({
  tKeyPrefix,
  selectedFertigationSplittingSchedules,
  growthScaleStagesData,
  useUpdateSplit,
  useAddSplit,
  useDeleteSplit,
  refetchSplittingConfigurations,
  updateSelectedSplittingScheduleState,
  nutrientsAndForms,
}) => {
  const tableTKeyPrefix = `${tKeyPrefix}.table`;
  const { t } = useTranslation('polaris', { keyPrefix: tableTKeyPrefix });

  const selectedSplittingScheduleSplits = useMemo(
    () => selectedFertigationSplittingSchedules?.configuration.data ?? [],
    [selectedFertigationSplittingSchedules],
  );

  const newSplitPayload = useMemo(() => {
    return constructNewFertigationSplittingScheduleSplit(
      nutrientsAndForms,
      defaultEmptyFertigationSplittingScheduleSplit,
    );
  }, [nutrientsAndForms]);

  const tableTitle = (
    <Card.Head data-cy='fertigation-splitting-pre-table-title' title={t(`title`)}>
      <Card.HeadActions>
        <AddButton
          onClick={() =>
            newSplitPayload &&
            useAddSplit({
              ...newSplitPayload,
              growthPhaseNo: selectedSplittingScheduleSplits.length + 1,
              name: `Growth phase ${selectedSplittingScheduleSplits.length + 1}`,
            })
          }
          variant='ghost'
          title={t(`addSplitButton`)}
          iconLeading='Plus'
          type='button'
          size='s'
          data-cy='fertigation-splitting-add-split-button'
        >
          {t(`addSplitButton`)}
        </AddButton>
      </Card.HeadActions>
    </Card.Head>
  );

  return (
    <SplittingScheduleTableWrapper data-cy='fertigation-splitting-schedule-content'>
      {tableTitle}

      <StyledDiv>
        <StyledTable data-cy='fertigation-splitting-schedule-table'>
          <SplittingScheduleVerticalTableBody
            tKeyPrefix={tableTKeyPrefix}
            selectedSplittingScheduleSplits={selectedSplittingScheduleSplits}
            growthScaleStagesData={growthScaleStagesData}
            useUpdateSplit={useUpdateSplit}
            useDeleteSplit={useDeleteSplit}
            refetchSplittingConfigurations={refetchSplittingConfigurations}
            updateSelectedSplittingScheduleState={updateSelectedSplittingScheduleState}
            nutrientsAndForms={nutrientsAndForms}
          />
        </StyledTable>
      </StyledDiv>
    </SplittingScheduleTableWrapper>
  );
};
