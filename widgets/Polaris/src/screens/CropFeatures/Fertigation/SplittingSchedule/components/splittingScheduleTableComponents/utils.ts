import { totalsByNutrientsDefault } from './constants';
import { defaultEmptyFertigationSplittingScheduleSplit } from './constants';
import {
  FertigationNutrientKeyAndFormData,
  FertigationConfigurationNutrientsSplitDetails,
  FertigationConfigurationSingleSplit,
} from '@common/types';

export const calcTotalFertigationSplitsDays = (
  fertigationSplittingScheduleSplits: Array<FertigationConfigurationSingleSplit>,
) => {
  return fertigationSplittingScheduleSplits.reduce<number>((total, current) => {
    total += current.daysDuration;
    return total;
  }, 0);
};

export const calcTotalPercentagesByNutrient = (
  fertigationSplittingScheduleSplits: Array<FertigationConfigurationSingleSplit>,
) => {
  const deepCopyTotalsByNutrients = JSON.parse(JSON.stringify(totalsByNutrientsDefault));

  fertigationSplittingScheduleSplits.map((data) => {
    for (const [key, value] of Object.entries(data.nutrientsSplit)) {
      // Using toFixed to help javascript with floating point math, otherwise sometimes there would be
      // a lot of decimals all of a sudden (reference https://0.30000000000000004.com/)
      deepCopyTotalsByNutrients[key] = Number(
        (deepCopyTotalsByNutrients[key] + Number(value.splitPercent)).toFixed(3),
      );
    }
  });

  return deepCopyTotalsByNutrients;
};

export const constructNewFertigationSplittingScheduleSplit = (
  nutrientsAndForms: FertigationNutrientKeyAndFormData[] | undefined,
  defaultEmptySplit: FertigationConfigurationSingleSplit,
) => {
  if (!nutrientsAndForms || nutrientsAndForms.length === 0) {
    return;
  }

  const nutrientsSplit = nutrientsAndForms.reduce<FertigationConfigurationNutrientsSplitDetails>(
    (acc, nutrient) => {
      if (isNutrientSplitKey(nutrient.nutrientKey)) {
        acc[nutrient.nutrientKey] = {
          ...defaultEmptySplit.nutrientsSplit[nutrient.nutrientKey],
          nutrientId: defaultEmptySplit.nutrientsSplit[nutrient.nutrientKey].nutrientId,
          nutrientFormId: nutrient.nutrientFormId,
        };
      }
      return acc;
    },
    defaultEmptyFertigationSplittingScheduleSplit.nutrientsSplit,
  );

  return {
    ...defaultEmptyFertigationSplittingScheduleSplit,
    nutrientsSplit,
  };
};

export const isNutrientSplitKey = (key: string) =>
  key === 'n' ||
  key === 'p' ||
  key === 'k' ||
  key === 'ca' ||
  key === 'mg' ||
  key === 's' ||
  key === 'b' ||
  key === 'cu' ||
  key === 'fe' ||
  key === 'mn' ||
  key === 'zn' ||
  key === 'mo';

export const getSingleSplitKey = (
  key: string,
): keyof FertigationConfigurationSingleSplit | undefined => {
  if (
    key === 'splitId' ||
    key === 'growthPhaseNo' ||
    key === 'name' ||
    key === 'daysDuration' ||
    key === 'mediaUri' ||
    key === 'nutrientsSplit'
  ) {
    return key;
  }
};

export const isCorrectSplitKey = (key: keyof FertigationConfigurationSingleSplit) =>
  key === 'splitId' || key === 'growthPhaseNo' || key === 'name' || key === 'daysDuration';
