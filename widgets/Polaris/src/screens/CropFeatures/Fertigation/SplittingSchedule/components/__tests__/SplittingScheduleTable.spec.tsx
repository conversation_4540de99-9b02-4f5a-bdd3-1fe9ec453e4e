import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { SplittingScheduleTable } from '../SplittingScheduleTable';
import userEvent from '@testing-library/user-event';
import { useFetchSplittingConfigurationResponse } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

const postUpdateSplittingObject = {
  daysDuration: 2,
  growthPhaseNo: 1,
  mediaUri: [
    {
      key: 'DEFAULT',
      value:
        'https://polaris-axial-static-medias.yarapolaris.com/growth_scale/sugar_cane_second_year/6.svg',
    },
  ],
  name: '',
  nutrientsSplit: {
    n: {
      splitPercent: 0,
      nutrientId: '880abcda-5ee5-4068-879c-94489be314d5',
      nutrientFormId: '393f6e30-0af3-4336-a65f-953701d1173a',
    },
    p: {
      splitPercent: 0,
      nutrientId: 'f2c275c4-1522-4524-8747-08ace254b155',
      nutrientFormId: '3eacac5f-d65d-4248-89c9-8d7f9337ebe9',
    },
    k: {
      splitPercent: 0,
      nutrientId: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
      nutrientFormId: 'ae2132a8-f1e6-4170-877c-ab8ccc2c6656',
    },
    ca: {
      splitPercent: 0,
      nutrientId: '5fe008ca-d2d7-48a6-921b-8b53c781a4ab',
      nutrientFormId: 'c93686e7-d4c5-4b37-9733-c00a882e7d56',
    },
    mg: {
      splitPercent: 0,
      nutrientId: 'd764880a-c5d8-4a9a-ab49-a2b3ba59970d',
      nutrientFormId: '041057eb-e8d3-492f-b82f-656e93b32800',
    },
    s: {
      splitPercent: 0,
      nutrientId: '0163eeff-5d87-4749-bcf7-3e4be6732808',
      nutrientFormId: '2a3d91f7-e1f2-4811-944b-32da80507d6b',
    },
    b: {
      splitPercent: 0,
      nutrientId: '6990195e-b1a3-4218-90db-f38c9cfae633',
      nutrientFormId: '0ca37d40-bb10-45b4-b2a3-664072b4693b',
    },
    cu: {
      splitPercent: 0,
      nutrientId: '578e38c1-e641-4aa9-9a8e-6a428e55f1ec',
      nutrientFormId: '168bc22c-a9ad-4cce-9ede-5b465aa48754',
    },
    fe: {
      splitPercent: 0,
      nutrientId: '5c7eb818-06c1-4013-b6d2-2f02f1e54d44',
      nutrientFormId: '9d3fc031-9c67-46c0-a259-126a2db5863d',
    },
    mn: {
      splitPercent: 0,
      nutrientId: '45b614a2-cce3-4c85-9c63-b459396e9aef',
      nutrientFormId: '46dc12da-31c5-4611-a7c3-a88344f7d682',
    },
    zn: {
      splitPercent: 0,
      nutrientId: 'a85856aa-c3cf-433d-8357-4dbe0259d1f9',
      nutrientFormId: '0a9927a7-c44d-417d-bdb1-c2e63b1689e3',
    },
    mo: {
      splitPercent: 0,
      nutrientId: '953dcd7a-0de1-4997-8360-985a73af3cb6',
      nutrientFormId: '3a65cae7-d5cf-46e4-be63-b69171d367da',
    },
  },
  splitId: '4e92604c-03da-4b6a-ac9e-a94ea1723a57',
};
const secondDefaultAddSplittingObject = {
  daysDuration: 0,
  growthPhaseNo: 2,
  mediaUri: null,
  name: 'Growth phase 2',
  nutrientsSplit: {
    n: {
      splitPercent: 0,
      nutrientId: '880abcda-5ee5-4068-879c-94489be314d5',
      nutrientFormId: '393f6e30-0af3-4336-a65f-953701d1173a',
    },
    p: {
      splitPercent: 0,
      nutrientId: 'f2c275c4-1522-4524-8747-08ace254b155',
      nutrientFormId: '3eacac5f-d65d-4248-89c9-8d7f9337ebe9',
    },
    k: {
      splitPercent: 0,
      nutrientId: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
      nutrientFormId: 'ae2132a8-f1e6-4170-877c-ab8ccc2c6656',
    },
    ca: {
      splitPercent: 0,
      nutrientId: '5fe008ca-d2d7-48a6-921b-8b53c781a4ab',
      nutrientFormId: 'c93686e7-d4c5-4b37-9733-c00a882e7d56',
    },
    mg: {
      splitPercent: 0,
      nutrientId: 'd764880a-c5d8-4a9a-ab49-a2b3ba59970d',
      nutrientFormId: '041057eb-e8d3-492f-b82f-656e93b32800',
    },
    s: {
      splitPercent: 0,
      nutrientId: '0163eeff-5d87-4749-bcf7-3e4be6732808',
      nutrientFormId: '2a3d91f7-e1f2-4811-944b-32da80507d6b',
    },
    b: {
      splitPercent: 0,
      nutrientId: '6990195e-b1a3-4218-90db-f38c9cfae633',
      nutrientFormId: '0ca37d40-bb10-45b4-b2a3-664072b4693b',
    },
    cu: {
      splitPercent: 0,
      nutrientId: '578e38c1-e641-4aa9-9a8e-6a428e55f1ec',
      nutrientFormId: '168bc22c-a9ad-4cce-9ede-5b465aa48754',
    },
    fe: {
      splitPercent: 0,
      nutrientId: '5c7eb818-06c1-4013-b6d2-2f02f1e54d44',
      nutrientFormId: '9d3fc031-9c67-46c0-a259-126a2db5863d',
    },
    mn: {
      splitPercent: 0,
      nutrientId: '45b614a2-cce3-4c85-9c63-b459396e9aef',
      nutrientFormId: '46dc12da-31c5-4611-a7c3-a88344f7d682',
    },
    zn: {
      splitPercent: 0,
      nutrientId: 'a85856aa-c3cf-433d-8357-4dbe0259d1f9',
      nutrientFormId: '0a9927a7-c44d-417d-bdb1-c2e63b1689e3',
    },
    mo: {
      splitPercent: 0,
      nutrientId: '953dcd7a-0de1-4997-8360-985a73af3cb6',
      nutrientFormId: '3a65cae7-d5cf-46e4-be63-b69171d367da',
    },
  },
};

const mockNutrientsAndForms = [
  {
    nutrientKey: 'n',
    nutrientFormId: '393f6e30-0af3-4336-a65f-953701d1173a',
    nutrientFormName: 'N',
    selected: true,
  },
  {
    nutrientKey: 'p',
    nutrientFormId: '3eacac5f-d65d-4248-89c9-8d7f9337ebe9',
    nutrientFormName: 'P2O5',
    selected: true,
  },
  {
    nutrientKey: 'k',
    nutrientFormId: 'ae2132a8-f1e6-4170-877c-ab8ccc2c6656',
    nutrientFormName: 'K2O',
    selected: true,
  },
  {
    nutrientKey: 'ca',
    nutrientFormId: 'c93686e7-d4c5-4b37-9733-c00a882e7d56',
    nutrientFormName: 'CaO',
    selected: true,
  },
  {
    nutrientKey: 'mg',
    nutrientFormId: '041057eb-e8d3-492f-b82f-656e93b32800',
    nutrientFormName: 'MgO',
    selected: true,
  },
  {
    nutrientKey: 's',
    nutrientFormId: '2a3d91f7-e1f2-4811-944b-32da80507d6b',
    nutrientFormName: 'SO3',
    selected: true,
  },
  {
    nutrientKey: 'b',
    nutrientFormId: '0ca37d40-bb10-45b4-b2a3-664072b4693b',
    nutrientFormName: 'B',
    selected: true,
  },
  {
    nutrientKey: 'cu',
    nutrientFormId: '168bc22c-a9ad-4cce-9ede-5b465aa48754',
    nutrientFormName: 'Cu',
    selected: true,
  },
  {
    nutrientKey: 'fe',
    nutrientFormId: '9d3fc031-9c67-46c0-a259-126a2db5863d',
    nutrientFormName: 'Fe',
    selected: true,
  },
  {
    nutrientKey: 'mn',
    nutrientFormId: '46dc12da-31c5-4611-a7c3-a88344f7d682',
    nutrientFormName: 'Mn',
    selected: true,
  },
  {
    nutrientKey: 'zn',
    nutrientFormId: '0a9927a7-c44d-417d-bdb1-c2e63b1689e3',
    nutrientFormName: 'Zn',
    selected: true,
  },
  {
    nutrientKey: 'mo',
    nutrientFormId: '3a65cae7-d5cf-46e4-be63-b69171d367da',
    nutrientFormName: 'Mo',
    selected: true,
  },
];

const mockSetDisplaySnackbar = jest.fn();

jest.mock('@auth0/auth0-react', () => ({
  ...jest.requireActual('@auth0/auth0-react'),
  useAuth0: () => ({
    getAccessTokenSilently: jest.fn(),
  }),
}));

jest.mock('@libs/snackbar-context/snackbar-context', () => ({
  ...jest.requireActual('@libs/snackbar-context/snackbar-context'),
  useSnackbar: () => ({
    setDisplaySnackbar: mockSetDisplaySnackbar,
  }),
}));

const setSelectedFertigationSplittingSchedulesMock = jest.fn();
const useGetSplittingConfigurationsMock = jest.fn();
const refetchSplittingConfigurationsMock = jest.fn();
const useUpdateStartDateMock = jest.fn();
const useUpdateSplitMock = jest.fn();
const updateSelectedSplittingScheduleStateMock = jest.fn();
const useAddSplitMock = jest.fn();
const useDeleteSplitMock = jest.fn();
const mockData = {
  selectedFertigationSplittingSchedules: useFetchSplittingConfigurationResponse.entities[0],
  setSelectedFertigationSplittingSchedules: setSelectedFertigationSplittingSchedulesMock,
  useGetSplittingConfigurations: useGetSplittingConfigurationsMock,
  refetchSplittingConfigurations: refetchSplittingConfigurationsMock,
  useUpdateStartDate: useUpdateStartDateMock,
  useUpdateSplit: useUpdateSplitMock,
  updateSelectedSplittingScheduleState: updateSelectedSplittingScheduleStateMock,
  useAddSplit: useAddSplitMock,
  useDeleteSplit: useDeleteSplitMock,
};
jest.mock('../../SplittingScheduleHooksLogic', () => ({
  SplittingScheduleHooksLogic: jest.fn(() => mockData),
}));

describe('FertigationSplittingScheduleTable', () => {
  afterEach(() => {
    mockSetDisplaySnackbar.mockClear();
  });
  const useUpdateSplitMock = jest.fn();
  const refetchSplittingConfigurationsMock = jest.fn();
  const updateSelectedSplittingScheduleStateMock = jest.fn();
  const useAddSplitMock = jest.fn();
  const useDeleteSplitMock = jest.fn();

  it('renders without crashing', async () => {
    const component = render(
      <SplittingScheduleTable
        selectedFertigationSplittingSchedules={useFetchSplittingConfigurationResponse.entities[0]}
        tKeyPrefix={''}
        useUpdateSplit={useUpdateSplitMock}
        useAddSplit={useAddSplitMock}
        useDeleteSplit={useDeleteSplitMock}
        refetchSplittingConfigurations={refetchSplittingConfigurationsMock}
        updateSelectedSplittingScheduleState={updateSelectedSplittingScheduleStateMock}
        growthScaleStagesData={[]}
        nutrientsAndForms={mockNutrientsAndForms}
      />,
    );

    const addSplitButtonComponent = component.getByTestId('fertigation-splitting-add-split-button');
    expect(addSplitButtonComponent).toBeInTheDocument();

    const tableComponent = component.getByTestId('fertigation-splitting-schedule-table');
    expect(tableComponent).toBeInTheDocument();
  });

  it('verify update method is called on user input', async () => {
    const component = render(
      <SplittingScheduleTable
        selectedFertigationSplittingSchedules={useFetchSplittingConfigurationResponse.entities[0]}
        tKeyPrefix={''}
        useUpdateSplit={useUpdateSplitMock}
        useAddSplit={useAddSplitMock}
        useDeleteSplit={useDeleteSplitMock}
        refetchSplittingConfigurations={refetchSplittingConfigurationsMock}
        updateSelectedSplittingScheduleState={updateSelectedSplittingScheduleStateMock}
        nutrientsAndForms={mockNutrientsAndForms}
        growthScaleStagesData={[]}
      />,
    );

    waitFor(() => {
      const daysDurationInput = component.getByTestId(
        'fertigation-splitting-schedule-cell-daysDuration-4e92604c-03da-4b6a-ac9e-a94ea1723a57-input',
      ) as HTMLInputElement;
      fireEvent.change(daysDurationInput, { target: { value: 7 } });
      expect(updateSelectedSplittingScheduleStateMock).toHaveBeenCalledWith(
        '4e92604c-03da-4b6a-ac9e-a94ea1723a57',
        postUpdateSplittingObject,
      );
      fireEvent.blur(daysDurationInput);
      expect(useUpdateSplitMock).toHaveBeenCalledWith(
        '4e92604c-03da-4b6a-ac9e-a94ea1723a57',
        postUpdateSplittingObject,
      );
    });
  });

  it('verify new split addition on add split button click', async () => {
    const component = render(
      <SplittingScheduleTable
        selectedFertigationSplittingSchedules={useFetchSplittingConfigurationResponse.entities[0]}
        tKeyPrefix={''}
        useUpdateSplit={useUpdateSplitMock}
        useAddSplit={useAddSplitMock}
        useDeleteSplit={useDeleteSplitMock}
        refetchSplittingConfigurations={refetchSplittingConfigurationsMock}
        updateSelectedSplittingScheduleState={updateSelectedSplittingScheduleStateMock}
        nutrientsAndForms={mockNutrientsAndForms}
        growthScaleStagesData={[]}
      />,
    );

    const addSplitButtonComponent = component.getByTestId('fertigation-splitting-add-split-button');
    await userEvent.click(addSplitButtonComponent);

    expect(useAddSplitMock).toHaveBeenCalledWith(secondDefaultAddSplittingObject);
  });
});
