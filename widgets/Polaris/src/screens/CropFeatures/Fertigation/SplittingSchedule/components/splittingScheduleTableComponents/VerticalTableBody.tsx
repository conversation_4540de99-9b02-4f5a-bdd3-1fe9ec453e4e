import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  SoloStyledTableHead,
  StyledAlignWrapper,
  StyledInfoIcon,
  StyledTableCellWithErrorState,
  StyledTableHeadMain,
  StyledTableHeadSecondary,
  StyledTableRow,
} from '../../SplittingSchedule.styled';
import { Button } from '@yaradigitallabs/ahua-react';
import {
  FERTIGATION_SPLITS_THRESHOLDS,
  SplittingScheduleVerticalTableBodyProps,
} from './constants';
import VerticalTableBodyLogic from './VerticalTableBodyLogic';
import { AddImageDialog } from './AddImageDialog';
import { ConfirmationDialog, TooltipWrapper } from '@widgets/Polaris/src/components';

export const SplittingScheduleVerticalTableBody: React.FC<
  SplittingScheduleVerticalTableBodyProps
> = ({
  tKeyPrefix,
  selectedSplittingScheduleSplits,
  growthScaleStagesData,
  useUpdateSplit,
  useDeleteSplit,
  refetchSplittingConfigurations,
  updateSelectedSplittingScheduleState,
  nutrientsAndForms,
}) => {
  const { t, i18n } = useTranslation('polaris', {
    keyPrefix: `${tKeyPrefix}.headers`,
  });
  const commonT = (key: string) => i18n.t(`polaris.common.${key}`);

  const {
    calcTotalDays,
    nutrientsRows,
    expandTable,
    splitToDelete,
    setExpandTable,
    extractRowDataByProperty,
    onSplitDelete,
    setSplitToDelete,
    addImageDialogOpen,
    setAddImageDialogOpen,
    selectedMediaUri,
    onSaveSplitImage,
  } = VerticalTableBodyLogic({
    tKeyPrefix,
    selectedSplittingScheduleSplits,
    useUpdateSplit,
    useDeleteSplit,
    refetchSplittingConfigurations,
    updateSelectedSplittingScheduleState,
    nutrientsAndForms,
  });

  return (
    <>
      <AddImageDialog
        isOpen={addImageDialogOpen}
        keyPrefix={tKeyPrefix}
        onClose={() => setAddImageDialogOpen(false)}
        selectedMediaUri={selectedMediaUri}
        growthPhaseStages={growthScaleStagesData}
        onSaveImage={onSaveSplitImage}
      />
      <StyledTableRow>
        <SoloStyledTableHead colSpan={2}>{t('growthPhaseNo')}</SoloStyledTableHead>

        {extractRowDataByProperty('growthPhaseNo')}
      </StyledTableRow>

      <StyledTableRow>
        <SoloStyledTableHead colSpan={2}>{t('growthPhaseName')}</SoloStyledTableHead>

        {extractRowDataByProperty('name')}
      </StyledTableRow>

      <StyledTableRow>
        <StyledTableHeadMain colSpan={1}>{t('growthPhaseDuration')}</StyledTableHeadMain>

        <StyledTableCellWithErrorState
          className={
            calcTotalDays > FERTIGATION_SPLITS_THRESHOLDS.MAX_DAYS ? 'validation-error' : ''
          }
          colSpan={1}
        >
          {`${calcTotalDays} ${t('growthPhaseTotalDays')}`}
        </StyledTableCellWithErrorState>

        {extractRowDataByProperty('daysDuration')}
      </StyledTableRow>

      <StyledTableRow style={{ height: '94px' }}>
        <SoloStyledTableHead colSpan={2}>{t('growthPhaseImage')}</SoloStyledTableHead>

        {extractRowDataByProperty('growthPhaseImage')}
      </StyledTableRow>

      <StyledTableRow>
        <StyledTableHeadMain colSpan={1}>{t('growthPhaseNutrient')}</StyledTableHeadMain>

        <StyledTableHeadSecondary colSpan={1}>
          <StyledAlignWrapper>
            {t('growthPhaseNutrientTotalPercent')}
            <TooltipWrapper
              showTooltip
              tooltipText={t('totalNutrientsTooltipMessage')}
              concept='inverse'
              dataCy='splitting-total-nutrients-tooltip-message'
              maxWidth={160}
            >
              <StyledInfoIcon icon={'Info'} iconSize={'x4'} colorConcept={'neutral'} />
            </TooltipWrapper>
          </StyledAlignWrapper>
        </StyledTableHeadSecondary>

        {extractRowDataByProperty('growthPhaseNutrientSplitPercent')}
      </StyledTableRow>

      {nutrientsRows}

      <StyledTableRow>
        <SoloStyledTableHead colSpan={2}>
          <Button
            style={{ fontWeight: 'var(--fontWeights-semiBold)' }}
            data-cy='fertigation-splitting-expand-button'
            label={t(expandTable ? `showLessButton` : `showMoreButton`)}
            size='s'
            variant='ghost'
            iconLeading={expandTable ? 'DropUp' : 'DropDown'}
            onClick={() => setExpandTable(!expandTable)}
          />
        </SoloStyledTableHead>
      </StyledTableRow>

      <ConfirmationDialog
        open={!!splitToDelete}
        title={t('dialog.title', {
          growthPhaseNo: splitToDelete?.growthPhaseNo,
        })}
        description={t('dialog.description')}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={commonT('yesDelete')}
        okButtonConcept='destructive'
        cancelButton={commonT('cancel')}
        onOk={onSplitDelete}
        onCancel={() => setSplitToDelete(null)}
      />
    </>
  );
};
