import {
  AhuaIcon,
  Card,
  Input,
  styled,
  Subtitle,
  Table,
  Button,
} from '@yaradigitallabs/ahua-react';

// Add split/image button
export const AddButton = styled(Card.Action, {
  '& svg': {
    width: '$x4',
    height: '$x4',
    minWidth: '$x4',
    minHeight: '$x4',
    marginRight: '$x1',
    strokeWidth: '10%',
  },
});

// Pre table title
export const StyledSubtitle = styled(Subtitle, {
  marginBottom: '14px',
  fontWeight: '$bold',
});

// Table wrappers
export const SplittingScheduleTableWrapper = styled('div', {
  paddingLeft: 0,
  position: 'relative',
});

export const StyledDiv = styled('div', {
  padding: '$x2',
  border: 'var(--borderWidths-x1) solid $border-tertiary-light',
  borderRadius: '$m',
});

export const StyledTable = styled(Table, {
  overflow: 'auto',
  display: 'block',
  borderCollapse: 'unset',
  [`& tr:last-child > th`]: {
    borderBottom: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',
  },
  [`& tr:nth-last-child(2) > th, & tr:nth-last-child(2) > td`]: {
    borderBottom: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light)',
  },
  [`& tr:last-child > th:last-child`]: {
    borderTop: '0 !important',
  },
});

// Table attributes
export const StyledTableRow = styled(Table.Row, {
  backgroundColor: '$white0 !important',
});

export const StyledTableHead = styled(Table.Head, {
  verticalAlign: 'middle',
  whiteSpace: 'break-spaces',
  fontWeight: '$semiBold !important',
  borderTop: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',
  borderLeft: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',

  backgroundColor: '$white100 !important',
  position: 'sticky',
  zIndex: 1,
});

export const SoloStyledTableHead = styled(StyledTableHead, {
  width: '295px',
  minWidth: '295px',
  left: 0,
  borderRight: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',
});

export const StyledTableHeadMain = styled(StyledTableHead, {
  width: '175px',
  minWidth: '175px',
  left: 0,
});

export const StyledTableHeadSecondary = styled(StyledTableHead, {
  width: '95px',
  minWidth: '95px',
  left: '200px',
  borderRight: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',
});

export const StyledTableCell = styled(Table.Cell, {
  width: '171px',
  minWidth: '171px',
  textAlign: 'left !important',
  fontWeight: '$semiBold !important',
  borderTop: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',
  borderRight: 'var(--borderWidths-x1) solid var(--colors-border-tertiary-light) !important',
});

export const StyledTableInputCell = styled(StyledTableCell, {
  paddingTop: 'var(--space-x2)',
  paddingBottom: 'var(--space-x2)',
});

// Editable cells

export const StyledGrowthPhaseNoContentWrapper = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  height: '16px',
});

export const StyledSplitInput = styled(Input, {
  fontSize: '$scale3',
  fontWeight: '$semiBold',
  textAlign: 'left',
  height: '40px !important',
});

export const StyledDeleteIconOnHoverTableCell = styled(StyledTableCell, {
  '&.delete-icon-on-hover .delete-split': {
    display: 'none !important',
    padding: 0,
  },
  '&.delete-icon-on-hover:hover .delete-split': {
    display: 'flex !important',
    padding: 0,
  },
  '&.delete-icon-on-hover:hover': {
    width: '196px',
    minWidth: '196px',
    border: 'var(--borderWidths-x1) solid var(--colors-blue50) !important',
  },
  '&.delete-icon-on-hover': {
    width: '197px',
    minWidth: '197px',
  },
});

export const StyledTableCellWithErrorState = styled(StyledTableHeadSecondary, {
  '&.validation-error': {
    border: 'var(--borderWidths-x1) solid var(--colors-border-input-error) !important',
    borderBottom: '1px solid var(--colors-border-input-error) !important',
  },
});

export const calendarStyles = {
  [`& tr`]: {
    backgroundColor: '$white0 !important',
  },
};

export const StyledInfoIcon = styled(AhuaIcon, {
  marginLeft: '$x1',
});

export const StyledAlignWrapper = styled('div', {
  display: 'flex',
  alignItems: 'center',
});

export const RadioElementWrapper = styled('div', {
  display: 'flex',
  alignItems: 'center',
  maxWidth: '100%',
  flexBasis: '100%',
  paddingLeft: '$x3',
  height: '$x12',
  justifyContent: 'space-between',
  boxSizing: 'border-box',
  lineHeight: '$scale4',
  padding: '$x3',
  cursor: 'pointer',
  borderRadius: '$m',
  '& label': {
    color: '$black100',
    alignSelf: 'baseline',
    padding: '$x1 $x3',
  },
  variants: {
    checked: {
      true: {
        border: '2px solid $blue50',
        backgroundColor: '$blue0',
      },
      false: {
        border: '1px solid $black20',
      },
    },
  },
  '& > div': {
    display: 'flex',
  },
});

export const ImageFrame = styled('div', {
  border: '1px solid $black15',
  borderRadius: '$xs',
  backgroundColor: '$white100',
  width: '106px',
  zIndex: '1',
  padding: '$xhalf',
  margin: '0 auto',
  boxSizing: 'border-box',
});

export const ImageFrameSmall = styled('div', {
  border: '0.5px solid $black15',
  backgroundColor: '$white100',
  borderRadius: '1px',
  height: '51px',
  width: '70px',
  padding: '1px',
});

export const StyledButton = styled(Button, {
  span: {
    overflow: 'inherit',
  },
  '&.visible': {
    display: 'flex',
  },
  '&.ghost': {
    color: 'transparent',
    position: 'absolute',
    left: '50%',
    top: '0',
    height: '100%',
    zIndex: '2',
    cursor: 'pointer',
    transform: 'translateX(-50%)',
  },
  '&.ghost:hover': {
    color: '$blue60',
    backgroundColor: '$white70',
  },
});

export const StyledCustomButtonDate = styled('div', {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'end',
  height: '$x5',
  padding: '$x1',
  cursor: 'default',
  variants: {
    type: {
      selected: {
        color: '$inverse-darkest',
        background: '$fill-action-primary-brand-enabled',
        borderRadius: '$xxl',
        padding: '$x3',
        width: '$x4',
      },
      disabled: {
        color: '$black50',
      },
      default: { cursor: 'pointer' },
    },
  },
});
