import { useTranslation } from 'react-i18next';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  useAddSplittingConfigurationSplit,
  useDeleteSplittingConfigurationSplit,
  useFetchDemandCalculations,
  useFetchNutrientsFromPolaris,
  useFetchSplittingConfiguration,
  useGrowthScaleStages,
  useUpdateSplittingConfigurationSplits,
  useUpdateSplittingConfigurationStartDate,
} from '@polaris-hooks/index';
import { METHOD } from '@common/constants';
import {
  FeatureConfigOptions,
  GrowthScaleStage,
  FertigationConfiguration,
  FertigationConfigurationSingleSplit,
  Splitting,
} from '@common/types';
import { useEffect, useMemo, useState } from 'react';
import { FilterOperatorType, FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { isEmpty, sortBy } from 'lodash';
import { FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS } from '../../shared/constants';
import { mapFertigationNutrientIdsToNutrientForms } from '../../shared/helpers/nutrientsListHelpers/nutrientsListHelpers';

export const SplittingScheduleHooksLogic = () => {
  const { setDisplaySnackbar } = useSnackbar();
  const { t, i18n } = useTranslation();
  const commonT = (key: string) => i18n.t(`polaris.common.${key}`);
  const [selectedFertigationSplittingSchedules, setSelectedFertigationSplittingSchedules] =
    useState<Splitting | null>();

  const [growthScaleStagesData, setGrowthScaleStagesData] = useState<GrowthScaleStage[] | []>();

  const {
    selectedCountry,
    cropRegion: selectedCropRegion,
    selectedFeature,
    demandCalculations,
    fertigationDemandCalculationNutrientsWithNutrientForms,
    selectedCountryUnits,
    methods: { setDemandCalculations, setFertigationDemandCalculationNutrientsWithNutrientForms },
  } = useAppContext();

  /*
  The logic below is needed to get the correct nutrient form names for the table and populate it with nutrient split values.
  Nutrient forms either come from the global state (fertigationDemandCalculationNutrientsWithNutrientForms)
  or are computed using Polaris units, Fertigation nutrients IDs and demand Calculations configurations.
  */
  const nutrientsAndForms = useMemo(() => {
    if (
      !fertigationDemandCalculationNutrientsWithNutrientForms ||
      isEmpty(fertigationDemandCalculationNutrientsWithNutrientForms)
    ) {
      return;
    }
    return FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.map((nutrientId) => {
      return fertigationDemandCalculationNutrientsWithNutrientForms[nutrientId].find(
        (nForm) => nForm.selected,
      );
    }).filter((nForm) => nForm !== undefined);
  }, [fertigationDemandCalculationNutrientsWithNutrientForms]);

  // Optional data fetching if there is no fertigationDemandCalculationNutrientsWithNutrientForms in the App state.
  const demandCalculationsFilter: GenericFilter[] | undefined = useMemo(() => {
    if (!selectedCountry?.id || !selectedCropRegion?.id) return;
    return [
      {
        key: 'countryId',
        value: selectedCountry.id,
        type: FilterType.EQ,
      },
      {
        key: 'cropRegionId',
        value: selectedCropRegion.id,
        type: FilterType.EQ,
      },
    ];
  }, [selectedCountry?.id, selectedCropRegion?.id]);
  const demandCalculationsData = useFetchDemandCalculations(
    FeatureConfigOptions.FERTIGATION,
    demandCalculationsFilter,
    Boolean(
      selectedCountry?.id &&
        selectedCropRegion?.id &&
        (!demandCalculations || isEmpty(demandCalculations)),
    ),
  );
  useEffect(() => {
    if (!demandCalculationsData) return;
    setDemandCalculations(demandCalculationsData);
  }, [demandCalculationsData]);

  const shouldFetchUnitsAndNutrients = useMemo(
    () =>
      !fertigationDemandCalculationNutrientsWithNutrientForms ||
      isEmpty(fertigationDemandCalculationNutrientsWithNutrientForms),
    [fertigationDemandCalculationNutrientsWithNutrientForms],
  );
  const nutrientsData = useFetchNutrientsFromPolaris(
    [
      {
        key: 'id',
        value: FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.join(','),
        type: FilterType.IN,
      },
    ],
    shouldFetchUnitsAndNutrients,
  );
  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      FERTIGATION_LEAF_AND_DEMAND_CALCULATION_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);

  useEffect(() => {
    const result = mapFertigationNutrientIdsToNutrientForms({
      nutrientList: sortedNutrients,
      unitList: selectedCountryUnits ?? undefined,
      demandCalculationConfigs: demandCalculations,
    });

    !isEmpty(result) && setFertigationDemandCalculationNutrientsWithNutrientForms(result);
  }, [selectedCountryUnits, demandCalculations, sortedNutrients]);
  /* End of nutrients to nutrient forms (re)computation logic */

  const { growthScaleIds } = selectedCropRegion?.featureGrowthScales?.find(
    ({ featureTagId }) => featureTagId === selectedFeature?.id,
  ) || { growthScaleIds: [] };

  const filtersStages: GenericFilter[] = growthScaleIds.map((id) => ({
    key: 'growthScaleId',
    type: FilterType.EQ,
    value: id,
  }));

  const growthScaleStages =
    filtersStages.length && useGrowthScaleStages(filtersStages, FilterOperatorType.OR);

  useEffect(() => {
    if (!growthScaleStages) return;
    setGrowthScaleStagesData(growthScaleStages);
  }, [growthScaleStages]);

  const filters: GenericFilter[] | null =
    selectedCountry && selectedCropRegion
      ? [
          {
            key: 'countryId',
            type: FilterType.EQ,
            value: selectedCountry.id,
          },
          {
            key: 'cropRegionId',
            type: FilterType.EQ,
            value: selectedCropRegion.id,
          },
        ]
      : null;

  const { trigger: fetchCropRegionSplittingSchedule } = useFetchSplittingConfiguration(
    FeatureConfigOptions.FERTIGATION,
  );
  const useGetSplittingConfigurations = () => {
    useEffect(() => {
      const fetchSplittingConfiguration = refetchSplittingConfigurations;

      filters && fetchSplittingConfiguration();
    }, [Boolean(filters)]);
  };

  const refetchSplittingConfigurations = async () => {
    try {
      await fetchCropRegionSplittingSchedule({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: filters || [],
        }),
      }).then((data) => {
        setSelectedFertigationSplittingSchedules(data?.entities[0] ?? null);
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const { trigger: triggerUpdateDate } = useUpdateSplittingConfigurationStartDate(
    selectedFertigationSplittingSchedules?.id,
  );
  const useUpdateStartDate = async (payload: Date) => {
    try {
      const result: { startDate: Date } | undefined = await triggerUpdateDate({
        method: METHOD.PUT,
        body: JSON.stringify({
          startDate: `${payload.getFullYear()}-${payload.getMonth() + 1}-${payload.getDate()}`,
        }),
      });

      if (result) {
        setDisplaySnackbar({
          title: commonT('changesSaved'),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
        });

        if (selectedFertigationSplittingSchedules && result.startDate) {
          const updatedConfiguration: FertigationConfiguration = {
            data: selectedFertigationSplittingSchedules.configuration.data,
            startDate: result.startDate,
          };
          setSelectedFertigationSplittingSchedules({
            ...selectedFertigationSplittingSchedules,
            configuration: updatedConfiguration,
          });
        }
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  const { trigger: triggerUpdateSplit } = useUpdateSplittingConfigurationSplits(
    selectedFertigationSplittingSchedules?.id,
  );
  const useUpdateSplit = async (splitId: string, payload: FertigationConfigurationSingleSplit) => {
    try {
      const result: FertigationConfigurationSingleSplit | undefined = await triggerUpdateSplit({
        method: METHOD.PUT,
        body: JSON.stringify(payload),
        extraUrl: `/${splitId}`,
      });

      if (result) {
        updateSelectedSplittingScheduleState(splitId, result);
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  const updateSelectedSplittingScheduleState = (
    splitId: string,
    result: FertigationConfigurationSingleSplit,
  ) => {
    if (selectedFertigationSplittingSchedules) {
      const updatedSplitConfiguration: FertigationConfigurationSingleSplit[] =
        selectedFertigationSplittingSchedules.configuration.data.map((split) => {
          if (split.splitId === splitId) {
            return result;
          }

          return split;
        });
      const updatedConfiguration: FertigationConfiguration = {
        data: updatedSplitConfiguration,
        startDate: selectedFertigationSplittingSchedules.configuration.startDate,
      };
      setSelectedFertigationSplittingSchedules({
        ...selectedFertigationSplittingSchedules,
        configuration: updatedConfiguration,
      });
    }
  };

  const { trigger: triggerAddSplit } = useAddSplittingConfigurationSplit(
    selectedFertigationSplittingSchedules?.id,
  );
  const useAddSplit = async (payload: FertigationConfigurationSingleSplit) => {
    try {
      const result: FertigationConfigurationSingleSplit | undefined = await triggerAddSplit({
        method: METHOD.POST,
        body: JSON.stringify(payload),
      });

      if (result) {
        setDisplaySnackbar({
          title: t('polaris.common.splitAdded', {
            growthPhaseNo: result.growthPhaseNo,
          }),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
        });

        if (selectedFertigationSplittingSchedules) {
          const updatedConfiguration: FertigationConfiguration = {
            data: [...selectedFertigationSplittingSchedules.configuration.data, result],
            startDate: selectedFertigationSplittingSchedules.configuration.startDate,
          };
          setSelectedFertigationSplittingSchedules({
            ...selectedFertigationSplittingSchedules,
            configuration: updatedConfiguration,
          });
        }
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  const { trigger: triggerDeleteSplit } = useDeleteSplittingConfigurationSplit(
    selectedFertigationSplittingSchedules?.id,
  );
  const useDeleteSplit = async (payload: FertigationConfigurationSingleSplit) => {
    try {
      const result: string | undefined = await triggerDeleteSplit({
        method: METHOD.DELETE,
        extraUrl: `/${payload.splitId}/perm`,
      });

      if (result) {
        setDisplaySnackbar({
          title: t('polaris.common.splitRemoved', {
            growthPhaseNo: payload.growthPhaseNo,
          }),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
        });
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  return {
    selectedFertigationSplittingSchedules,
    growthScaleStagesData,
    useGetSplittingConfigurations,
    useUpdateStartDate,
    useUpdateSplit,
    useAddSplit,
    useDeleteSplit,
    refetchSplittingConfigurations,
    updateSelectedSplittingScheduleState,
    nutrientsAndForms,
  };
};

export default SplittingScheduleHooksLogic;
