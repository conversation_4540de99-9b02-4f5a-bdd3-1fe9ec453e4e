import React, { useMemo } from 'react';
import { SplittingScheduleTable } from './components/SplittingScheduleTable';
import { Calendar } from '@yaradigitallabs/ahua-react';
import { SplittingScheduleHooksLogic } from './SplittingScheduleHooksLogic';
import { calendarStyles, StyledCustomButtonDate } from './SplittingSchedule.styled';
import { useTranslation } from 'react-i18next';
import { CalendarDayCellProps } from '@yaradigitallabs/ahua-react/src/lib/calendar/calendar-day';

const SplittingSchedule = () => {
  const tKeyPrefix = 'polaris.fpDetails.planConfiguration.pages.splittingSchedule';
  const { t } = useTranslation('polaris', { keyPrefix: tKeyPrefix });

  const {
    selectedFertigationSplittingSchedules,
    growthScaleStagesData,
    useGetSplittingConfigurations,
    useUpdateStartDate,
    useUpdateSplit,
    useAddSplit,
    useDeleteSplit,
    refetchSplittingConfigurations,
    updateSelectedSplittingScheduleState,
    nutrientsAndForms,
  } = SplittingScheduleHooksLogic();
  useGetSplittingConfigurations();

  const startDate = useMemo(() => {
    if (selectedFertigationSplittingSchedules) {
      return new Date(selectedFertigationSplittingSchedules.configuration.startDate);
    }
    return undefined;
  }, [selectedFertigationSplittingSchedules]);

  const getDateType = (dayProps: CalendarDayCellProps) => {
    if (dayProps.date.toDateString() === startDate?.toDateString()) {
      return 'selected';
    }

    return 'default';
  };

  return (
    <>
      <Calendar
        data-cy='fertigation-splitting-schedule-start-date'
        mode='single'
        type='dialog'
        inputLabel='Start date'
        onChange={({ startDate: newStartDate }) => {
          if (newStartDate) {
            useUpdateStartDate(newStartDate);
          }
        }}
        startDate={startDate}
        css={calendarStyles}
        showHeader={false}
        showNavigationHeader={false}
        inputHelperText={t('calendarHelperText')}
        inputFormat={'dd MMM'}
      >
        <Calendar.Start>
          <Calendar.Start.MonthPicker />
        </Calendar.Start>
        <Calendar.Middle>
          <Calendar.Body
            format='LLLL yyyy'
            renderDay={(dayProps) => {
              return (
                <StyledCustomButtonDate type={getDateType(dayProps)}>
                  {dayProps.date?.getDate()}
                </StyledCustomButtonDate>
              );
            }}
          />
        </Calendar.Middle>
      </Calendar>

      <SplittingScheduleTable
        tKeyPrefix={tKeyPrefix}
        selectedFertigationSplittingSchedules={selectedFertigationSplittingSchedules}
        growthScaleStagesData={growthScaleStagesData || []}
        useUpdateSplit={useUpdateSplit}
        useAddSplit={useAddSplit}
        useDeleteSplit={useDeleteSplit}
        refetchSplittingConfigurations={refetchSplittingConfigurations}
        updateSelectedSplittingScheduleState={updateSelectedSplittingScheduleState}
        nutrientsAndForms={nutrientsAndForms}
      />
    </>
  );
};

export default SplittingSchedule;
