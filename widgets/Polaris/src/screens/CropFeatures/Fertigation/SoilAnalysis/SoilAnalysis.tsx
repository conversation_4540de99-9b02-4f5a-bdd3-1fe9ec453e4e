import React from 'react';
import { Subtitle } from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import { AddContainer, CollapsibleWrapper } from './SoilAnalysis.styled';
import { DeleteAnalysisMethodPopup } from '../../shared/DeleteAnalysisMethodPopup';
import {
  AddAnalysisMethodPopup,
  ConfigurationsAnalysisMethodCollapsible,
  ConfirmationDialog,
  MeatballsMenu,
  NutrientsStaticList,
} from '@widgets/Polaris/src/components';
import {
  AnalysisConfiguration,
  AnalysisTypeOptions,
  FeatureConfigOptions,
  ValidationStatus,
} from '@common/types';
import FertigationSoilAnalysisLogic from './SoilAnalysisLogic';
import { UNIT_IDS } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers';

const FertigationSoilAnalysis = () => {
  const keyPrefix = 'polaris.fpDetails.soilAnalysis';
  const keyPrefixDeletePopup = 'polaris.deleteAnalysisMethodPopup.deleteAlertDialog';
  const { t } = useTranslation('polaris');

  const { selectedMMMValidation } = useAppContext();

  const {
    showDeleteMethodDialog,
    setShowDeleteMethodDialog,
    showDeleteMethodAlertDialog,
    setShowDeleteMethodAlertDialog,
    showAddSoilAnalysisConfigurationPopup,
    setShowAddSoilAnalysisConfigurationPopup,
    analysisMethodsData,
    nutrientsData,
    baseUnits,
    addDeleteMenuItemList,
    handleDeleteAnalysisMethod,
    selectedNutrient,
    nutrientElement,
    selectedSoilAnalysisConfigurationData,
    setConfigurationUpdated,
  } = FertigationSoilAnalysisLogic({ keyPrefix, t });
  return (
    <>
      {' '}
      <NutrientsStaticList
        selectedNutrient={selectedNutrient}
        nutrientParams={nutrientsData}
        title={t(`${keyPrefix}.soilParamsList.title`)}
        emptyStateText={t(`${keyPrefix}.soilParamsList.emptyStateText`)}
      />
      <AddContainer>
        <Subtitle size='s'>
          {`${nutrientElement}${t(`${keyPrefix}.soilParamsList.addTitle`, {
            count: selectedSoilAnalysisConfigurationData?.length,
          })}`}
        </Subtitle>
        <MeatballsMenu
          itemList={addDeleteMenuItemList}
          triggerDataCy={'analysis-method-edit-menu-btn'}
        />
      </AddContainer>
      <DeleteAnalysisMethodPopup
        showDialog={showDeleteMethodDialog}
        onOpenChange={setShowDeleteMethodDialog}
        analysisMethods={analysisMethodsData?.entities}
        onSave={(selectedAnalysisMethodsToDelete) => {
          handleDeleteAnalysisMethod(selectedAnalysisMethodsToDelete);
        }}
      />
      <ConfirmationDialog
        open={showDeleteMethodAlertDialog}
        title={t(`${keyPrefixDeletePopup}.title`)}
        description={t(`${keyPrefixDeletePopup}.description`)}
        icon='Info'
        iconColorConcept='brand'
        okButton={t(`${keyPrefixDeletePopup}.ok`)}
        onOk={() => setShowDeleteMethodAlertDialog(false)}
        hideCancelButton
      />
      <CollapsibleWrapper>
        {selectedSoilAnalysisConfigurationData?.map(
          (configuration: AnalysisConfiguration, index: number) => (
            <ConfigurationsAnalysisMethodCollapsible
              key={`${index}_${configuration.id}`}
              index={index}
              data={configuration}
              baseUnits={baseUnits}
              keyPrefix={keyPrefix}
              configType={FeatureConfigOptions.FERTIGATION}
              analysisType={AnalysisTypeOptions.SOIL}
              triggerErrorState={
                configuration.analysisBaseUnitId === UNIT_IDS.DEFAULT &&
                selectedMMMValidation?.validationStatus === ValidationStatus.FAILED
              }
            />
          ),
        )}
      </CollapsibleWrapper>
      {showAddSoilAnalysisConfigurationPopup && (
        <AddAnalysisMethodPopup
          configType={FeatureConfigOptions.FERTIGATION}
          analysisType={AnalysisTypeOptions.SOIL}
          selectedConfiguration={selectedSoilAnalysisConfigurationData}
          showAddAnalysisMethodPopup={showAddSoilAnalysisConfigurationPopup}
          setShowAddAnalysisMethodPopup={setShowAddSoilAnalysisConfigurationPopup}
          setConfigurationUpdated={setConfigurationUpdated}
          baseUnits={baseUnits}
          defaultManualCreationUnitId={
            selectedSoilAnalysisConfigurationData?.[0]?.analysisBaseUnitId
          }
          selectedNutrient={selectedNutrient}
        />
      )}
    </>
  );
};

export default FertigationSoilAnalysis;
