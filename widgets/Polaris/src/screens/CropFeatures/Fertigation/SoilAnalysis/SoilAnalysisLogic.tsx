import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  useGetAnalysisConfigurations,
  useBulkPermDeleteAnalysisConfigurations,
  filterUnitsByNutrientElementName,
  useFetchNutrientsFromPolaris,
  useAnalysisMethods,
} from '@polaris-hooks/index';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { FeatureConfigOptions, AnalysisTypeOptions } from '@common/types';
import { ComboboxOption } from '@yaradigitallabs/ahua-react';
import { FilterType, Translation } from '@widgets/Polaris/src/types';
import { FERTIGATION_SOIL_ANALYSIS_NUTRIENTS } from '../../shared/constants';
import { METHOD } from '@common/constants';
import { isEqual, sortBy } from 'lodash';

const FertigationSoilAnalysisLogic = ({ keyPrefix, t }: Translation) => {
  const [showDeleteMethodDialog, setShowDeleteMethodDialog] = useState<boolean>(false);
  const [showDeleteMethodAlertDialog, setShowDeleteMethodAlertDialog] = useState<boolean>(false);
  const [showAddSoilAnalysisConfigurationPopup, setShowAddSoilAnalysisConfigurationPopup] =
    useState<boolean>(false);
  const {
    selectedPlanConfigTab,
    selectedFeatureNutrients,
    selectedCountryUnits,
    cropRegion,
    analysisConfigurations: soilAnalysisConfigurationsData,
    methods: { setAnalysisConfigurations, filterAnalysisConfigurations },
  } = useAppContext();
  const { setDisplaySnackbar } = useSnackbar();
  const [configurationUpdated, setConfigurationUpdated] = useState(false);

  const { analysisMethodsData, trigger: triggerGetAnalysisMethods } = useAnalysisMethods();

  const nutrientID = useMemo(
    () => selectedPlanConfigTab && selectedFeatureNutrients?.[selectedPlanConfigTab],
    [selectedPlanConfigTab, selectedFeatureNutrients],
  );
  const nutrientsData = useFetchNutrientsFromPolaris([
    {
      key: 'id',
      value: FERTIGATION_SOIL_ANALYSIS_NUTRIENTS.join(','),
      type: FilterType.IN,
    },
  ]);
  const sortedNutrients = useMemo(() => {
    return sortBy(nutrientsData, (nutrient) =>
      FERTIGATION_SOIL_ANALYSIS_NUTRIENTS.indexOf(nutrient.id),
    );
  }, [nutrientsData.length]);
  const nutrientsRef = useRef(sortedNutrients);
  if (!isEqual(nutrientsRef.current, sortedNutrients)) {
    nutrientsRef.current = sortedNutrients;
  }
  const selectedNutrient = useMemo(
    () => sortedNutrients && sortedNutrients.find(({ id }) => id === nutrientID),
    [nutrientsRef.current, nutrientID],
  );
  const nutrientElement = selectedNutrient?.elementalName;

  const { triggerBulkPermDeleteAnalysisConfigurations } = useBulkPermDeleteAnalysisConfigurations(
    FeatureConfigOptions.FERTIGATION,
    AnalysisTypeOptions.SOIL,
  );

  const baseUnits = filterUnitsByNutrientElementName(selectedCountryUnits, nutrientElement);

  const { trigger: triggerGetSoilAnalysisConfigurations } = useGetAnalysisConfigurations(
    FeatureConfigOptions.FERTIGATION,
    AnalysisTypeOptions.SOIL,
  );

  const selectedSoilAnalysisConfigurationData = useMemo(
    () =>
      soilAnalysisConfigurationsData?.filter(
        (configuration) => configuration?.nutrientId === nutrientID,
      ),
    [soilAnalysisConfigurationsData, nutrientID],
  );

  const analysisMethodsIds = useMemo(
    () => selectedSoilAnalysisConfigurationData?.map((item) => item.analysisMethodId),
    [selectedSoilAnalysisConfigurationData],
  );
  const selectedSoilAnalysisConfigurationDataRef = useRef(analysisMethodsIds);
  if (!isEqual(selectedSoilAnalysisConfigurationDataRef.current, analysisMethodsIds)) {
    selectedSoilAnalysisConfigurationDataRef.current = analysisMethodsIds;
  }
  const selectedFertigationSoilAnalysisConfigurationIds = useMemo(
    () => selectedSoilAnalysisConfigurationDataRef.current,
    [selectedSoilAnalysisConfigurationDataRef.current],
  );

  const handleOpenAddMethodDialog = () => {
    setShowAddSoilAnalysisConfigurationPopup(true);
  };
  const handleOpenDeleteMethodDialog = () => {
    setShowDeleteMethodDialog(true);
  };
  const disableDeleteSelection =
    selectedSoilAnalysisConfigurationData && selectedSoilAnalysisConfigurationData.length <= 1;

  const addDeleteMenuItemList = [
    {
      icon: 'Plus',
      title: t(`${keyPrefix}.soilParamsList.dropdownItems.addTitle`),
      dataCy: 'add-analysis-method-btn',
      onClick: handleOpenAddMethodDialog,
    },
    {
      icon: 'Delete',
      title: t(`${keyPrefix}.soilParamsList.dropdownItems.deleteTitle`),
      subTitle: t(`${keyPrefix}.soilParamsList.dropdownItems.deleteSubtitle`),
      dataCy: 'delete-analysis-method-btn',
      disabled: disableDeleteSelection,
      onClick: handleOpenDeleteMethodDialog,
    },
  ];

  const handleDeleteAnalysisMethod = useCallback(
    (selectedAnalysisMethodsToDelete: ComboboxOption[]): void => {
      if (
        selectedAnalysisMethodsToDelete?.length ===
        selectedFertigationSoilAnalysisConfigurationIds.length
      ) {
        setShowDeleteMethodAlertDialog(true);
      } else {
        deleteSelectedAnalysisMethod(selectedAnalysisMethodsToDelete);
      }
    },
    [selectedFertigationSoilAnalysisConfigurationIds.length],
  );

  const deleteSelectedAnalysisMethod = async (
    selectedAnalysisMethodsToDelete: ComboboxOption[],
  ): Promise<void> => {
    // Get fertigation soil analysis configurations based on selected analysis methods
    const analysisMethodIds = selectedAnalysisMethodsToDelete.map((el) => {
      return el.value;
    });
    const soilAnalysisConfigurationsToDelete = selectedSoilAnalysisConfigurationData?.filter(
      (item) => analysisMethodIds.includes(item.analysisMethodId),
    );

    // Delete fertigation soil analysis configurations
    const soilAnalysisConfigurationsIds = soilAnalysisConfigurationsToDelete.map(
      (configuration) => configuration.id || '',
    );
    const deletedSoilAnalysisConfigurations = await triggerBulkPermDeleteAnalysisConfigurations(
      soilAnalysisConfigurationsIds,
    );
    if (deletedSoilAnalysisConfigurations && deletedSoilAnalysisConfigurations?.length > 0) {
      setDisplaySnackbar({
        title:
          deletedSoilAnalysisConfigurations?.length > 1
            ? t(`polaris.deleteAnalysisMethodPopup.snackbarMessagePlural`, {
                count: deletedSoilAnalysisConfigurations.length,
              })
            : t(`polaris.deleteAnalysisMethodPopup.snackbarMessageSingular`, {
                count: deletedSoilAnalysisConfigurations.length,
              }),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
      filterAnalysisConfigurations(deletedSoilAnalysisConfigurations);
    }
  };

  useEffect(() => {
    if (selectedFertigationSoilAnalysisConfigurationIds.length) {
      triggerGetAnalysisMethods({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'id',
              value: selectedFertigationSoilAnalysisConfigurationIds.join(','),
              type: FilterType.IN,
            },
          ],
        }),
      });
    }
  }, [selectedFertigationSoilAnalysisConfigurationIds]);

  useEffect(() => {
    if (cropRegion) {
      triggerGetSoilAnalysisConfigurations({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'cropRegionId',
              value: cropRegion?.id,
              type: FilterType.EQ,
            },
          ],
        }),
      }).then((data) => {
        setAnalysisConfigurations(data?.entities || []);
        setConfigurationUpdated(false);
      });
    }
  }, [cropRegion, configurationUpdated]);

  return {
    showDeleteMethodDialog,
    setShowDeleteMethodDialog,
    showDeleteMethodAlertDialog,
    setShowDeleteMethodAlertDialog,
    showAddSoilAnalysisConfigurationPopup,
    setShowAddSoilAnalysisConfigurationPopup,
    analysisMethodsData,
    nutrientsData: sortedNutrients,
    baseUnits,
    addDeleteMenuItemList,
    handleDeleteAnalysisMethod,
    selectedNutrient,
    nutrientElement,
    selectedSoilAnalysisConfigurationData,
    selectedFertigationSoilAnalysisConfigurationIds,
    setConfigurationUpdated,
  };
};

export default FertigationSoilAnalysisLogic;
