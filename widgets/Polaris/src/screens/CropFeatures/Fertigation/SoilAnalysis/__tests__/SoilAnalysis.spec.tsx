import React from 'react';
import { render } from '@testing-library/react';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { NavbarProvider } from '@libs/nav-context';
import { BrowserRouter as Router } from 'react-router-dom';
import { setupServer } from 'msw/node';
import FertigationSoilAnalysis from '../SoilAnalysis';
import * as useNutrientsService from '@polaris-hooks/polarisMockService/useNutrientsService/useNutrientsService';
import {
  unitCountriesHandler,
  allUnitsHandler,
  fertigationSoilAnalysisConfigurationHandler,
  getAnalysisMethodsHandler,
  allPolarisNutrientsHandler,
  updateFertigationSoilAnalysisNutrientClassificationHighHandler,
  updateFertigationSoilAnalysisNutrientClassificationVeryLowHandler,
  nutrientsFertigationMock,
  featureNutrientMockMg,
  mockFertigationAppProviderValue,
  soilAnalysisConfigurationsMock,
} from '@common/mocks';
import { useTranslation } from 'react-i18next';

const server = setupServer(
  getAnalysisMethodsHandler,
  allUnitsHandler,
  unitCountriesHandler,
  fertigationSoilAnalysisConfigurationHandler,
  updateFertigationSoilAnalysisNutrientClassificationHighHandler,
  updateFertigationSoilAnalysisNutrientClassificationVeryLowHandler,
  allPolarisNutrientsHandler,
);
beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest
  .spyOn(useNutrientsService, 'useFetchNutrientsFromPolaris')
  .mockImplementation(() => nutrientsFertigationMock)
  .mockImplementationOnce(() => nutrientsFertigationMock)
  .mockImplementationOnce(() => []);

jest.mock('@widgets/Polaris/src/components/NutrientsStaticList/NutrientsStaticList', () => ({
  NutrientsStaticList: () => <div>Nutrients list</div>,
}));

const keyPrefix = 'polaris.fpDetails.soilAnalysis';
const { t } = useTranslation();
describe('FertigationSoilAnalysis Component', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <FertigationSoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(container).toBeInTheDocument();
  });

  it('renders the list', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <FertigationSoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );
    expect(component.getByText('Nutrients list')).toBeInTheDocument();
  });

  it('renders methods count line with selected nutrient specified', () => {
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <FertigationSoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const selectedElement = featureNutrientMockMg;

    const matcher = RegExp(
      `${selectedElement.elementalName}${t(`${keyPrefix}.soilParamsList.addTitle`)}`,
    );

    const methodsCounterText = component.getByText(matcher);

    expect(methodsCounterText).toBeInTheDocument();
  });

  it('renders ConfigurationsAnalysisMethodCollapsible when crop demand analyses are available for the selected nutrient', () => {
    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          analysisConfigurations: soilAnalysisConfigurationsMock?.entities,
        }}
      >
        <NavbarProvider>
          <Router>
            <AppContext.Consumer>{() => <FertigationSoilAnalysis />}</AppContext.Consumer>
          </Router>
        </NavbarProvider>
      </AppContext.Provider>,
    );

    const collapsible = component.queryByTestId('analyses-collapsible');
    expect(collapsible).toBeInTheDocument();
  });
});
