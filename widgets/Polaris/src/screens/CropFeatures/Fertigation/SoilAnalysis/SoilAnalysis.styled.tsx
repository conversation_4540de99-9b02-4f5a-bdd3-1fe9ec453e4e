import { styled } from '@yaradigitallabs/ahua-react';

export const AddContainer = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingBottom: '$x6',
  paddingTop: '$x6',

  '& > h2': {
    lineHeight: '$scale5',
  },
});

export const CollapsibleWrapper = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  gap: '$x4',
  width: '100%',
});
