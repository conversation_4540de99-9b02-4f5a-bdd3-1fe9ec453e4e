import { ADDITIONAL_PROP_NAME } from '@common/constants';
import { BaseUnit, GrowthScale, CropRegion } from '@common/types';

export const isCNPPopupFieldNotTouched = (
  cropRegion: CropRegion | undefined | null,
  yieldUnit: BaseUnit | null,
  unitSolid: BaseUnit | null,
  growthScale: GrowthScale | null,
) => {
  return (
    cropRegion?.yieldBaseUnitId === yieldUnit?.id &&
    cropRegion?.demandBaseUnitId === unitSolid?.id &&
    cropRegion?.growthScaleId === growthScale?.id
  );
};

export const isCMMMPopupFieldNotTouched = (
  cropRegion: CropRegion | undefined | null,
  yieldUnit: BaseUnit | null,
  unitSolid: BaseUnit | null,
  growthScale: GrowthScale | null,
  defaultYield: number | null,
) => {
  const noDefaultYieldValue = !defaultYield && typeof defaultYield !== 'number';
  const defaultYieldProp = cropRegion?.additionalProperties?.find(
    ({ Name }) => Name === ADDITIONAL_PROP_NAME.DEFAULT_YIELD,
  );
  const noPropNoValue = !defaultYieldProp && noDefaultYieldValue;
  return (
    cropRegion?.yieldBaseUnitId === yieldUnit?.id &&
    cropRegion?.demandBaseUnitId === unitSolid?.id &&
    cropRegion?.growthScaleId === growthScale?.id &&
    (noPropNoValue || defaultYieldProp?.DefaultValue === defaultYield)
  );
};

export const findOne = <T extends GrowthScale | BaseUnit>(
  data: T[] | undefined,
  value: string,
): T | undefined => {
  return data?.find((unit) => unit?.id === value);
};
