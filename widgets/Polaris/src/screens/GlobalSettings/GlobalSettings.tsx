import React from 'react';
import { ROUTES } from '@src/routes';
import { useSetNavbar } from '@polaris-hooks/index';
import { Navigation } from './Navigation';
import './styles.scss';
import { Outlet } from 'react-router';
import { AdminProvider } from '../../providers';

const GlobalSettings = (): JSX.Element => {
  useSetNavbar(ROUTES.globalSettings, true, 'globalSettings');

  return (
    <AdminProvider>
      <div className='global-settings-container main-content' data-cy='global-settings-container'>
        <aside className='sidebar'>
          <Navigation />
        </aside>
        <article className='content'>
          <Outlet />
        </article>
      </div>
    </AdminProvider>
  );
};

export default GlobalSettings;
