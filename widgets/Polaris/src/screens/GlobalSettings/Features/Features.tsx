import moment from 'moment';

import { AdminContext, Resource } from '../../../providers';
import React, { Fragment, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import {
  Button,
  Caption,
  IconButton,
  Input,
  Pagination,
  Status,
  Subtitle,
  Switch,
  Table,
  Title,
  Tooltip,
} from '@yaradigitallabs/ahua-react';
import './styles.scss';
import { useTranslation } from 'react-i18next';
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  Header,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { debounce } from 'lodash';
import { GenericObject } from '@common/types';
import { GLOBAL_RESOURCE_ACTION_TYPES } from '@widgets/Polaris/src/providers/AdminProvider/ResourceReducer';
import { AddFeatureForm } from '@widgets/Polaris/src/screens/GlobalSettings/Features/components';
import { useGetAllResources } from '@polaris-hooks/index';
import {
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@widgets/Polaris/src/hooks/globalSettings/shared/useDisplaySnackbar';

export const Features = () => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings.features',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const [addFeatureDialogIsOpen, setAddFeatureDialogIsOpen] = useState<boolean>(false);
  // TODO: https://yaradigitalfarming.atlassian.net/browse/YAPF-16451
  // const [selectedEditFeature, setSelectedEditFeature] = useState<Nullable<Resource>>();
  // const [selectedDeleteFeature, setSelectedDeleteFeature] = useState<Nullable<Resource>>();
  const { data: resourcesData, isMutating, error } = useGetAllResources();
  const { state: adminState, dispatch } = useContext(AdminContext);
  const { resources } = adminState;
  const [headerFilterGroupValues, setHeaderFilterGroupValues] = useState<GenericObject<string>>();
  const { displaySnackbar } = useDisplaySnackbar();

  useEffect(() => {
    if (resourcesData && !isMutating && !error) {
      dispatch({
        type: GLOBAL_RESOURCE_ACTION_TYPES.INIT_RESOURCE,
        payload: resourcesData,
      });
    }
  }, [resourcesData, isMutating, error]);

  const debouncedChange = useCallback(
    debounce((args) => {
      const [header, value]: [Header<Resource, unknown>, string] = args;
      header.column.setFilterValue(value);
    }, 350),
    [],
  );

  const handleFilterGroupChange = (columnId: string, value: string): void => {
    setHeaderFilterGroupValues((prevState) => ({
      ...prevState,
      [columnId]: value,
    }));
  };

  const copyToClipboard = (data: string): void => {
    navigator.clipboard.writeText(data);
    displaySnackbar(tRootLang('common.clipboardCopyMessage'), SNACKBAR_VARIANT.SUCCESS);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const columns = useMemo<ColumnDef<Resource, any>[]>(
    () => [
      {
        header: t('table.headers.featureCode'),
        accessorKey: 'metadata.code',
        id: 'code',
      },
      {
        header: t('table.headers.featureID'),
        accessorKey: 'metadata.polarisId',
        cell: (info) => (
          <div className='featureID'>
            <Caption size={'n'}>{info.getValue()}</Caption>
            <IconButton
              className='copy-icon'
              colorConcept='brand'
              size='xs'
              icon='Copy'
              type='ghost'
              onClick={() => copyToClipboard(info.getValue())}
            />
          </div>
        ),
      },
      { header: t('table.headers.feature'), accessorKey: 'name' },
      { header: t('table.headers.displayName'), accessorKey: 'metadata.displayName' },
      {
        header: t('table.headers.group'),
        accessorKey: 'metadata.group',
        cell: (info) => (
          <div className='groupStatus'>
            <Status size={'s'} colorType={'blue'}>
              {info.getValue()}
            </Status>
          </div>
        ),
      },
      {
        header: t('table.headers.lastUpdated'),
        id: 'lastUpdated',
        enableColumnFilter: false,
        accessorFn: (row) => `${moment(row.updatedAt).format('D MMM YYYY, h:mm:ss a')}`,
        sortingFn: (firstRow, secondRow) => {
          return (
            new Date(firstRow.original.updatedAt).getTime() -
            new Date(secondRow.original.updatedAt).getTime()
          );
        },
        cell: (info) => (
          <Tooltip
            concept={'inverse'}
            position={'top'}
            maxWidth={250}
            className='default-tooltip featureTableTooltip'
            text={info.getValue()}
          >
            <IconButton icon='Info' colorConcept='neutral' size='xs' />
          </Tooltip>
        ),
      },
    ],
    [],
  );
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: 'lastUpdated',
      desc: true, // Latest lastUpdated data by default first row
    },
  ]);
  const [columnVisibility, setColumnVisibility] = useState({
    code: false,
    id: false,
  });
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 7 });
  const table = useReactTable({
    columns,
    data: resources,
    state: { columnFilters, columnVisibility, pagination, sorting },
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <>
      <div className='featureContainer' data-cy='feature-container'>
        <div className='head'>
          <Title>{t('header')}</Title>
          <Button
            size='s'
            data-cy='add-feature-btn'
            onClick={() => {
              setAddFeatureDialogIsOpen(true);
            }}
          >
            {t('addFeature')}
          </Button>
        </div>
        <div className='subhead'>
          <Subtitle className='subtitle' size={'s'}>
            {t('totalFeatures')}: <b>{resources?.length}</b>
          </Subtitle>
          <div className='switch' data-cy='switch'>
            <Switch
              data-cy='switch-label'
              label={t('switchLabel')}
              onCheckedChange={(checked: boolean) =>
                setColumnVisibility({ code: checked, id: checked })
              }
            />
          </div>
        </div>
        <div className='featureTableWrapper'>
          {isMutating ? (
            <div>Loading...</div>
          ) : (
            <Table className='featureTable'>
              <thead>
                {/* Table header */}
                {table.getHeaderGroups().map((headerGroup) => (
                  <Fragment key={headerGroup.id}>
                    <Table.Row>
                      {headerGroup.headers.map((header) => (
                        <Table.SortHead
                          data-cy={header.id}
                          key={header.id}
                          className={`header-cell-${header.id}`}
                          direction={header.column.getIsSorted()}
                          active={Boolean(header.column.getIsSorted())}
                          onClick={header.column.getToggleSortingHandler()}
                          color={'primary'}
                          colSpan={header.colSpan}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                        </Table.SortHead>
                      ))}
                      <Table.Head>{t('table.headers.action')}</Table.Head>
                    </Table.Row>
                    <Table.Row>
                      {headerGroup.headers.map((header) => (
                        <Table.Head key={header.id}>
                          {header.column.getCanFilter() && (
                            <Input
                              size='xs'
                              aria-placeholder='Search'
                              onChange={() => null}
                              value={headerFilterGroupValues?.[header.column.id] || ''}
                              onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
                                handleFilterGroupChange(header.column.id, e.target.value);
                                debouncedChange([header, e.target.value]);
                              }}
                              label='Search'
                              iconTrailing={
                                header.column.getFilterValue()
                                  ? {
                                      icon: 'Close',
                                      onClick: () => {
                                        header.column.setFilterValue('');
                                        handleFilterGroupChange(header.column.id, '');
                                      },
                                    }
                                  : {
                                      icon: 'Search',
                                      onClick: () => null,
                                    }
                              }
                              data-cy={`partner-head-search-row ${header.id}`}
                            />
                          )}
                        </Table.Head>
                      ))}
                    </Table.Row>
                  </Fragment>
                ))}
              </thead>
              <tbody>
                {/* Table body */}
                {table.getRowModel().rows.map((row) => (
                  <Table.Row key={row.id} data-cy={`feature-table-row-${row.id}`}>
                    {row.getVisibleCells().map((cell) => (
                      <Table.Cell
                        key={cell.id}
                        className={
                          cell.column.id === 'lastUpdated' ? 'cellLastUpdated' : 'cellDefault'
                        }
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </Table.Cell>
                    ))}
                    <Table.ActionsCell>
                      <Table.ActionMenu title='Actions'>
                        <Table.ActionMenuItem
                          icon='Edit'
                          data-cy='feature-data-cell-actions-edit'
                          onClick={() => {
                            throw Error('NotImplemented.');
                            // TODO: https://yaradigitalfarming.atlassian.net/browse/YAPF-16451
                            // setSelectedEditFeature(row.original);
                          }}
                        >
                          {t('table.actions.edit')}
                        </Table.ActionMenuItem>
                        <Table.ActionMenuItem
                          icon='Delete'
                          data-cy='feature-data-cell-actions-delete'
                          onClick={() => {
                            throw Error('NotImplemented.');
                            // TODO: https://yaradigitalfarming.atlassian.net/browse/YAPF-16451
                            // setSelectedDeleteFeature(row.original)
                          }}
                        >
                          {t('table.actions.delete')}
                        </Table.ActionMenuItem>
                      </Table.ActionMenu>
                    </Table.ActionsCell>
                  </Table.Row>
                ))}
              </tbody>
            </Table>
          )}
        </div>
        {table?.getPageCount() > 1 && (
          <div className='pagination' data-cy='feature-table-pagination'>
            <Pagination>
              <Pagination.Control
                pageIndex={pagination.pageIndex}
                pageCount={table.getPageCount()}
                ariaLabel='paginationControl'
                onFirstPage={() => table.firstPage()}
                onLastPage={() => table.lastPage()}
                onNextPage={() => table.nextPage()}
                onPrevPage={() => table.previousPage()}
                canNextPage={table.getCanNextPage()}
                canPreviousPage={table.getCanPreviousPage()}
              />
            </Pagination>
          </div>
        )}
      </div>
      {addFeatureDialogIsOpen && (
        <AddFeatureForm
          resources={resources}
          isModalOpen={addFeatureDialogIsOpen}
          onDialogClose={setAddFeatureDialogIsOpen}
        />
      )}
      {/*{selectedEditFeature && (*/}
      {/*  <EditFeature feature={null} onChange={() => setSelectedEditFeature(null)} />*/}
      {/*)}*/}
      {/*{selectedDeleteFeature && (*/}
      {/*  <DeleteFeature*/}
      {/*    feature={null}*/}
      {/*    onChange={() => setSelectedDeleteFeature(null)}*/}
      {/*  />*/}
      {/*)}*/}
    </>
  );
};

export default Features;
