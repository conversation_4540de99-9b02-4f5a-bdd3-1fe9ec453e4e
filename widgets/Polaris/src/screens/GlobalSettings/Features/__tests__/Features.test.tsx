import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import Features from '../Features';
import { useGetAllResources } from '@polaris-hooks/globalSettings';
import { AdminContext } from '../../../../providers';
import userEvent from '@testing-library/user-event';
import { resourceResponseMock } from '@common/mocks';
import { GLOBAL_RESOURCE_ACTION_TYPES } from '@widgets/Polaris/src/providers/AdminProvider/ResourceReducer';

// Mock dependencies
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));

const mockDispatch = jest.fn();

const mockAdminContext = {
  state: {
    features: [], // TODO: Marked for deprecation
    partners: [], // TODO: Marked for deprecation
    resources: resourceResponseMock,
    applications: [],
  },
  dispatch: mockDispatch,
};

describe('Features', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useGetAllResources as jest.Mock).mockReturnValue({
      data: resourceResponseMock,
      isMutating: false,
      error: null,
    });
  });

  const renderFeatures = () => {
    return render(
      <AdminContext.Provider value={mockAdminContext}>
        <Features />
      </AdminContext.Provider>,
    );
  };

  test('renders Features component', () => {
    renderFeatures();
    expect(screen.getByTestId('feature-container')).toBeInTheDocument();
  });

  test('initializes features from API', () => {
    renderFeatures();
    expect(mockDispatch).toHaveBeenCalledWith({
      type: GLOBAL_RESOURCE_ACTION_TYPES.INIT_RESOURCE,
      payload: resourceResponseMock,
    });
  });

  test('renders table with correct columns', () => {
    renderFeatures();
    expect(screen.getByText('table.headers.feature')).toBeInTheDocument();
    expect(screen.getByText('table.headers.displayName')).toBeInTheDocument();
    expect(screen.getByText('table.headers.group')).toBeInTheDocument();
    expect(screen.getByText('table.headers.lastUpdated')).toBeInTheDocument();
  });

  test('handles column visibility toggle', () => {
    renderFeatures();
    const switchElement = screen.getByTestId('switch');
    fireEvent.click(switchElement);

    // Check if code and id columns are visible
    expect(screen.getAllByTestId('feature-table-row-', { exact: false })).toBeDefined();
    expect(screen.getByText(resourceResponseMock[0].name)).toBeInTheDocument();
    expect(screen.getByText(resourceResponseMock[0].metadata.displayName)).toBeInTheDocument();
    expect(screen.getByText(resourceResponseMock[0].metadata.group)).toBeInTheDocument();
  });

  test('handles pagination when there are more than 7 rows in table', () => {
    render(
      <AdminContext.Provider value={mockAdminContext}>
        <Features />
      </AdminContext.Provider>,
    );
    const nonePaginationControl = screen.queryByTestId('feature-table-pagination');
    expect(nonePaginationControl).not.toBeInTheDocument();

    // More than 7 rows in the table
    const context = {
      state: { resources: Array(8).fill(resourceResponseMock[0]), partners: [], features: [] },
      dispatch: mockDispatch,
    };
    render(
      <AdminContext.Provider value={context}>
        <Features />
      </AdminContext.Provider>,
    );
    const paginationControl = screen.getByLabelText('paginationControl');
    expect(paginationControl).toBeInTheDocument();
  });

  test('displays correct total features count', () => {
    renderFeatures();
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  test('handles search by keyword', async () => {
    renderFeatures();
    const filterInputs = screen.getAllByRole('textbox');
    const nameFilter = filterInputs[2]; // Name column filter

    // Search by keyword - results available
    fireEvent.change(nameFilter, { target: { value: 'CNP' } });
    expect(nameFilter).toHaveValue('CNP');
    await waitFor(() => {
      expect(screen.getByText('Nutrition Plan')).toBeInTheDocument();
      expect(screen.getByText('Crop Nutrition Plan')).toBeInTheDocument();
    });

    // Search by keyboard - no results found
    fireEvent.change(nameFilter, { target: { value: 'Match nobody' } });
    await waitFor(() => {
      expect(screen.queryByText('Nutrition Plan')).not.toBeInTheDocument();
      expect(screen.queryByText('Crop Nutrition Plan')).not.toBeInTheDocument();
    });
  });

  test('handles default sorting and sorting behavior', async () => {
    renderFeatures();
    let rows = screen.getAllByTestId('feature-table-row-', { exact: false });

    // Sort default state is order by lastUpdated (desc)
    await waitFor(() => {
      expect(rows.length).toBe(2);
      expect(rows[0]).toHaveTextContent(
        '8bbb6805-ba1b-47ab-b8f6-1927d5fddac2NDVINDVIBiomass Monitoring',
      );
      expect(rows[1]).toHaveTextContent(
        '92b5d200-0ccb-4d6a-a6f4-3651b935521cCNPNutrition PlanCrop Nutrition Plan',
      );
    });
    // Sort - by ASC
    await userEvent.click(screen.getByTestId('lastUpdated'));
    await waitFor(() => {
      rows = screen.getAllByTestId('feature-table-row-', { exact: false });
      expect(rows[0]).toHaveTextContent(
        '92b5d200-0ccb-4d6a-a6f4-3651b935521cCNPNutrition PlanCrop Nutrition Plan',
      );
      expect(rows[1]).toHaveTextContent(
        '8bbb6805-ba1b-47ab-b8f6-1927d5fddac2NDVINDVIBiomass Monitoring',
      );
    });
  });
});
