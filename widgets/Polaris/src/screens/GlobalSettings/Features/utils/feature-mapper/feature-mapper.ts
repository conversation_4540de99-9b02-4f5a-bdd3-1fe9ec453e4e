import { CropDropdownValue, Resource } from '@common/types';
import { isEqual, orderBy, uniqWith } from 'lodash';

export const featureGroupOptions = (resource: Resource[]) => {
  const selectItems: CropDropdownValue[] = resource.map((resource: Resource) => {
    return { text: resource.metadata.group, value: resource.metadata.group };
  });

  return orderBy(uniqWith(selectItems, isEqual), ['text'], ['asc']);
};
