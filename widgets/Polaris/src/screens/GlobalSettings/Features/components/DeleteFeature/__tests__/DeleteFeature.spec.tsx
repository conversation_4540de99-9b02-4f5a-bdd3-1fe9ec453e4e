import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useCanDeleteFeature, useSoftDeleteFeature } from '@polaris-hooks/index';
import { DeleteFeature, DeleteFeatureProps } from '../DeleteFeature'; // Mock dependencies

// Mock dependencies
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));

describe('DeleteFeature', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useCanDeleteFeature as jest.Mock).mockReturnValue({
      canDeleteFeature: jest.fn().mockResolvedValue({ success: false }),
    });
    (useSoftDeleteFeature as jest.Mock).mockReturnValue({
      softDeleteFeature: jest.fn().mockResolvedValue({ success: false }),
    });
  });

  const defaultProps: DeleteFeatureProps = {
    feature: {
      id: '613e75b4-c565-4eb2-8487-4c62a6c61f1a',
      name: 'Mock name',
      displayName: 'Mock display name',
      group: 'Mock group',
      code: 'Mock code',
      modifiedBy: '<EMAIL>',
      modified: new Date().toISOString(),
      created: new Date().toISOString(),
      deleted: null,
    },
    onChange: jest.fn(),
  };

  const renderComponent = (props: DeleteFeatureProps) => {
    render(<DeleteFeature {...props} />);
  };

  it('should not show modal when feature is undefined', async () => {
    const props = { ...defaultProps, feature: { ...defaultProps.feature, id: '' } };
    renderComponent(props);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).not.toBeInTheDocument();
    });
  });

  it('should show modal, and show loader is searching for linked entities', async () => {
    const deferredPromise = () => new Promise(() => null);
    (useCanDeleteFeature as jest.Mock).mockReturnValue({
      canDeleteFeature: jest.fn().mockImplementationOnce(deferredPromise),
    });
    renderComponent(defaultProps);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).toBeInTheDocument();
      expect(screen.queryByText('common.confirmDelete')).not.toBeInTheDocument();
      expect(screen.queryByText('common.confirmDeleteDescription')).not.toBeInTheDocument();
    });
  });

  it('should show modal, and show delete success confirmation', async () => {
    (useCanDeleteFeature as jest.Mock).mockReturnValue({
      canDeleteFeature: jest
        .fn()
        .mockImplementationOnce(() => new Promise((r) => r({ success: true }))),
    });
    renderComponent(defaultProps);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).not.toBeInTheDocument();
      expect(screen.queryByText('common.confirmDelete')).toBeInTheDocument();
      expect(screen.queryByText('common.confirmDeleteDescription')).toBeInTheDocument();
    });
  });

  it('should show modal, and show delete failure with a collapsible list', async () => {
    (useCanDeleteFeature as jest.Mock).mockReturnValue({
      canDeleteFeature: jest
        .fn()
        .mockResolvedValueOnce({ success: false, usedIn: ['error1', 'error2'] }),
    });
    renderComponent(defaultProps);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).not.toBeInTheDocument();
      expect(screen.queryByText('common.confirmDelete')).not.toBeInTheDocument();
      expect(screen.queryByText('common.cannotDelete')).toBeInTheDocument();
      expect(screen.queryByText('common.cannotDeleteDescription')).toBeInTheDocument();
      expect(screen.queryByText('common.viewLinkedLocations')).toBeInTheDocument();
    });
  });
});
