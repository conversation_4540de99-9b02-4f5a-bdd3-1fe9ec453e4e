import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AdminContext, Feature, GLOBAL_FEATURE_ACTION_TYPES } from '@widgets/Polaris/src/providers';
import {
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@widgets/Polaris/src/hooks/globalSettings/shared/useDisplaySnackbar';

import { useCanDeleteFeature, useSoftDeleteFeature } from '@polaris-hooks/index';
import { CheckLinkedDataPopup } from '../../../components/CheckLinkedDataPopup';
import { ConfirmDeleteLinkedDataDialog } from '../../../components/ConfirmDeleteLinkedDataDialog';
import { ErrorDeleteLinkedDataDialog } from '../../../components/ErrorDeleteLinkedDataDialog';

export interface DeleteFeatureProps {
  feature: Feature;
  onChange: () => void;
}

export const DeleteFeature: React.FC<DeleteFeatureProps> = ({ feature, onChange }) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });

  const { canDeleteFeature } = useCanDeleteFeature(feature?.id);
  const { softDeleteFeature, isMutating } = useSoftDeleteFeature(feature?.id);
  const { displaySnackbar } = useDisplaySnackbar();
  const { dispatch } = useContext(AdminContext);
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [isDeleteAllowed, setIsDeleteAllowed] = useState<boolean>(false);
  const [deleteErrorMessages, setDeleteErrorMessages] = useState<string[]>([]);

  useEffect(() => {
    const execDeleteFeature = async () => {
      setIsVerifying(true);
      try {
        const response = await canDeleteFeature();
        setIsDeleteAllowed(response.success);
        if (!response.success) {
          setDeleteErrorMessages(response.usedIn);
        }
      } catch (e) {
        console.error('Error checking on feature tag links:', e);
        handleError();
      } finally {
        setIsVerifying(false);
      }
    };
    feature?.id && execDeleteFeature();
  }, [feature]);

  const handleError = () => {
    displaySnackbar(tRootLang('error.wentWrongTitle'), SNACKBAR_VARIANT.ERROR);
  };

  const handleOnChange = () => {
    setIsDeleteAllowed(false);
    onChange();
  };

  const handleOnOk = async () => {
    try {
      const response = await softDeleteFeature();
      if (response.success) {
        dispatch({
          type: GLOBAL_FEATURE_ACTION_TYPES.DELETE_FEATURE,
          payload: feature,
        });
        displaySnackbar(
          tRootLang('common.snackbarDeleteMessage', {
            name: feature.displayName,
          }),
          SNACKBAR_VARIANT.SUCCESS,
        );
      } else {
        handleError();
      }
    } catch (error) {
      console.error('Error deleting feature data:', error);
      handleError();
    } finally {
      handleOnChange();
    }
  };

  return (
    <>
      <CheckLinkedDataPopup isOpen={isVerifying} title={t('common.checkLink')} />
      <ConfirmDeleteLinkedDataDialog
        isOpen={isDeleteAllowed}
        title={t('common.confirmDelete', { resourceName: feature.name })}
        description={t('common.confirmDeleteDescription')}
        onOk={handleOnOk}
        onCancel={handleOnChange}
        isMutating={isMutating}
      />
      <ErrorDeleteLinkedDataDialog
        isOpen={!isDeleteAllowed && deleteErrorMessages.length > 0}
        title={t('common.cannotDelete', { resourceName: feature.name })}
        description={t('common.cannotDeleteDescription')}
        okButtonLabel={tRootLang('common.ok')}
        collapsibleLabel={t('common.viewLinkedLocations')}
        errorMessages={deleteErrorMessages}
        onCancel={handleOnChange}
      />
    </>
  );
};
