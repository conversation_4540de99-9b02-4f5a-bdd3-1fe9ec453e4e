import React, { useContext } from 'react';
import { Input, Select } from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import { GenericDialog } from '@widgets/Polaris/src/components';
import { Nullable } from '@common/types';
import { useFormik } from 'formik';
import { AdminContext, Feature, GLOBAL_FEATURE_ACTION_TYPES } from '@widgets/Polaris/src/providers';
import './styles.scss';
import { editFeatureSchema } from '@widgets/Polaris/src/screens/GlobalSettings/Features/validationSchemas';
import { useEditFeature } from '@polaris-hooks/index';
import {
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@polaris-hooks/globalSettings/shared/useDisplaySnackbar';

export interface EditFeatureProps {
  feature: Feature;
  onChange: (feature: Nullable<Feature>) => void;
}

export const EditFeature: React.FC<EditFeatureProps> = ({ feature, onChange }) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings.features',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const { dispatch } = useContext(AdminContext);
  const { editFeature } = useEditFeature(feature?.id);
  // const { features } = adminState; // TODO: EditFeature with UserMS integration
  const { displaySnackbar } = useDisplaySnackbar();

  const formik = useFormik({
    initialValues: feature || { id: '', name: '', displayName: '', group: '' },
    validationSchema: editFeatureSchema(tRootLang, t),
    onSubmit: async (feature) => {
      let output: Nullable<Feature> = null;
      formik.setSubmitting(true);
      try {
        output = await editFeature({
          name: feature.name,
          group: feature.group,
          displayName: feature.displayName,
        });
        if (output) {
          dispatch({
            type: GLOBAL_FEATURE_ACTION_TYPES.UPDATE_FEATURE,
            payload: output,
          });
          displaySnackbar(t('editFeature.successMessage'), SNACKBAR_VARIANT.SUCCESS);
        }
      } catch (error) {
        console.error('Error editing feature data:', error);
        displaySnackbar(tRootLang('error.wentWrongTitle'), SNACKBAR_VARIANT.ERROR);
      } finally {
        handleDialogClose(output);
      }
    },
  });

  const handleDialogClose = (feature: Nullable<Feature>) => {
    formik.resetForm();
    onChange(feature);
  };

  return (
    <>
      {feature?.id && (
        <GenericDialog
          styles={{
            middleStyle: {
              gap: '$x4',
            },
          }}
          isOpen={feature?.id.length > 0}
          isSubmitDisabled={formik.isSubmitting}
          title={tRootLang('common.edit', { name: feature.name })}
          submitLabel={tRootLang('common.saveChanges')}
          closeOnOverlayClick={false}
          onClose={() => handleDialogClose(null)}
          onSubmit={formik.handleSubmit}
        >
          <Input
            size='s'
            label={t('table.headers.featureID')}
            name='id'
            className='noBorder'
            aria-label='id'
            data-cy={`global-settings-features-edit-input-id`}
            value={formik.values.id}
            variant='default'
            disabled
          />
          <Input
            size='s'
            label={t('table.headers.featureName')}
            name='name'
            className='noBorder'
            aria-label='name'
            data-cy={`global-settings-features-edit-input-name`}
            value={formik.values.name}
            variant='default'
            disabled
          />
          <Input
            size='s'
            label={t('table.headers.displayName')}
            name='displayName'
            aria-label='displayName'
            data-cy={`global-settings-features-edit-input-displayName`}
            helperText={(formik.touched.displayName && formik.errors.displayName) || ''}
            value={formik.values.displayName}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            variant={formik.touched.displayName && formik.errors.displayName ? 'error' : 'default'}
            disabled={formik.isSubmitting}
            required
          />
          <Select
            size='s'
            cover='outline'
            label={t('table.headers.group')}
            variant={formik.touched.group && formik.errors.group ? 'error' : 'default'}
            items={[]} // TODO: EditFeature with UserMS integration
            helper-text={(formik.touched.group && formik.errors.group) || ''}
            value={formik.values.group}
            onChange={(value: string) => formik.setFieldValue('group', value, true)}
            disabled={formik.isSubmitting}
            required
            onFocus={null}
            onBlur={null}
            position='popper'
          />
        </GenericDialog>
      )}
    </>
  );
};
