import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useEditFeature } from '@polaris-hooks/index';
import { generateUUID } from '@widgets/Polaris/src/screens/GlobalSettings/utils';
import {
  EditFeature,
  EditFeatureProps,
} from '@widgets/Polaris/src/screens/GlobalSettings/Features/components/EditFeature/EditFeature';
import { AdminContext, Feature } from '@widgets/Polaris/src/providers';

// Mock dependencies
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));
jest.mock('@polaris-hooks/globalSettings/shared/useDisplaySnackbar', () => ({
  useDisplaySnackbar: jest.fn().mockReturnValue({
    displaySnackbar: jest.fn(),
  }),
  SNACKBAR_VARIANT: {
    ERROR: 0,
    SUCCESS: 1,
  },
}));

describe('EditFeature', () => {
  const mockFeature = {
    id: generateUUID(),
    group: 'testGroup',
    displayName: 'testDisplayName',
    name: 'testName',
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockAdminContext: any = {
    state: { features: [mockFeature, mockFeature], partners: [] },
    dispatch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useEditFeature as jest.Mock).mockReturnValue({
      editFeature: jest.fn(),
      data: mockFeature,
      isMutating: false,
      error: null,
    });
  });

  const mockOnChange = jest.fn();
  const EditFeatureProps: EditFeatureProps = {
    feature: mockFeature as Feature,
    onChange: mockOnChange,
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderFeatures = (props: any) => {
    return render(
      <AdminContext.Provider value={mockAdminContext}>
        render(
        <EditFeature {...props} />
        );
      </AdminContext.Provider>,
    );
  };

  it('should not show modal when isOpen is false', async () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const props: any = { ...EditFeatureProps, feature: null };
    renderFeatures(props);
    expect(screen.queryByText('common.edit')).not.toBeInTheDocument();
    expect(screen.queryByText('common.saveChanges')).not.toBeInTheDocument();
  });

  it('should show modal when isOpen is true', async () => {
    const props = { ...EditFeatureProps };
    renderFeatures(props);
    expect(screen.getByText('common.edit')).toBeInTheDocument();
    expect(screen.getByText('common.saveChanges')).toBeInTheDocument();
    expect(screen.getByText('table.headers.featureID')).toBeInTheDocument();
    expect(screen.getByText('table.headers.featureName')).toBeInTheDocument();
    expect(screen.getByText('table.headers.displayName')).toBeInTheDocument();
    expect(screen.getByText('table.headers.group')).toBeInTheDocument();
    expect(screen.getByText('common.saveChanges')).toBeInTheDocument();
  });

  it('should handle close action', () => {
    const props = { ...EditFeatureProps };
    renderFeatures(props);
    const closeButton = screen.getByTestId('generic-dialog-continue-btn');
    expect(closeButton).toBeInTheDocument();
    fireEvent.click(closeButton);
    waitFor(() => {
      expect(mockOnChange).toHaveBeenCalled();
    });
  });

  it('should input show error state when required', async () => {
    const props = { ...EditFeatureProps };
    renderFeatures(props);
    // Default state - no errors
    const input = screen.getByTestId('global-settings-features-edit-input-displayName');
    const submitButton = screen.getByTestId('generic-dialog-continue-btn');
    expect(screen.queryByText('error.isRequired')).not.toBeInTheDocument();
    // Display errors
    await userEvent.clear(input);
    await userEvent.click(submitButton);
    expect(screen.queryByText('error.isRequired')).toBeInTheDocument();
    // Clear errors
    await userEvent.type(input, 'This is an valid input');
    await userEvent.click(submitButton);
    expect(screen.queryByText('error.isRequired')).not.toBeInTheDocument();
  });
});
