import React, { useContext, useState } from 'react';
import { GenericDialog, SelectWrapper } from '@widgets/Polaris/src/components';
import { useTranslation } from 'react-i18next';
import { Input, Select } from '@yaradigitallabs/ahua-react';
import { defaultAddFeatureFormState, featureGroupOptions } from '../../utils';
import { AdminContext } from '@widgets/Polaris/src/providers';
import { useCreateResource, useGetResourceById } from '@polaris-hooks/index';
import { useFormik } from 'formik';
import { AddFeatureFormProps, AddFeatureFormState } from '../../Features.type';
import { StyledInput } from './AddFeatureForm.styled';
import './style.scss';
import { addFeatureSchema } from '../../validationSchemas';
import { GLOBAL_RESOURCE_ACTION_TYPES } from '@widgets/Polaris/src/providers/AdminProvider/ResourceReducer';
import {
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@widgets/Polaris/src/hooks/globalSettings/shared/useDisplaySnackbar';

export const AddFeatureForm = ({ resources, isModalOpen, onDialogClose }: AddFeatureFormProps) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings.features',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const { displaySnackbar } = useDisplaySnackbar();
  const [featureFormState, setFeatureFormState] = useState<AddFeatureFormState>(
    defaultAddFeatureFormState(),
  );
  const { dispatch } = useContext(AdminContext);
  const { createResource } = useCreateResource();
  const { getResourceById } = useGetResourceById();
  const featureGroupSelectOptions = featureGroupOptions(resources);
  // Default selected feature group
  featureFormState.metadata.group = featureGroupSelectOptions[0]?.text;

  const handleDialogClose = () => {
    formik.resetForm();
    setFeatureFormState(defaultAddFeatureFormState());
    onDialogClose(false);
  };

  const formik = useFormik({
    initialValues: featureFormState,
    validationSchema: addFeatureSchema(tRootLang, t),
    onSubmit: async (feature: AddFeatureFormState) => {
      formik.setSubmitting(true);
      try {
        const createResponse: { ids: string[] } = await createResource(feature);
        const resource = await getResourceById(createResponse.ids[0]);
        dispatch({ type: GLOBAL_RESOURCE_ACTION_TYPES.ADD_RESOURCE, payload: resource });
        handleDialogClose();

        displaySnackbar(
          t('addFeatureModal.snackBarSuccessMessage', {
            displayName: feature.metadata.displayName,
          }),
          SNACKBAR_VARIANT.SUCCESS,
        );
      } catch (error) {
        console.error('Error creating new feature data:', error);
        formik.setFieldError('metadata.polarisId', t('addFeatureModal.errors.alreadyExistsId'));
      }
    },
  });

  return (
    <>
      {isModalOpen && (
        <GenericDialog
          styles={{
            middleStyle: {
              gap: '$x4',
            },
          }}
          isOpen={isModalOpen}
          isSubmitDisabled={formik.isSubmitting}
          title={t('addFeatureModal.title')}
          submitLabel={tRootLang('common.saveChanges')}
          closeOnOverlayClick={false}
          onClose={handleDialogClose}
          onSubmit={formik.handleSubmit}
        >
          <Input
            size='s'
            label={t('table.headers.featureID')}
            name='metadata.polarisId'
            aria-label='feature-id'
            data-cy={`global-settings-feature-input-feature-id`}
            className={
              formik.touched.metadata?.polarisId && formik.errors.metadata?.polarisId
                ? 'featureId'
                : ''
            }
            helperText={
              (formik.touched.metadata?.polarisId && formik.errors.metadata?.polarisId) ||
              t('addFeatureModal.inputIdHelperText')
            }
            value={formik.values.metadata?.polarisId}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            variant={
              formik.touched.metadata?.polarisId && formik.errors.metadata?.polarisId
                ? 'error'
                : 'default'
            }
            disabled={formik.isSubmitting}
            required
          />

          <StyledInput>
            <Input
              size='s'
              label={t('table.headers.featureName')}
              name='name'
              aria-label='featureName'
              className='feature-name'
              data-cy={`global-settings-feature-input-feature-name`}
              helperText={(formik.touched.name && formik.errors.name) || ''}
              value={formik.values.name}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              variant={formik.touched.name && formik.errors.name ? 'error' : 'default'}
              disabled={formik.isSubmitting}
              required
            />
          </StyledInput>

          <Input
            size='s'
            label={t('table.headers.displayName')}
            name='metadata.displayName'
            aria-label='displayName'
            className='display-name'
            data-cy={`global-settings-feature-dialog-input-display-name`}
            helperText={
              (formik.touched.metadata?.displayName && formik.errors.metadata?.displayName) ||
              t('addFeatureModal.displayNameHint')
            }
            value={formik.values.metadata?.displayName}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            variant={
              formik.touched.metadata?.displayName && formik.errors.metadata?.displayName
                ? 'error'
                : 'default'
            }
            disabled={formik.isSubmitting}
            required
          />

          <SelectWrapper dataCy='global-settings-feature-dialog-input-group'>
            <Select
              size='s'
              cover='outline'
              label={t('table.headers.group')}
              items={featureGroupSelectOptions}
              value={formik.values.metadata?.group}
              onChange={(value: string) => {
                formik.setFieldValue('metadata.group', value, true);
              }}
              disabled={formik.isSubmitting}
              required
              onFocus={null}
              onBlur={null}
              position='popper'
            />
          </SelectWrapper>
        </GenericDialog>
      )}
    </>
  );
};
