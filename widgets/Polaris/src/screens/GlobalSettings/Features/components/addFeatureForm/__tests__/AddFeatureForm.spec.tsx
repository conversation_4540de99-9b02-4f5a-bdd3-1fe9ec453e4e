import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useCreateResource, useGetResourceById } from '@polaris-hooks/index';
import { AddFeatureForm } from '../AddFeatureForm';
import { AddFeatureFormProps } from '@widgets/Polaris/src/screens/GlobalSettings/Features/Features.type';
import { resourceResponseMock } from '@common/mocks';

// Mock dependencies
jest.mock('@widgets/Polaris/src/providers/AppProvider', () => ({
  useAppContext: jest.fn(),
}));
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));
jest.mock('@libs/snackbar-context/snackbar-context', () => ({
  ...jest.requireActual('@libs/snackbar-context/snackbar-context'),
  useSnackbar: () => ({
    setDisplaySnackbar: jest.fn(),
  }),
}));

describe('AddFeatureFormDialog', () => {
  const mockOnDialogClose = jest.fn();
  const defaultProps: AddFeatureFormProps = {
    resources: resourceResponseMock,
    isModalOpen: false,
    onDialogClose: mockOnDialogClose,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useCreateResource as jest.Mock).mockReturnValue({
      createResource: jest.fn(),
    });
    (useGetResourceById as jest.Mock).mockReturnValue({
      getResourceById: jest.fn(),
    });
  });

  it('should not open modal when isModalOpen is false', async () => {
    const addFeatureProps = { ...defaultProps, isModalOpen: false };
    render(<AddFeatureForm {...addFeatureProps} />);
    expect(screen.queryByText('addFeatureModal.title')).not.toBeInTheDocument();
  });

  it('should open modal when isModalOpen is true', async () => {
    const addFeatureProps = { ...defaultProps, isModalOpen: true };
    render(<AddFeatureForm {...addFeatureProps} />);

    // Check if the modal form fields is rendered
    expect(screen.getByText('addFeatureModal.title')).toBeInTheDocument();
    expect(screen.getByText('table.headers.featureID')).toBeInTheDocument();
    expect(screen.getByText('addFeatureModal.inputIdHelperText')).toBeInTheDocument();
    expect(screen.getByText('table.headers.featureName')).toBeInTheDocument();
    expect(screen.getByText('table.headers.displayName')).toBeInTheDocument();
    expect(screen.getByText('table.headers.group')).toBeInTheDocument();
    expect(screen.getByText('common.saveChanges')).toBeInTheDocument();
  });

  it('should perform input and dropdown change', () => {
    // Prepare props and render the component
    const addFeatureProps = { ...defaultProps, isModalOpen: true };
    render(<AddFeatureForm {...addFeatureProps} />);

    // Check if the modal title is rendered
    expect(screen.getByText('addFeatureModal.title')).toBeInTheDocument();

    // Get input fields and dropdown
    const featureId = screen.getByTestId('global-settings-feature-input-feature-id');
    const featureName = screen.getByTestId('global-settings-feature-input-feature-name');
    const displayName = screen.getByTestId('global-settings-feature-dialog-input-display-name');
    const featureGroupSelect = screen.getByTestId('global-settings-feature-dialog-input-group');

    // Simulate user input for featureId
    fireEvent.change(featureId, { target: { value: '123' } });
    waitFor(() => {
      expect(featureId).toHaveAttribute('value', '123');
    });

    // Simulate user input for featureName
    fireEvent.change(featureName, { target: { value: 'Test Feature' } });
    waitFor(() => {
      expect(featureName).toHaveAttribute('value', 'Test Feature');
    });

    // Simulate user input for displayName
    fireEvent.change(displayName, { target: { value: 'Display Name' } });
    waitFor(() => {
      expect(displayName).toHaveAttribute('value', 'Display Name');
    });

    expect(featureGroupSelect).toBeInTheDocument();
  });

  it('should close modal on click of button action', () => {
    const addFeatureProps = { ...defaultProps, isModalOpen: true };
    render(<AddFeatureForm {...addFeatureProps} />);

    const closeButton = screen.getByTestId('generic-dialog-continue-btn');
    expect(closeButton).toBeInTheDocument();
    fireEvent.click(closeButton);
    waitFor(() => {
      expect(mockOnDialogClose).toHaveBeenCalled();
    });
  });

  it('should display the input field without error styling and show helper text when there are no validation errors', () => {
    // Render the component with required props
    const addFeatureProps = { ...defaultProps, isModalOpen: true };
    render(<AddFeatureForm {...addFeatureProps} />);

    // Find the input field
    const input = screen.getByTestId('global-settings-feature-input-feature-id');

    // Verify the input is in its default state
    expect(input).not.toHaveClass('error');
    expect(screen.queryByText('isRequired')).toBeNull();

    // Verify the helper text is displayed
    expect(screen.getByText('addFeatureModal.inputIdHelperText')).toBeInTheDocument();
  });

  it('should display error styling and error message when there are validation errors', () => {
    // Render the component with required props and simulate an error state
    const addFeatureProps = {
      ...defaultProps,
      isModalOpen: true,
      hasError: true,
    };
    render(<AddFeatureForm {...addFeatureProps} />);

    const input = screen.getByText('addFeatureModal.inputIdHelperText');
    expect(input).toBeInTheDocument();
    expect(screen.queryByText('addFeatureModal.inputIdHelperText')).toBeInTheDocument();
  });
});
