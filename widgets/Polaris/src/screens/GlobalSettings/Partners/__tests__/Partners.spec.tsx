import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { AdminContext } from '@widgets/Polaris/src/providers';
import Partners from '../Partners';
import {
  CountryCombobox,
  FeatureCombobox,
  useGetApplications,
  useGetComboboxItems,
} from '@polaris-hooks/globalSettings';
import { applicationResponseMock } from '@common/mocks';

// Mock dependencies
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));

describe('Widget: Partners Component', () => {
  const mockDispatch = jest.fn();
  const defaultContext = {
    state: {
      features: [],
      partners: [],
      resources: [],
      applications: applicationResponseMock,
    },
    dispatch: mockDispatch,
  };
  const extendedContext = {
    state: {
      features: [],
      partners: [],
      resources: [],
      applications: Array(8).fill(applicationResponseMock[0]),
    },
    dispatch: mockDispatch,
  };

  const renderPartners = (context = defaultContext) => {
    return render(
      <AdminContext.Provider value={context}>
        <Partners />
      </AdminContext.Provider>,
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useGetApplications as jest.Mock).mockReturnValue({
      data: { entities: applicationResponseMock },
      isMutating: false,
      error: null,
    });
    (useGetComboboxItems as jest.Mock).mockReturnValue({
      countriesItems: [
        { id: '1', value: { id: '1', name: 'Country A' }, label: 'Country A' },
        { id: '2', value: { id: '2', name: 'Country B' }, label: 'Country B' },
      ] as unknown as CountryCombobox[],
      featuresItems: [
        { id: '1', value: { id: '1', displayName: 'Feature 1' }, label: 'Feature 1' },
        { id: '2', value: { id: '2', displayName: 'Feature 2' }, label: 'Feature 2' },
      ] as unknown as FeatureCombobox[],
    });
  });

  describe('Initial Rendering', () => {
    it('should render the component container', () => {
      renderPartners();
      expect(screen.getByTestId('partner-container')).toBeInTheDocument();
    });

    it('should display the correct partner count', () => {
      renderPartners();
      expect(screen.getByText('1')).toBeInTheDocument();
    });

    it('should render all table columns', () => {
      renderPartners();
      const expectedHeaders = [
        'headers.partner',
        'headers.displayName',
        'headers.partnerType',
        'headers.countries',
        'headers.features',
        'headers.lastUpdated',
        'headers.actions',
      ];

      expectedHeaders.forEach((header) => {
        expect(screen.getByText(header)).toBeInTheDocument();
      });
    });
  });

  describe('Column Visibility Toggle', () => {
    it('should toggle hidden columns when switch is clicked', () => {
      renderPartners();
      const switchElement = screen.getByRole('switch');

      fireEvent.click(switchElement);

      expect(screen.getByTestId('partner-data-row')).toBeInTheDocument();
      expect(screen.getByText(applicationResponseMock[0].id)).toBeInTheDocument();
      expect(
        screen.getAllByText(applicationResponseMock[0].metadata.displayName)[0],
      ).toBeInTheDocument();
      expect(screen.getByText(applicationResponseMock[0].code)).toBeInTheDocument();
    });

    it('should show additional search inputs when columns are visible', () => {
      renderPartners();
      const switchElement = screen.getByRole('switch');

      // Initial search inputs
      const initialInputs = [
        'partner-head-search-row name',
        'partner-head-search-row displayName',
        'partner-head-search-row countries',
        'partner-head-search-row capabilities',
      ];

      initialInputs.forEach((testId) => {
        expect(screen.getByTestId(testId)).toBeInTheDocument();
      });

      // Toggle to show hidden columns
      fireEvent.click(switchElement);

      // Additional input after toggle
      expect(screen.getByTestId('partner-head-search-row id')).toBeInTheDocument();
    });
  });

  describe('Pagination', () => {
    it('should not show pagination for 7 or fewer items', () => {
      renderPartners();
      expect(screen.queryByTestId('partner-table-pagination')).not.toBeInTheDocument();
    });

    it('should show pagination for more than 7 items', () => {
      renderPartners(extendedContext);
      expect(screen.getByLabelText('paginationControl')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('should handle search input changes', () => {
      renderPartners();

      const testCases = [
        { testId: 'partner-head-search-row name', value: 'demo' },
        { testId: 'partner-head-search-row displayName', value: 'FarmGo' },
        { testId: 'partner-head-search-row countries', value: 'india, spain' },
        { testId: 'partner-head-search-row capabilities', value: 'CNP' },
      ];

      testCases.forEach(({ testId, value }) => {
        const input = screen.getByTestId(testId);
        fireEvent.change(input, { target: { value } });
        expect(input).toHaveAttribute('value', value);
      });
    });
  });
});
