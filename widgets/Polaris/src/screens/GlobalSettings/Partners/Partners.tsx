import React, { Fragment, useContext, useEffect, useMemo, useState } from 'react';
import {
  Button,
  Caption,
  IconButton,
  Input,
  Pagination,
  Subtitle,
  Switch,
  Table,
  Title,
  Tooltip,
} from '@yaradigitallabs/ahua-react';
import { useGetApplications } from '@polaris-hooks/index';
import { ReactI18NextChild, useTranslation } from 'react-i18next';
import './styles.scss';
import { AdminContext, Application } from '@widgets/Polaris/src/providers';
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { GLOBAL_APPLICATION_ACTION_TYPES } from '@widgets/Polaris/src/providers/AdminProvider/ApplicationReducer';

export default function Partners() {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings.partners',
  });

  // const { data, isMutating, error } = usePartnerServices();

  const { data, isMutating: isMutating, error } = useGetApplications();

  // TODO: to be enabled after YAPF-16473 is done
  // const { countriesItems, featuresItems } = useGetComboboxItems();
  // const [addPartnerDialogIsOpen, setAddPartnerDialogIsOpen] = useState<boolean>(false);
  // const [selectedDeletePartner, setSelectedDeletePartner] = useState<Nullable<Partner>>();
  // const [selectedEditPartner, setSelectedEditPartner] = useState<Nullable<Partner>>();

  const { state: adminState, dispatch } = useContext(AdminContext);
  const { applications } = adminState;

  useEffect(() => {
    if (data && !isMutating && !error) {
      dispatch({
        type: GLOBAL_APPLICATION_ACTION_TYPES.INIT_APPLICATION,
        payload: data,
      });
    }
  }, [data, isMutating, error]);

  const getPartnerColumns = useMemo<ColumnDef<Application, ReactI18NextChild>[]>(
    () => [
      {
        header: t('headers.partnerCode'),
        accessorKey: 'code',
        enableColumnFilter: false,
      },
      {
        header: t('headers.partnerId'),
        accessorKey: 'id',
        enableSorting: false,
        cell: (info) => (
          <div className='partnerId'>
            <Caption size={'n'}>{info.getValue()}</Caption>
            <IconButton
              className='copy-icon'
              colorConcept='brand'
              size='xs'
              icon='Copy'
              type='ghost'
            />
          </div>
        ),
      },
      {
        header: t('headers.partner'),
        accessorKey: 'name',
      },
      {
        header: t('headers.displayName'),
        accessorKey: 'displayName',
        enableSorting: false,
        cell: ({ row }) => {
          const { metadata } = row.original;

          return (
            <span
              data-cy={`${metadata.displayName}-status`}
              className={`status ${metadata.isInternal ? 'blue' : 'brown'}`}
            >
              {metadata.displayName}
            </span>
          );
        },
      },
      {
        header: t('headers.partnerType'),
        accessorKey: 'isInternal',
        enableColumnFilter: false,
        cell: (cell) => {
          const { metadata } = cell.row.original;
          const partnerType = metadata?.isInternal ? 'content.internal' : 'content.external';
          return (
            <Caption
              data-cy={`partner type ${cell.column.id}`}
              className={`partnerGroup ${metadata?.isInternal ? 'blue' : 'brown'}`}
            >
              {t(partnerType)}
            </Caption>
          );
        },
      },
      {
        header: t('headers.countries'),
        accessorKey: 'countries',
        enableSorting: false,
        cell: (country) => {
          const countries =
            country.row.original.metadata?.countries?.map(({ name }) => name).join(', ') || '-';
          return <p>{countries}</p>;
        },
        filterFn: (row, _, filterValue) => {
          const countries =
            row.original?.metadata?.countries?.map(({ name }) => name)?.join(', ') || '';

          return countries.toLocaleLowerCase()?.includes(filterValue.toLowerCase());
        },
      },
      {
        header: t('headers.features'),
        accessorKey: 'capabilities',
        enableSorting: false,
        cell: ({ row }) => {
          const { capabilities } = row.original.metadata;

          return (
            <ul>
              {capabilities?.length
                ? capabilities?.slice(0, 4)?.map((cap) => (
                    <li key={`${cap.id}${cap.name}`}>
                      <Caption size='n'>{cap.name}</Caption>
                    </li>
                  ))
                : null}
            </ul>
          );
        },
        filterFn: (row, _, filterValue) => {
          const capabilities = row.original?.metadata?.capabilities || [];
          const features = capabilities
            .map(({ name }) => name || '')
            .join(', ')
            .toLowerCase();
          return features.includes((filterValue || '').toLowerCase());
        },
      },
      {
        header: t('headers.lastUpdated'),
        id: 'lastUpdated',
        accessorKey: 'modified',
        enableColumnFilter: false,
        accessorFn: (row) => `${row.updatedBy} \n ${new Date(row.updatedAt).toLocaleString()}`,
        cell: (row) => {
          return (
            <div className='last-updated-partners'>
              <Tooltip
                concept='inverse'
                maxWidth={232}
                position={'top'}
                text={row.getValue<string>()}
                className='last-updated-tooltip'
              >
                <IconButton
                  data-cy={`last-updated-partners`}
                  icon='Info'
                  size='xs'
                  colorConcept='neutral'
                />
              </Tooltip>
            </div>
          );
        },
        sortingFn: (firstRow, secondRow) => {
          return (
            new Date(firstRow.original.updatedAt).getTime() -
            new Date(secondRow.original.updatedAt).getTime()
          );
        },
      },
      {
        header: t('headers.actions'),
        accessorKey: 'actions',
        enableSorting: false,
        enableColumnFilter: false,
      },
    ],
    [],
  );

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState({
    code: false,
    id: false,
  });
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 7 });
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: 'lastUpdated',
      desc: true, // Latest lastUpdated data by default first row
    },
  ]);
  const table = useReactTable({
    columns: getPartnerColumns,
    data: applications,
    state: { columnFilters, columnVisibility, pagination, sorting },
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div className='partnerContainer' data-cy='partner-container'>
      <div className='head'>
        <Title maxLineCount={1} size='n'>
          {t('header')}
        </Title>
        <Button
          size='s'
          //  onClick={() => setAddPartnerDialogIsOpen(true)}
        >
          {t('addPartner.title')}
        </Button>
      </div>
      <div className='subhead'>
        <Subtitle className='subtitle' size={'s'}>
          {t('partners')}: <b>{applications?.length}</b>
        </Subtitle>
        <div className='switch' data-cy='switch'>
          <Switch
            data-cy='switch-label'
            label={t('switchLabel')}
            onCheckedChange={(checked: boolean) =>
              setColumnVisibility({ code: checked, id: checked })
            }
          />
        </div>
      </div>
      <div className='partnerTableWrapper'>
        <Table className='partner-table'>
          <thead className='table-head' data-cy='table-head'>
            {table.getHeaderGroups().map((headerGroup) => {
              return (
                <Fragment key={headerGroup.id}>
                  <Table.Row className='table-head-row' data-cy='partner-head-row'>
                    {headerGroup.headers.map((header) => (
                      <Table.SortHead
                        key={header.id}
                        data-cy={`partner-head-row ${header.id}`}
                        className={header.column.id}
                        direction={header.column.getIsSorted()}
                        active={Boolean(header.column.getIsSorted())}
                        onClick={header.column.getToggleSortingHandler()}
                        color={'primary'}
                        colSpan={header.colSpan}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                      </Table.SortHead>
                    ))}
                  </Table.Row>
                  <Table.Row className='table-head-search-row' data-cy='partner-head-search-row'>
                    {headerGroup.headers.map((header) => (
                      <Table.Head key={`id ${header.id}`}>
                        {header.column.getCanFilter() && (
                          <Input
                            size='xs'
                            aria-placeholder='Search'
                            onChange={() => null}
                            onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) =>
                              header.column.setFilterValue(e.target.value)
                            }
                            label='Search'
                            value={
                              header.column.getFilterValue()
                                ? String(header.column.getFilterValue())
                                : ''
                            }
                            iconTrailing={
                              header.column.getFilterValue()
                                ? {
                                    icon: 'Close',
                                    onClick: () => header.column.setFilterValue(''),
                                  }
                                : {
                                    icon: 'Search',
                                    onClick: () => header.column.setFilterValue(''),
                                  }
                            }
                            data-cy={`partner-head-search-row ${header.id}`}
                          />
                        )}
                      </Table.Head>
                    ))}
                  </Table.Row>
                </Fragment>
              );
            })}
          </thead>
          <tbody className='table-body' data-cy='table-body'>
            {table.getRowModel().rows.map((row) => {
              return (
                <Table.Row key={row.id} data-cy='partner-data-row' className='partner-data-row'>
                  {row.getVisibleCells().map((cell) => (
                    <Fragment key={cell.column.id}>
                      {cell.column.id !== 'actions' ? (
                        <Table.Cell
                          key={cell.id}
                          className={cell.column.id}
                          data-cy='partner-data-cell'
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </Table.Cell>
                      ) : (
                        <Table.ActionsCell
                          className={cell.column.id}
                          key={cell.id}
                          data-cy={`partner-data-cell ${cell.id}`}
                        >
                          <Table.ActionMenu title='Actions'>
                            <Table.ActionMenuItem
                              icon='Edit'
                              data-cy='partner-data-cell-actions-edit'
                              // onClick={() => setSelectedEditPartner(row?.original)}
                            >
                              {t('actions.edit')}
                            </Table.ActionMenuItem>
                            <Table.ActionMenuItem
                              icon='Delete'
                              data-cy='partner-data-cell-actions-delete'
                              // onClick={() => setSelectedDeletePartner(row.original)}
                            >
                              {t('actions.delete')}
                            </Table.ActionMenuItem>
                          </Table.ActionMenu>
                        </Table.ActionsCell>
                      )}
                    </Fragment>
                  ))}
                </Table.Row>
              );
            })}
          </tbody>
        </Table>
      </div>
      {table?.getPageCount() > 1 && (
        <div className='pagination' data-cy='partner-table-pagination'>
          <Pagination>
            <Pagination.Control
              pageIndex={pagination.pageIndex}
              pageCount={table.getPageCount()}
              ariaLabel='paginationControl'
              onFirstPage={() => table.firstPage()}
              onLastPage={() => table.lastPage()}
              onNextPage={() => table.nextPage()}
              onPrevPage={() => table.previousPage()}
              canNextPage={table.getCanNextPage()}
              canPreviousPage={table.getCanPreviousPage()}
            />
          </Pagination>
        </div>
      )}
      {/* {addPartnerDialogIsOpen && (
        <AddPartnerDialog
          isOpen={addPartnerDialogIsOpen}
          onClose={() => setAddPartnerDialogIsOpen(false)}
        />
      )}
      {selectedEditPartner && (
        <EditPartnerDialog
          partner={selectedEditPartner}
          countriesItems={countriesItems}
          featuresItems={featuresItems}
          onChange={() => setSelectedEditPartner(null)}
        />
      )}
      {selectedDeletePartner && (
        <DeletePartner
          partner={selectedDeletePartner}
          onChange={() => setSelectedDeletePartner(null)}
        />
      )} */}
    </div>
  );
}
