/* eslint-disable @typescript-eslint/no-explicit-any */
import { Country, Feature } from '@common/types';

export const getCountryObject = (data: any): Country | undefined => {
  if (
    'id' in data &&
    'name' in data &&
    'translationKey' in data &&
    'countryCode' in data &&
    'productSetCode' in data &&
    'applicationTags' in data &&
    'tagsConfiguration' in data &&
    'currencyId' in data &&
    'isDefaultUnitSystemMetric' in data &&
    'windSpeedDefaultUnitId' in data &&
    'precipitationAmountDefaultUnitId' in data &&
    'evapotranspirationRefDefaultUnitId' in data &&
    'qpfSnowAmountDefaultUnitId' in data &&
    'temperatureDefaultUnitId' in data &&
    'dewPointDefaultUnitId' in data &&
    'availableNutrients' in data &&
    'created' in data &&
    'modified' in data &&
    'modifiedBy' in data &&
    'deleted' in data
  ) {
    return data;
  }
};

export const getFeatureObject = (data: any): Feature | undefined => {
  if (
    'id' in data &&
    'name' in data &&
    'displayName' in data &&
    'group' in data &&
    'code' in data &&
    'created' in data &&
    'modified' in data &&
    'modifiedBy' in data &&
    'deleted' in data
  ) {
    return data;
  }
};
