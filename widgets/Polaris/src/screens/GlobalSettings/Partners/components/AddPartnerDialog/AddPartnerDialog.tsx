import React, { useContext, useEffect, useState } from 'react';
import { Combobox, ComboboxOption, Input, Select } from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import { GenericDialog } from '@widgets/Polaris/src/components';
import { Country, CropDropdownValue, Feature, PartnerTagInputDTO } from '@common/types';
import { useAddPartner, useFeatureServices } from '@polaris-hooks/index';
import { useFormik } from 'formik';
import { orderBy } from 'lodash';
import {
  AdminContext,
  GLOBAL_PARTNER_ACTION_TYPES,
  useAppContext,
} from '@widgets/Polaris/src/providers';
import { addPartnerSchema } from '../../validationSchemas';
import { defaultPartnerTagInput } from './constants';
import './styles.scss';
import {
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@widgets/Polaris/src/hooks/globalSettings/shared/useDisplaySnackbar';
import { getCountryObject, getFeatureObject } from '../../utils/PartnersUtils';

export interface AddPartnerDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddPartnerDialog: React.FC<AddPartnerDialogProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings.partners',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const { countries } = useAppContext();
  const { data: features } = useFeatureServices();
  const { addPartner } = useAddPartner();
  const { displaySnackbar } = useDisplaySnackbar();
  const { dispatch } = useContext(AdminContext);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [partner, setPartner] = useState<PartnerTagInputDTO>(defaultPartnerTagInput());
  const [countriesItems, setCountriesItems] = useState<ComboboxOption[]>();
  const [featuresItems, setFeaturesItems] = useState<ComboboxOption[]>();
  const isInternalSelectItems: CropDropdownValue[] = [
    {
      text: t('content.internal'),
      value: 'internal',
    },
    {
      text: t('content.external'),
      value: 'external',
    },
  ];

  useEffect(() => {
    setIsModalOpen(isOpen);
  }, [isOpen]);

  useEffect(() => {
    if (features) {
      const items: ComboboxOption[] = features.entities.map((f: Feature) => ({
        value: f,
        label: f.displayName,
        description: f.group,
      }));
      setFeaturesItems(orderBy(items, ['description'], ['asc']));
    }
  }, [features]);

  useEffect(() => {
    const items: ComboboxOption[] = countries.map((c: Country) => ({
      value: c,
      label: c.name,
    }));
    setCountriesItems(items);
  }, [countries]);

  const formik = useFormik({
    initialValues: partner,
    validationSchema: addPartnerSchema(tRootLang, t),
    onSubmit: async (partner: PartnerTagInputDTO) => {
      formik.setSubmitting(true);
      try {
        const response = await addPartner(partner);
        dispatch({
          type: GLOBAL_PARTNER_ACTION_TYPES.ADD_PARTNER,
          payload: response,
        });
        handleDialogClose();
        displaySnackbar(
          t('addPartner.snackBarSuccessMessage', {
            displayName: partner.displayName,
          }),
          SNACKBAR_VARIANT.SUCCESS,
        );
      } catch (error) {
        console.error('Error creating add-partner data:', error);
        formik.setFieldError('id', t('addPartner.errors.alreadyExists'));
      }
    },
  });

  const handleDialogClose = () => {
    formik.resetForm();
    setPartner(defaultPartnerTagInput());
    onClose();
  };

  return (
    <>
      {isModalOpen && (
        <GenericDialog
          styles={{
            middleStyle: {
              gap: '$x4',
            },
          }}
          isOpen={isModalOpen}
          isSubmitDisabled={formik.isSubmitting}
          title={t('addPartner.title')}
          submitLabel={tRootLang('common.saveChanges')}
          closeOnOverlayClick={false}
          onClose={handleDialogClose}
          onSubmit={formik.handleSubmit}
        >
          {/*TODO: Generate Partner.ID in backend https://yaradigitalfarming.atlassian.net/browse/YAPF-15572*/}
          <Input
            size='s'
            label={t('headers.partnerId')}
            name='id'
            aria-label='id'
            data-cy={`global-settings-partners-dialog-input-id`}
            helperText={
              (formik.touched.id && formik.errors.id) || t('addPartner.inputIdHelperText')
            }
            value={formik.values.id}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            variant={formik.touched.id && formik.errors.id ? 'error' : 'default'}
            disabled={formik.isSubmitting}
            required
          />
          <div className={formik.touched.name && formik.errors.name ? '' : 'hidden'}>
            <Input
              size='s'
              label={t('headers.partnerName')}
              name='name'
              aria-label='name'
              data-cy={`global-settings-partners-dialog-input-name`}
              helperText={(formik.touched.name && formik.errors.name) || 'hidden'}
              value={formik.values.name}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              variant={formik.touched.name && formik.errors.name ? 'error' : 'default'}
              disabled={formik.isSubmitting}
              required
            />
          </div>
          <Input
            size='s'
            label={t('headers.displayName')}
            name='displayName'
            aria-label='displayName'
            data-cy={`global-settings-partners-dialog-input-displayName`}
            helperText={
              (formik.touched.displayName && formik.errors.displayName) ||
              t('addPartner.inputDisplayNameHelperText')
            }
            value={formik.values.displayName}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            variant={formik.touched.displayName && formik.errors.displayName ? 'error' : 'default'}
            disabled={formik.isSubmitting}
            required
          />
          <Select
            size='s'
            cover='outline'
            label={t('headers.partnerType')}
            variant={formik.errors.isInternal ? 'error' : 'default'}
            items={isInternalSelectItems}
            helper-text={(formik.touched.isInternal && formik.errors.isInternal) || ''}
            value={formik.values.isInternal ? 'internal' : 'external'}
            onChange={(value: string) => {
              formik.setFieldValue('isInternal', value === 'internal', true);
            }}
            disabled={formik.isSubmitting}
            required
            onFocus={null}
            onBlur={null}
            position='popper'
          />
          <Combobox
            className={'global-settings-styled-combo-box'}
            size='s'
            cover='outline'
            placeholder={t('headers.countries')}
            label={t('headers.countries')}
            ariaLabel='countries'
            maxOptions={5}
            options={countriesItems}
            onChange={(selected: ComboboxOption[]) => {
              const selectedCountries = selected.map((s) => getCountryObject(s.value));
              formik.setFieldValue('countries', selectedCountries);
            }}
          />
          <Combobox
            className={'global-settings-styled-combo-box'}
            size='s'
            cover='outline'
            placeholder={t('headers.features')}
            label={t('headers.features')}
            ariaLabel='capabilities'
            maxOptions={5}
            options={featuresItems}
            onChange={(selected: ComboboxOption[]) => {
              const selectedFeatures = selected.map((s) => getFeatureObject(s.value));
              formik.setFieldValue('capabilities', selectedFeatures);
            }}
          />
        </GenericDialog>
      )}
    </>
  );
};
