import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { AddPartnerDialog, AddPartnerDialogProps } from '../AddPartnerDialog';
import { useAddPartner, useFeatureServices } from '@polaris-hooks/index';
import { useAppContext } from '@widgets/Polaris/src/providers';
import { countriesMock } from '@common/mocks';

// Mock dependencies
jest.mock('@widgets/Polaris/src/providers/AppProvider', () => ({
  useAppContext: jest.fn(),
}));
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));
const mockSetDisplaySnackbar = jest.fn();
jest.mock('@libs/snackbar-context/snackbar-context', () => ({
  ...jest.requireActual('@libs/snackbar-context/snackbar-context'),
  useSnackbar: () => ({
    setDisplaySnackbar: mockSetDisplaySnackbar,
  }),
}));
const mockPartners = [
  {
    id: '613e75b4-c565-4eb2-8487-4c62a6c61f1a',
    name: 'Mock name',
    displayName: 'Mock display name',
    isInternal: true,
    countries: [],
    capabilities: [],
  },
];

describe('AddPartnerDialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useFeatureServices as jest.Mock).mockReturnValue({
      data: { entities: mockPartners },
      isMutating: false,
      error: null,
    });
    (useAddPartner as jest.Mock).mockReturnValue({
      triggerAddPartnerTag: jest.fn(),
    });
    (useAppContext as jest.Mock).mockReturnValue({
      countries: countriesMock,
      methods: {
        setCountries: jest.fn(),
      },
    });
  });

  const mockOnClose = jest.fn();
  const AddPartnerDialogProps: AddPartnerDialogProps = {
    isOpen: false,
    onClose: mockOnClose,
  };

  it('should not show modal when isOpen is false', async () => {
    const props = { ...AddPartnerDialogProps, isOpen: false };
    render(<AddPartnerDialog {...props} />);
    expect(screen.queryByText('addPartner.title')).not.toBeInTheDocument();
  });

  it('should show modal when isOpen is true', async () => {
    const props = { ...AddPartnerDialogProps, isOpen: true };
    render(<AddPartnerDialog {...props} />);
    expect(screen.getByText('addPartner.title')).toBeInTheDocument();
    expect(screen.getByText('headers.partnerId')).toBeInTheDocument();
    expect(screen.getByText('headers.partnerName')).toBeInTheDocument();
    expect(screen.getByText('headers.displayName')).toBeInTheDocument();
    expect(screen.getByText('headers.partnerType')).toBeInTheDocument();
    expect(screen.getByText('headers.countries')).toBeInTheDocument();
    expect(screen.getByText('headers.features')).toBeInTheDocument();
    expect(screen.getByText('common.saveChanges')).toBeInTheDocument();
  });

  it('should handle close action', () => {
    const props = { ...AddPartnerDialogProps, isOpen: true };
    render(<AddPartnerDialog {...props} />);

    const closeButton = screen.getByTestId('generic-dialog-continue-btn');
    expect(closeButton).toBeInTheDocument();
    fireEvent.click(closeButton);
    waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('shows default variant and helper text when there are no errors', () => {
    const props = { ...AddPartnerDialogProps, isOpen: true };
    render(<AddPartnerDialog {...props} />);

    const input = screen.getByTestId('global-settings-partners-dialog-input-id');
    expect(input).not.toHaveClass('error');
    expect(screen.queryByText('isRequired')).toBeNull();
    expect(screen.queryByText('addPartner.inputIdHelperText')).toBeInTheDocument();
    expect(screen.queryByText('addPartner.inputDisplayNameHelperText')).toBeInTheDocument();
  });
});
