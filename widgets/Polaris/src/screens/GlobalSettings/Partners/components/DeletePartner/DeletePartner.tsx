import React, { useContext, useEffect, useState } from 'react';
import { useCanDeletePartner, useSoftDeletePartner } from '@polaris-hooks/index';
import { useTranslation } from 'react-i18next';
import { AdminContext, GLOBAL_PARTNER_ACTION_TYPES, Partner } from '@widgets/Polaris/src/providers';
import {
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@polaris-hooks/globalSettings/shared/useDisplaySnackbar';
import { CheckLinkedDataPopup } from '../../../components/CheckLinkedDataPopup';
import { ConfirmDeleteLinkedDataDialog } from '../../../components/ConfirmDeleteLinkedDataDialog';
import { ErrorDeleteLinkedDataDialog } from '../../../components/ErrorDeleteLinkedDataDialog';
export interface DeletePartnerProps {
  partner: Partner;
  onChange: () => void;
}

export const DeletePartner: React.FC<DeletePartnerProps> = ({ partner, onChange }) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });

  const { canDeletePartner } = useCanDeletePartner(partner?.id);
  const { softDeletePartner, isMutating } = useSoftDeletePartner(partner?.id);
  const { displaySnackbar } = useDisplaySnackbar();
  const { dispatch } = useContext(AdminContext);
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [isDeleteAllowed, setIsDeleteAllowed] = useState<boolean>(false);
  const [deleteErrorMessages, setDeleteErrorMessages] = useState<string[]>([]);

  useEffect(() => {
    const execDeletePartner = async () => {
      setIsVerifying(true);
      try {
        const response = await canDeletePartner();
        setIsDeleteAllowed(response.success);
        if (!response.success) {
          setDeleteErrorMessages(response.usedIn);
        }
      } catch (e) {
        console.error('Error checking on partner tag links:', e);
        handleError();
      } finally {
        setIsVerifying(false);
      }
    };
    partner?.id && execDeletePartner();
  }, [partner]);

  const handleError = () => {
    displaySnackbar(tRootLang('error.wentWrongTitle'), SNACKBAR_VARIANT.ERROR);
  };

  const handleOnChange = () => {
    setIsDeleteAllowed(false);
    onChange();
  };

  const handleOnOk = async () => {
    try {
      const response = await softDeletePartner();
      if (response.success) {
        dispatch({
          type: GLOBAL_PARTNER_ACTION_TYPES.DELETE_PARTNER,
          payload: partner,
        });
        displaySnackbar(
          tRootLang('common.snackbarDeleteMessage', {
            name: partner.displayName,
          }),
          SNACKBAR_VARIANT.SUCCESS,
        );
      } else {
        handleError();
      }
    } catch (error) {
      console.error('Error deleting partner data:', error);
      handleError();
    } finally {
      handleOnChange();
    }
  };

  return (
    <>
      <CheckLinkedDataPopup isOpen={isVerifying} title={t('common.checkLink')} />
      <ConfirmDeleteLinkedDataDialog
        isOpen={isDeleteAllowed}
        title={t('common.confirmDelete', { resourceName: partner.name })}
        description={t('common.confirmDeleteDescription')}
        onOk={handleOnOk}
        onCancel={handleOnChange}
        isMutating={isMutating}
      />
      <ErrorDeleteLinkedDataDialog
        isOpen={!isDeleteAllowed && deleteErrorMessages.length > 0}
        title={t('common.cannotDelete', { resourceName: partner.name })}
        description={t('common.cannotDeleteDescription')}
        okButtonLabel={tRootLang('common.ok')}
        collapsibleLabel={t('common.viewLinkedLocations')}
        errorMessages={deleteErrorMessages}
        onCancel={handleOnChange}
      />
    </>
  );
};
