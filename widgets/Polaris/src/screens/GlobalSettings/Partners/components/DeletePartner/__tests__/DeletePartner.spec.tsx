import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import {
  DeletePartner,
  DeletePartnerProps,
} from '@widgets/Polaris/src/screens/GlobalSettings/Partners/components/DeletePartner/DeletePartner';
import { useCanDeletePartner, useSoftDeletePartner } from '@polaris-hooks/index';
import { Partner } from '@widgets/Polaris/src/providers';

// Mock dependencies
jest.mock('@polaris-hooks/globalSettings');
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({
    t: jest.fn((key) => key),
  }),
}));

describe('DeletePartner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useCanDeletePartner as jest.Mock).mockReturnValue({
      canDeletePartner: jest.fn().mockResolvedValue({ success: false }),
    });
    (useSoftDeletePartner as jest.Mock).mockReturnValue({
      softDeletePartner: jest.fn().mockResolvedValue({ success: false }),
    });
  });

  const defaultProps: DeletePartnerProps = {
    partner: {
      id: '613e75b4-c565-4eb2-8487-4c62a6c61f1a',
      name: 'Mock name',
      displayName: 'Mock display name',
      isInternal: true,
      countries: '',
      capabilities: '',
    } as unknown as Partner,
    onChange: jest.fn(),
  };

  const renderComponent = (props: DeletePartnerProps) => {
    render(<DeletePartner {...props} />);
  };

  it('should not show modal when partner is undefined', async () => {
    const props = { ...defaultProps, partner: { ...defaultProps.partner, id: '' } };
    renderComponent(props);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).not.toBeInTheDocument();
    });
  });

  it('should show modal, and show loader is searching for linked entities', async () => {
    const deferredPromise = () => new Promise(() => null);
    (useCanDeletePartner as jest.Mock).mockReturnValue({
      canDeletePartner: jest.fn().mockImplementationOnce(deferredPromise),
    });
    renderComponent(defaultProps);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).toBeInTheDocument();
      expect(screen.queryByText('common.confirmDelete')).not.toBeInTheDocument();
      expect(screen.queryByText('common.confirmDeleteDescription')).not.toBeInTheDocument();
    });
  });

  it('should show modal, and show delete success confirmation', async () => {
    (useCanDeletePartner as jest.Mock).mockReturnValue({
      canDeletePartner: jest
        .fn()
        .mockImplementationOnce(() => new Promise((r) => r({ success: true }))),
    });
    renderComponent(defaultProps);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).not.toBeInTheDocument();
      expect(screen.queryByText('common.confirmDelete')).toBeInTheDocument();
      expect(screen.queryByText('common.confirmDeleteDescription')).toBeInTheDocument();
    });
  });

  it('should show modal, and show delete failure with a collapsible list', async () => {
    (useCanDeletePartner as jest.Mock).mockReturnValue({
      canDeletePartner: jest
        .fn()
        .mockResolvedValueOnce({ success: false, usedIn: ['error1', 'error2'] }),
    });
    renderComponent(defaultProps);
    await waitFor(() => {
      expect(screen.queryByText('common.checkLink')).not.toBeInTheDocument();
      expect(screen.queryByText('common.confirmDelete')).not.toBeInTheDocument();
      expect(screen.queryByText('common.cannotDelete')).toBeInTheDocument();
      expect(screen.queryByText('common.cannotDeleteDescription')).toBeInTheDocument();
      expect(screen.queryByText('common.viewLinkedLocations')).toBeInTheDocument();
    });
  });
});
