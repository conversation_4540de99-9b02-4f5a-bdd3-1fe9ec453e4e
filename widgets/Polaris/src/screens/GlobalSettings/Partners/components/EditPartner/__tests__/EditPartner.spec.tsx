import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import {
  CountryCombobox,
  FeatureCombobox,
  useEditPartner,
  useFeatureServices,
} from '@polaris-hooks/index';
import { Partner, useAppContext } from '@widgets/Polaris/src/providers';
import { countriesMock, partnersResponseMock } from '@common/mocks';
import EditPartnerDialog, { EditPartnerProps } from '../EditPartner';
import { countryResponse } from '@widgets/Polaris/src/screens/Home/mock-data/MockData';

// Mock all dependencies at the top level
jest.mock('@widgets/Polaris/src/providers');
jest.mock('@polaris-hooks/index');
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));
jest.mock('@polaris-hooks/globalSettings/shared/useDisplaySnackbar', () => ({
  useDisplaySnackbar: jest.fn().mockReturnValue({
    displaySnackbar: jest.fn(),
  }),
  SNACKBAR_VARIANT: {
    ERROR: 0,
    SUCCESS: 1,
  },
}));

// Test constants
const TEST_PARTNER = partnersResponseMock[0] as Partner;
const UPDATED_NAME = 'Updated Partner';
const ERROR_MESSAGE = 'Edit failed';

// Mock functions
const mockOnClose = jest.fn();
const mockEditPartner = jest.fn();
const mockDispatch = jest.fn();

const defaultProps: EditPartnerProps = {
  partner: TEST_PARTNER,
  countriesItems: countryResponse as unknown as CountryCombobox[],
  featuresItems: partnersResponseMock as unknown as FeatureCombobox[],
  onChange: mockOnClose,
};

describe('EditPartnerDialog', () => {
  beforeAll(() => {
    // Global mock setup
    (useAppContext as jest.Mock).mockReturnValue({
      countries: countriesMock,
      methods: { setCountries: jest.fn() },
      dispatch: mockDispatch,
    });

    (useFeatureServices as jest.Mock).mockReturnValue({
      data: { entities: partnersResponseMock },
      isMutating: false,
      error: null,
    });
  });

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    (useEditPartner as jest.Mock).mockReturnValue({
      editPartner: mockEditPartner,
      triggerEditPartnerTag: jest.fn(),
    });
  });

  describe('Rendering', () => {
    test('renders dialog when partner is provided', () => {
      render(<EditPartnerDialog {...defaultProps} />);
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByLabelText('name')).toHaveValue(TEST_PARTNER.name);
    });

    test('does not render when no partner provided', () => {
      render(
        <EditPartnerDialog
          {...defaultProps}
          partner={null as unknown as Partner}
          onChange={jest.fn()}
        />,
      );
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    test('updates displayName when input changes', () => {
      render(<EditPartnerDialog {...defaultProps} />);
      const displayNameInput = screen.getByLabelText('displayName');
      fireEvent.change(displayNameInput, { target: { value: 'New Name' } });
      expect(displayNameInput).toHaveValue('New Name');
    });

    test('updates partner type when select changes', () => {
      render(<EditPartnerDialog {...defaultProps} />);
      const select = screen.getByLabelText('partnerType');
      fireEvent.change(select, { target: { value: 'internal' } });
      expect(select).toHaveValue('internal');
    });
  });

  describe('Validation States', () => {
    test('shows default variant when no errors', () => {
      render(<EditPartnerDialog {...defaultProps} />);
      const input = screen.getByTestId('global-settings-partners-edit-input-displayName');
      expect(input).not.toHaveClass('error');
    });
  });

  describe('Dialog Actions', () => {
    test('handles close action', async () => {
      render(<EditPartnerDialog {...defaultProps} />);
      fireEvent.click(screen.getByTestId('generic-dialog-continue-btn'));
      await waitFor(() => expect(mockOnClose).toHaveBeenCalled());
    });
  });

  describe('Form Submission', () => {
    test('successful partner edit', async () => {
      const updatedPartner = { ...TEST_PARTNER, name: UPDATED_NAME };
      mockEditPartner.mockResolvedValue(updatedPartner);
      render(<EditPartnerDialog {...defaultProps} />);

      fireEvent.change(screen.getByLabelText('name'), {
        target: { value: UPDATED_NAME },
      });
      fireEvent.click(screen.getByTestId('generic-dialog-continue-btn'));

      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    test('failed partner edit shows error snackbar', async () => {
      const error = new Error(ERROR_MESSAGE);
      mockEditPartner.mockRejectedValue(error);

      render(<EditPartnerDialog {...defaultProps} />);
      fireEvent.click(screen.getByTestId('generic-dialog-continue-btn'));

      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    test('sets submitting state during submission', async () => {
      mockEditPartner.mockImplementation(() => new Promise((resolve) => setTimeout(resolve, 100)));

      render(<EditPartnerDialog {...defaultProps} />);
      const submitButton = screen.getByTestId('generic-dialog-continue-btn');

      fireEvent.click(submitButton);
      expect(submitButton).toBeDisabled();

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });
    });
  });
});
