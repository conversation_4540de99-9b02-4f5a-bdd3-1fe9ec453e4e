import React, { useContext } from 'react';
import { GenericDialog } from '@widgets/Polaris/src/components';
import { useTranslation } from 'react-i18next';
import { useFormik } from 'formik';
import { AdminContext, GLOBAL_PARTNER_ACTION_TYPES, Partner } from '@widgets/Polaris/src/providers';
import { Nullable } from '@common/types';
import { Combobox, ComboboxOption, Input, Select } from '@yaradigitallabs/ahua-react';
import {
  CountryCombobox,
  FeatureCombobox,
  useEditPartner,
  SNACKBAR_VARIANT,
  useDisplaySnackbar,
} from '@polaris-hooks/index';
import { getPartnerTypes } from '../shared';
import { editPartnerSchema } from '../../validationSchemas';
import './style.scss';
import { getCountryObject, getFeatureObject } from '../../utils/PartnersUtils';

export interface EditPartnerProps {
  partner: Partner;
  onChange: (partner: Nullable<Partner>) => void;
  countriesItems: CountryCombobox[] | undefined;
  featuresItems: FeatureCombobox[] | undefined;
}

export default function EditPartnerDialog({
  partner,
  countriesItems,
  featuresItems,
  onChange,
}: EditPartnerProps) {
  const { displaySnackbar } = useDisplaySnackbar();
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.globalSettings.partners',
  });
  const { t: tRootLang } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });

  const { dispatch } = useContext(AdminContext);
  const { editPartner } = useEditPartner(partner?.id);

  const formik = useFormik({
    initialValues: partner,
    validationSchema: editPartnerSchema(tRootLang, t),
    onSubmit: async (partner) => {
      let output: Nullable<Partner> = null;
      formik.setSubmitting(true);
      try {
        output = await editPartner({
          ...partner,
        });
        if (output) {
          dispatch({
            type: GLOBAL_PARTNER_ACTION_TYPES.UPDATE_PARTNER,
            payload: output,
          });
          displaySnackbar(t('successMsg'), SNACKBAR_VARIANT.SUCCESS);
        }
      } catch (error) {
        console.error('Error editing feature data:', error);
        displaySnackbar(tRootLang('error.wentWrongTitle'), SNACKBAR_VARIANT.ERROR);
      } finally {
        handleDialogClose(output);
      }
    },
  });

  const handleDialogClose = (partner: Nullable<Partner>) => {
    formik.resetForm();
    onChange(partner);
  };

  return (
    <>
      {partner?.id && (
        <GenericDialog
          styles={{
            middleStyle: {
              gap: '$x4',
            },
          }}
          isOpen={partner?.id.length > 0}
          isSubmitDisabled={formik.isSubmitting}
          title={t('editPartner')}
          submitLabel={tRootLang('common.saveChanges')}
          closeOnOverlayClick={false}
          onClose={() => handleDialogClose(null)}
          onSubmit={formik.handleSubmit}
        >
          <Input
            size='s'
            label={t('headers.partnerName')}
            name='name'
            className='noBorder-for-partnerName'
            aria-label='name'
            data-cy={`global-settings-partners-edit-input-name`}
            value={formik.values.name}
            variant='default'
            disabled
          />
          <Input
            size='s'
            label={t('headers.displayName')}
            name='displayName'
            aria-label='displayName'
            data-cy={`global-settings-partners-edit-input-displayName`}
            helperText={
              (formik.touched.displayName && formik.errors.displayName) || t('displayNameHint')
            }
            value={formik.values.displayName}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            variant={formik.touched.displayName && formik.errors.displayName ? 'error' : 'default'}
            disabled={formik.isSubmitting}
          />
          <Select
            size='s'
            cover='outline'
            position='popper'
            ariaLabel='partnerType'
            label={t('headers.partnerType')}
            variant={formik.errors.isInternal ? 'error' : 'default'}
            items={getPartnerTypes(t)}
            helper-text={(formik.touched.isInternal && formik.errors.isInternal) || ''}
            value={formik.values.isInternal ? 'internal' : 'external'}
            onChange={(value: string) => {
              formik.setFieldValue('isInternal', value === 'internal', true);
            }}
            disabled={formik.isSubmitting}
            onFocus={null}
            onBlur={null}
          />

          <Combobox
            className={'global-settings-styled-combo-box'}
            size='s'
            data-cy={`global-settings-partners-edit-input-countries`}
            name='countries-combobox'
            cover='outline'
            placeholder={t('headers.countries')}
            label={t('headers.countries')}
            ariaLabel='countries'
            options={countriesItems}
            value={countriesItems?.filter((ci) =>
              formik.values.countries?.some((c) => c?.id === ci.value?.id),
            )}
            onChange={(selected: ComboboxOption[]) => {
              formik.setFieldValue(
                'countries',
                selected.map((s) => getCountryObject(s.value)),
              );
            }}
          />
          <Combobox
            size='s'
            name='capabilities'
            cover='outline'
            placeholder={t('headers.features')}
            label={t('headers.features')}
            ariaLabel='capabilities'
            className={'global-settings-styled-combo-box'}
            data-cy={`global-settings-partners-edit-input-capabilities`}
            value={featuresItems?.filter((fi) =>
              formik.values.capabilities?.some((c) => c.id === fi.value?.id),
            )}
            options={featuresItems}
            onChange={(selected: ComboboxOption[]) => {
              formik.setFieldValue(
                'capabilities',
                selected.map((s) => getFeatureObject(s.value)),
              );
            }}
          />
        </GenericDialog>
      )}
    </>
  );
}
