import React, { useMemo } from 'react';
import {
  NutrientClassification,
  ParameterLevel,
  FeatureConfig,
  AnalysisType,
  AnalysisConfiguration,
} from '@common/types';
import { useNutrientClassifications } from '@polaris-hooks/index';
import { SelectWrapper } from '@widgets/Polaris/src/components/Select/index';
import { Select } from '@yaradigitallabs/ahua-react';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { useTranslation } from 'react-i18next';
import { CellContainer, Circle, Level } from './ParameterLevelCell.styled';
import { METHOD, ORDER } from '@common/constants';
import {
  isHighestLevel,
  isLowestLevel,
} from '../../screens/CropFeatures/shared/helpers/soilAnalysisHelpers';
import { ParameterLevelRange } from '../../screens/CropFeatures/shared/NutritionParamsWithValuesTable/NutritionParamsWithValuesTable.type';
import { getParameterLevel } from '../AdditionalParametersPopup';

type ParameterLevelCellOptions = {
  value: string;
  text: ParameterLevel;
};

export type ParameterLevelCellProps = {
  analysisNutrientClassification: NutrientClassification;
  analysisNutrientClassificationData: NutrientClassification[];
  configuration: AnalysisConfiguration;
  configType: FeatureConfig;
  analysisType: AnalysisType;
  updateAnalysisConfigurations: (analysisConfiguration: AnalysisConfiguration) => void;
};

export const ParameterLevelCell = ({
  analysisNutrientClassification,
  analysisNutrientClassificationData,
  configuration,
  configType,
  analysisType,
  updateAnalysisConfigurations,
}: ParameterLevelCellProps) => {
  const { trigger } = useNutrientClassifications(configType, analysisType);
  const { setDisplaySnackbar } = useSnackbar();
  const { t, i18n } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.soilAnalysis.parameterLevelsTable',
  });
  const commonT = (key: string) => i18n.t(`polaris.common.${key}`);

  const onChange = async (
    value: ParameterLevel,
    analysisNutrientClassification: NutrientClassification,
  ) => {
    const isNewLowestLevel = isLowestLevel(value, analysisNutrientClassificationData);
    const isNewHighestLevel = isHighestLevel(value, analysisNutrientClassificationData);

    const newNutrientClassification: NutrientClassification = {
      ...analysisNutrientClassification,
      nutrientClassification: value,
      greaterOrEqual: isNewLowestLevel
        ? ParameterLevelRange.Min
        : analysisNutrientClassification.greaterOrEqual,
      lowerThan: isNewHighestLevel
        ? ParameterLevelRange.Max
        : analysisNutrientClassification.lowerThan,
    };

    delete newNutrientClassification.id;
    const updateResponse = await trigger({
      method: METHOD.PUT,
      body: JSON.stringify(newNutrientClassification),
      extraUrl: `/${analysisNutrientClassification.id}`,
    });

    if (typeof updateResponse === 'object' && updateResponse?.actionTriggerParameterLevelResult) {
      setDisplaySnackbar({
        title: t(`dialog.updateMessageLevel`),
        colorConcept: 'successLight',
        icon: 'Check',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
      updateAnalysisConfigurations({
        ...configuration,
        nutrientClassifications: [
          updateResponse.actionTriggerParameterLevelResult,
          ...updateResponse.impactedParameterLevels,
        ],
      });
    }
  };

  const getDropDownData = () => {
    const cellOptions: ParameterLevelCellOptions[] = [];
    for (const [key, value] of Object.entries(ORDER)) {
      const parameterLevelEnumKey = getParameterLevel(key);
      if (parameterLevelEnumKey) {
        cellOptions.push({
          value: String(value + Math.random()),
          text: parameterLevelEnumKey,
        });
      }
    }

    const existingParameterValues: ParameterLevelCellOptions[] =
      analysisNutrientClassificationData?.map((el) => ({
        value: el?.id || '',
        text: el.nutrientClassification,
      }));

    const dropdownData: ParameterLevelCellOptions[] = [
      {
        value: analysisNutrientClassification?.id || '',
        text: analysisNutrientClassification.nutrientClassification,
      },
      ...cellOptions.filter((el) => {
        return existingParameterValues?.every((elem) => elem.text !== el.text);
      }),
    ];

    return dropdownData
      .sort((a, b) => ORDER[a.text] - ORDER[b.text])
      .map((el) => ({
        ...el,
        text: commonT(`parameterLevel.${el.text}`),
      }));
  };
  const dropDownData = useMemo(() => {
    return getDropDownData();
  }, [analysisNutrientClassification, analysisNutrientClassificationData]);

  return (
    <CellContainer
      key={`${analysisNutrientClassification.id}-${
        analysisNutrientClassification.nutrientClassification
      }-${ORDER[analysisNutrientClassification.nutrientClassification]}`}
      data-cy='nutrient-classification-cell'
    >
      <Circle
        variant={ORDER[analysisNutrientClassification.nutrientClassification]}
        data-cy='nutrient-classification-colored-circle'
      />
      {analysisNutrientClassificationData?.length === 5 ? (
        <Level data-cy='nutrient-classification-text'>
          {commonT(`parameterLevel.${analysisNutrientClassification.nutrientClassification}`)}
        </Level>
      ) : (
        <SelectWrapper
          key={`${analysisNutrientClassification.id}-${
            ORDER[analysisNutrientClassification.nutrientClassification]
          }`}
          dataCy='nutrient-classification-select'
        >
          <Select
            key={`${analysisNutrientClassification.id}-${
              ORDER[analysisNutrientClassification.nutrientClassification]
            }`}
            className='select-nutrient-parameter-level'
            ariaLabel={`Select nutrient classification-${analysisNutrientClassification.nutrientClassification}`}
            cover='outline'
            items={dropDownData}
            position='popper'
            size='s'
            value={analysisNutrientClassification.id}
            onChange={(value: string) => {
              if (dropDownData?.length) {
                const parameterLevel = dropDownData.find((el) => el.value === value)?.text;
                const analysisLevelName = getParameterLevel(
                  parameterLevel?.split(' ').join('_').toUpperCase(),
                );
                if (analysisLevelName && analysisLevelName in ORDER) {
                  onChange(analysisLevelName, analysisNutrientClassification);
                }
              }
            }}
            onFocus={null}
            onBlur={null}
          />
        </SelectWrapper>
      )}
    </CellContainer>
  );
};
