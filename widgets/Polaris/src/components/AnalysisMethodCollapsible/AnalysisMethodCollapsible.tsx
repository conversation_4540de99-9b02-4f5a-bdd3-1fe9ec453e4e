import React, { useCallback, useEffect, useState } from 'react';
import {
  Collapsible,
  IconButton,
  Input,
  Label,
  Select,
  Separator,
  Switch,
  Title,
  Tooltip,
} from '@yaradigitallabs/ahua-react';
import {
  useBulkCreateCropDemandAnalysisNutrients,
  useBulkDeleteCropDemandAnalysisNutrients,
  useExpressionBuilder,
  useGetMockedAnalysisMethod,
  useGetCropDemandAnalysis,
  useGetNutrientsByCropDemandAnalysisId,
  useUpdateCropDemandAnalysis,
} from '@polaris-hooks/index';

import { SelectWrapper } from '@widgets/Polaris/src/components/Select/index';
import { useTranslation } from 'react-i18next';
import './styles.scss';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';

import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import {
  getActivatedExpression,
  getDefaultCropDemandAnalysisNutrients,
  isDefaultActive,
} from '../../screens/CropFeatures/CNP/SoilAnalysis/utils';
import { DefaultClassificationEnum } from '@widgets/Polaris/src/screens/CropFeatures/shared/constants';
import { BaseUnit, CropDemandAnalysis, CropDemandAnalysisDefaultParameters } from '@common/types';

import { ExpressionBuilder } from '..';
import {
  collapsibleStyles,
  FirstRowInput,
  InputContainer,
  InputContainerOptional,
  NutritionParameterContainer,
  SecondRowInput,
  SwitchContainer,
  switchLabelStyles,
  tooltipStyles,
} from './AnalysisMethodCollapsible.styled';
import { NutritionParametersTable } from '../../screens/CropFeatures/CNP/SoilAnalysis/NutritionParametersTable';
import { METHOD } from '@common/constants';

export interface AnalysisCollapsibleProps {
  cropDemandAnalysis: CropDemandAnalysis;
  index: number;
  baseUnits: BaseUnit[] | undefined;
  elementUnits?: BaseUnit[] | undefined;
  keyPrefix?: string;
}

export const AnalysisMethodCollapsible = ({
  cropDemandAnalysis,
  index,
  baseUnits,
  elementUnits,
  keyPrefix,
}: AnalysisCollapsibleProps) => {
  const { t, i18n } = useTranslation('polaris', {
    keyPrefix: keyPrefix,
  });
  const commonT = (key: string) => i18n.t(`polaris.common.${key}`);
  const {
    selectedNutrient,
    methods: { updateCropDemandAnalyses },
  } = useAppContext();

  const { data: analysisMethod } = useGetMockedAnalysisMethod(cropDemandAnalysis?.analysisMethodId);

  const {
    expression,
    setExpression,
    modified,
    setModified,
    modifiedBy,
    setModifiedBy,
    isExpressionBuilderOpen,
    setIsExpressionBuilderOpen,
  } = useExpressionBuilder();

  const { setDisplaySnackbar } = useSnackbar();

  const {
    trigger: triggerUpdateCropDemandAnalysis,
    isMutating: isMutatingUpdateCropDemandAnalysis,
  } = useUpdateCropDemandAnalysis(cropDemandAnalysis?.id);

  const [baseUnitId, setBaseUnitId] = useState<string>();
  const [nutrientsParameters, setNutrientsParameters] = useState<
    CropDemandAnalysisDefaultParameters[]
  >([]);

  const [elementUnitId, setElementUnitId] = React.useState<string>();
  const [isPlanActive, setIsPlanActive] = React.useState<boolean | null>(null);
  const cropDemandAnalysesNutrientsData = useGetNutrientsByCropDemandAnalysisId(
    cropDemandAnalysis?.id,
    isPlanActive,
  );

  const { data: cDemandAnalysisData, isLoading } = useGetCropDemandAnalysis(cropDemandAnalysis?.id);

  const getIsDefaultActive = (isDefault = false) =>
    useCallback(() => {
      return isDefaultActive(cDemandAnalysisData, isDefault);
    }, [analysisMethod, cDemandAnalysisData]);

  const { triggerBulkCreateCropDemandAnalysisNutrients, isBulkCreating } =
    useBulkCreateCropDemandAnalysisNutrients();
  const { triggerBulkPermDeleteCropDemandAnalysisNutrients, isMutating: isBulkDeleting } =
    useBulkDeleteCropDemandAnalysisNutrients();

  useEffect(() => {
    setElementUnitId(cropDemandAnalysis?.demandBaseUnitId);
  }, [cropDemandAnalysis]);

  useEffect(() => {
    setBaseUnitId(cropDemandAnalysis?.analysisBaseUnitId);
  }, [cropDemandAnalysis?.analysisBaseUnitId]);

  const updateData = async (value: string, property: string) => {
    try {
      const result = await triggerUpdateCropDemandAnalysis({
        method: METHOD.PUT,
        body: JSON.stringify({
          ...cropDemandAnalysis,
          [property]: value,
        }),
      });

      if (result) {
        setDisplaySnackbar({
          title: commonT('changesSaved'),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
        });
        updateCropDemandAnalyses(result);
      }
    } catch (error) {
      console.error('Error updating data', error);
    }
  };

  const openExpressionBuilder = (expression: string) => {
    setExpression(expression);
    setModified(cropDemandAnalysis?.modified);
    setModifiedBy(cropDemandAnalysis?.modifiedBy);
    setIsExpressionBuilderOpen(true);
  };

  const planSwitchHandler = async (value: boolean) => {
    const nutrientParameters: CropDemandAnalysisDefaultParameters[] = [];

    try {
      let createdNutrients: CropDemandAnalysisDefaultParameters[] = [];
      // Determine the nutrient parameters based on the value
      const { defaultCropDemandAnalysisNutrients, secondaryCropDemandAnalysisNutrients } =
        getDefaultCropDemandAnalysisNutrients({
          cropDemandAnalysis: cropDemandAnalysis,
          clayClassification: DefaultClassificationEnum.DEFAULT,
          nutrientName: selectedNutrient?.elementalName || '',
          isSecondaryRequired: value,
        });

      nutrientParameters.push(
        ...(value ? secondaryCropDemandAnalysisNutrients : defaultCropDemandAnalysisNutrients),
      );

      const cropDemandAnalysisPayload: CropDemandAnalysis = {
        ...cropDemandAnalysis,
        clayClassificationExpression: getActivatedExpression(value, selectedNutrient),
      };
      // Update cropDemand analysis clayClassificationExpression
      const cropDemandAnalysisResponse = await triggerUpdateCropDemandAnalysis({
        method: METHOD.PUT,
        body: JSON.stringify(cropDemandAnalysisPayload),
      });
      if (cropDemandAnalysisResponse) updateCropDemandAnalyses(cropDemandAnalysisResponse);

      // Delete existing crop demand analysis nutrients
      await triggerBulkPermDeleteCropDemandAnalysisNutrients({
        method: METHOD.DELETE,
        body: JSON.stringify({
          ids: cropDemandAnalysesNutrientsData?.map((el) => el.id),
        }),
      });

      // Bulk create new crop demand analysis nutrients
      const bulkCreatedCropDemandAnalysisNutrients =
        await triggerBulkCreateCropDemandAnalysisNutrients({
          method: METHOD.POST,
          body: JSON.stringify(nutrientParameters),
        });

      if (bulkCreatedCropDemandAnalysisNutrients)
        createdNutrients = bulkCreatedCropDemandAnalysisNutrients;
      if (createdNutrients.length) {
        setNutrientsParameters(createdNutrients);
      }

      setIsPlanActive(value);
    } catch (error) {
      console.error('Error in planSwitchHandler:', error);
    }
  };

  return (
    <>
      <ExpressionBuilder
        expression={expression}
        modified={modified}
        modifiedBy={modifiedBy}
        isExpressionBuilderOpen={isExpressionBuilderOpen}
        onCloseExpressionBuilder={() => {
          setIsExpressionBuilderOpen(!isExpressionBuilderOpen);
        }}
      />
      <Collapsible
        css={collapsibleStyles}
        defaultOpen={index === 0}
        data-cy='soil-analyses-collapsible'
        header={analysisMethod?.name ? analysisMethod.name : 'Default'}
        className='collapsible-section'
      >
        <InputContainer>
          <FirstRowInput>
            <SelectWrapper dataCy='analysis-base-unit-select'>
              <Select
                ariaLabel='Select unit'
                cover='outline'
                items={
                  baseUnits
                    ? baseUnits.map((e) => ({
                        text: e.name,
                        value: e.id,
                      }))
                    : []
                }
                css={{ maxWidth: '100%' }}
                position='popper'
                size='s'
                label={t('analysisBase')}
                onChange={(value: string) => updateData(value, 'analysisBaseUnitId')}
                value={baseUnitId}
              />
            </SelectWrapper>
          </FirstRowInput>
          <>
            <FirstRowInput>
              <SelectWrapper dataCy='element-form-select'>
                <Select
                  ariaLabel='Select unit'
                  cover='outline'
                  items={
                    elementUnits
                      ? elementUnits.map((e) => ({
                          text: e.name,
                          value: e.id,
                        }))
                      : []
                  }
                  position='popper'
                  size='s'
                  label={t('elementForm')}
                  onChange={(value: string) => updateData(value, 'demandBaseUnitId')}
                  value={elementUnitId}
                />
              </SelectWrapper>
            </FirstRowInput>
            <FirstRowInput>
              <Input
                size='s'
                type='text'
                className='input-text-color read-only'
                readOnly
                value={`"${cropDemandAnalysis?.analysisNormalRangeExpressionTree?.value}"`}
                onChange={() => {
                  console.log('change value');
                }}
                onClick={() =>
                  openExpressionBuilder(
                    String(cropDemandAnalysis?.analysisNormalRangeExpressionTree?.value),
                  )
                }
                variant={'default'}
                label={t('defaultValue')}
                data-cy='normal-range-expression'
                css={{ paddingTop: '18px' }}
              />
            </FirstRowInput>
          </>
        </InputContainer>
        <>
          <InputContainerOptional>
            <SecondRowInput>
              <Input
                size='s'
                type='text'
                className='input-text-color read-only'
                readOnly
                value={cropDemandAnalysis?.analysisValueExpressionTree?.value}
                onChange={() => {
                  console.log('change value');
                }}
                onClick={() =>
                  openExpressionBuilder(
                    String(cropDemandAnalysis?.analysisValueExpressionTree?.value),
                  )
                }
                variant={'default'}
                label={t('analysisValueOptional')}
                data-cy='analysis-value-expression'
                css={{ paddingTop: '18px' }}
              />
            </SecondRowInput>
            <SecondRowInput>
              <Input
                size='s'
                type='text'
                className='input-text-color read-only'
                readOnly
                value={cropDemandAnalysis?.baseValueExpressionTree?.value}
                onChange={() => {
                  console.log('change value');
                }}
                onClick={() =>
                  openExpressionBuilder(String(cropDemandAnalysis?.baseValueExpressionTree?.value))
                }
                variant='default'
                label={t('baseValueExpression')}
                data-cy='base-value-expression'
                css={{ paddingTop: '18px' }}
              />
            </SecondRowInput>
          </InputContainerOptional>
          <Separator width='100%' mode='light' orientation='horizontal' />
        </>

        <NutritionParameterContainer variant={'cnp'}>
          <Title size='s'>{t('nutritionParamTitle')}</Title>
          <SwitchContainer>
            <div>
              {' '}
              <Switch
                data-cy='additional-plan-switch'
                labelPosition='right'
                checked={!isLoading && (isPlanActive || isDefaultActive(cDemandAnalysisData))}
                onCheckedChange={(value: boolean) =>
                  !(isMutatingUpdateCropDemandAnalysis || isBulkDeleting || isBulkCreating)
                    ? planSwitchHandler(value)
                    : null
                }
                disabled={isMutatingUpdateCropDemandAnalysis || isBulkDeleting || isBulkCreating}
                defaultChecked={false}
              />
            </div>

            <Label size='s' css={switchLabelStyles}>
              {t('switchLabel')}
            </Label>
            <Tooltip
              data-cy='default-value-tooltip'
              className='default-tooltip'
              css={tooltipStyles}
              concept='inverse'
              maxWidth={225}
              minWidth={225}
              position='right'
              text={t('defaultActiveMsg')}
              tipVisibility={isDefaultActive(cDemandAnalysisData, true)}
            >
              <IconButton
                icon='Info'
                colorConcept='neutral'
                size='xs'
                className='secondary-param-info-btn'
                data-cy='secondary-param-info-button'
              />
            </Tooltip>
          </SwitchContainer>
          <div className='secondary-input' style={{ width: '55%' }}>
            <Input
              size='n'
              type='text'
              className={`secondary-param ${
                isDefaultActive(cDemandAnalysisData) ? 'active' : 'inActive'
              }`}
              value={
                getIsDefaultActive()
                  ? getActivatedExpression(isDefaultActive(cDemandAnalysisData), selectedNutrient)
                  : '"Default"'
              }
              cover={isDefaultActive(cDemandAnalysisData) ? 'outline' : 'fill'}
              onChange={() => {
                console.log('change value');
              }}
              onClick={() => {
                setIsExpressionBuilderOpen(!isExpressionBuilderOpen);
                setExpression(
                  getActivatedExpression(isDefaultActive(cDemandAnalysisData), selectedNutrient),
                );
              }}
              variant={'default'}
              label={t('secondaryParameterExpression')}
              disabled={isDefaultActive(cDemandAnalysisData, true)}
              data-cy='secondary-parameter-value-expression'
            />
          </div>
        </NutritionParameterContainer>
        <NutritionParametersTable
          setExpression={setExpression}
          setModified={setModified}
          setModifiedBy={setModifiedBy}
          setIsExpressionBuilderOpen={setIsExpressionBuilderOpen}
          cropDemandAnalysesData={cropDemandAnalysis}
          cropDemandAnalysisNutrients={nutrientsParameters}
          isDefaultActive={isDefaultActive(cDemandAnalysisData)}
        />
      </Collapsible>
    </>
  );
};
