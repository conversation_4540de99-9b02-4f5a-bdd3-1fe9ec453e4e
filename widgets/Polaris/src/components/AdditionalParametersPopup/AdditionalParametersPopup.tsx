import React, { FC, useEffect, useState } from 'react';
import { AdditionalProperty } from '@common/types';
import { useParameterFormState } from '@polaris-hooks/index';
import {
  AdditionalParameterActions,
  AdditionalParametersPopupProps,
  SelectWrapper,
  ValueTypeEnum,
} from '@widgets/Polaris/src/components';
import {
  Button,
  Card,
  CheckBox,
  Dialog,
  IconButton,
  Input,
  Label,
  Select,
  SelectOption,
  Tabs,
} from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import {
  BodyWrapper,
  CheckboxWrapper,
  CloseButtonStyle,
  ContentStyle,
  DescriptionStyle,
  FieldsWrapper,
  HeaderWrapper,
  TailWrapperStyle,
} from './AdditionalParametersPopup.styled';
import {
  additionalParameterInitialState,
  createNewParameter,
  getBooleanTypeOptions,
  getUnitOptions,
  getValueTypeOptions,
  validateParameters,
  getBaseUnitOptionsForSelectedTag,
  updateExistingParameter,
  createBaseAdditionalProperty,
  hasChanges,
  isAhdbActive,
} from './utils';
import { ListItemsComponent } from './components/ListItemsComponent';
import { getAllUnitTags, useAdditionalParameterConfigs } from '@polaris-hooks/index';
import { SelectOptions } from '../../types';
import { displaySnackbarMessage, sortArrObjectsAlphabetically } from '../../../utils';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { AhdbTabs } from '@common/constants';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import AhdbParameter from './components/ahdb/AhdbParameter';
import './components/ahdb/styles.scss';
import { updateParameters } from '../../screens/CropFeatures/CNP/Parameters';

export const AdditionalParametersPopup: FC<AdditionalParametersPopupProps> = ({
  showDialog,
  onOpenChange,
  cropRegion,
  onSave,
  onEdit,
  isEditMode = false,
  initialData,
  triggerUpdateCropRegion,
  activeTab,
  setActiveTab,
}) => {
  const { t } = useTranslation('polaris');
  const keyPrefix =
    'polaris.cnpDetails.configuration.additionalParameters.additionalParametersPopup';
  const prefix = 'polaris.cnpDetails.configuration.additionalParameters';
  const valueTypeOptions: SelectOptions<string> = getValueTypeOptions(t, keyPrefix);
  const booleanTypeOptions: SelectOptions<string> = getBooleanTypeOptions(t, keyPrefix);
  const [ahdbParam, otherParam] = AhdbTabs;
  const {
    state,
    dispatch,
    handleParameterNameChange,
    handleParameterOrdinalChange,
    handleNumberMinChange,
    handleNumberMaxChange,
    handleUnitTagsChange,
    handleBaseUnitChange,
    handleRequiredChange,
    handleValueTypeChange,
    handleBooleanChange,
    handleDefaultChange,
    handleOptionChange,
  } = useParameterFormState(
    additionalParameterInitialState,
    cropRegion,
    valueTypeOptions,
    isEditMode,
    isEditMode ? initialData?.Name : null,
  );
  const {
    getNameOrdinalConfig,
    getDefaultValConfig,
    getNumberMinMaxConfig,
    getValueTypeBooleanConfig,
    getUnitTagsBaseUnitConfig,
  } = useAdditionalParameterConfigs(t, keyPrefix, state);
  const {
    selectedCountry,
    selectedCountryUnits,
    methods: { setCropRegion },
  } = useAppContext();

  const isNameOrOrdinalError = state.isNameInvalid || state.isOrdinalInvalid;
  const isValueTypeError = state.isValueTypeInvalid;
  const additionalProperties = cropRegion?.additionalProperties || [];
  const isDefaultValueError = state.isDefaultValueInvalid;
  const isMinOrMaxError = state.isNumberMinInvalid || state.isNumberMaxInvalid;
  const isUnitTagsOrBaseUnitError = state.isUnitTagsInvalid || state.isBaseUnitInvalid;

  const [allUnitTagsByCountry, setAllUnitTagsByCountry] = useState<SelectOptions<string> | null>(
    null,
  );
  const [baseUnitsForSelectedTag, setBaseUnitsForSelectedTag] =
    useState<SelectOptions<string> | null>(null);
  const [savedAhdbParams, setSavedAhdbParams] = useState<AdditionalProperty[]>([]);

  const { setDisplaySnackbar } = useSnackbar();

  const nameOrdinalConfig = getNameOrdinalConfig(
    handleParameterNameChange,
    handleParameterOrdinalChange,
    isEditMode,
  );
  const valueTypeBoolConfig = getValueTypeBooleanConfig(
    handleValueTypeChange,
    handleBooleanChange,
    valueTypeOptions,
    booleanTypeOptions,
  );
  const defaultValConfig = getDefaultValConfig(handleDefaultChange);
  const numberMinMaxConfig = getNumberMinMaxConfig(handleNumberMinChange, handleNumberMaxChange);
  const unitTagsBaseUnitConfig = getUnitTagsBaseUnitConfig(
    unitTagsChangeHandler,
    handleBaseUnitChange,
    allUnitTagsByCountry ?? [],
    baseUnitsForSelectedTag ?? [],
  );

  const isAhdb = selectedCountry && isAhdbActive(selectedCountry);

  useEffect(() => {
    if (selectedCountryUnits) {
      const unitOptions = sortArrObjectsAlphabetically<SelectOption<string>>(
        getUnitOptions(getAllUnitTags(selectedCountryUnits)),
        'text',
      );
      setAllUnitTagsByCountry(unitOptions);
    }
  }, [selectedCountryUnits]);

  useEffect(() => {
    const baseUnitOptions = getBaseUnitOptionsForSelectedTag(
      selectedCountryUnits || [],
      state.unitTags || '',
    );
    setBaseUnitsForSelectedTag(baseUnitOptions || []);
  }, [state.unitTags, selectedCountryUnits]);

  const handleSave = (): void => {
    const { isValid } = validateParameters(
      state,
      additionalProperties,
      valueTypeOptions,
      dispatch,
      isEditMode,
    );

    if (!isValid) {
      return;
    }

    if (isEditMode && initialData) {
      const editedParameter = createBaseAdditionalProperty(state);
      const hasChangesResult = hasChanges(editedParameter, initialData);
      if (hasChangesResult) {
        const editedParameter = updateExistingParameter(state, initialData);
        onEdit(editedParameter);
        displaySnackbarMessage(t(`${prefix}.parameterUpdatedMessage`), setDisplaySnackbar);
      } else {
        onOpenChange(false);
        return;
      }
    } else {
      const newParameter = createNewParameter(state);
      onSave(newParameter);
      displaySnackbarMessage(t(`${prefix}.parameterAddedMessage`), setDisplaySnackbar);
    }

    // Reset form fields and close dialog after save
    dispatch({ type: AdditionalParameterActions.RESET_FORM });
    onOpenChange(false);
  };

  const ahdbHandler = async (): Promise<void> => {
    if (savedAhdbParams?.length) {
      const updatedParameters = [...(cropRegion?.additionalProperties || []), ...savedAhdbParams];

      await updateParameters(cropRegion, updatedParameters, triggerUpdateCropRegion, setCropRegion);
      displaySnackbarMessage(t(`${prefix}.paramsAdded`), setDisplaySnackbar);
    }
    onOpenChange(false);
    setSavedAhdbParams([]);
  };

  function unitTagsChangeHandler(selectedUnitTag: string): void {
    const unitTagExists = allUnitTagsByCountry?.some((unitTag) => unitTag.text === selectedUnitTag);
    if (unitTagExists) handleUnitTagsChange(selectedUnitTag);
  }

  const handleClose = (isOpen: boolean): void => {
    if (!isOpen) {
      dispatch({ type: AdditionalParameterActions.RESET_FORM });
    }
    onOpenChange(isOpen);
  };

  const handleChangeTab = (tab: string): void => {
    // Reset other-parameters form or ahdb parameters fields on switching tabs
    if (!isEditMode) {
      dispatch({ type: AdditionalParameterActions.RESET_FORM });
    }
    setSavedAhdbParams([]);
    tab && setActiveTab(tab);
  };

  useEffect(() => {
    if (initialData) {
      dispatch({
        type: AdditionalParameterActions.SET_INITIAL_DATA,
        initialData: initialData,
      });
    } else {
      dispatch({ type: AdditionalParameterActions.RESET_FORM });
    }
  }, [initialData, dispatch]);

  return (
    <Dialog open={showDialog} onOpenChange={handleClose}>
      <Dialog.Content data-cy='additional-properties-content' css={ContentStyle}>
        <HeaderWrapper>
          <Dialog.Header>
            <Dialog.Title data-cy='additional-properties-title'>
              {t(`${keyPrefix}.${isEditMode ? 'editTitle' : 'title'}`)}
            </Dialog.Title>
          </Dialog.Header>
          <Dialog.Close>
            <IconButton
              css={CloseButtonStyle}
              data-cy='additional-properties-close-btn'
              icon='Close'
              colorConcept='brand'
              size='xs'
            />
          </Dialog.Close>
        </HeaderWrapper>
        <Card.Divider />
        {isAhdb && !isEditMode ? (
          <Tabs defaultValue={activeTab} onValueChange={handleChangeTab} className='tabs'>
            <Tabs.List aria-label='Components'>
              <Tabs.Trigger value={ahdbParam} key={ahdbParam}>
                {t(`${keyPrefix}.ahdbParameter`)}
              </Tabs.Trigger>
              <Tabs.Trigger value={otherParam} key={otherParam}>
                {t(`${keyPrefix}.otherParameter`)}
              </Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content value={ahdbParam} className='ahdb-wrapper'>
              <AhdbParameter
                additionalProperties={additionalProperties}
                setSavedAhdbParams={setSavedAhdbParams}
              />
            </Tabs.Content>
            <Tabs.Content value={otherParam} className='other-parameters'>
              {OtherParameters()}
            </Tabs.Content>
          </Tabs>
        ) : (
          <>{OtherParameters()}</>
        )}
        <Card.Divider />
        <TailWrapperStyle>
          <Button
            onClick={isAhdb && activeTab === ahdbParam && !isEditMode ? ahdbHandler : handleSave}
            data-cy='additional-properties-save-btn'
          >
            {isAhdb && activeTab === ahdbParam && !isEditMode
              ? t(`${keyPrefix}.saveAhdb`)
              : activeTab === otherParam
              ? t(`${keyPrefix}.saveOther`)
              : isEditMode
              ? t('polaris.common.saveChanges')
              : t('polaris.common.saveButton')}
          </Button>
        </TailWrapperStyle>
      </Dialog.Content>
    </Dialog>
  );

  function OtherParameters() {
    return (
      <BodyWrapper
        data-cy='additional-properties-body-wrapper'
        className={isAhdb && activeTab === otherParam ? 'body-wrapper' : ''}
      >
        <Dialog.Description style={DescriptionStyle}>
          {t(`${keyPrefix}.checkboxDescription`)}
        </Dialog.Description>

        <CheckboxWrapper data-cy='additional-properties-checkbox-wrapper'>
          <Label size={'n'} data-cy='additional-properties-checkbox-label'>
            {t(`${keyPrefix}.checkboxLabel`)}
          </Label>
          <CheckBox
            data-cy='additional-properties-checkbox'
            checked={state.isRequired}
            concept='brand'
            onCheckedChange={handleRequiredChange}
          />
        </CheckboxWrapper>
        <FieldsWrapper
          isEditMode={isEditMode}
          hasError={isNameOrOrdinalError}
          hasHelperText={isNameOrOrdinalError}
          data-cy='additional-properties-fieldsWrapper-first'
        >
          {nameOrdinalConfig.map((conf) => (
            <Input
              key={conf?.dataCy}
              size='n'
              type={conf?.type}
              value={conf?.value}
              onChange={conf?.onChange}
              onKeyDown={conf?.onKeyDown}
              variant={conf?.variant === 'default' ? 'default' : 'error'}
              helperText={conf?.helperText}
              label={conf?.label}
              data-cy={conf?.dataCy}
              disabled={conf?.disabled}
              cover={conf?.cover === 'fill' ? 'fill' : 'outline'}
            />
          ))}
        </FieldsWrapper>
        <FieldsWrapper
          hasError={isValueTypeError || isDefaultValueError}
          hasHelperText={isValueTypeError || isDefaultValueError}
          data-cy='additional-properties-fieldsWrapper-second'
        >
          {valueTypeBoolConfig.map(
            (conf) =>
              conf?.shouldShow && (
                <SelectWrapper key={conf?.wrapperDataCy} dataCy={conf?.wrapperDataCy}>
                  <Select
                    ariaLabel={conf?.ariaLabel}
                    cover='outline'
                    items={conf?.items}
                    loadingText={t(`${keyPrefix}.loadingText`)}
                    variant={conf?.variant === 'default' ? 'default' : 'error'}
                    helper-text={conf?.helperText}
                    position='popper'
                    size='n'
                    label={conf?.label}
                    onChange={conf?.onChange}
                    value={conf?.value}
                  />
                </SelectWrapper>
              ),
          )}
          {defaultValConfig?.map(
            (conf) =>
              conf?.shouldShow && (
                <Input
                  key={conf?.dataCy}
                  size='n'
                  type={conf?.type}
                  value={conf?.value}
                  onChange={conf?.onChange}
                  onKeyDown={conf?.onKeyDown}
                  helperText={conf?.helperText}
                  variant={conf?.variant === 'default' ? 'default' : 'error'}
                  data-cy={conf?.dataCy}
                  label={conf?.label}
                />
              ),
          )}
        </FieldsWrapper>
        {state.valueType === ValueTypeEnum.NumberWithUnit && (
          <FieldsWrapper
            hasError={isUnitTagsOrBaseUnitError}
            hasHelperText={true}
            data-cy='additional-properties-fieldsWrapper-third'
          >
            {unitTagsBaseUnitConfig.map((conf) => (
              <SelectWrapper key={conf?.wrapperDataCy} dataCy={conf?.wrapperDataCy}>
                <Select
                  ariaLabel={conf?.ariaLabel}
                  items={conf?.items}
                  loadingText={t(`${keyPrefix}.loadingText`)}
                  variant={conf?.variant === 'default' ? 'default' : 'error'}
                  helper-text={conf?.helperText}
                  position='popper'
                  size='n'
                  label={conf?.label}
                  onChange={conf?.onChange}
                  value={conf?.value}
                  disabled={conf?.disabled}
                  cover={conf?.cover === 'fill' ? 'fill' : 'outline'}
                />
              </SelectWrapper>
            ))}
          </FieldsWrapper>
        )}
        {(state.valueType === ValueTypeEnum.Number ||
          state.valueType === ValueTypeEnum.NumberWithUnit) && (
          <FieldsWrapper
            hasError={isMinOrMaxError}
            hasHelperText={isMinOrMaxError}
            data-cy='additional-properties-fieldsWrapper-fourth'
          >
            {numberMinMaxConfig.map((conf) => (
              <Input
                key={conf?.dataCy}
                size='n'
                type='number'
                variant={conf?.variant === 'default' ? 'default' : 'error'}
                data-cy={conf?.dataCy}
                label={conf?.label}
                onChange={conf?.onChange}
                onKeyDown={conf?.onKeyDown}
                value={conf?.value}
                helperText={conf?.helperText}
                cover={conf?.cover === 'fill' ? 'fill' : 'outline'}
              />
            ))}
          </FieldsWrapper>
        )}
        {state.valueType === ValueTypeEnum.List && (
          <ListItemsComponent
            initialOptions={state.options || []}
            onOptionsChange={handleOptionChange}
          />
        )}
      </BodyWrapper>
    );
  }
};
