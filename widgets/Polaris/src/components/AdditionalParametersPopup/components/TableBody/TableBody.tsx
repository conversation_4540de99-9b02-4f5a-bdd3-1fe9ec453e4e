import React from 'react';
import { Caption, Table } from '@yaradigitallabs/ahua-react';
import { Table as TanstackTable } from '@tanstack/table-core/build/lib/types';
import { flexRender } from '@tanstack/react-table';
import { ConfirmationDialog } from '@widgets/Polaris/src/components';
import { Option } from '@common/types';
import { TableCellStyled } from '@widgets/Polaris/src/components/AdditionalParametersPopup/components/TableBody/TableBody.styled';
import { useDeleteConfirmation } from '@polaris-hooks/index';
import { useTranslation } from 'react-i18next';

export type TableBodyProps = {
  table: TanstackTable<Option>;
  handleDeleteItem: (name: Option['Name']) => void;
  handleEditDialog: (index: number) => void;
};

export function TableBody({ table, handleDeleteItem, handleEditDialog }: TableBodyProps) {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris',
  });
  const translationPrefix = 'cnpDetails.configuration.additionalParameters';
  const { dialog, openDialog, closeDialog, confirmDelete } = useDeleteConfirmation();

  return (
    <>
      {table.getRowModel().rows.map((row, index) => (
        <Table.Row key={row.id + index}>
          {row.getVisibleCells().map((cell) => {
            const cellKey = `${row.id}-${cell.id}`;
            if (cell.column.id !== 'actions') {
              return (
                <TableCellStyled key={cellKey} hasEllipsis={true} title={cell.getValue<string>()}>
                  <Caption>{flexRender(cell.column.columnDef.cell, cell.getContext())}</Caption>
                </TableCellStyled>
              );
            } else {
              return (
                <Table.ActionsCell key={cell.id} data-cy={`list-type-table-action-cell-${row.id}`}>
                  <Table.ActionMenu title='Action menu'>
                    <Table.ActionMenuItem
                      icon='Edit'
                      title={t(`${translationPrefix}.table.actions.editSubParameter`)}
                      data-cy='list-type-table-edit-action'
                      onClick={() => {
                        const realIndex =
                          table.getState().pagination.pageIndex *
                            table.getState().pagination.pageSize +
                          index;
                        handleEditDialog(realIndex);
                      }}
                    >
                      {t(`${translationPrefix}.table.actions.editSubParameter`)}
                    </Table.ActionMenuItem>
                    <Table.ActionMenuItem
                      icon='Delete'
                      onClick={() => openDialog(row.original.Name)}
                      title={t(`${translationPrefix}.table.actions.deleteSubParameter`)}
                      data-cy='list-type-table-delete-action'
                    >
                      {t(`${translationPrefix}.table.actions.deleteSubParameter`)}
                    </Table.ActionMenuItem>
                  </Table.ActionMenu>
                </Table.ActionsCell>
              );
            }
          })}
        </Table.Row>
      ))}
      <ConfirmationDialog
        open={dialog.isOpen}
        title={dialog.title}
        description={t(`${translationPrefix}.dialog.description`)}
        icon='Bang'
        iconColorConcept='destructive'
        okButton={t('common.yesDelete')}
        cancelButton={t('common.cancel')}
        onOk={() => confirmDelete(handleDeleteItem)}
        onCancel={closeDialog}
      />
    </>
  );
}
