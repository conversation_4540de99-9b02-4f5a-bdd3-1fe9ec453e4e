import { CalculationType } from '../../AdditionalParametersPopup.constants';
import { additionalParameterInitialState, additionalParameterReducer } from '../parameterReducers';
import { AdditionalParameterActions, SetOptionsAction, AdditionalParameterAction } from '../../';

describe('parameterReducers', () => {
  const baseState = {
    parameterName: 'Test Name',
    parameterOrdinal: '1',
    valueType: '',
    defaultValue: null,
    isRequired: true,
    isNameInvalid: false,
    isOrdinalInvalid: false,
    isValueTypeInvalid: false,
    isNumberMinInvalid: false,
    isNumberMaxInvalid: false,
    isUnitTagsInvalid: false,
    isBaseUnitInvalid: false,
    isDefaultValueInvalid: false,
    options: [
      {
        Name: 'Test new',
        Value: '5',
        TranslationKey: 'testNew',
      },
    ],
    numberMin: '0',
    numberMax: '10',
    unitTags: '',
    baseUnit: '',
    initialData: {
      ApiPropertyName: '',
      BaseUnit: '',
      CalculationType: CalculationType.SOIL,
      DefaultValue: '2024-04-24T21:00:00.000Z',
      Max: 0,
      Min: 0,
      Name: 'date 1',
      Ordinal: 1,
      Options: [],
      Required: true,
      TranslationKey: 'Date1',
      UnitTags: '',
      ValueType: 'date',
    },
  };

  describe('additionalParameterReducer', () => {
    it('should handle SET_PARAMETER_NAME', () => {
      const action: AdditionalParameterAction = {
        type: AdditionalParameterActions.SET_PARAMETER_NAME,
        value: 'New Name',
        isInvalid: false,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.parameterName).toEqual('New Name');
      expect(newState.isNameInvalid).toEqual(false);
    });

    it('should handle SET_PARAMETER_ORDINAL', () => {
      const action: AdditionalParameterAction = {
        type: AdditionalParameterActions.SET_PARAMETER_ORDINAL,
        value: '10',
        isInvalid: undefined,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.parameterOrdinal).toEqual('10');
      expect(newState.isOrdinalInvalid).toEqual(undefined);
    });

    it('should handle SET_PARAMETER_NUMBER_MIN', () => {
      const action = {
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
        value: '5',
        isInvalid: false,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.numberMin).toEqual('5');
      expect(newState.isNumberMinInvalid).toEqual(false);
    });

    it('should handle SET_PARAMETER_NUMBER_MAX', () => {
      const action = {
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
        value: '10',
        isInvalid: true,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.numberMax).toEqual('10');
      expect(newState.isNumberMaxInvalid).toEqual(true);
    });

    it('should handle SET_PARAMETER_UNIT_TAGS', () => {
      const action = {
        type: AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS,
        value: 'New Tags',
        isInvalid: false,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.unitTags).toEqual('New Tags');
      expect(newState.isUnitTagsInvalid).toEqual(false);
    });

    it('should handle SET_PARAMETER_BASE_UNIT', () => {
      const action = {
        type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
        value: 'Unit1',
        isInvalid: true,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.baseUnit).toEqual('Unit1');
      expect(newState.isBaseUnitInvalid).toEqual(true);
    });

    it('should handle SET_VALUE_TYPE', () => {
      const action: AdditionalParameterAction = {
        type: AdditionalParameterActions.SET_VALUE_TYPE,
        value: 'Type 1',
        isInvalid: false,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.valueType).toEqual('Type 1');
      expect(newState.isValueTypeInvalid).toEqual(false);
    });

    it('should handle SET_DEFAULT_VALUE with different value types', () => {
      const testValues = ['text', 123, true, null];
      testValues.forEach((testValue) => {
        const action: AdditionalParameterAction = {
          type: AdditionalParameterActions.SET_DEFAULT_VALUE,
          value: testValue,
        };
        const newState = additionalParameterReducer(additionalParameterInitialState, action);
        expect(newState.defaultValue).toEqual(testValue);
      });
    });

    it('should handle SET_IS_REQUIRED', () => {
      const action: AdditionalParameterAction = {
        type: AdditionalParameterActions.SET_IS_REQUIRED,
        value: false,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.isRequired).toEqual(false);
    });

    it('should handle SET_INITIAL_DATA correctly', () => {
      const initialData = {
        Name: 'Initial Name',
        Ordinal: '1',
        ValueType: 'text',
        DefaultValue: 'Initial Value',
        Min: '0',
        Max: '100',
        UnitTags: 'Tag1',
        BaseUnit: 'Unit1',
        Required: true,
        Options: [{ Name: 'Init Option', Value: 'Init', TranslationKey: 'initOption' }],
      };
      const action = {
        type: AdditionalParameterActions.SET_INITIAL_DATA,
        initialData: initialData,
      };
      const newState = additionalParameterReducer(baseState, action);
      expect(newState).toMatchObject({
        parameterName: initialData.Name,
        parameterOrdinal: initialData.Ordinal,
        valueType: 'text',
        defaultValue: initialData.DefaultValue,
        numberMin: initialData.Min,
        numberMax: initialData.Max,
        unitTags: initialData.UnitTags,
        baseUnit: initialData.BaseUnit,
        isRequired: initialData.Required,
        options: initialData.Options,
        isNameInvalid: false,
        isOrdinalInvalid: false,
        isValueTypeInvalid: false,
        isDefaultValueInvalid: false,
        isNumberMinInvalid: false,
        isNumberMaxInvalid: false,
        isUnitTagsInvalid: false,
        isBaseUnitInvalid: false,
      });
    });

    it('should handle RESET_FORM', () => {
      const action: AdditionalParameterAction = {
        type: AdditionalParameterActions.RESET_FORM,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState).toEqual(additionalParameterInitialState);
    });

    it('should handle SET_OPTIONS', () => {
      const options = [
        { Name: 'option1', Value: 'Option 1', TranslationKey: 'option1' },
        { Name: 'option2', Value: 'Option 2', TranslationKey: 'option2' },
      ];
      const action: SetOptionsAction = {
        type: AdditionalParameterActions.SET_OPTIONS,
        options: options,
      };
      const newState = additionalParameterReducer(additionalParameterInitialState, action);
      expect(newState.options).toEqual(options);
    });

    it('should throw an error on unhandled action types', () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const action = { type: 'UNKNOWN_ACTION' as any, value: 'test' };
      expect(() => {
        additionalParameterReducer(additionalParameterInitialState, action);
      }).toThrow('Unhandled action type');
    });
  });
});
