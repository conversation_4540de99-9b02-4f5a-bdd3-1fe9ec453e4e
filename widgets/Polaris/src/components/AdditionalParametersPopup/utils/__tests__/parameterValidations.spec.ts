import { ChangeEvent } from 'react';
import {
  createBaseUnitChangeHandler,
  createNameChangeHandler,
  createNumberMaxChangeHandler,
  createNumberMinChangeHandler,
  createOrdinalChangeHandler,
  createUnitTagsChangeHandler,
  createValueTypeChangeHandler,
  validateParameterName,
  validateParameterNumber,
  validateParameterOrdinal,
  validateParameters,
  validateValueType,
} from '../parameterValidations';
import { AdditionalParameterActions, ValueTypeEnum } from '../../';
import { additionalPropertiesMock } from '@common/mocks';
import { CalculationType } from '@common/types';

describe('parameterUtils', () => {
  const baseState = {
    parameterName: 'Test Name',
    parameterOrdinal: '1',
    valueType: '',
    defaultValue: null,
    isRequired: true,
    isNameInvalid: false,
    isOrdinalInvalid: false,
    isValueTypeInvalid: false,
    isNumberMinInvalid: false,
    isNumberMaxInvalid: false,
    isUnitTagsInvalid: false,
    isBaseUnitInvalid: false,
    isDefaultValueInvalid: false,
    options: [
      {
        Name: 'Test new',
        Value: '5',
        TranslationKey: 'testNew',
      },
    ],
    numberMin: '0',
    numberMax: '10',
    unitTags: '',
    baseUnit: '',
    initialData: {
      ApiPropertyName: '',
      BaseUnit: '',
      CalculationType: CalculationType.SOIL,
      DefaultValue: '2024-04-24T21:00:00.000Z',
      Max: 0,
      Min: 0,
      Name: 'date 1',
      Ordinal: 1,
      Options: [],
      Required: true,
      TranslationKey: 'Date1',
      UnitTags: '',
      ValueType: 'date',
    },
  };

  describe('validateParameterName', () => {
    it('returns false if the name is empty', () => {
      expect(validateParameterName('', [])).toBe(false);
    });

    it('returns false if the name already exists', () => {
      expect(validateParameterName('date 1', additionalPropertiesMock)).toBe(false);
    });

    it('returns true if the name is valid', () => {
      expect(validateParameterName('new', additionalPropertiesMock)).toBe(true);
    });
  });

  describe('validateParameterOrdinal', () => {
    it('should return true for a valid ordinal', () => {
      expect(validateParameterOrdinal('4', additionalPropertiesMock)).toBeTruthy();
    });

    it('should return false for an empty ordinal', () => {
      expect(validateParameterOrdinal('   ', additionalPropertiesMock)).toBeFalsy();
      expect(validateParameterOrdinal('', additionalPropertiesMock)).toBeFalsy();
    });

    it('should return false for a non-numeric ordinal', () => {
      expect(validateParameterOrdinal('abc', additionalPropertiesMock)).toBeFalsy();
    });

    it('should return true for a non-existing ordinal', () => {
      expect(validateParameterOrdinal('4', additionalPropertiesMock)).toBeTruthy();
    });
  });

  describe('validateParameterNumber', () => {
    it('returns true for valid numeric strings', () => {
      expect(validateParameterNumber('123')).toBe(true);
      expect(validateParameterNumber('0')).toBe(true);
      expect(validateParameterNumber('123.456')).toBe(true);
    });

    it('returns false for non-numeric strings', () => {
      expect(validateParameterNumber('abc')).toBe(false);
      expect(validateParameterNumber('123abc')).toBe(false);
      expect(validateParameterNumber('')).toBe(false);
    });

    it('returns false for strings that are just whitespace', () => {
      expect(validateParameterNumber(' ')).toBe(false);
      expect(validateParameterNumber('\t')).toBe(false);
    });
  });

  describe('validateValueType', () => {
    const valueTypeOptionsMock = [
      { value: 'type1', text: 'Type 1' },
      { value: 'type2', text: 'Type 2' },
      { value: 'type3', text: 'Type 3' },
    ];

    it('should return true for a valid value', () => {
      expect(validateValueType('type2', valueTypeOptionsMock)).toBeTruthy();
    });

    it('should return false for an empty value', () => {
      expect(validateValueType('', valueTypeOptionsMock)).toBeFalsy();
    });

    it('should return false for a value not in options', () => {
      expect(validateValueType('type4', valueTypeOptionsMock)).toBeFalsy();
    });

    it('should return false when options array is empty', () => {
      expect(validateValueType('type1', [])).toBeFalsy();
    });
  });

  describe('createNameChangeHandler', () => {
    const mockDispatch = jest.fn();

    it('should dispatch action with isInvalid true when name is invalid', () => {
      const handler = createNameChangeHandler(mockDispatch, additionalPropertiesMock);
      const event: ChangeEvent<HTMLInputElement> = {
        // @ts-ignore
        target: { value: 'date 1' },
      };
      handler(event);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'SET_PARAMETER_NAME',
        value: 'date 1',
        isInvalid: true,
      });
    });

    it('should dispatch action with isInvalid false when name is valid', () => {
      const handler = createNameChangeHandler(mockDispatch, additionalPropertiesMock);
      const event: ChangeEvent<HTMLInputElement> = {
        // @ts-ignore
        target: { value: 'ValidName' },
      };
      handler(event);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'SET_PARAMETER_NAME',
        value: 'ValidName',
        isInvalid: false,
      });
    });
  });

  describe('createOrdinalChangeHandler', () => {
    const mockDispatch = jest.fn();
    it('should dispatch action with isInvalid true when name is invalid', () => {
      const handler = createOrdinalChangeHandler(mockDispatch, additionalPropertiesMock);
      // @ts-ignore
      const event: ChangeEvent<HTMLInputElement> = { target: { value: '1' } };
      handler(event);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'SET_PARAMETER_ORDINAL',
        value: '1',
        isInvalid: true,
      });
    });

    it('should dispatch action with isInvalid false when name is valid', () => {
      const handler = createOrdinalChangeHandler(mockDispatch, additionalPropertiesMock);
      // @ts-ignore
      const event: ChangeEvent<HTMLInputElement> = { target: { value: '2' } };
      handler(event);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'SET_PARAMETER_ORDINAL',
        value: '2',
        isInvalid: false,
      });
    });
  });

  describe('createNumberMinChangeHandler', () => {
    const mockDispatch = jest.fn();
    const mockValidateParameterNumber = jest.fn();

    jest.mock('../index', () => ({
      validateParameterNumber: mockValidateParameterNumber,
    }));

    beforeEach(() => {
      mockDispatch.mockClear();
      mockValidateParameterNumber.mockClear();
    });

    it('dispatches valid action for valid number input', () => {
      mockValidateParameterNumber.mockReturnValue(true);
      const handler = createNumberMinChangeHandler(baseState.numberMax, '', mockDispatch);
      const mockEvent = { target: { value: '5' } };
      // @ts-ignore
      handler(mockEvent);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
        value: '5',
        isInvalid: false,
      });
    });

    it('dispatches invalid action for invalid number input', () => {
      mockValidateParameterNumber.mockReturnValue(false);
      const handler = createNumberMinChangeHandler(baseState.numberMax, '', mockDispatch);
      const mockEvent = { target: { value: 'abc' } };
      // @ts-ignore
      handler(mockEvent);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
        value: 'abc',
        isInvalid: true,
      });
    });

    it('handles edge cases like empty strings', () => {
      mockValidateParameterNumber.mockReturnValue(false);
      const handler = createNumberMinChangeHandler(baseState.numberMax, '', mockDispatch);
      const mockEventEmpty = { target: { value: '' } };
      // @ts-ignore
      handler(mockEventEmpty);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
        value: '',
        isInvalid: true,
      });
    });

    it('handles edge cases like extreme values', () => {
      mockValidateParameterNumber.mockReturnValue(true);
      const handler = createNumberMinChangeHandler('9999999999999999999999999', '', mockDispatch);
      const mockEventExtreme = {
        target: { value: '999999999999999999999999' },
      };

      // @ts-ignore
      handler(mockEventExtreme);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
        value: '999999999999999999999999',
        isInvalid: false,
      });
    });
  });

  describe('createNumberMaxChangeHandler', () => {
    const mockDispatch = jest.fn();
    const mockValidateParameterNumber = jest.fn();
    jest.mock('../index', () => ({
      validateParameterNumber: mockValidateParameterNumber,
    }));

    beforeEach(() => {
      mockDispatch.mockClear();
      mockValidateParameterNumber.mockClear();
    });

    it('dispatches valid action for valid number input', () => {
      mockValidateParameterNumber.mockReturnValue(true);
      const handler = createNumberMaxChangeHandler(baseState.numberMin, '', mockDispatch);
      const mockEvent = { target: { value: '100' } };
      // @ts-ignore
      handler(mockEvent);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
        value: '100',
        isInvalid: false,
      });
    });

    it('dispatches invalid action for invalid number input', () => {
      mockValidateParameterNumber.mockReturnValue(false);
      const handler = createNumberMaxChangeHandler(baseState.numberMin, '', mockDispatch);
      const mockEvent = { target: { value: 'xyz' } };
      // @ts-ignore
      handler(mockEvent);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
        value: 'xyz',
        isInvalid: true,
      });
    });
  });

  describe('createUnitTagsChangeHandler', () => {
    const mockDispatch = jest.fn();
    const mockValidateEmptySelection = jest.fn();

    beforeEach(() => {
      mockDispatch.mockClear();
      mockValidateEmptySelection.mockClear();
    });

    it('dispatches action with valid selection', () => {
      const selectedItem = 'unit1';
      mockValidateEmptySelection.mockReturnValue(true);
      createUnitTagsChangeHandler(mockDispatch, selectedItem);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS,
        value: selectedItem,
        isInvalid: false,
      });
    });

    it('dispatches action with invalid (empty) selection', () => {
      const selectedItem = '';
      mockValidateEmptySelection.mockReturnValue(false);
      createUnitTagsChangeHandler(mockDispatch, selectedItem);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS,
        value: selectedItem,
        isInvalid: true,
      });
    });
  });

  describe('createBaseUnitChangeHandler', () => {
    const mockDispatch = jest.fn();
    const mockValidateEmptySelection = jest.fn();

    beforeEach(() => {
      mockDispatch.mockClear();
      mockValidateEmptySelection.mockClear();
    });

    it('dispatches action with invalid (empty) base unit selection', () => {
      const selectedItem = '';
      mockValidateEmptySelection.mockReturnValue(false);
      createBaseUnitChangeHandler(mockDispatch, selectedItem);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
        value: selectedItem,
        isInvalid: true,
      });
    });

    it('handles edge cases like unusual or special characters', () => {
      const selectedItem = '#$%^&*';
      mockValidateEmptySelection.mockReturnValue(true);
      createBaseUnitChangeHandler(mockDispatch, selectedItem);

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
        value: selectedItem,
        isInvalid: false,
      });
    });
  });

  describe('createValueTypeChangeHandler', () => {
    const mockDispatch = jest.fn();
    const mockValidateValueType = jest.fn();
    const fixedDate = new Date('2024-05-09T08:55:00.019Z');

    beforeEach(() => {
      jest.spyOn(global, 'Date').mockImplementation(() => fixedDate);
      mockDispatch.mockClear();
      mockValidateValueType.mockClear();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('dispatches correct actions for Date type with mocked Date object', () => {
      const selectedItem = ValueTypeEnum.Date;
      const valueTypeOptions = [{ value: 'date', text: 'Date text' }];
      mockValidateValueType.mockReturnValue(true);

      createValueTypeChangeHandler(
        mockDispatch,
        mockValidateValueType,
        valueTypeOptions,
        selectedItem,
      );

      expect(mockValidateValueType).toHaveBeenCalledWith(selectedItem, valueTypeOptions);
      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_VALUE_TYPE,
        value: selectedItem,
        isInvalid: false,
      });
      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: '',
      });
    });

    it('dispatches correct actions for Boolean type', () => {
      const selectedItem = ValueTypeEnum.Boolean;
      const valueTypeOptions = [{ value: 'boolean', text: 'Boolean text' }];
      mockValidateValueType.mockReturnValue(true);

      createValueTypeChangeHandler(
        mockDispatch,
        mockValidateValueType,
        valueTypeOptions,
        selectedItem,
      );

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_VALUE_TYPE,
        value: selectedItem,
        isInvalid: false,
      });
      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: null,
      });
    });

    it('calls resetExtraParameters when valueType is Number or NumberWithUnit', () => {
      const selectedItem = ValueTypeEnum.Number;
      const valueTypeOptions = [{ value: selectedItem, text: 'Number' }];
      mockValidateValueType.mockReturnValue(true);
      createValueTypeChangeHandler(
        mockDispatch,
        mockValidateValueType,
        valueTypeOptions,
        selectedItem,
      );

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_VALUE_TYPE,
        value: selectedItem,
        isInvalid: false,
      });
      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: null,
      });
    });

    it.each([ValueTypeEnum.Number, ValueTypeEnum.NumberWithUnit])(
      'dispatches correct actions for %s type',
      (selectedItem) => {
        const valueTypeOptions = [
          { value: selectedItem.toLowerCase(), text: `${selectedItem} text` },
        ];
        mockValidateValueType.mockReturnValue(true);

        createValueTypeChangeHandler(
          mockDispatch,
          mockValidateValueType,
          valueTypeOptions,
          selectedItem,
        );

        expect(mockDispatch).toHaveBeenCalledWith({
          type: AdditionalParameterActions.SET_VALUE_TYPE,
          value: selectedItem,
          isInvalid: false,
        });
        expect(mockDispatch).toHaveBeenCalledWith({
          type: AdditionalParameterActions.SET_DEFAULT_VALUE,
          value: null,
        });
      },
    );

    it.each([ValueTypeEnum.Text, ValueTypeEnum.List])(
      'dispatches correct actions for %s type',
      (selectedItem) => {
        const valueTypeOptions = [
          { value: selectedItem.toLowerCase(), text: `${selectedItem} text` },
        ];
        mockValidateValueType.mockReturnValue(true);

        createValueTypeChangeHandler(
          mockDispatch,
          mockValidateValueType,
          valueTypeOptions,
          selectedItem,
        );

        expect(mockDispatch).toHaveBeenCalledWith({
          type: AdditionalParameterActions.SET_DEFAULT_VALUE,
          value: '',
        });
      },
    );

    it('dispatches correct actions for unknown type', () => {
      const selectedItem = 'UnknownType';
      const valueTypeOptions = [{ value: 'known', text: 'Known text' }];
      mockValidateValueType.mockReturnValue(false);

      createValueTypeChangeHandler(
        mockDispatch,
        mockValidateValueType,
        valueTypeOptions,
        selectedItem,
      );

      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_VALUE_TYPE,
        value: selectedItem,
        isInvalid: true,
      });
      expect(mockDispatch).toHaveBeenCalledWith({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: null,
      });
    });
  });

  describe('validateParameters', () => {
    const mockValidateName = jest.fn();
    const mockValidateOrdinal = jest.fn();
    const mockValidateType = jest.fn();
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return invalid if any validation fails', () => {
      const mockDispatch = jest.fn();
      mockValidateName.mockReturnValue(false);
      mockValidateOrdinal.mockReturnValue(true);
      mockValidateType.mockReturnValue(true);
      const valueTypeOptions = [{ value: '1', text: 'Type 1' }];

      const result = validateParameters(
        baseState,
        additionalPropertiesMock,
        valueTypeOptions,
        mockDispatch,
        false,
      );
      expect(result.isValid).toBe(false);
    });
  });
});
