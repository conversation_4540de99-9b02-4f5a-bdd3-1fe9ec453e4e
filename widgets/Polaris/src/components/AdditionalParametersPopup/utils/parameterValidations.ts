import { AdditionalProperty } from '@common/types';
import React, { Dispatch } from 'react';
import {
  AdditionalParameterAction,
  AdditionalParameterErrors,
  AdditionalParameterState,
  ValueTypeOption,
  AdditionalParameterActions,
  ValueTypeEnum,
  Action,
} from '@widgets/Polaris/src/components';
import { additionalParameterInitialState } from './parameterReducers';

/**
 * Checks if a given string value is not empty after trimming spaces.
 * @param {string} value - The string to check.
 * @return {boolean} True if the string is not empty, false otherwise.
 */
export const validateEmptySelection = (value: string): boolean => value?.trim() !== '';

/**
 * Validates a parameter name by ensuring it is not empty and does not already exist in a given list.
 * @param {string} name - The parameter name to validate.
 * @param {AdditionalProperty[]} additionalProperties - Array of existing properties to check against.
 * @return {boolean} True if the name is valid, false otherwise.
 */
export const validateParameterName = (
  name: string,
  additionalProperties: AdditionalProperty[],
): boolean => {
  const exists = additionalProperties.some(
    (prop) => prop.Name.toLowerCase() === name.toLowerCase(),
  );
  return validateEmptySelection(name) && !exists;
};

/**
 * Validates a parameter ordinal by checking it is numeric, not empty, and does not exist in a list unless allowed.
 * @param {string} ordinal - The ordinal to validate.
 * @param {AdditionalProperty[]} additionalProperties - Array of existing properties to check against.
 * @param {string|null} currentParameterName - Current parameter name for exclusion in existence check.
 * @return {boolean} True if the ordinal is valid, false otherwise.
 */
export const validateParameterOrdinal = (
  ordinal: string,
  additionalProperties: AdditionalProperty[],
  currentParameterName: string | null = null,
): boolean => {
  const isNumeric = !isNaN(Number(ordinal));
  const exists = additionalProperties.some(
    (prop) =>
      prop.Ordinal === Number(ordinal) &&
      (currentParameterName ? prop.Name !== currentParameterName : true),
  );
  return validateEmptySelection(ordinal) && isNumeric && !exists;
};

/**
 * Validates that a string represents a valid number.
 * @param {string} number - The string representation of the number to validate.
 * @return {boolean} True if the string is a valid number, false otherwise.
 */
export const validateParameterNumber = (number: string): boolean => {
  const isNumeric = !isNaN(Number(number));
  return validateEmptySelection(number) && isNumeric;
};

/**
 * Validates that the number entered in min is not greater than max.
 * @param {string} numberMin - The string representation of numberMin to validate.
 * @param {string} numberMax - The string representation of numberMax to validate.
 * @return {boolean} True if the number is less than max, false otherwise.
 */
export const validateParameterIsMinLessThanMax = (numberMin: string, numberMax: string): boolean =>
  Number(numberMin) <= Number(numberMax);

/**
 * Validates that the number is within min/max range.
 * @param {string} value - The string representation of the number to validate.
 * @param {string} numberMin - The string representation of numberMin to validate.
 * @param {string} numberMax - The string representation of numberMax to validate.
 * @return {boolean} True if the number is within min/max range, false otherwise.
 */
export const validateParameterWithinMinMaxRange = (
  value: string,
  numberMin: string,
  numberMax: string,
): boolean => {
  const valueToNum = Number(value);
  return valueToNum >= Number(numberMin) && valueToNum <= Number(numberMax);
};

/**
 * Validates if a given value type is included within the allowed options.
 * @param {string} value - The value type to validate.
 * @param {Array<{value: string, text: string}>} valueTypeOptions - Array of possible value types.
 * @return {boolean} True if the value type is included in the options, false otherwise.
 */
export const validateValueType = (
  value: string,
  valueTypeOptions: { value: string; text: string }[],
): boolean => {
  return !!value && valueTypeOptions.some((option) => option.value === value);
};

/**
 * Creates a handler function for changes in the parameter name input field.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch function to call with the new state.
 * @param {AdditionalProperty[]} additionalProperties - Array of existing properties to validate against.
 * @return {Function} A function to handle changes to the parameter name input.
 */
export const createNameChangeHandler =
  (
    dispatch: Dispatch<AdditionalParameterAction>,
    additionalProperties: AdditionalProperty[],
  ): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
  (event: React.ChangeEvent<HTMLInputElement>) => {
    const newName = event.target.value;
    const isInvalid = !validateParameterName(newName, additionalProperties);
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NAME,
      value: newName,
      isInvalid,
    });
  };

/**
 * Creates a handler function for changes in the parameter ordinal input field.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch function to call with the new state.
 * @param {AdditionalProperty[]} additionalProperties - Array of existing properties to validate against.
 * @param {string|null} currentParameterName - The current name of the parameter being edited, if applicable.
 * @return {Function} A function to handle changes to the parameter ordinal input.
 */
export const createOrdinalChangeHandler =
  (
    dispatch: Dispatch<AdditionalParameterAction>,
    additionalProperties: AdditionalProperty[],
    currentParameterName: string | null = null,
  ): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
  (event: React.ChangeEvent<HTMLInputElement>) => {
    const newOrdinal = event.target.value;
    const isInvalid = !validateParameterOrdinal(
      newOrdinal,
      additionalProperties,
      currentParameterName,
    );
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_ORDINAL,
      value: newOrdinal,
      isInvalid,
    });
  };

/**
 * Creates an event handler for changes to the minimum number input, validates the input, and updates the state accordingly.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch method to invoke actions.
 * @return {Function} Event handler function for the minimum number input changes.
 */
export const createNumberMinChangeHandler =
  (
    numberMax: string,
    defaultValue: string,
    dispatch: Dispatch<AdditionalParameterAction>,
  ): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
  (event: React.ChangeEvent<HTMLInputElement>) => {
    const newNumber = event.target.value;
    const isEmptyOrNotNumber = validateParameterNumber(newNumber);
    let checkMinMax = false;
    if (isEmptyOrNotNumber && validateParameterNumber(numberMax))
      checkMinMax = !validateParameterIsMinLessThanMax(newNumber, numberMax);
    const isInvalid = !isEmptyOrNotNumber || checkMinMax;

    let isDefaultValInvalid = false;
    if (
      (validateParameterNumber(newNumber),
      validateParameterNumber(numberMax),
      validateParameterNumber(defaultValue))
    )
      isDefaultValInvalid = !validateParameterWithinMinMaxRange(defaultValue, newNumber, numberMax);

    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
      value: newNumber,
      isInvalid,
    });
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
      value: numberMax,
      isInvalid: newNumber ? isInvalid : false,
    });
    dispatch({
      type: AdditionalParameterActions.SET_DEFAULT_VALUE,
      value: defaultValue,
      isInvalid: isDefaultValInvalid,
    });
  };

/**
 * Creates an event handler for changes to the maximum number input, validates the input, and updates the state accordingly.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch method to invoke actions.
 * @return {Function} Event handler function for the maximum number input changes.
 */
export const createNumberMaxChangeHandler =
  (
    numberMin: string,
    defaultValue: string,
    dispatch: Dispatch<AdditionalParameterAction>,
  ): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
  (event: React.ChangeEvent<HTMLInputElement>) => {
    const newNumber = event.target.value;
    const isEmptyOrNotNumber = validateParameterNumber(newNumber);
    let checkMinMax = false;
    if (isEmptyOrNotNumber && validateParameterNumber(numberMin))
      checkMinMax = !validateParameterIsMinLessThanMax(numberMin, newNumber);
    const isInvalid = !isEmptyOrNotNumber || checkMinMax;

    let isDefaultValInvalid = false;
    if (
      (validateParameterNumber(newNumber),
      validateParameterNumber(numberMin),
      validateParameterNumber(defaultValue))
    )
      isDefaultValInvalid = !validateParameterWithinMinMaxRange(defaultValue, numberMin, newNumber);

    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
      value: newNumber,
      isInvalid,
    });
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
      value: numberMin,
      isInvalid: newNumber ? isInvalid : false,
    });
    dispatch({
      type: AdditionalParameterActions.SET_DEFAULT_VALUE,
      value: defaultValue,
      isInvalid: isDefaultValInvalid,
    });
  };

/**
 * Creates an event handler for selecting unit tags, validates the selection, and updates the state accordingly.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch method to invoke actions.
 * @param {string} selectedItem - The selected unit tag.
 */
export const createUnitTagsChangeHandler = (
  dispatch: Dispatch<AdditionalParameterAction>,
  selectedItem: string,
): void => {
  const isInvalid = !validateEmptySelection(selectedItem);
  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS,
    value: selectedItem,
    isInvalid,
  });

  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
    value: '',
    isInvalid: true,
  });
};

/**
 * Creates an event handler for selecting a base unit, validates the selection, and updates the state accordingly.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch method to invoke actions.
 * @param {string} selectedItem - The selected base unit.
 */
export const createBaseUnitChangeHandler = (
  dispatch: Dispatch<AdditionalParameterAction>,
  selectedItem: string,
): void => {
  const isInvalid = !validateEmptySelection(selectedItem);
  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
    value: selectedItem,
    isInvalid,
  });
};

/**
 * Creates an event handler for toggling the 'required' state of a parameter.
 * @param {boolean} checked - The new state of the 'required' checkbox.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch method to invoke actions.
 */
export const createRequiredHandler = (
  checked: boolean,
  dispatch: Dispatch<AdditionalParameterAction>,
): void => {
  dispatch({
    type: AdditionalParameterActions.SET_IS_REQUIRED,
    value: checked,
  });
};

/**
 * Creates an event handler for default value changes based on the valueType, performing specific validations and updates.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - The dispatch method to invoke actions.
 * @param {string} valueType - The type of value being handled, influencing validation logic.
 * @return {Function} Event handler function for default value changes.
 */
export const createDefaultChangeHandler =
  (
    valueType: string,
    numberMin: string,
    numberMax: string,
    dispatch: Dispatch<AdditionalParameterAction>,
  ): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
  (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    let isInvalid = false;

    if (valueType === ValueTypeEnum.Number || valueType === ValueTypeEnum.NumberWithUnit) {
      // Regex to allow negative numbers, decimals, and incomplete numeric entries like "-0"
      const isValidInput = /^-?\d*\.?\d*$/.test(value);
      let checkMinMax = false;
      if (
        validateParameterNumber(numberMin) &&
        validateParameterNumber(numberMax) &&
        validateParameterNumber(value)
      )
        checkMinMax = !validateParameterWithinMinMaxRange(value, numberMin, numberMax);
      isInvalid = !isValidInput || checkMinMax;

      dispatch({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: value,
        isInvalid,
      });
    } else if (valueType === ValueTypeEnum.Date) {
      const dateValue = value ? new Date(value) : '';

      dispatch({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: dateValue,
        isInvalid,
      });
    } else {
      dispatch({
        type: AdditionalParameterActions.SET_DEFAULT_VALUE,
        value: value,
        isInvalid,
      });
    }
  };

/**
 * Resets extra parameters to their initial state values and dispatches the update actions.
 * @param {Dispatch<Action>} dispatch - The dispatch method used to update the state.
 */
export const resetExtraParameters = (dispatch: (value: Action) => void) => {
  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
    value: additionalParameterInitialState.numberMin,
    isInvalid: additionalParameterInitialState.isNumberMinInvalid,
  });
  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
    value: additionalParameterInitialState.numberMax,
    isInvalid: additionalParameterInitialState.isNumberMaxInvalid,
  });
  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS,
    value: additionalParameterInitialState.unitTags,
    isInvalid: additionalParameterInitialState.isUnitTagsInvalid,
  });
  dispatch({
    type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
    value: additionalParameterInitialState.baseUnit,
    isInvalid: additionalParameterInitialState.isBaseUnitInvalid,
  });
  dispatch({
    type: AdditionalParameterActions.SET_OPTIONS,
    options: additionalParameterInitialState.options,
  });
};

/**
 * Determines the default value based on the specified value type.
 * @param {string} valueType - The type of value that determines what default to return.
 * @return {string | null} The default value appropriate for the given value type.
 */
function getDefaultValueBasedOnType(valueType: string): string | null {
  switch (valueType) {
    case ValueTypeEnum.Date:
    case ValueTypeEnum.Text:
    case ValueTypeEnum.List:
      return '';
    case ValueTypeEnum.Number:
    case ValueTypeEnum.NumberWithUnit:
      return null;
    default:
      return null;
  }
}

/**
 * Handles changes in value type by validating the new type, setting the default value, and resetting extra parameters.
 * @param {Dispatch<Action>} dispatch - Function to dispatch actions.
 * @param {Function} validateValueType - Function to validate the new value type.
 * @param {ValueTypeOption[]} valueTypeOptions - Array of all possible value types.
 * @param {string} selectedItem - The selected item's value type.
 */
export function createValueTypeChangeHandler(
  dispatch: (value: Action) => void,
  validateValueType: (value: string, options: ValueTypeOption[]) => boolean,
  valueTypeOptions: ValueTypeOption[],
  selectedItem: string,
) {
  const isValid = validateValueType(selectedItem, valueTypeOptions);
  const defaultValue = getDefaultValueBasedOnType(selectedItem);

  dispatch({
    type: AdditionalParameterActions.SET_VALUE_TYPE,
    value: selectedItem,
    isInvalid: !isValid,
  });
  dispatch({
    type: AdditionalParameterActions.SET_DEFAULT_VALUE,
    value: defaultValue,
  });
  resetExtraParameters(dispatch);
}

/**
 * Validates all parameters within a form state, determining if the current state is valid.
 * @param {AdditionalParameterState} state - Current state of the form.
 * @param {AdditionalProperty[]} additionalProperties - Array of existing additional properties for validation.
 * @param {ValueTypeOption[]} valueTypeOptions - List of valid value type options.
 * @param {Dispatch<AdditionalParameterAction>} dispatch - Dispatch function for actions.
 * @param {boolean} isEditMode - Indicates if the current mode is edit, affecting validation logic.
 * @return {Object} Object containing a boolean indicating if the form is valid.
 */
export function validateParameters(
  state: AdditionalParameterState,
  additionalProperties: AdditionalProperty[],
  valueTypeOptions: { value: string; text: string }[],
  dispatch: Dispatch<AdditionalParameterAction>,
  isEditMode: boolean,
): {
  isValid: boolean;
} {
  const valueTypeNumberOrNumWithUnit =
    state.valueType === ValueTypeEnum.Number || state.valueType === ValueTypeEnum.NumberWithUnit;
  let defaultValueValidation = true;
  if (
    valueTypeNumberOrNumWithUnit &&
    state.numberMin &&
    state.numberMax &&
    state.defaultValue &&
    typeof state.defaultValue === 'string'
  ) {
    defaultValueValidation = validateParameterWithinMinMaxRange(
      state.defaultValue,
      state.numberMin,
      state.numberMax,
    );
  }

  let isValidMinMax = true;
  if (validateParameterNumber(state.numberMin) && validateParameterNumber(state.numberMax)) {
    isValidMinMax = validateParameterIsMinLessThanMax(state.numberMin, state.numberMax);
  }

  const errors: AdditionalParameterErrors = {
    parameterName: isEditMode
      ? true
      : validateParameterName(state.parameterName, additionalProperties),
    parameterOrdinal: validateParameterOrdinal(
      state.parameterOrdinal,
      additionalProperties,
      isEditMode ? state.parameterName : null,
    ),
    numberMin: validateParameterNumber(state.numberMin) && isValidMinMax,
    numberMax: validateParameterNumber(state.numberMax) && isValidMinMax,
    unitTags: validateEmptySelection(state.unitTags || ''),
    baseUnit: validateEmptySelection(state.baseUnit || ''),
    valueType: validateValueType(state.valueType, valueTypeOptions),
    defaultValue: defaultValueValidation,
  };

  if (!errors.parameterName) {
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NAME,
      value: state.parameterName,
      isInvalid: true,
    });
  }
  if (!errors.parameterOrdinal) {
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_ORDINAL,
      value: state.parameterOrdinal,
      isInvalid: true,
    });
  }
  if (!errors.numberMin && valueTypeNumberOrNumWithUnit) {
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN,
      value: state.numberMin,
      isInvalid: true,
    });
  }
  if (!errors.numberMax && valueTypeNumberOrNumWithUnit) {
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX,
      value: state.numberMax,
      isInvalid: true,
    });
  }
  if (!errors.unitTags && valueTypeNumberOrNumWithUnit) {
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS,
      value: state.unitTags,
      isInvalid: true,
    });
  }
  if (!errors.baseUnit && valueTypeNumberOrNumWithUnit) {
    dispatch({
      type: AdditionalParameterActions.SET_PARAMETER_BASE_UNIT,
      value: state.baseUnit,
      isInvalid: true,
    });
  }
  if (!errors.valueType) {
    dispatch({
      type: AdditionalParameterActions.SET_VALUE_TYPE,
      value: state.valueType,
      isInvalid: true,
    });
  }
  if (!errors.defaultValue) {
    dispatch({
      type: AdditionalParameterActions.SET_DEFAULT_VALUE,
      value: state.defaultValue,
      isInvalid: true,
    });
  }

  const numberType =
    state.valueType === ValueTypeEnum.Number || state.valueType === ValueTypeEnum.NumberWithUnit
      ? errors.numberMin && errors.numberMax
      : true;
  const numberWithUnitType =
    state.valueType === ValueTypeEnum.NumberWithUnit ? errors.unitTags && errors.baseUnit : true;

  return {
    isValid:
      errors.parameterName &&
      errors.parameterOrdinal &&
      errors.valueType &&
      errors.defaultValue &&
      numberType &&
      numberWithUnitType,
  };
}
