import { AdditionalProperty, BaseUnit, Country } from '@common/types';
import {
  AdditionalParameterState,
  MAX_ORDINAL_LENGTH,
  ValueType,
  ValueTypeEnum,
  MAX_INTEGER_LENGTH,
  MAX_FRACTIONAL_LENGTH,
  MAX_TEXT_CHAR_LENGTH,
  CalculationType,
} from '@widgets/Polaris/src/components';
import { TFunction } from 'i18next';
import {
  generateTranslationKey,
  sortArrObjectsAlphabetically,
  toPascalCase,
  truncateString,
} from '@widgets/Polaris/utils';
import { TranslataionKeys } from '@widgets/Polaris/consts';
import { AhdbCountries, ParameterLevelEnum } from '@common/constants';
import { SelectOptions } from '@widgets/Polaris/src/types';

/**
 * Normalizes a given value type string by removing spaces and converting to lowercase,
 * then maps it to a corresponding enum value.
 * @param {string} valueType - The value type string to normalize.
 * @return {string} The normalized value type as defined in ValueTypeEnum.
 */
export function normalizeValueType(valueType: string): string {
  const normalized = valueType.toLowerCase().replace(/\s+/g, '');
  switch (normalized) {
    case 'boolean':
      return ValueTypeEnum.Boolean;
    case 'numberwithunit':
      return ValueTypeEnum.NumberWithUnit;
    case 'list':
      return ValueTypeEnum.List;
    case 'date':
      return ValueTypeEnum.Date;
    case 'number':
      return ValueTypeEnum.Number;
    case 'text':
      return ValueTypeEnum.Text;
    default:
      console.error('Unexpected ValueType received:', valueType);
      return valueType;
  }
}

/**
 * Filters and maps base units by a specific tag to select options.
 * @param {BaseUnit[]} units - Array of base units.
 * @param {string} tag - The tag used to filter the base units.
 * @return {SelectOptions<string>} Select options created from the filtered base units.
 */
export const getBaseUnitOptionsForSelectedTag = (
  units: BaseUnit[],
  tag: string,
): SelectOptions<string> => {
  const unitsWithSelectedTag = units?.filter(({ tags }) => tags.split(',').includes(tag));
  return sortArrObjectsAlphabetically(
    unitsWithSelectedTag.map(({ id, name }) => ({ value: id, text: name })),
    'text',
  );
};

/**
 * Converts an array of unit strings to select options.
 * @param {string[]} units - An array of unit strings.
 * @return {SelectOptions<string>} Select options created from the unit strings.
 */
export const getUnitOptions = (units: string[]): SelectOptions<string> =>
  units.map((unit) => ({ value: unit, text: unit }));

/**
 * Generates options for selecting value types, localized based on the provided translation function.
 * @param {TFunction} t - The i18next translation function to localize the option texts.
 * @param {string} keyPrefix - The prefix used to form the keys for translations.
 * @return {SelectOptions<string>} An array of options for value types, each containing a value and a localized text.
 */
export const getValueTypeOptions = (t: TFunction, keyPrefix: string): SelectOptions<string> => [
  { value: 'number', text: t(`${keyPrefix}.select.number`) },
  { value: 'numberWithUnit', text: t(`${keyPrefix}.select.numberWithUnit`) },
  { value: 'text', text: t(`${keyPrefix}.select.text`) },
  { value: 'list', text: t(`${keyPrefix}.select.list`) },
  { value: 'date', text: t(`${keyPrefix}.select.date`) },
  { value: 'boolean', text: t(`${keyPrefix}.select.boolean`) },
];

/**
 * Generates options for selecting boolean values, localized based on the provided translation function.
 * @param {TFunction} t - The i18next translation function to localize the option texts.
 * @param {string} keyPrefix - The prefix used to form the keys for translations.
 * @return {SelectOptions<string>} An array of options for boolean types, each containing a value and a localized text.
 */
export const getBooleanTypeOptions = (t: TFunction, keyPrefix: string): SelectOptions<string> => [
  { value: 'true', text: t(`${keyPrefix}.booleanSelect.true`) },
  { value: 'false', text: t(`${keyPrefix}.booleanSelect.false`) },
];

/**
 * Truncates a number string to specified maximum integer and fractional lengths, considering negative signs.
 * @param {string} input - The number string to truncate.
 * @return {string|null} The truncated number string, or null if the input is not a valid number.
 */
export const getTruncatedNumber = (input: string): string | null => {
  if (!input || isNaN(Number(input))) {
    return null;
  }

  const isNegative = Math.sign(Number(input)) === -1;
  let [integerPart, fractionalPart = ''] = input.split('.');
  integerPart = truncateString(
    isNegative ? integerPart.substring(1) : integerPart,
    MAX_INTEGER_LENGTH,
  );

  if (fractionalPart) {
    fractionalPart = truncateString(fractionalPart, MAX_FRACTIONAL_LENGTH);
  }

  const result = isNegative ? `-${integerPart}` : integerPart;
  return fractionalPart ? `${result}.${fractionalPart}` : result;
};

/**
 * Formats a default value based on its type to a string suitable for display or storage.
 * @param {string} valueType - The type of the value.
 * @param {ValueType} defaultValue - The value to format.
 * @return {string} The formatted default value.
 */
export function formatDefaultValue(valueType: string, defaultValue: ValueType) {
  switch (valueType) {
    case ValueTypeEnum.Date:
      return defaultValue instanceof Date ? defaultValue.toISOString() : '';
    case ValueTypeEnum.Boolean:
    case ValueTypeEnum.Number:
    case ValueTypeEnum.NumberWithUnit:
      return defaultValue ? defaultValue.toString() : '';
    case ValueTypeEnum.Text:
      return defaultValue
        ? truncateString(convertToString(defaultValue), MAX_TEXT_CHAR_LENGTH)
        : '';
    case ValueTypeEnum.List:
      return defaultValue ? defaultValue : '';
    default:
      return '';
  }
}

/**
 * Creates a base structure for an additional property from given state, handling specific transformations and truncations.
 * @param {AdditionalParameterState} state - The state object containing values to form an additional property.
 * @return {AdditionalProperty} A structured additional property object with appropriate transformations applied to certain fields.
 */
export function createBaseAdditionalProperty(state: AdditionalParameterState): AdditionalProperty {
  const truncatedOrdinal = truncateString(state.parameterOrdinal, MAX_ORDINAL_LENGTH);
  const truncatedMin = getTruncatedNumber(state.numberMin);
  const truncatedMax = getTruncatedNumber(state.numberMax);
  return {
    ApiPropertyName: toPascalCase(state.parameterName),
    BaseUnit: state.baseUnit,
    CalculationType: CalculationType.SOIL, // discussed with PO, CalculationType should be set to SOIL
    DefaultValue: formatDefaultValue(state.valueType, state.defaultValue),
    Min: truncatedMin ? parseFloat(truncatedMin) : null,
    Max: truncatedMax ? parseFloat(truncatedMax) : null,
    Name: state.parameterName,
    Ordinal: parseFloat(truncatedOrdinal),
    Options: state.options,
    Required: state.isRequired,
    TranslationKey: generateTranslationKey(
      TranslataionKeys.ADDITIONAL_PARAMETERS,
      state.parameterName,
    ),
    UnitTags: state.unitTags,
    ValueType: normalizeValueType(state.valueType),
  };
}

/**
 * Creates a new parameter object based on the provided state, leveraging the structure defined by `createBaseAdditionalProperty`.
 * @param {AdditionalParameterState} state - The state object containing values to form a new parameter.
 * @return {AdditionalProperty} A new additional property object.
 */
export function createNewParameter(state: AdditionalParameterState) {
  return createBaseAdditionalProperty(state);
}

/**
 * Updates an existing parameter object with new values from the state, merging with the base properties generated from the state.
 * @param {AdditionalParameterState} state - The current state with updated values.
 * @param {AdditionalProperty | undefined} existingParameter - The existing parameter to update, or undefined if creating new.
 * @return {AdditionalProperty | undefined} The updated parameter object, or undefined if the input was undefined.
 */
export function updateExistingParameter(
  state: AdditionalParameterState,
  existingParameter: AdditionalProperty | undefined,
) {
  const baseProperty = createBaseAdditionalProperty(state);
  return { ...existingParameter, ...baseProperty };
}

/**
 * Converts a given value of various types to a string. Handles `null`, `undefined`, `Date`, boolean, and number types explicitly.
 * @param {ValueType} value - The value to convert to a string.
 * @return {string} The converted string, or an empty string if the value is null or undefined.
 */
export function convertToString(value: ValueType): string {
  if (value === null || value === undefined) {
    return '';
  } else if (value instanceof Date) {
    return value.toISOString().split('T')[0];
  } else if (typeof value === 'boolean' || typeof value === 'number') {
    return value.toString();
  }
  return value;
}

/**
 * Checks if any of the additional properties in a list have empty options.
 * @param {AdditionalProperty[]} additionalProperties - The list of additional properties to check.
 * @return {boolean} True if any properties have empty `Options`, false otherwise.
 */
export const hasEmptyOptions = (additionalProperties: AdditionalProperty[]): boolean => {
  return additionalProperties.some((property) => {
    return property?.Options?.length === 0;
  });
};

/**
 * Compares two additional property objects to determine if there are any differences between them.
 * @param {AdditionalProperty} current - The current state of the property.
 * @param {AdditionalProperty} initial - The initial state of the property to compare against.
 * @return {boolean} True if there are changes between the initial and current states, false otherwise.
 */
export const hasChanges = (current: AdditionalProperty, initial: AdditionalProperty): boolean => {
  const stringEqualIgnoreCase = (s1: string | null, s2: string | null): boolean =>
    (s1?.toLowerCase() || '') === (s2?.toLowerCase() || '');

  const numericEqual = (n1: number | null, n2: number | null): boolean => n1 === n2;

  const optionsEqual = JSON.stringify(current?.Options) === JSON.stringify(initial.Options);

  return !(
    stringEqualIgnoreCase(current?.Name, initial.Name) &&
    current?.Ordinal === initial.Ordinal &&
    stringEqualIgnoreCase(
      normalizeValueType(current?.ValueType),
      normalizeValueType(initial.ValueType),
    ) &&
    current?.DefaultValue === initial.DefaultValue &&
    current?.Required === initial.Required &&
    stringEqualIgnoreCase(current?.BaseUnit, initial.BaseUnit) &&
    numericEqual(current?.Min, initial.Min) &&
    numericEqual(current?.Max, initial.Max) &&
    stringEqualIgnoreCase(current?.UnitTags, initial.UnitTags) &&
    stringEqualIgnoreCase(current?.TranslationKey, initial.TranslationKey) &&
    stringEqualIgnoreCase(current?.ApiPropertyName, initial.ApiPropertyName) &&
    optionsEqual
  );
};

export const isAhdbActive = (selectedCountry: Country): boolean => {
  return selectedCountry && AhdbCountries?.includes(selectedCountry?.countryCode);
};

export const getParameterLevel = (value: string | undefined): ParameterLevelEnum | null => {
  switch (value) {
    case ParameterLevelEnum.LOW:
    case ParameterLevelEnum.VERY_LOW:
    case ParameterLevelEnum.MEDIUM:
    case ParameterLevelEnum.HIGH:
    case ParameterLevelEnum.VERY_HIGH:
      return value;
    default:
      return null;
  }
};
