import {
  AdditionalParameterState,
  AdditionalParameterActions,
  ValueTypeEnum,
  Action,
} from '../../index';
import { normalizeValueType } from './parameterUtils';

export const additionalParameterInitialState: AdditionalParameterState = {
  parameterName: '',
  parameterOrdinal: '',
  valueType: '',
  numberMin: '',
  numberMax: '',
  unitTags: '',
  baseUnit: '',
  isRequired: true,
  isNameInvalid: false,
  isOrdinalInvalid: false,
  isValueTypeInvalid: false,
  isNumberMinInvalid: false,
  isNumberMaxInvalid: false,
  isUnitTagsInvalid: false,
  isBaseUnitInvalid: false,
  isDefaultValueInvalid: false,
  defaultValue: null,
  options: [],
  initialData: null,
};

/**
 * This reducer manages the state of form elements within the AdditionalParametersPopup component.
 * It handles updates to state based on dispatched actions, ensuring that each part of the state
 * is updated correctly in response to user interactions and form validations.
 *
 * Actions:
 * - 'SET_PARAMETER_NAME': Updates the 'parameterName' and its validity.
 * - 'SET_PARAMETER_ORDINAL': Updates the 'parameterOrdinal' and its validity.
 * - 'SET_PARAMETER_NUMBER_MIN': Updates the 'numberMin' and its validity.
 * - 'SET_PARAMETER_NUMBER_MAX': Updates the 'numberMax' and its validity.
 * - 'SET_PARAMETER_UNIT_TAGS': Updates the 'unitTags' and its validity.
 * - 'SET_VALUE_TYPE': Updates the 'valueType' and checks its validity.
 * - 'SET_IS_REQUIRED': Toggles the 'isRequired' checkbox state.
 * - 'SET_INITIAL_DATA': Sets the initial data needed in edit mode
 * - 'RESET_FORM': Resets all form fields to their initial state, typically used when closing the form or after a submit.
 *
 * This structure ensures that each action clearly defines how it modifies the state, providing a predictable
 * and easy-to-understand state management process.
 */
export function additionalParameterReducer(
  state: AdditionalParameterState,
  action: Action,
): AdditionalParameterState {
  const stringValue = 'value' in action && typeof action.value === 'string' ? action.value : '';
  switch (action.type) {
    case AdditionalParameterActions.SET_PARAMETER_NAME:
      return {
        ...state,
        parameterName: stringValue,
        isNameInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_PARAMETER_ORDINAL:
      return {
        ...state,
        parameterOrdinal: stringValue,
        isOrdinalInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_PARAMETER_NUMBER_MIN:
      return {
        ...state,
        numberMin: stringValue,
        isNumberMinInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_PARAMETER_NUMBER_MAX:
      return {
        ...state,
        numberMax: stringValue,
        isNumberMaxInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_PARAMETER_UNIT_TAGS:
      return {
        ...state,
        unitTags: stringValue,
        isUnitTagsInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_PARAMETER_BASE_UNIT:
      return {
        ...state,
        baseUnit: stringValue,
        isBaseUnitInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_VALUE_TYPE:
      return {
        ...state,
        valueType: stringValue,
        isValueTypeInvalid: action.isInvalid || false,
      };
    case AdditionalParameterActions.SET_DEFAULT_VALUE:
      return {
        ...state,
        defaultValue: action.value ?? null,
        isDefaultValueInvalid: action.isInvalid,
      };
    case AdditionalParameterActions.SET_IS_REQUIRED:
      return { ...state, isRequired: typeof action.value === 'boolean' ? action.value : false };
    case AdditionalParameterActions.SET_OPTIONS:
      if ('options' in action) {
        return { ...state, options: action.options };
      }
      return { ...state };

    case AdditionalParameterActions.SET_INITIAL_DATA:
      if ('initialData' in action) {
        const initialDataAction = action;
        const normalizedValueType = normalizeValueType(initialDataAction.initialData.ValueType);
        let defaultValue = initialDataAction.initialData.DefaultValue;
        if (normalizedValueType === ValueTypeEnum.Date) {
          if (defaultValue) {
            const parsedDate = new Date(String(defaultValue));
            if (!isNaN(parsedDate.getTime())) {
              defaultValue = parsedDate;
            } else {
              defaultValue = '';
            }
          } else {
            defaultValue = '';
          }
        }

        const options = initialDataAction?.initialData?.Options?.map((option) => ({
          Name: option.Name,
          Value: option.Value,
          TranslationKey: option.TranslationKey,
        }));

        return {
          ...state,
          parameterName: initialDataAction.initialData.Name,
          parameterOrdinal: initialDataAction.initialData.Ordinal.toString(),
          valueType: normalizedValueType,
          numberMin: initialDataAction.initialData.Min?.toString() || '',
          numberMax: initialDataAction.initialData.Max?.toString() || '',
          unitTags: initialDataAction.initialData.UnitTags,
          baseUnit: initialDataAction.initialData.BaseUnit,
          isRequired: initialDataAction.initialData.Required,
          defaultValue: defaultValue,
          options: options,
          isNameInvalid: false,
          isOrdinalInvalid: false,
          isValueTypeInvalid: false,
          isNumberMinInvalid: false,
          isNumberMaxInvalid: false,
          isUnitTagsInvalid: false,
          isBaseUnitInvalid: false,
          isDefaultValueInvalid: false,
        };
      }
      return { ...state };
    case AdditionalParameterActions.RESET_FORM:
      return { ...additionalParameterInitialState };
    default:
      throw new Error('Unhandled action type');
  }
}
