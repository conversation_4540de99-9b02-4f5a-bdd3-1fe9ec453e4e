import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Card,
  Dialog,
  IconButton,
  Input,
  Label,
  RadioButton,
  Select,
} from '@yaradigitallabs/ahua-react';
import {
  BodyWrapper,
  CloseButtonStyle,
  DescriptionStyle,
  DisabledInputsSection,
  FirstSectionWrapper,
  HeaderWrapper,
  InputStyle,
  PopupWrapperStyle,
  RadioButtonGroupStyled,
  RadioButtonWrapper,
  SecondSectionWrapper,
  SelectStyle,
  TailWrapper,
} from './ParameterEditPopup.styled';
import { CalculationParameter, CropSequence } from '@common/types';
import {
  useFetchAllCropSequence,
  filterUnitsByTag,
  useGetCropDescriptions,
  useGetCropSubClasses,
  useResetBodyPointerEvents,
} from '@polaris-hooks/index';
import { SelectWrapper } from '../Select/index';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  UNIT_TAGS,
  CALCULATION_PARAM_TYPE,
  NONE_OPTION_VALUE,
  NONE_VALUE_UUID,
  PARAMETERS_WITH_DROPDOWN,
  VALUE_TYPE,
} from '@common/constants';
import { sanitizeParameterValue } from '../../../utils/parameterUtils/parameterUtils';

import { capitalizeFirstLetter } from '@widgets/Polaris/utils/stringUtils/stringUtils';
import { cropDropdownValues } from '../../screens/Home';
import { DisplayOption, ParameterEditPopupProps } from './ParameterEditPopup.type';
import { useAppContext } from '../../providers';
import { FilterType, GenericFilter } from '@widgets/Polaris/src/types';
import {
  getCropSequencesByType,
  getFormattedOptions,
  getInitialEditedValue,
} from '../../screens/CropFeatures/CNP/Parameters/CalculationParameters/utils/calculationParametersUtils';

export const ParameterEditPopup: FC<ParameterEditPopupProps> = ({
  isOpen,
  onOpenChange,
  cropRegion,
  onSave,
  parameterData,
  translatedParameterName,
  cropResidueManagements,
  soilTypes,
  soilTypeCountries,
}) => {
  useResetBodyPointerEvents(isOpen);
  const { selectedCountryUnits } = useAppContext();
  const { t } = useTranslation('polaris');
  const keyPrefix = 'polaris.cnpDetails.parameters.parameterEditPopup';
  const title = t(`${keyPrefix}.title`, {
    calcParameterName: `${translatedParameterName.toLowerCase()}`,
  });
  const { PRE_CROP_TYPE_ID, POST_CROP_TYPE_ID } = CALCULATION_PARAM_TYPE;
  const isDefaultValueNone = parameterData.defaultValue === 0;
  const isDefaultUnitIdNone = parameterData.defaultUnitId === NONE_VALUE_UUID;
  const requiresUnitDropdown = PARAMETERS_WITH_DROPDOWN.includes(parameterData.name);
  const VALUE_NONE_OPTION = {
    value: NONE_OPTION_VALUE,
    text: t('common.none'),
  };
  const radioOptions = [
    { value: 'yes', label: t('polaris.common.yes') },
    { value: 'no', label: t('polaris.common.no') },
  ];
  const [editedValue, setEditedValue] = useState<string>(getInitialEditedValue(parameterData));
  const [isValueNumeric, setIsValueNumeric] = useState(true);
  const [isDefaultValueInvalid, setIsDefaultValueInvalid] = useState(false);
  const [selectedUnitId, setSelectedUnitId] = useState<string>(
    isDefaultValueNone || isDefaultUnitIdNone
      ? NONE_OPTION_VALUE
      : parameterData.defaultUnitId || NONE_OPTION_VALUE,
  );
  const [displayValue, setDisplayValue] = useState(
    parameterData.shouldDisplay ? DisplayOption.Yes : DisplayOption.No,
  );
  const { setDisplaySnackbar } = useSnackbar();
  const filteredUnits = filterUnitsByTag(selectedCountryUnits, UNIT_TAGS.YIELD_UNIT);

  const { cropSequences = [] } = useFetchAllCropSequence(cropRegion);

  // filter by isPreCrop or isPostCrop entity to get the crop-sequence data
  const cropItems = useMemo(() => {
    const sequences: CropSequence[] | undefined = getCropSequencesByType(
      cropSequences,
      parameterData?.name,
    );

    return sequences || [];
  }, [parameterData?.name, cropSequences]);

  const fetchCropDescriptionsFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!cropItems?.length) return;
    const cropDescriptionIds = cropItems.map((region) => region.cropDescriptionId);
    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: cropDescriptionIds.join(','),
      },
    ];
  }, [cropItems]);

  const { cropDescriptions = [] } = useGetCropDescriptions(fetchCropDescriptionsFilters || []);

  const fetchCropSubclassesFilters: GenericFilter[] | undefined = useMemo(() => {
    if (!cropDescriptions?.length) return;
    const cropSubClassIds = cropDescriptions.map((region) => region.cropSubClassId);
    return [
      {
        key: 'id',
        type: FilterType.IN,
        value: cropSubClassIds.join(','),
      },
    ];
  }, [cropDescriptions]);

  const { cropSubClasses = [] } = useGetCropSubClasses(fetchCropSubclassesFilters);

  const prePostCropItems = useMemo(() => {
    if (!cropSubClasses && !cropDescriptions) return [VALUE_NONE_OPTION];

    const prePostCropOptions = [VALUE_NONE_OPTION];
    prePostCropOptions.push(...cropDropdownValues(cropSubClasses, cropDescriptions));
    return prePostCropOptions || [];
  }, [cropSequences, cropSubClasses, cropDescriptions, cropSubClasses, cropDescriptions]);

  const cropResidueMOptions = useMemo(() => {
    if (!cropResidueManagements?.length) return [VALUE_NONE_OPTION];
    return getFormattedOptions(cropResidueManagements, VALUE_NONE_OPTION);
  }, [cropResidueManagements]);

  const soilTypeOptions = useMemo(() => {
    if (!soilTypeCountries?.length && !soilTypes?.length) return [VALUE_NONE_OPTION];
    return (
      soilTypeCountries &&
      soilTypes &&
      getFormattedOptions(soilTypeCountries, VALUE_NONE_OPTION, soilTypes)
    );
  }, [soilTypeCountries, soilTypes]);

  const unitSelectItems = useMemo(() => {
    if (!filteredUnits?.length) return [VALUE_NONE_OPTION];
    return getFormattedOptions(filteredUnits, VALUE_NONE_OPTION);
  }, [filteredUnits]);

  // filter calculation parameters data by name for selected.
  const getParametersByName = (name: string) => {
    switch (name) {
      case CALCULATION_PARAM_TYPE.YIELD:
      case CALCULATION_PARAM_TYPE.PRE_CROP_YIELD:
      case CALCULATION_PARAM_TYPE.POST_CROP_YIELD:
        return unitSelectItems;
      case CALCULATION_PARAM_TYPE.PRE_CROP_TYPE_ID:
        return prePostCropItems;
      case CALCULATION_PARAM_TYPE.POST_CROP_TYPE_ID:
        return prePostCropItems;
      case CALCULATION_PARAM_TYPE.PRE_CROP_RESIDUE_M_ID:
      case CALCULATION_PARAM_TYPE.POST_CROP_RESIDUE_M_ID:
      case CALCULATION_PARAM_TYPE.CROP_RESIDUE_M_ID:
        return cropResidueMOptions;
      case CALCULATION_PARAM_TYPE.SOIL_TYPE_ID:
        return soilTypeOptions;
      default:
        return [];
    }
  };
  const displaySnackbarMessage = (): void => {
    setDisplaySnackbar({
      title: t(`${keyPrefix}.snackbarMessage`),
      colorConcept: 'successLight',
      icon: 'Check',
      placement: 'bottomRight',
      duration: 3000,
      open: true,
    });
  };

  const handleSave = (): void => {
    // Trim the input to remove leading/trailing whitespace
    let valueToSave = editedValue.trim();
    const isNotPreviouslyAppliedProducts =
      parameterData?.name !== CALCULATION_PARAM_TYPE.PREV_APPLIED_PRODUCTS;

    // Ensure the value is not empty and is numeric before saving
    if ((!valueToSave || !isValueNumeric) && isNotPreviouslyAppliedProducts) {
      if (!valueToSave) {
        setIsDefaultValueInvalid(true);
      }
      return;
    }
    setIsDefaultValueInvalid(false);

    // Remove a trailing dot from the numeric value, if present, to sanitize it before saving
    if (valueToSave.endsWith('.')) {
      valueToSave = valueToSave.slice(0, -1);
    }
    const sanitizedValue = sanitizeParameterValue(parameterData.type, valueToSave);
    const updatedParameter: CalculationParameter = {
      ...parameterData,
      defaultValue: sanitizedValue,
      defaultUnitId: selectedUnitId === NONE_OPTION_VALUE ? NONE_VALUE_UUID : selectedUnitId,
      shouldDisplay: displayValue === DisplayOption.Yes,
    };

    const changesMade =
      updatedParameter.defaultValue !== parameterData.defaultValue ||
      updatedParameter.defaultUnitId !== parameterData.defaultUnitId ||
      updatedParameter.shouldDisplay !== parameterData.shouldDisplay;

    if (changesMade) {
      onSave(updatedParameter);
    }
    onOpenChange(false);
    if (changesMade) {
      displaySnackbarMessage();
    }
  };

  const handleValueChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = event.target.value;
    setEditedValue(value);

    // This regex checks for a valid number that starts with an optional digit sequence,
    // optionally followed by a dot, but only if it's not a lone dot and if the dot is followed by digits or there were digits before the dot.
    const isNumeric = value === '' || /^(\d+(\.\d*)?|\.\d+)$/.test(value);
    setIsValueNumeric(isNumeric);

    // Reset the invalid state if the user corrects the value
    if (value.trim()) {
      setIsDefaultValueInvalid(false);
    }
  };

  const handleUnitSelect = (selectedValue: string): void => {
    setSelectedUnitId(selectedValue);
  };

  const resetPopupState = useCallback((): void => {
    setEditedValue(getInitialEditedValue(parameterData));

    const unitSelection = isDefaultUnitIdNone
      ? NONE_OPTION_VALUE
      : parameterData.defaultUnitId || NONE_OPTION_VALUE;
    setSelectedUnitId(unitSelection);
    setDisplayValue(parameterData.shouldDisplay ? DisplayOption.Yes : DisplayOption.No);
    setIsDefaultValueInvalid(false);
    setIsValueNumeric(true);
  }, [parameterData, isDefaultValueNone, isDefaultUnitIdNone, NONE_OPTION_VALUE]);

  useEffect(() => {
    if (isOpen) {
      resetPopupState();
    }
  }, [isOpen, resetPopupState]);

  if (!isOpen) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Content css={PopupWrapperStyle} data-cy='parameter-edit-popup'>
        <HeaderWrapper>
          <Dialog.Title data-cy='parameter-edit-popup-title'>{title}</Dialog.Title>
          <Dialog.Close>
            <IconButton
              css={CloseButtonStyle}
              data-cy='parameter-edit-popup-close-btn'
              icon='Close'
              colorConcept='brand'
              size='xs'
              onClick={() => onOpenChange(false)}
            />
          </Dialog.Close>
        </HeaderWrapper>
        <Card.Divider />
        <BodyWrapper>
          <DisabledInputsSection>
            <Input
              size='n'
              type='text'
              value={translatedParameterName}
              disabled={true}
              label={t(`${keyPrefix}.parameterName`)}
              data-cy='parameter-edit-popup-parameter-name'
              cover='fill'
            />
            <Input
              size='n'
              type='text'
              value={capitalizeFirstLetter(parameterData.type)}
              disabled={true}
              label={t(`${keyPrefix}.parameterType`)}
              data-cy='parameter-edit-popup-parameter-type'
              cover='fill'
            />
          </DisabledInputsSection>
          {(parameterData?.type === VALUE_TYPE.ID ||
            parameterData?.type === VALUE_TYPE.NUMBER ||
            requiresUnitDropdown) && (
            <FirstSectionWrapper>
              {parameterData?.type === VALUE_TYPE.ID && (
                <SelectWrapper dataCy={`parameter-edit-popup-${parameterData?.name}-select`}>
                  <Select
                    className='parameter-edit-popup-select'
                    ariaLabel={`Select ${parameterData?.name}`}
                    cover='outline'
                    items={getParametersByName(parameterData.name) || []}
                    position='popper'
                    size='n'
                    label={t(`${keyPrefix}.defaultValue`)}
                    onChange={(value: string) => setEditedValue(value)}
                    value={editedValue || ''}
                    helper-text={
                      parameterData?.name === PRE_CROP_TYPE_ID ||
                      parameterData?.name === POST_CROP_TYPE_ID
                        ? t(`${keyPrefix}.helperText`, {
                            cropType: translatedParameterName?.split(' ')[0]?.toLowerCase(),
                          })
                        : ''
                    }
                  />
                </SelectWrapper>
              )}
              {parameterData?.type === VALUE_TYPE.NUMBER && (
                <Input
                  css={InputStyle}
                  size='n'
                  type='text'
                  value={editedValue}
                  onChange={handleValueChange}
                  variant={isDefaultValueInvalid || !isValueNumeric ? 'error' : 'default'}
                  helperText={
                    isDefaultValueInvalid
                      ? t(`polaris.error.isRequired`, {
                          name: 'Default value',
                        })
                      : !isValueNumeric
                      ? t(`polaris.error.mustBeNumber`)
                      : undefined
                  }
                  label={editedValue !== null ? t(`${keyPrefix}.defaultValue`) : undefined}
                  data-cy='parameter-edit-popup-defaultValue'
                />
              )}
              {requiresUnitDropdown && (
                <SelectWrapper dataCy='parameter-edit-popup-yieldUnit-select'>
                  <Select
                    css={SelectStyle}
                    ariaLabel='Select unit'
                    cover='outline'
                    items={getParametersByName(parameterData.name) || []}
                    position='popper'
                    size='n'
                    label={t(`${keyPrefix}.yieldUnit`)}
                    onChange={handleUnitSelect}
                    value={selectedUnitId || ''}
                  />
                </SelectWrapper>
              )}
            </FirstSectionWrapper>
          )}
          <SecondSectionWrapper>
            <Dialog.Description data-cy='parameter-edit-popup-description' style={DescriptionStyle}>
              {t(`${keyPrefix}.description`)}
            </Dialog.Description>
            <RadioButtonGroupStyled
              value={displayValue}
              onValueChange={setDisplayValue}
              aria-label='Display Option'
              data-cy='parameter-edit-radio-btn-group'
            >
              {radioOptions.map((option, index) => (
                <RadioButtonWrapper key={option.value}>
                  <Label htmlFor={`displayOption${index}`}>{option.label}</Label>
                  <RadioButton
                    value={option.value}
                    id={`displayOption${index}`}
                    data-cy={`parameter-edit-radio-btn-${option.label.toLowerCase()}`}
                  />
                </RadioButtonWrapper>
              ))}
            </RadioButtonGroupStyled>
          </SecondSectionWrapper>
        </BodyWrapper>
        <Card.Divider />
        <TailWrapper>
          <Button size='s' onClick={handleSave} data-cy='parameter-edit-popup-save-btn'>
            {t(`polaris.common.saveChanges`)}
          </Button>
        </TailWrapper>
      </Dialog.Content>
    </Dialog>
  );
};
