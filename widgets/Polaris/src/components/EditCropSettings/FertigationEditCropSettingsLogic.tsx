import { useEffect, useMemo, useState } from 'react';
import './styles.scss';
import { useUpdateFertigationCropSettings } from '@polaris-hooks/index';
import { BaseUnit, CropRegion, Feature, FertigationCropSettingsConfigData } from '@common/types';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { TFunction } from 'react-i18next';
import { METHOD } from '@common/constants';
import EditCropSettingsHelpers from './EditCropSettingsHelpers';
import { isEqual } from 'lodash';
import { useAppContext } from '../../providers';
import { FertigationSettingsFieldKeys } from '../../screens/CropFeatures/Fertigation/Details/CropSettings.types';

interface EditCropSettingsLogicProps {
  isEditCropSettingOpened: boolean;
  setIsEditCropSettingOpened: React.Dispatch<boolean>;
  setShouldShowUnitChangeImpactMessage: React.Dispatch<boolean>;
  selectedCropRegion: CropRegion | null | undefined;
  fertigationYieldUnitsData: BaseUnit[] | undefined;
  recommendedSolidUnitsPerAreaData: BaseUnit[] | undefined;
  recommendedLiquidsUnitsPerAreaData: BaseUnit[] | undefined;
  recommendedSolidUnitsPerPlantData: BaseUnit[] | undefined;
  recommendedLiquidsUnitsPerPlantData: BaseUnit[] | undefined;
  nutrientRemovalUnitsData: BaseUnit[] | undefined;
  nutrientDemandUnitsData: BaseUnit[] | undefined;
  totalIrrigationWaterUnitsData: BaseUnit[] | undefined;
  plantDensityUnitsData: BaseUnit[] | undefined;
  selectedFeature: Feature | null;
  t: TFunction;
}

const FertigationEditCropSettingsLogic = ({
  isEditCropSettingOpened,
  setIsEditCropSettingOpened,
  setShouldShowUnitChangeImpactMessage,
  fertigationYieldUnitsData,
  recommendedSolidUnitsPerAreaData,
  recommendedLiquidsUnitsPerAreaData,
  recommendedSolidUnitsPerPlantData,
  recommendedLiquidsUnitsPerPlantData,
  nutrientRemovalUnitsData,
  nutrientDemandUnitsData,
  totalIrrigationWaterUnitsData,
  plantDensityUnitsData,
  selectedCropRegion,
  selectedFeature,
  t,
}: EditCropSettingsLogicProps) => {
  const { prepareDropdownUnitItems } = EditCropSettingsHelpers({
    selectedCropRegion,
    selectedFeature,
  });
  // ------------ States ------------ //

  const {
    selectedFeatureUnitSettings,
    methods: { setSelectedFeatureUnitSettings },
  } = useAppContext();
  const { setDisplaySnackbar } = useSnackbar();
  const [unitSettingsCopy, setUnitSettingsCopy] = useState<FertigationCropSettingsConfigData>();
  const shouldDisableSaveFertigationChanges = useMemo(() => {
    const hasAllSelections = Object.entries(unitSettingsCopy || {})
      .filter(([key]) => key !== FertigationSettingsFieldKeys.DefaultTargetYield) // Optional field that should not be considered
      .every(([_, value]) => value);
    return (
      !hasAllSelections ||
      isEqual(selectedFeatureUnitSettings?.configuration.data, unitSettingsCopy)
    );
  }, [unitSettingsCopy]);

  // ------------ Unit lists preparation ------------ //

  const yieldUnitOptions = useMemo(() => {
    return prepareDropdownUnitItems(fertigationYieldUnitsData);
  }, [fertigationYieldUnitsData]);

  const recommendedLiquidsPerAreaUnitOptions = useMemo(() => {
    return prepareDropdownUnitItems(recommendedLiquidsUnitsPerAreaData);
  }, [recommendedLiquidsUnitsPerAreaData]);

  const recommendedSolidPerPlantUnitOptions = useMemo(() => {
    return prepareDropdownUnitItems(recommendedSolidUnitsPerPlantData);
  }, [recommendedSolidUnitsPerPlantData]);

  const recommendedLiquidsPerPlantUnitOptions = useMemo(() => {
    return prepareDropdownUnitItems(recommendedLiquidsUnitsPerPlantData);
  }, [recommendedLiquidsUnitsPerPlantData]);

  const totalIrrigationWaterUnitOptions = useMemo(() => {
    return prepareDropdownUnitItems(totalIrrigationWaterUnitsData);
  }, [totalIrrigationWaterUnitsData]);

  const plantDensityUnitOptions = useMemo(() => {
    return prepareDropdownUnitItems(plantDensityUnitsData);
  }, [plantDensityUnitsData]);

  // Find the potential nutrient removal option candidates, based on the selected yield unit
  const nutrientRemovalUnitTempOptions = useMemo(() => {
    if (nutrientRemovalUnitsData && unitSettingsCopy?.yieldUnitId && yieldUnitOptions) {
      const yieldUnitDetails = yieldUnitOptions?.find(
        (el) => el.value === unitSettingsCopy.yieldUnitId,
      );
      const nutrientRemovalUnitSecondPart = yieldUnitDetails?.text.split('/')[0].toLowerCase();

      return nutrientRemovalUnitsData?.filter((el) =>
        el.name.toLowerCase().endsWith(`/${nutrientRemovalUnitSecondPart}`),
      );
    }
  }, [unitSettingsCopy, nutrientRemovalUnitsData, yieldUnitOptions]);

  // Prepare the available options for recommended solids per area based on the first part of the potential nutrient removal candidates
  const recommendedSolidPerAreaUnitOptions = useMemo(() => {
    if (nutrientRemovalUnitTempOptions && recommendedSolidUnitsPerAreaData) {
      const nutrientRemovalUnitFirstPartOptions = nutrientRemovalUnitTempOptions.reduce(
        (items, item) => {
          items.add(item.name.split('/')[0].toLowerCase());
          return items;
        },
        new Set(),
      );

      const availableOptions = recommendedSolidUnitsPerAreaData?.filter((el) =>
        Array.from(nutrientRemovalUnitFirstPartOptions).some((opt) =>
          el.name.toLowerCase().startsWith(`${opt}/`),
        ),
      );
      return prepareDropdownUnitItems(availableOptions);
    }

    return [];
  }, [nutrientRemovalUnitTempOptions, recommendedSolidUnitsPerAreaData]);

  // Prepare the final list of available nutrient removal options by filtering the potential candidates with the selected recommended solids by area unit
  const nutrientRemovalUnitOptions = useMemo(() => {
    if (unitSettingsCopy?.recommendedSolidPerAreaUnitId && nutrientRemovalUnitTempOptions) {
      const recommendedSolidsPerAreaUnitDetails = recommendedSolidPerAreaUnitOptions?.find(
        (el) => el.value === unitSettingsCopy.recommendedSolidPerAreaUnitId,
      );
      const recommendedSolidsPerAreUnitFirstPart = recommendedSolidsPerAreaUnitDetails?.text
        .split('/')[0]
        .toLowerCase();

      const availableOptions = nutrientRemovalUnitTempOptions.filter((el) =>
        el.name.toLowerCase().startsWith(`${recommendedSolidsPerAreUnitFirstPart}/`),
      );
      return prepareDropdownUnitItems(availableOptions);
    }

    return [];
  }, [unitSettingsCopy, recommendedSolidPerAreaUnitOptions, nutrientRemovalUnitTempOptions]);

  // Prepare the final list of available nutrient demand option by finding the match
  const nutrientDemandUnitOptions = useMemo(() => {
    if (
      unitSettingsCopy?.recommendedSolidPerAreaUnitId &&
      recommendedSolidPerAreaUnitOptions &&
      nutrientDemandUnitsData
    ) {
      const yieldUnitDetails = yieldUnitOptions?.find(
        (el) => el.value === unitSettingsCopy.yieldUnitId,
      );
      const recommendedSolidsPerAreaUnitDetails = recommendedSolidPerAreaUnitOptions?.find(
        (el) => el.value === unitSettingsCopy.recommendedSolidPerAreaUnitId,
      );
      const recommendedSolidsPerAreUnitFirstPart = recommendedSolidsPerAreaUnitDetails?.text
        .split('/')[0]
        .toLowerCase();
      const yieldUnitSecondPart = yieldUnitDetails?.text.split('/')[1].toLowerCase();

      const availableOptions = nutrientDemandUnitsData.find(
        (el) =>
          el.name.toLowerCase() ===
          `${recommendedSolidsPerAreUnitFirstPart}/${yieldUnitSecondPart}`,
      );
      return availableOptions ? prepareDropdownUnitItems([availableOptions]) : [];
    }

    return [];
  }, [unitSettingsCopy, recommendedSolidPerAreaUnitOptions, nutrientDemandUnitsData]);

  // ------------ onChange ------------ //

  const onSettingsChange = (key: string, value: string | number | null) => {
    if (unitSettingsCopy) {
      const unitSettingsUpdated = { ...unitSettingsCopy, [key]: value };

      // Correct dependent fields based on the prerequisite
      if (key === FertigationSettingsFieldKeys.YieldUnitId) {
        unitSettingsUpdated.nutrientRemovalUnitId = null;
        unitSettingsUpdated.recommendedSolidPerAreaUnitId = null;
        unitSettingsUpdated.nutrientDemandUnitId = null;
      }

      if (key === FertigationSettingsFieldKeys.RecSolidPerAreaUnitId) {
        unitSettingsUpdated.nutrientRemovalUnitId = null;
        unitSettingsUpdated.nutrientDemandUnitId = null;
      }

      setUnitSettingsCopy(unitSettingsUpdated);
    }
  };

  // ------------ onSaveChanges ------------ //

  const { trigger: updateSettings } = useUpdateFertigationCropSettings(
    selectedFeatureUnitSettings?.id,
  );

  const fertigationCropSettingUpdateHandler = async () => {
    setIsEditCropSettingOpened(false);
    setShouldShowUnitChangeImpactMessage(false);
    if (shouldDisableSaveFertigationChanges) {
      return;
    }

    try {
      const response = await updateSettings({
        method: METHOD.PUT,
        body: JSON.stringify({ configuration: unitSettingsCopy }),
      });

      if (response && selectedFeatureUnitSettings) {
        const updatedUnitSettings = {
          ...selectedFeatureUnitSettings,
          configuration: { data: response },
        };
        setSelectedFeatureUnitSettings(updatedUnitSettings);
        setDisplaySnackbar({
          title: t('common.changesSaved'),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 5000,
          open: true,
        });
      }
    } catch (error) {
      console.error('Error updating cropRegion data:', error);
    }
  };

  const isFirstTimeUnitsUpdate = () => {
    return Object.entries(selectedFeatureUnitSettings?.configuration.data || {})
      .filter(([key]) => key !== FertigationSettingsFieldKeys.DefaultTargetYield) // Optional field that should not be considered
      .every(([_, value]) => !value);
  };

  useEffect(() => {
    if (selectedFeatureUnitSettings && isEditCropSettingOpened) {
      setUnitSettingsCopy(selectedFeatureUnitSettings.configuration.data);
    }
  }, [selectedFeatureUnitSettings, isEditCropSettingOpened]);

  return {
    fertigationDropdownOptions: {
      recommendedSolidPerAreaUnitOptions,
      recommendedLiquidsPerAreaUnitOptions,
      recommendedSolidPerPlantUnitOptions,
      recommendedLiquidsPerPlantUnitOptions,
      nutrientRemovalUnitOptions,
      yieldUnitOptions,
      nutrientDemandUnitOptions,
      totalIrrigationWaterUnitOptions,
      plantDensityUnitOptions,
    },
    unitSettingsCopy,
    shouldDisableSaveFertigationChanges,
    onSettingsChange,
    fertigationCropSettingUpdateHandler,
    isFirstTimeUnitsUpdate,
  };
};

export default FertigationEditCropSettingsLogic;
