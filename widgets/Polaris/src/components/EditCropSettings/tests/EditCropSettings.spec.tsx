/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import { DefaultEditCropSettings, EditCropSettings, FertigationCropSettings } from '../index';
import {
  countryResponse,
  cropDescriptionResponse,
  cropRegionsResponse,
  cropSubclassResponse,
  regionResponse,
} from '../../../screens/Home/mock-data/MockData';
import {
  canModifyGrowthScaleHandler,
  growthScalesHandler,
  updateCropRegionHandler,
  yieldSolidUnitsHandler,
  canModifyCropRegionGrowthScaleHandler,
  featureResponse,
  FertigationNutrientRemovalUnitResponse,
  FertigationRecommendedLiquidsPerAreaUnitResponse,
  FertigationRecommendedLiquidsPerPlantUnitResponse,
  FertigationRecommendedSolidsPerAreaUnitResponse,
  FertigationRecommendedSolidsPerPlantUnitResponse,
  FertigationTotalIrrigationWaterUnitResponse,
  fertigationUnitSettingsResponse,
  FertigationNutrientDemandUnitResponse,
  FertigationYieldUnitResponse,
  growthScalesResponse,
  yieldSolidUnitsResponse,
  mockCMMMAppProviderValue,
  mockFertigationAppProviderValue,
  snackbarInitialStateMock,
  FertigationPlantDensityUnitResponse,
} from '@common/mocks';
import * as useCropSettingsModule from '@polaris-hooks/masterMindMap/useCropSettings/useCropSettings';

import {
  findOne,
  isCMMMPopupFieldNotTouched,
  isCNPPopupFieldNotTouched,
} from '../../../screens/helper';
import { setupServer } from 'msw/node';

import { HttpResponse, http } from 'msw';
import { act } from '@testing-library/react';
import { BaseUnit, GrowthScale, Nullable, CropRegion } from '@common/types';

import { SnackbarContext } from '@libs/snackbar-context/snackbar-context';
import userEvent from '@testing-library/user-event';
import { POLARIS_API_GW_ENDPOINTS } from '@common/constants';
import AppContext from '@widgets/Polaris/src/providers/AppProvider';
import { TooltipWrapperProps } from '../../TooltipWrapper/TooltipWrapperProps.type';

const server = setupServer(
  yieldSolidUnitsHandler,
  growthScalesHandler,
  updateCropRegionHandler,
  canModifyGrowthScaleHandler,
  canModifyCropRegionGrowthScaleHandler,
);

beforeAll(() => server.listen());
afterAll(() => server.close());
afterEach(() => server.resetHandlers());

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Trans: ({ children }: any) => children,
}));

jest.mock('../../TooltipWrapper', () => ({
  TooltipWrapper: (props: TooltipWrapperProps) => <>{props.children}</>,
}));

jest.mock('@polaris-hooks/masterMindMap/useCropSettings/useCropSettings', () => ({
  useUpdateFertigationCropSettings: jest.fn(() => ({
    trigger: jest.fn(),
  })),
}));

describe('widget: CropEditPopup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  type IsPopupFieldNotTouched = {
    cropRegion: CropRegion;
    yieldUnit: BaseUnit;
    unitSolid: BaseUnit;
    growthScale: GrowthScale;
  };

  type IsCMMMPopupFieldNotTouched = IsPopupFieldNotTouched & {
    defaultYield: Nullable<number>;
  };

  const cropSettingProps = {
    dataPrefix: 'cnp',
    selectedFeature: featureResponse[0],
    selectedCropRegion: cropRegionsResponse[0],
    selectedCrop: cropSubclassResponse[0],
    selectedCountry: countryResponse[0],
    selectedRegion: regionResponse[0],
    setCropRegion: jest.fn(),
    selectedCropDescription: cropDescriptionResponse[0],
    setIsEditCropSettingOpened: jest.fn(),
    yieldUnitsData: yieldSolidUnitsResponse,
    growthScalesData: growthScalesResponse.data,
  } as unknown as DefaultEditCropSettings;

  const fpCropSettingProps = {
    dataPrefix: 'fertigation-plan',
    selectedFeature: featureResponse[1],
    selectedCropRegion: cropRegionsResponse[0],
    selectedCrop: cropSubclassResponse[0],
    selectedCountry: countryResponse[0],
    selectedRegion: regionResponse[0],
    setCropRegion: jest.fn(),
    selectedCropDescription: cropDescriptionResponse[0],
    setIsEditCropSettingOpened: jest.fn(),
    fertigationYieldUnitsData: FertigationYieldUnitResponse,
    recommendedSolidUnitsPerAreaData: FertigationRecommendedSolidsPerAreaUnitResponse,
    recommendedLiquidsUnitsPerAreaData: FertigationRecommendedLiquidsPerAreaUnitResponse,
    nutrientRemovalUnitsData: FertigationNutrientRemovalUnitResponse,
    recommendedSolidUnitsPerPlantData: FertigationRecommendedSolidsPerPlantUnitResponse,
    recommendedLiquidsUnitsPerPlantData: FertigationRecommendedLiquidsPerPlantUnitResponse,
    nutrientDemandUnitsData: FertigationNutrientDemandUnitResponse,
  } as unknown as FertigationCropSettings;

  const cmmmCropSettingProps = {
    dataPrefix: 'cmmm',
    selectedFeature: featureResponse[2],
    selectedCropRegion: cropRegionsResponse[0],
    selectedCrop: cropSubclassResponse[0],
    selectedCountry: countryResponse[0],
    selectedRegion: regionResponse[0],
    setCropRegion: jest.fn(),
    selectedCropDescription: cropDescriptionResponse[0],
    setIsEditCropSettingOpened: jest.fn(),
    yieldUnitsData: yieldSolidUnitsResponse,
    growthScalesData: growthScalesResponse.data,
  } as unknown as DefaultEditCropSettings;

  const mockSetDisplaySnackbar = jest.fn();
  const preSelectedFertigationSettings = {
    ...fertigationUnitSettingsResponse,
    configuration: {
      data: {
        ...fertigationUnitSettingsResponse.configuration.data,
        yieldUnitId: FertigationYieldUnitResponse[0].id,
        defaultTargetYield: 1,
        nutrientRemovalUnitId: FertigationNutrientRemovalUnitResponse[0].id,
        recommendedSolidPerAreaUnitId: FertigationRecommendedSolidsPerAreaUnitResponse[0].id,
        recommendedLiquidPerAreaUnitId: FertigationRecommendedLiquidsPerAreaUnitResponse[0].id,
        recommendedSolidPerPlantUnitId: FertigationRecommendedSolidsPerPlantUnitResponse[0].id,
        recommendedLiquidPerPlantUnitId: FertigationRecommendedLiquidsPerPlantUnitResponse[0].id,
        nutrientDemandUnitId: FertigationNutrientDemandUnitResponse[0].id,
        totalIrrigationWaterUnitId: FertigationTotalIrrigationWaterUnitResponse[0].id,
        plantDensityUnitsData: FertigationPlantDensityUnitResponse[0].id,
      },
    },
  };

  const { t } = useTranslation('polaris');
  it('EditCropSettings: should not render when isEditCropSettingOpened is false', () => {
    const props = { ...cropSettingProps, isEditCropSettingOpened: false };
    render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    expect(screen.queryByTestId('cnp-edit-popup-container')).not.toBeInTheDocument();
  });
  test('EditCropSettings: should render when isEditCropSettingOpened is true', async () => {
    const props = { ...cropSettingProps, isEditCropSettingOpened: true };
    render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    expect(screen.getByTestId('cnp-edit-popup-container')).toBeInTheDocument();
    expect(screen.getByTestId('cnp-edit-popup-location')).toBeInTheDocument();
    expect(screen.getByTestId('cnp-edit-popup-cropname')).toBeInTheDocument();
    expect(screen.getByTestId('cnp-edit-popup-close-btn')).toBeInTheDocument();
  });

  test('EditCropSettings: isCNPPopupFieldNotTouched should return true', () => {
    const popupFieldProps = {
      cropRegion: cropSettingProps.selectedCropRegion,
      yieldUnit: { id: '394e8163-636e-48dd-8ded-a9fd6436d335' },
      unitSolid: { id: '53f8ea06-3244-4a0f-ae83-6914bac5f235' },
      growthScale: { id: '74255580-9feb-4fc2-a5ee-7635a4161bbf' },
    } as IsPopupFieldNotTouched;
    const isNotTouched = isCNPPopupFieldNotTouched(
      popupFieldProps.cropRegion,
      popupFieldProps.yieldUnit,
      popupFieldProps.unitSolid,
      popupFieldProps.growthScale,
    );
    expect(isNotTouched).toBeTruthy();
  });

  test('EditCropSettings: isCMMMPopupFieldNotTouched should return false', () => {
    const popupFieldProps = {
      cropRegion: cropSettingProps.selectedCropRegion,
      yieldUnit: { id: '394e8163-636e-48dd-8ded-a9fd6436d335' },
      unitSolid: { id: '53f8ea06-3244-4a0f-ae83-6914bac5f235' },
      growthScale: { id: '74255580-9feb-4fc2-a5ee-7635a4161bbf' },
      defaultYield: 42,
    } as IsCMMMPopupFieldNotTouched;
    const isNotTouched = isCMMMPopupFieldNotTouched(
      popupFieldProps.cropRegion,
      popupFieldProps.yieldUnit,
      popupFieldProps.unitSolid,
      popupFieldProps.growthScale,
      popupFieldProps.defaultYield,
    );
    expect(isNotTouched).toBeFalsy();
  });

  test('EditCropSettings: growth scale, yeild unit, and unit solid should render', async () => {
    const props = { ...cropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    expect(component.getByText('BBCH - Cereals')).toBeInTheDocument();
  });

  test('EditCropSettings: should update crop region after making change', async () => {
    const props = { ...cropSettingProps, isEditCropSettingOpened: true };

    server.use(
      http.put(
        `${POLARIS_API_GW_ENDPOINTS.CROP_API}/crop-regions/${cropRegionsResponse[0]?.id}`,
        async () => {
          return HttpResponse.json({ ...cropRegionsResponse[0], name: 'BBCH' }, { status: 200 });
        },
      ),
    );

    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <SnackbarContext.Provider
          value={{
            displaySnackbar: snackbarInitialStateMock,
            setDisplaySnackbar: mockSetDisplaySnackbar,
          }}
        >
          <EditCropSettings {...props} />
        </SnackbarContext.Provider>
        ,
      </AppContext.Provider>,
    );

    const growthScaleByLabel = component.getByLabelText(
      t('polaris.cnpDetails.cropSettings.growthScaleLabel'),
    );
    const yieldUnitLabel = component.getByLabelText(t('polaris.cnpDetails.cropSettings.yieldUnit'));
    const recommendationUnitLabel = component.getByLabelText(
      t('polaris.cnpDetails.cropSettings.recommendationUnit'),
    );
    const editSaveBtn = component.getByText(t('common.saveChanges'));

    expect(growthScaleByLabel).toBeInTheDocument();
    fireEvent.change(growthScaleByLabel, { target: { value: 'BBCH - Maize' } });
    expect(growthScaleByLabel).toHaveValue('BBCH - Maize');

    expect(yieldUnitLabel).toBeInTheDocument();
    fireEvent.change(yieldUnitLabel, { target: { value: 'quintales' } });
    expect(yieldUnitLabel).toHaveValue('quintales');

    expect(recommendationUnitLabel).toBeInTheDocument();
    fireEvent.change(recommendationUnitLabel, {
      target: { value: 'Pastos Colombia' },
    });
    expect(recommendationUnitLabel).toHaveValue('Pastos Colombia');

    fireEvent.click(editSaveBtn);

    expect(cropSettingProps.setIsEditCropSettingOpened).toHaveBeenCalledTimes(1);

    await waitFor(() => {
      expect(mockSetDisplaySnackbar).toHaveBeenCalledWith(
        expect.objectContaining({
          title: t('common.changesSaved'),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 5000,
          open: true,
        }),
      );
    });
  });

  test('EditCropSettings: function findOne should return object if found', () => {
    const selectedYieldUnit = '37e43c19-e008-4291-9580-71ce094680c4';
    const yieldUnit = findOne(cropSettingProps.yieldUnitsData, selectedYieldUnit);
    expect(yieldUnit).toEqual(cropSettingProps?.yieldUnitsData?.[0]);
  });

  test('EditCropSettings: function findOne should not return', () => {
    const selectedYieldUnit = '';
    const yieldUnit = findOne(cropSettingProps.yieldUnitsData, selectedYieldUnit);
    expect(yieldUnit).not.toEqual(cropSettingProps?.yieldUnitsData?.[0]);
  });
  test('EditCropSettings: should not show popup fields after clicking on cross btn', () => {
    const props = { ...cropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const crossButton = component.getByTestId('cnp-edit-popup-close-btn');

    expect(crossButton).toBeInTheDocument();
    act(() => {
      fireEvent.click(crossButton);
    });
    expect(cropSettingProps.setIsEditCropSettingOpened).toHaveBeenCalledTimes(2);
    expect(cropSettingProps.setIsEditCropSettingOpened).toHaveBeenCalledWith(false);
  });

  test('EditCropSettings: should match snapshot', () => {
    const props = { ...cropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    expect(component).toMatchSnapshot();
  });

  test('EditCropSettings: CMMM dropdown area should render', async () => {
    const props = { ...cmmmCropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockCMMMAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const defaultYieldLabel = component.getByLabelText(
      t(`polaris.cmmmDetails.cropSettings.defaultYieldLabel`, {
        yieldUnit: 'N/A',
      }),
    );

    expect(component.getByText('BBCH - Cereals')).toBeInTheDocument();

    expect(defaultYieldLabel).toBeInTheDocument();
    await userEvent.type(defaultYieldLabel, '7');
    expect(defaultYieldLabel).toHaveValue(7);
  });

  test('EditCropSettings: FP: dropdown area should render when opened', async () => {
    const props = { ...fpCropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const defaultTargetYieldLabel = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.defaultTargetYieldUnitOpt`),
    );

    expect(defaultTargetYieldLabel).toBeInTheDocument();
  });

  test('EditCropSettings: FP: should have disabled action button if not all/partial selections are made', async () => {
    const props = { ...fpCropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    const saveChangesButton = component.getByTestId('fertigation-plan-edit-popup-save-btn');

    expect(saveChangesButton).toBeInTheDocument();
    expect(saveChangesButton).toBeDisabled();
  });

  test('EditCropSettings: FP: should have disabled action button if no changes are made', async () => {
    const props = {
      ...fpCropSettingProps,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    const saveChangesButton = component.getByTestId('fertigation-plan-edit-popup-save-btn');

    expect(saveChangesButton).toBeInTheDocument();
    expect(saveChangesButton).toBeDisabled();
  });

  test('EditCropSettings: FP: should have enabled action button if changes are made', async () => {
    const props = {
      ...fpCropSettingProps,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const saveChangesButton = component.getByTestId('fertigation-plan-edit-popup-save-btn');
    const defaultTargetYieldLabel = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.defaultTargetYieldUnitOpt`),
    );

    expect(saveChangesButton).toBeInTheDocument();
    expect(saveChangesButton).toBeDisabled();

    await userEvent.clear(defaultTargetYieldLabel);
    await userEvent.type(defaultTargetYieldLabel, '7');

    expect(saveChangesButton).not.toBeDisabled();
  });

  test('EditCropSettings: FP: should have disabled recommended solids per area select until yield unit is selected', async () => {
    const props = {
      ...fpCropSettingProps,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider value={{ ...mockFertigationAppProviderValue }}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const yieldUnitSelect = component.getByRole('combobox', {
      name: t(`polaris.fpDetails.cropSettings.yieldUnit`),
    });
    const recommendedSolidsPerAreaUnitSelect = component.getByRole('combobox', {
      name: t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`),
    });

    expect(yieldUnitSelect).toBeInTheDocument();
    expect(recommendedSolidsPerAreaUnitSelect).toBeInTheDocument();
    expect(recommendedSolidsPerAreaUnitSelect).toHaveAttribute('aria-disabled', 'true');

    component.rerender(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    const recommendedSolidsPerAreaUnitRerenderSelect = component.getByRole('combobox', {
      name: t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`),
    });
    expect(recommendedSolidsPerAreaUnitRerenderSelect).toHaveAttribute('aria-disabled', 'false');
  });

  test('EditCropSettings: FP: should have disable nutrient removal select until recommended solids per area is selected', async () => {
    const preSelectedSettings = {
      ...fertigationUnitSettingsResponse,
      configuration: {
        data: {
          ...fertigationUnitSettingsResponse.configuration.data,
          yieldUnitId: FertigationYieldUnitResponse[0].id,
        },
      },
    };
    const props = {
      ...fpCropSettingProps,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const recommendedSolidsPerAreaUnitSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`),
    );
    const nutrientRemovalSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.nutrientRemovalUnit`),
    );

    expect(recommendedSolidsPerAreaUnitSelect).toBeInTheDocument();
    expect(nutrientRemovalSelect).toBeInTheDocument();
    expect(nutrientRemovalSelect).toHaveAttribute('aria-disabled', 'true');

    component.rerender(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    const nutrientRemovalRerenderSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.nutrientRemovalUnit`),
    );
    expect(nutrientRemovalRerenderSelect).toHaveAttribute('aria-disabled', 'false');
  });

  test('EditCropSettings: FP: should have disable nutrient demand select until recommended solids per area is selected', async () => {
    const preSelectedSettings = {
      ...fertigationUnitSettingsResponse,
      configuration: {
        data: {
          ...fertigationUnitSettingsResponse.configuration.data,
          yieldUnitId: FertigationYieldUnitResponse[0].id,
        },
      },
    };
    const props = {
      ...fpCropSettingProps,
      unitSettings: preSelectedSettings,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const recommendedSolidsPerAreaUnitSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`),
    );
    const nutrientDemandSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`),
    );

    expect(recommendedSolidsPerAreaUnitSelect).toBeInTheDocument();
    expect(nutrientDemandSelect).toBeInTheDocument();
    expect(nutrientDemandSelect).toHaveAttribute('aria-disabled', 'true');

    component.rerender(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    const nutrientDemandRerenderSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`),
    );
    expect(nutrientDemandRerenderSelect).toHaveAttribute('aria-disabled', 'false');
  });

  test('EditCropSettings: FP: should have disable nutrient demand select until recommended solids per area is selected', async () => {
    const preSelectedSettings = {
      ...fertigationUnitSettingsResponse,
      configuration: {
        data: {
          ...fertigationUnitSettingsResponse.configuration.data,
          yieldUnitId: FertigationYieldUnitResponse[0].id,
        },
      },
    };
    const props = {
      ...fpCropSettingProps,
      unitSettings: preSelectedSettings,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const recommendedSolidsPerAreaUnitSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.recommendationSolidsUnitPerArea`),
    );
    const nutrientDemandSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`),
    );

    expect(recommendedSolidsPerAreaUnitSelect).toBeInTheDocument();
    expect(nutrientDemandSelect).toBeInTheDocument();
    expect(nutrientDemandSelect).toHaveAttribute('aria-disabled', 'true');

    component.rerender(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );
    const nutrientDemandRerenderSelect = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.nutrientDemandUnit`),
    );
    expect(nutrientDemandRerenderSelect).toHaveAttribute('aria-disabled', 'false');
  });

  test('EditCropSettings: FP: should trigger successful update on action button click', async () => {
    const updateSettings = jest
      .fn()
      .mockReturnValue(preSelectedFertigationSettings.configuration.data);
    jest
      .spyOn(useCropSettingsModule, 'useUpdateFertigationCropSettings')
      .mockImplementation(() => ({
        data: { ...preSelectedFertigationSettings.configuration.data },
        trigger: updateSettings,
        isMutating: false,
        error: undefined,
      }));

    const props = {
      ...fpCropSettingProps,
      isEditCropSettingOpened: true,
    };

    const component = render(
      <AppContext.Provider
        value={{
          ...mockFertigationAppProviderValue,
          selectedFeatureUnitSettings: preSelectedFertigationSettings,
        }}
      >
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const saveChangesButton = component.getByTestId('fertigation-plan-edit-popup-save-btn');
    const defaultTargetYieldLabel = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.defaultTargetYieldUnitOpt`),
    );

    await userEvent.clear(defaultTargetYieldLabel);
    await userEvent.type(defaultTargetYieldLabel, '5');

    await waitFor(() => {
      fireEvent.click(saveChangesButton);
      const confirmButtonInfo = component.getByText('common.ok');
      expect(confirmButtonInfo).toBeInTheDocument();
      fireEvent.click(confirmButtonInfo);
      expect(updateSettings).toHaveBeenCalledTimes(1);
    });
  });

  test('EditCropSettings: FP: dropdown area should modify target yield when typed', async () => {
    const props = { ...fpCropSettingProps, isEditCropSettingOpened: true };
    const component = render(
      <AppContext.Provider value={mockFertigationAppProviderValue}>
        <EditCropSettings {...props} />
      </AppContext.Provider>,
    );

    const defaultTargetYieldLabel = component.getByLabelText(
      t(`polaris.fpDetails.cropSettings.defaultTargetYieldUnitOpt`),
    );

    expect(component.getByTestId('recommended-solids-unit-dropdown')).toBeInTheDocument();

    expect(defaultTargetYieldLabel).toBeInTheDocument();
    await userEvent.clear(defaultTargetYieldLabel);
    await userEvent.type(defaultTargetYieldLabel, '7');
    expect(defaultTargetYieldLabel).toHaveValue(7);
  });
});
