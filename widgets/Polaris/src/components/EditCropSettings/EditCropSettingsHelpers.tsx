import React from 'react';
import {
  BaseUnit,
  GrowthScale,
  Nullable,
  Feature,
  AdditionalProperty,
  CalculationType,
  CropRegion,
} from '@common/types';
import {
  findOne,
  isCMMMPopupFieldNotTouched,
  isCNPPopupFieldNotTouched,
} from '@widgets/Polaris/src/screens/helper';
import { ADDITIONAL_PROP_FIND_BY_KEY, ADDITIONAL_PROP_NAME, FEATURE_IDS } from '@common/constants';
import { MAX_NUM_INPUT_LENGTH } from './constants';
import {
  toPascalCase,
  toSnakeCase,
  truncateToLimitedNumberOfDecimals,
} from '@widgets/Polaris/utils';
import { SelectOptions } from '../../types';

interface EditCropSettingsHelpers {
  selectedCropRegion: CropRegion | null | undefined;
  selectedFeature: Feature | null;
}

const EditCropSettingsHelpers = ({
  selectedCropRegion,
  selectedFeature,
}: EditCropSettingsHelpers) => {
  const findCropRegionAdditionalPropertyBy = (
    searchKey: string,
    findBy: string = ADDITIONAL_PROP_FIND_BY_KEY.UNIT_TAGS,
  ): AdditionalProperty | null => {
    switch (findBy) {
      case ADDITIONAL_PROP_FIND_BY_KEY.UNIT_TAGS:
        return (
          selectedCropRegion?.additionalProperties?.find(
            ({ UnitTags }) => UnitTags === searchKey,
          ) ?? null
        );
      case ADDITIONAL_PROP_FIND_BY_KEY.NAME:
        return (
          selectedCropRegion?.additionalProperties?.find(({ Name }) => Name === searchKey) ?? null
        );
      default:
        return null;
    }
  };

  const prepareDropdownUnitItems = (
    originalItems: BaseUnit[] | GrowthScale[] | undefined,
  ): SelectOptions<string> | undefined => {
    return originalItems?.map((unit: BaseUnit | GrowthScale) => ({
      value: unit?.id,
      text: unit?.name,
    }));
  };

  const onUnitChange = (
    units: BaseUnit[] | undefined,
    searchedValue: string,
    setter: (value: BaseUnit | null) => void,
  ): void => {
    const unit = findOne(units, searchedValue);

    if (unit) setter(unit);
  };

  const onYieldChange = (
    value: string,
    setter: (value: React.SetStateAction<Nullable<number>>) => void,
  ): void => {
    if (value === '') {
      setter(null);
      return;
    }

    const shortenedValue =
      value.length >= MAX_NUM_INPUT_LENGTH ? value.slice(0, MAX_NUM_INPUT_LENGTH) : value;

    const maxTwoDecimals = truncateToLimitedNumberOfDecimals(shortenedValue, 2);

    setter(Math.abs(Number(maxTwoDecimals)));
  };

  const isPopupFieldNotTouched = (
    selectedYieldUnit: BaseUnit | null,
    selectedUnitSolid: BaseUnit | null,
    selectedGrowthScale: GrowthScale | null,
    defaultYield: number | null,
  ) => {
    const isCNPNotTouched =
      selectedFeature?.id === FEATURE_IDS.CNP
        ? isCNPPopupFieldNotTouched(
            selectedCropRegion,
            selectedYieldUnit,
            selectedUnitSolid,
            selectedGrowthScale,
          )
        : true;

    const isCMMMNotTouched =
      selectedFeature?.id === FEATURE_IDS.CMMM
        ? isCMMMPopupFieldNotTouched(
            selectedCropRegion,
            selectedYieldUnit,
            selectedUnitSolid,
            selectedGrowthScale,
            defaultYield,
          )
        : true;

    return isCNPNotTouched && isCMMMNotTouched;
  };

  const updateCMMMAdditionalPropsData = (
    defaultYield: number | null,
    defaultYieldParam: Nullable<AdditionalProperty> | undefined,
  ) => {
    if (!selectedCropRegion) return null;

    if (selectedFeature?.id !== FEATURE_IDS.CMMM) return selectedCropRegion.additionalProperties;

    if (
      // no Default yield param existed and none was added/modified (falsy non-zero value)
      (!defaultYieldParam && !defaultYield && typeof defaultYield !== 'number') ||
      // Default yield param is being unchanged
      (defaultYieldParam && defaultYieldParam.DefaultValue === defaultYield)
    ) {
      return selectedCropRegion.additionalProperties;
    }

    const existingAdditionalProperties = selectedCropRegion.additionalProperties;
    let additionalPropertiesCopy: AdditionalProperty[] | null = null;

    if (existingAdditionalProperties && defaultYieldParam) {
      additionalPropertiesCopy = existingAdditionalProperties.map((additionalProp) =>
        additionalProp.Name === ADDITIONAL_PROP_NAME.DEFAULT_YIELD
          ? { ...additionalProp, DefaultValue: defaultYield }
          : additionalProp,
      );
    } else {
      const newDefaultYieldAdditionalParam: AdditionalProperty = {
        Name: ADDITIONAL_PROP_NAME.DEFAULT_YIELD,
        Ordinal: existingAdditionalProperties?.length
          ? existingAdditionalProperties?.length + 1
          : 1,
        ValueType: 'number',
        DefaultValue: defaultYield,
        Required: true,
        BaseUnit: null,
        UnitTags: null,
        CalculationType: CalculationType.SOIL,
        Min: 0,
        Max: 0,
        ApiPropertyName: toPascalCase(ADDITIONAL_PROP_NAME.DEFAULT_YIELD),
        TranslationKey: `additional.parameters.${toSnakeCase(ADDITIONAL_PROP_NAME.DEFAULT_YIELD)}`,
        Options: null,
      };

      additionalPropertiesCopy = existingAdditionalProperties
        ? [...existingAdditionalProperties, newDefaultYieldAdditionalParam]
        : [newDefaultYieldAdditionalParam];
    }

    return additionalPropertiesCopy;
  };

  return {
    findCropRegionAdditionalPropertyBy,
    prepareDropdownUnitItems,
    onUnitChange,
    onYieldChange,
    isPopupFieldNotTouched,
    updateCMMMAdditionalPropsData,
  };
};

export default EditCropSettingsHelpers;
