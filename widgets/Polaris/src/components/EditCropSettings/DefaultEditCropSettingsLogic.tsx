import React, { useMemo, useState } from 'react';
import './styles.scss';
import {
  useStateUnits,
  useGrowthScale,
  useUpdateCropRegion,
  useFetchCanModifyGrowthScale,
} from '@polaris-hooks/index';
import {
  Nullable,
  BaseUnit,
  CanModifyGrowthScaleResponse,
  CropRegion,
  GrowthScale,
  Feature,
} from '@common/types';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import { findOne } from '@widgets/Polaris/src/screens/helper';
import { TFunction } from 'react-i18next';
import {
  ADDITIONAL_PROP_FIND_BY_KEY,
  ADDITIONAL_PROP_NAME,
  FEATURE_IDS,
  METHOD,
} from '@common/constants';
import EditCropSettingsHelpers from './EditCropSettingsHelpers';
import { useFetchCropSplits } from '@polaris-hooks/index';
import { FilterType } from '../../types';

interface DefaultEditCropSettingsLogicProps {
  selectedCropRegion: CropRegion | null | undefined;
  setCropRegion: React.Dispatch<CropRegion>;
  isEditCropSettingOpened: boolean;
  setIsEditCropSettingOpened: React.Dispatch<boolean>;
  yieldUnitsData: BaseUnit[] | undefined;
  growthScalesData: GrowthScale[] | undefined;
  demandUnitsData: BaseUnit[] | undefined;
  selectedFeature: Feature | null;
  t: TFunction;
}

const DefaultEditCropSettingsLogic = ({
  yieldUnitsData,
  isEditCropSettingOpened,
  setIsEditCropSettingOpened,
  selectedCropRegion,
  demandUnitsData,
  growthScalesData,
  selectedFeature,
  setCropRegion,
  t,
}: DefaultEditCropSettingsLogicProps) => {
  const {
    findCropRegionAdditionalPropertyBy,
    prepareDropdownUnitItems,
    onUnitChange,
    onYieldChange,
    isPopupFieldNotTouched,
    updateCMMMAdditionalPropsData,
  } = EditCropSettingsHelpers({ selectedCropRegion, selectedFeature });

  // ------------ useMemo - cropRegion additional property by ------------

  const defaultYieldParam = useMemo(() => {
    // Used in CNP and CMMM
    return findCropRegionAdditionalPropertyBy(
      ADDITIONAL_PROP_NAME.DEFAULT_YIELD,
      ADDITIONAL_PROP_FIND_BY_KEY.NAME,
    );
  }, [selectedCropRegion?.additionalProperties]);

  // ------------ useMemo - prepare dropdown unit options ------------

  const yieldAndSolidsUnit = useMemo(() => {
    // Used in CNP and CMMM
    return prepareDropdownUnitItems(yieldUnitsData);
  }, [yieldUnitsData]);

  const demandUnits = useMemo(() => {
    // Used in CNP and CMMM
    return prepareDropdownUnitItems(demandUnitsData);
  }, [demandUnitsData]);

  const growthScales = useMemo(() => {
    // Used in CNP and CMMM
    return prepareDropdownUnitItems(growthScalesData);
  }, [growthScalesData]);

  // ------------ States ------------

  const { setDisplaySnackbar } = useSnackbar();
  const [defaultYield, setDefaultYield] = useState<Nullable<number>>(
    defaultYieldParam?.DefaultValue || defaultYieldParam?.DefaultValue === 0
      ? Number(defaultYieldParam.DefaultValue)
      : null,
  );

  // ------------ Polaris hooks ------------

  const {
    // Used in CNP and CMMM
    selectedUnit: selectedYieldUnit,
    setSelectedUnit: setSelectedYieldUnit,
  } = useStateUnits(yieldUnitsData, isEditCropSettingOpened, selectedCropRegion?.yieldBaseUnitId);

  const {
    // Used in CNP and CMMM
    selectedUnit: selectedUnitSolid,
    setSelectedUnit: setSelectedUnitSolid,
  } = useStateUnits(demandUnitsData, isEditCropSettingOpened, selectedCropRegion?.demandBaseUnitId);

  const {
    // Used in CNP and CMMM
    growthScale: selectedGrowthScale,
    setGrowthScale: setSelectedGrowthScale,
  } = useGrowthScale(growthScalesData, isEditCropSettingOpened, selectedCropRegion?.growthScaleId);

  const { canModifyResponse } = useFetchCanModifyGrowthScale({
    // Used in CNP and CMMM
    id: selectedCropRegion?.id,
    checkCropSplit: false,
    checkNTester: true,
  });

  const cropSplits = useFetchCropSplits({
    filter: [
      {
        key: 'cropRegionId',
        type: FilterType.EQ,
        value: selectedCropRegion?.id ?? '',
      },
    ],
    paging: {
      size: 1,
    },
    shouldFetch: true,
  });

  // ------------ Handlers onChange ------------

  const onYieldUnitChange = (value: string) => {
    // Used in CNP and CMMM
    onUnitChange(yieldUnitsData, value, setSelectedYieldUnit);
  };

  const ondUnitSolidChange = (value: string) => {
    // Used in CNP and CMMM
    onUnitChange(demandUnitsData, value, setSelectedUnitSolid);
  };

  const onGrowthScaleChange = (value: string) => {
    // Used in CNP and CMMM
    const growthScale = findOne(growthScalesData, value);
    if (growthScale) setSelectedGrowthScale(growthScale);
  };

  const onDefaultYieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Used in CNP and CMMM
    onYieldChange(event.target.value, setDefaultYield);
  };

  // ------------ Handlers onSaveChanges ------------

  const { trigger: updateCropRegion } = useUpdateCropRegion(selectedCropRegion?.id);

  const defaultCropSettingUpdateHandler = async () => {
    setIsEditCropSettingOpened(false);
    if (!selectedCropRegion) return;

    if (
      isPopupFieldNotTouched(
        selectedYieldUnit,
        selectedUnitSolid,
        selectedGrowthScale,
        defaultYield,
      )
    )
      return;

    const newAdditionalProps = () => {
      switch (selectedFeature?.id) {
        case FEATURE_IDS.CMMM:
          return updateCMMMAdditionalPropsData(defaultYield, defaultYieldParam);
        default:
          return selectedCropRegion.additionalProperties;
      }
    };

    const cropRegionNewData: CropRegion = {
      ...selectedCropRegion,
      yieldBaseUnitId: selectedYieldUnit?.id || selectedCropRegion.yieldBaseUnitId,
      demandBaseUnitId: selectedUnitSolid?.id || selectedCropRegion.demandBaseUnitId,
      growthScaleId: selectedGrowthScale?.id || selectedCropRegion.growthScaleId,
      additionalProperties: newAdditionalProps(),
    };

    try {
      const response = await updateCropRegion({
        method: METHOD.PUT,
        body: JSON.stringify(cropRegionNewData),
      });
      if (response) {
        setCropRegion(response);
        setDisplaySnackbar({
          title: t('common.changesSaved'),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 5000,
          open: true,
        });
      }
    } catch (error) {
      console.error('Error updating cropRegion data:', error);
    }
  };

  // ------------ Handlers on close dialog ------------

  const defaultCloseHandler = () => {
    setDefaultYield(
      defaultYieldParam?.DefaultValue || defaultYieldParam?.DefaultValue === 0
        ? Number(defaultYieldParam.DefaultValue)
        : null,
    );
  };

  const haveCropSplits = cropSplits.length > 0;
  const canModifyGrowthScale: CanModifyGrowthScaleResponse = {
    success: (canModifyResponse?.success ?? false) && !haveCropSplits,
    message: canModifyResponse?.message ?? '',
    usedIn: (canModifyResponse?.usedIn ?? []).concat(
      haveCropSplits ? [t('common.cropNutritionPlanData')] : [],
    ),
  };

  return {
    selectedUnitSolid,
    selectedYieldUnit,
    selectedGrowthScale,
    canModifyResponse: canModifyGrowthScale,
    defaultYield,
    onYieldUnitChange,
    ondUnitSolidChange,
    onGrowthScaleChange,
    onDefaultYieldChange,
    defaultCropSettingUpdateHandler,
    defaultCloseHandler,
    yieldAndSolidsUnit,
    demandUnits,
    growthScales,
  };
};

export default DefaultEditCropSettingsLogic;
