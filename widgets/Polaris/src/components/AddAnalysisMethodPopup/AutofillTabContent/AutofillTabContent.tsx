import React, { <PERSON> } from 'react';
import { useTranslation } from 'react-i18next';
import { Select, Paragraph } from '@yaradigitallabs/ahua-react';
import { SelectWrapper } from '../../Select';
import { TabContentWrapper } from '../';
import { AutofillTabContentProps } from '.';

export const AutofillTabContent: FC<AutofillTabContentProps> = ({
  selectedConfigurationData,
  analysisMethodsData,
  selectedConfigurationId,
  selectedNewAnalysisMethodId,
  isSaveAttempted,
  setSelectedConfigurationId,
  setSelectedNewAnalysisMethodId,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.soilAnalysis.soilParamsList.addAnalysisMethod.autofillDetails',
  });

  const existingAnalysisMethodText = t('existingAnalysisMethod');
  const newAnalysisMethodText = t('newAnalysisMethod');

  return (
    <TabContentWrapper>
      <Paragraph>{t('from')}</Paragraph>
      <SelectWrapper dataCy='autofill-details-from-select'>
        <Select
          ariaLabel={existingAnalysisMethodText}
          cover='outline'
          items={
            selectedConfigurationData?.map((el) => {
              const analysisMethod = analysisMethodsData?.find(
                (analysisMethod) => analysisMethod.id === el.analysisMethodId,
              );
              return {
                value: el.id || '',
                text: analysisMethod?.name || '',
              };
            }) || []
          }
          helper-text={
            isSaveAttempted && !selectedConfigurationId
              ? t('isRequired', { analysisMethod: existingAnalysisMethodText })
              : undefined
          }
          loading={!selectedConfigurationData}
          placeholder={existingAnalysisMethodText}
          position='popper'
          size='s'
          variant={isSaveAttempted && !selectedConfigurationId ? 'error' : 'default'}
          value={selectedConfigurationId || ''}
          onChange={setSelectedConfigurationId}
        />
      </SelectWrapper>
      <Paragraph>{t('to')}</Paragraph>
      <SelectWrapper dataCy='autofill-details-to-select'>
        <Select
          ariaLabel={newAnalysisMethodText}
          cover='outline'
          items={
            analysisMethodsData
              ?.filter(
                (el) => !selectedConfigurationData?.some((elem) => elem.analysisMethodId === el.id),
              )
              .map((el) => ({
                value: el.id,
                text: el.name,
              })) || []
          }
          helper-text={
            isSaveAttempted && !selectedNewAnalysisMethodId
              ? t('isRequired', { analysisMethod: newAnalysisMethodText })
              : undefined
          }
          loading={!analysisMethodsData}
          placeholder={newAnalysisMethodText}
          position='popper'
          size='s'
          variant={isSaveAttempted && !selectedNewAnalysisMethodId ? 'error' : 'default'}
          value={selectedNewAnalysisMethodId || ''}
          onChange={setSelectedNewAnalysisMethodId}
        />
      </SelectWrapper>
    </TabContentWrapper>
  );
};
