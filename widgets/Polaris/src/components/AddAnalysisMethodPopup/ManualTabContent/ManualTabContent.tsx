import React, { Dispatch, FC, SetStateAction, useMemo } from 'react';
import {
  radioButtonGroupStyles,
  RadioElementWrapper,
  SecondaryParamsSelectOptions,
  TabContentWrapper,
} from '../';
import { SelectWrapper } from '../../Select';
import {
  Label,
  Paragraph,
  RadioButton,
  RadioButtonGroup,
  Select,
} from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import { AnalysisMethod, BaseUnit, AnalysisConfiguration } from '@common/types';

export interface ManualTabContentProps {
  baseUnits?: BaseUnit[];
  analysisMethodsData: AnalysisMethod[] | undefined;
  selectedConfigurationData: AnalysisConfiguration[];
  selectedNewAnalysisMethodId: string | null;
  selectedUnitId: string | null;
  setSelectedNewAnalysisMethodId: Dispatch<SetStateAction<string | null>>;
  setSelectedUnitId: Dispatch<SetStateAction<string | null>>;
  isSaveAttempted: boolean;
  disableUnitSelection: boolean;
}

export const ManualTabContent: FC<ManualTabContentProps> = ({
  baseUnits,
  analysisMethodsData,
  selectedConfigurationData,
  selectedNewAnalysisMethodId,
  selectedUnitId,
  setSelectedNewAnalysisMethodId,
  setSelectedUnitId,
  isSaveAttempted,
  disableUnitSelection,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix:
      'polaris.cmmmDetails.soilAnalysis.soilParamsList.addAnalysisMethod.manuallyFillDetails',
  });

  const analysisMethodSelectLabelText = t('analysisMethodSelectLabel');
  const analysisUnitSelectLabelText = t('analysisUnitSelectLabel');

  const baseUnitItems = useMemo(() => {
    return (
      baseUnits?.map((unit) => ({
        text: unit.name,
        value: unit.id,
      })) || []
    );
  }, []);

  const analysisMethodItems = useMemo(() => {
    return (
      analysisMethodsData
        ?.filter(
          (method) =>
            !selectedConfigurationData.some((config) => config.analysisMethodId === method.id),
        )
        .map((method) => ({
          value: method.id,
          text: method.name,
        })) || []
    );
  }, []);

  return (
    <TabContentWrapper>
      <SelectWrapper dataCy='manually-fill-details-method-select'>
        <Select
          ariaLabel={analysisMethodSelectLabelText}
          cover='outline'
          items={analysisMethodItems}
          helper-text={
            isSaveAttempted && !selectedNewAnalysisMethodId
              ? t('isRequired', {
                  selectItem: analysisMethodSelectLabelText,
                })
              : undefined
          }
          loading={!analysisMethodsData}
          placeholder={analysisMethodSelectLabelText}
          position='popper'
          size='s'
          variant={isSaveAttempted && !selectedNewAnalysisMethodId ? 'error' : 'default'}
          value={selectedNewAnalysisMethodId || ''}
          onChange={setSelectedNewAnalysisMethodId}
        />
      </SelectWrapper>

      <SelectWrapper dataCy='manually-fill-details-unit-select'>
        <Select
          ariaLabel={analysisUnitSelectLabelText}
          cover='outline'
          items={baseUnitItems}
          helper-text={
            isSaveAttempted && !selectedUnitId
              ? t('isRequired', {
                  selectItem: analysisUnitSelectLabelText,
                })
              : undefined
          }
          loading={!baseUnits}
          placeholder={analysisUnitSelectLabelText}
          position='popper'
          size='s'
          variant={isSaveAttempted && !selectedUnitId ? 'error' : 'default'}
          value={selectedUnitId || ''}
          disabled={disableUnitSelection}
          onChange={setSelectedUnitId}
        />
      </SelectWrapper>

      <Paragraph
        size='s'
        css={{
          fontWeight: '$medium',
          lineHeight: '$scale4',
          paddingTop: '$x6',
        }}
      >
        {t('secondaryParametersRadioSelectText')}
      </Paragraph>

      <RadioButtonGroup
        css={radioButtonGroupStyles}
        defaultValue={SecondaryParamsSelectOptions.NO}
        value={SecondaryParamsSelectOptions.NO}
      >
        <RadioElementWrapper state='active'>
          <Label htmlFor='no_secondary_params'>{t('secondaryParametersRadioSelectLabel.no')}</Label>
          <RadioButton value={SecondaryParamsSelectOptions.NO} id='no_secondary_params' />
        </RadioElementWrapper>
        <RadioElementWrapper state='disabled'>
          <Label htmlFor='yes_secondary_params'>
            {t('secondaryParametersRadioSelectLabel.yes')}
          </Label>
          <RadioButton
            value={SecondaryParamsSelectOptions.YES}
            id='yes_secondary_params'
            disabled
          />
        </RadioElementWrapper>
      </RadioButtonGroup>
    </TabContentWrapper>
  );
};
