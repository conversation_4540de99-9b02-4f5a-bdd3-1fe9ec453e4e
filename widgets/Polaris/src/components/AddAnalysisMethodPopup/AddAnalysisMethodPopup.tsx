import React, { FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { Button, Card, Dialog, IconButton, Tabs } from '@yaradigitallabs/ahua-react';
import { useSnackbar } from '@libs/snackbar-context/snackbar-context';
import {
  HeaderWrapper,
  SaveButtonWrapper,
  AddAnalysisMethodPopupProps,
  AddAnalysisMethodPopupTabs,
  StyledDialogContent,
  StyledTabsList,
  StyledTabsTrigger,
} from '.';
import { AutofillTabContent } from './AutofillTabContent';
import { ManualTabContent } from './ManualTabContent';
import { AnalysisMethod, FeatureConfigOptions } from '@common/types';
import { METHOD, STATUS_CODE } from '@common/constants';
import {
  useCloneAnalysisConfiguration,
  useAnalysisMethods,
  useAnalysisMethodCountries,
  useAnalysisConfiguration,
  useCreateMultiNutrientClassifications,
} from '@polaris-hooks/index';
import { getDefaultAnalysisNutrientClassifications } from '../../screens/CropFeatures/shared/helpers';
import { FilterType } from '@widgets/Polaris/src/types';

export const AddAnalysisMethodPopup: FC<AddAnalysisMethodPopupProps> = ({
  configType,
  analysisType,
  selectedConfiguration,
  showAddAnalysisMethodPopup,
  setShowAddAnalysisMethodPopup,
  setConfigurationUpdated,
  baseUnits,
  defaultManualCreationUnitId,
  selectedNutrient,
}) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.cmmmDetails.soilAnalysis.soilParamsList.addAnalysisMethod',
  });
  const {
    selectedCountry,
    cropRegion,
    selectedPlanConfigTab,
    methods: { addAnalysisConfigurations },
  } = useAppContext();
  const { setDisplaySnackbar } = useSnackbar();
  const [activeTab, setActiveTab] = useState<AddAnalysisMethodPopupTabs>(
    AddAnalysisMethodPopupTabs.AUTOFILL,
  );
  const [isSaveAttempted, setIsSaveAttempted] = useState<boolean>(false);
  const [analysisMethodsData, setAnalysisMethodsData] = useState<AnalysisMethod[] | undefined>(
    undefined,
  );

  const [selectedConfigurationId, setSelectedConfigurationId] = useState<string | null>(null);
  const [selectedNewAnalysisMethodId, setSelectedNewAnalysisMethodId] = useState<string | null>(
    null,
  );
  const [selectedUnitId, setSelectedUnitId] = useState<string | undefined>(
    defaultManualCreationUnitId,
  );

  // hooks for getting data
  const { analysisMethodCountryData, trigger: getAnalysisMethodCountries } =
    useAnalysisMethodCountries();
  const { trigger: getAnalysisMethods } = useAnalysisMethods();

  // hook for cloning data
  const { trigger: triggerCloneConfiguration } = useCloneAnalysisConfiguration(
    configType,
    analysisType,
  );
  const { trigger: triggerCreateAnalysisConfiguration } = useAnalysisConfiguration(
    configType,
    analysisType,
  );
  const { triggerCreateMultiNutrientClassifications } = useCreateMultiNutrientClassifications(
    configType,
    analysisType,
  );

  useEffect(() => {
    if (selectedCountry) {
      getAnalysisMethodCountries({
        method: METHOD.POST,
        body: JSON.stringify({
          filter: [
            {
              key: 'countryId',
              type: FilterType.EQ,
              value: selectedCountry.id,
            },
          ],
        }),
      });
    }
  }, [selectedCountry]);

  useEffect(() => {
    const fetchAnalysisMethods = async () => {
      try {
        const analysisMethodsResponse = await getAnalysisMethods({
          method: METHOD.POST,
          body: JSON.stringify({
            filter: [
              {
                key: 'id',
                type: FilterType.IN,
                value: analysisMethodCountryData?.map((item) => item.analysisMethodId).join(','),
              },
            ],
          }),
        });

        const filteredAnalysisMethods = analysisMethodsResponse?.entities?.filter(
          (analysisMethod) => {
            if (selectedPlanConfigTab && selectedNutrient) {
              if (typeof analysisMethod.tags === 'string') {
                return analysisMethod.tags
                  .split(',')
                  .some(
                    (tag) =>
                      tag ===
                      `${selectedNutrient?.elementalName}${selectedPlanConfigTab.split(' ')[0]}`,
                  );
              } else {
                return false;
              }
            } else {
              return false;
            }
          },
        );
        setAnalysisMethodsData(filteredAnalysisMethods);
      } catch (error) {
        console.error('Error fetching analysis methods:', error);
      }
    };
    if (analysisMethodCountryData?.length) fetchAnalysisMethods();
  }, [selectedCountry, selectedPlanConfigTab, selectedNutrient, analysisMethodCountryData?.length]);

  const handleClose = () => {
    setShowAddAnalysisMethodPopup(false);
  };

  const handleSave = useCallback(async () => {
    setIsSaveAttempted(true);
    if (!(selectedConfigurationId && selectedNewAnalysisMethodId)) {
      console.warn('Selects are not filled');
      return;
    }

    try {
      if (selectedConfigurationId && selectedNewAnalysisMethodId) {
        const cloneConfigurationResponse = await triggerCloneConfiguration({
          method: METHOD.POST,
          body: JSON.stringify({
            sourceConfigurationId: selectedConfigurationId,
            analysisMethodId: selectedNewAnalysisMethodId,
          }),
        });

        if (!cloneConfigurationResponse) {
          handleClose();
          throw new Error('Unknown error');
        }

        if (cloneConfigurationResponse) {
          setDisplaySnackbar({
            title: t('analysisMethodCreated'),
            colorConcept: 'successLight',
            icon: 'Check',
            placement: 'bottomRight',
            duration: 3000,
            open: true,
          });
          setConfigurationUpdated(true);
          handleClose();
        }
      }
    } catch (error) {
      console.error('Error creating configuration:', error);
      setDisplaySnackbar({
        title: t('analysisMethodCreationError'),
        colorConcept: 'destructiveLight',
        icon: 'Clear',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
      if (
        error &&
        typeof error === 'object' &&
        'statusCode' in error &&
        'message' in error &&
        (error?.statusCode === STATUS_CODE.BAD_REQUEST ||
          error?.statusCode === STATUS_CODE.CONFLICT)
      ) {
        handleClose();
        throw new Error(`${error?.message}`);
      }
      handleClose();
    }
  }, [
    selectedConfigurationId,
    selectedNewAnalysisMethodId,
    setConfigurationUpdated,
    handleClose,
    t,
  ]);

  const handleManualSave = useCallback(async () => {
    setIsSaveAttempted(true);

    if (!(selectedNewAnalysisMethodId && selectedUnitId)) {
      console.warn('Selects are not filled');
      return;
    }

    try {
      let createAnalysisConfigurationResponse;

      if (selectedNutrient && selectedPlanConfigTab && selectedCountry && cropRegion) {
        createAnalysisConfigurationResponse = await triggerCreateAnalysisConfiguration({
          method: METHOD.POST,
          body: JSON.stringify({
            countryId: selectedCountry.id,
            cropRegionId: cropRegion.id,
            nutrientId: selectedNutrient.id,
            analysisBaseUnitId: selectedUnitId,
            considerSecondaryParameters: false,
            analysisMethodId: selectedNewAnalysisMethodId,
          }),
        });
      }

      if (!createAnalysisConfigurationResponse) {
        throw new Error(`Failed to create new ${analysisType} configuration`);
      }

      const defaultNutrientClassifications = getDefaultAnalysisNutrientClassifications(
        createAnalysisConfigurationResponse,
        analysisType,
      );

      const createdNutrientClassifications = await triggerCreateMultiNutrientClassifications(
        defaultNutrientClassifications,
      );

      if (createdNutrientClassifications && createAnalysisConfigurationResponse) {
        setDisplaySnackbar({
          title: t('analysisMethodCreated'),
          colorConcept: 'successLight',
          icon: 'Check',
          placement: 'bottomRight',
          duration: 3000,
          open: true,
        });
        const newFullSoilAnalysisConfiguration = {
          ...createAnalysisConfigurationResponse,
          nutrientClassifications: [...createdNutrientClassifications],
        };
        addAnalysisConfigurations(newFullSoilAnalysisConfiguration);
      }

      handleClose();
    } catch (error) {
      console.error('Error creating configuration:', error);
      setDisplaySnackbar({
        title: t('analysisMethodCreationError'),
        colorConcept: 'destructiveLight',
        icon: 'Clear',
        placement: 'bottomRight',
        duration: 3000,
        open: true,
      });
      handleClose();
    }
  }, [selectedConfigurationId, selectedNewAnalysisMethodId, selectedUnitId]);

  useEffect(() => {
    setSelectedConfigurationId(null);
    setSelectedNewAnalysisMethodId(null);
    setIsSaveAttempted(false);
  }, [activeTab]);

  return (
    <Dialog open={showAddAnalysisMethodPopup} onOpenChange={handleClose}>
      <StyledDialogContent data-cy='add-analysis-method-popup-content'>
        <HeaderWrapper>
          <Dialog.Header>
            <Dialog.Title data-cy='add-analysis-method-popup-title'>{t('title')}</Dialog.Title>
          </Dialog.Header>
          <Dialog.Close>
            <IconButton
              data-cy='add-analysis-method-popup-close-btn'
              icon='Close'
              colorConcept='brand'
              size='xs'
            />
          </Dialog.Close>
        </HeaderWrapper>
        <Card.Divider />
        <Tabs
          defaultValue={activeTab}
          onValueChange={(value: AddAnalysisMethodPopupTabs) => setActiveTab(value)}
        >
          <StyledTabsList aria-label='Components'>
            <StyledTabsTrigger
              value={AddAnalysisMethodPopupTabs.AUTOFILL}
              data-cy='autofill-details-tab'
            >
              {t('autofillDetails.title')}
            </StyledTabsTrigger>
            <StyledTabsTrigger
              value={AddAnalysisMethodPopupTabs.MANUALLY}
              data-cy='manually-fill-details-tab'
            >
              {t('manuallyFillDetails.title')}
            </StyledTabsTrigger>
          </StyledTabsList>
          <Tabs.Content value={AddAnalysisMethodPopupTabs.AUTOFILL}>
            <AutofillTabContent
              selectedConfigurationData={selectedConfiguration}
              analysisMethodsData={analysisMethodsData}
              selectedConfigurationId={selectedConfigurationId}
              selectedNewAnalysisMethodId={selectedNewAnalysisMethodId}
              isSaveAttempted={isSaveAttempted}
              setSelectedConfigurationId={setSelectedConfigurationId}
              setSelectedNewAnalysisMethodId={setSelectedNewAnalysisMethodId}
            />
          </Tabs.Content>
          <Tabs.Content value={AddAnalysisMethodPopupTabs.MANUALLY}>
            <ManualTabContent
              baseUnits={baseUnits}
              analysisMethodsData={analysisMethodsData}
              selectedConfigurationData={selectedConfiguration}
              selectedNewAnalysisMethodId={selectedNewAnalysisMethodId}
              setSelectedNewAnalysisMethodId={setSelectedNewAnalysisMethodId}
              selectedUnitId={selectedUnitId}
              setSelectedUnitId={setSelectedUnitId}
              isSaveAttempted={isSaveAttempted}
              disableUnitSelection={configType !== FeatureConfigOptions.FERTIGATION}
            />
          </Tabs.Content>
        </Tabs>
        <Card.Divider />
        <Dialog.End>
          <SaveButtonWrapper>
            <Button
              data-cy='add-analysis-method-popup-save-btn'
              size='s'
              colorConcept='brand'
              variant='primary'
              onClick={
                activeTab === AddAnalysisMethodPopupTabs.AUTOFILL ? handleSave : handleManualSave
              }
            >
              {t('saveButton')}
            </Button>
          </SaveButtonWrapper>
        </Dialog.End>
      </StyledDialogContent>
    </Dialog>
  );
};
