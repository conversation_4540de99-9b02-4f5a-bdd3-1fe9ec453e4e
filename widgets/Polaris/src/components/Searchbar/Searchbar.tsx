import React, { useEffect, useState } from 'react';
import { Input } from '@yaradigitallabs/ahua-react';
import { useTranslation } from 'react-i18next';
import { SearchbarProps } from './SearchbarProps.types';
import './styles.scss';

export const Searchbar = ({
  column,
  resetSearch,
  autoResetPageIndexRef,
  dataCy,
}: SearchbarProps) => {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.common',
  });

  const searchTerm = column.getFilterValue();
  const stringSearchTerm = typeof searchTerm === 'string' ? searchTerm : '';
  const [hasSearched, setHasSearched] = useState<boolean>(!!searchTerm);
  const [searchingQuery, setSearchingQuery] = useState<string>(stringSearchTerm);

  const handleSearching = (query: string) => {
    setSearchingQuery(query);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!searchingQuery) return;
    //enable page reset when searching
    autoResetPageIndexRef.current = true;
    column.setFilterValue(searchingQuery);
    setHasSearched(true);
  };

  const deleteSearchTerm = () => {
    setSearchingQuery('');
    setHasSearched(false);
    //disable page reset when deleting search term
    autoResetPageIndexRef.current = false;
  };
  useEffect(() => {
    if (searchingQuery?.length === 0) {
      setHasSearched(false);
      column.setFilterValue('');
    }
  }, [searchingQuery]);

  useEffect(() => {
    deleteSearchTerm();
  }, [resetSearch]);

  return (
    <form onSubmit={handleSubmit} className={`search-input`}>
      <Input
        value={searchingQuery}
        onChange={(e) => handleSearching(e.target.value)}
        iconTrailing={
          hasSearched
            ? {
                icon: 'Close',
                onClick: (e: React.FormEvent<HTMLFormElement>) => {
                  e.stopPropagation();
                  deleteSearchTerm();
                },
              }
            : {
                icon: 'Search',
                onClick: (e: React.FormEvent<HTMLFormElement>) => {
                  e.stopPropagation();
                  handleSubmit(e);
                },
                dataCy: 'search-button',
              }
        }
        label={t('searchbarLabel')}
        size='xs'
        onClick={(e) => e.stopPropagation()}
        data-cy={dataCy}
      />
    </form>
  );
};
