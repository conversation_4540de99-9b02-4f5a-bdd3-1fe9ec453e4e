import { Button, Collapsible, Separator, Switch } from '@yaradigitallabs/ahua-react';
import React, { useState } from 'react';
import { ButtonContainer, TableCardContent, ToolbarContent } from './Collapsible.styled';
import './styles.scss';
import { useTranslation } from 'react-i18next';
import { CollapsibleComponentProps } from './Collapsible.type';
import { InfoMessage } from './InfoMessage';

const CollapsibleSection: React.FC<CollapsibleComponentProps> = ({
  children,
  headerTitle,
  showCardContent,
  cardContentText,
  defaultOpen = false,
  onAddBtnClick,
  showMoreDetails,
  disabledMoreDetails,
  dataCY,
  ahdbTooltipMsg,
  open,
  onOpenChange,
}) => {
  const { t } = useTranslation();
  const [isClosed, setIsClosed] = useState(true);

  return (
    <Collapsible
      defaultOpen={defaultOpen}
      data-cy={dataCY}
      header={headerTitle}
      className='collapsible-section'
      open={open}
      onOpenChange={onOpenChange}
    >
      <InfoMessage tooltipMsg={ahdbTooltipMsg} setIsClosed={setIsClosed} isClosed={isClosed} />

      {showCardContent && (
        <TableCardContent>
          <ToolbarContent>
            <div className='content-text'>{cardContentText}</div>
            <ButtonContainer>
              {showMoreDetails && (
                <div className='details-button'>
                  <Switch
                    data-cy='additional-plan-switch'
                    label={'Show more details'}
                    labelPosition='left'
                    defaultChecked={false}
                    disabled={disabledMoreDetails}
                  ></Switch>
                </div>
              )}
              <Button data-cy='collapsible-add-button' size='s' onClick={onAddBtnClick}>
                {t('polaris.common.addButton')}
              </Button>
            </ButtonContainer>
          </ToolbarContent>
        </TableCardContent>
      )}
      <Separator width='100%' mode='light' orientation='horizontal' />
      {children}
    </Collapsible>
  );
};

export default CollapsibleSection;
