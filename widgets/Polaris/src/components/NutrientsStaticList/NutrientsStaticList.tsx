import React, { useEffect, useMemo } from 'react';
import { Title } from '@yaradigitallabs/ahua-react';
import { Nutrient } from '@common/types';
import {
  NutrientList,
  NutrientListBody,
  TitleStyle,
  HeaderWrapper,
  EmptyStateText,
} from './NutrientsStaticList.styled';
import { useAppContext } from '@widgets/Polaris/src/providers/AppProvider';
import { NutrientTile } from '../NutrientTile';
import { useParams } from 'react-router';
import { configTabInParamsToConfigTabName } from '@widgets/Polaris/utils';
import { NUTRIENT_IDS_BY_TAB } from '../../screens/CropFeatures/shared/constants';

interface NutrientsStaticListProps {
  selectedNutrient?: Nutrient | null;
  nutrientParams: Nutrient[];
  title: string;
  emptyStateText: string;
}

export const NutrientsStaticList = ({
  selectedNutrient,
  nutrientParams,
  title,
  emptyStateText,
}: NutrientsStaticListProps) => {
  const params = useParams();
  const {
    selectedPlanConfigTab,
    selectedFeatureNutrients,
    selectedFeature,
    methods: { setSelectedFeatureNutrients },
  } = useAppContext();

  const nutrientIdFromURLParams = useMemo(() => params.nutrientId, [params.nutrientId]);

  const handleSelectFeatureNutrient = (nutrient: Nutrient) => {
    if (selectedPlanConfigTab) {
      setSelectedFeatureNutrients({
        ...selectedFeatureNutrients,
        [selectedPlanConfigTab]: nutrient.id,
      });
    }
  };

  useEffect(() => {
    if (selectedPlanConfigTab && selectedFeature && nutrientParams.length) {
      if (!selectedFeatureNutrients?.[selectedPlanConfigTab]) {
        // If we just landed directly on the route which renders component with a nutrient list, we take the nutrient from the path.
        // In this case, the config tab from the params should already be in the state (this is managed by parent components), and the two should match
        const configTabFromUrl = configTabInParamsToConfigTabName(params.configTab);
        const tabsMatch = configTabFromUrl === selectedPlanConfigTab;
        const nutrientFromParams = nutrientParams.find(
          (nutrient) => nutrient.id === nutrientIdFromURLParams,
        );

        const newNutrient =
          nutrientFromParams && tabsMatch ? nutrientFromParams : nutrientParams?.[0];

        setSelectedFeatureNutrients({
          ...selectedFeatureNutrients,
          [selectedPlanConfigTab]: newNutrient.id,
        });
      } else {
        // Handling case when nutrient set for the respective config tab is impossible,
        // e.g. user pastes URL into browser tab where they had config tab with the same name but from a different feature open
        const nutrientId = selectedFeatureNutrients[selectedPlanConfigTab];
        const wrongNutrient =
          !NUTRIENT_IDS_BY_TAB[selectedFeature.id][selectedPlanConfigTab].includes(nutrientId);
        wrongNutrient &&
          setSelectedFeatureNutrients({
            ...selectedFeatureNutrients,
            [selectedPlanConfigTab]: nutrientParams?.[0]?.id,
          });
      }
    }
  }, [nutrientParams, nutrientIdFromURLParams, selectedFeature]);

  return (
    <>
      <HeaderWrapper data-cy='nutrients-static-list-header'>
        <Title data-cy='nutrient-list-title' style={TitleStyle} size='xs'>
          {title}
        </Title>
      </HeaderWrapper>
      <NutrientListBody data-cy='nutrient-list-body'>
        <NutrientList data-cy='nutrients-static-list'>
          {nutrientParams && nutrientParams.length > 0 ? (
            nutrientParams.map((nutrient, index) => (
              <NutrientTile
                key={nutrient.id}
                nutrient={nutrient}
                selected={selectedNutrient?.id === nutrient?.id}
                onSelect={handleSelectFeatureNutrient}
                variant='list'
                dataCy={`nutrient-tile-${index}`}
              />
            ))
          ) : (
            <EmptyStateText>{emptyStateText}</EmptyStateText>
          )}
        </NutrientList>
      </NutrientListBody>
    </>
  );
};
