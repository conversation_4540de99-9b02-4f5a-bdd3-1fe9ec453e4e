import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  AhuaIconProps,
  Button,
  ButtonGroup,
  ButtonProps,
  Dialog as AhuaDialog,
  DialogMediaProps,
} from '@yaradigitallabs/ahua-react';
import { CustomContentWrapperStyled } from './ConfirmationDialog.styled';

interface ConfirmationDialogComponentProps {
  onOk: () => void;
  title: string;
  icon?: AhuaIconProps['icon'];
  iconColorConcept?: DialogMediaProps['colorConcept'];
  description?: string;
  alignDescription?: 'center' | 'left' | 'right';
  buttonOrientation?: 'horizontal' | 'vertical';
  buttonSize?: 's' | 'xs' | 'n' | 'l' | 'xl';
  open?: boolean;
  okButton?: string;
  okButtonConcept?: ButtonProps['colorConcept'];
  cancelButton?: string;
  onCancel?: () => void;
  isLoading?: boolean;
  hideCancelButton?: boolean;
  hideAllButtons?: boolean;
  css?: React.CSSProperties;
  customContentComponent?: React.JSX.Element;
}

export function ConfirmationDialog(
  props: React.PropsWithChildren<ConfirmationDialogComponentProps>,
) {
  const { t } = useTranslation('polaris', {
    keyPrefix: 'polaris.common',
  });
  const {
    title,
    description,
    alignDescription,
    icon,
    iconColorConcept,
    okButton,
    okButtonConcept,
    cancelButton,
    buttonOrientation,
    buttonSize,
    onOk,
    open,
    onCancel,
    isLoading = false,
    hideCancelButton = false,
    hideAllButtons = false,
    customContentComponent = null,
    css,
  } = props;
  const { Content, Trigger, Start, Middle, End, Media, Title, Description } = AhuaDialog;

  return (
    <AhuaDialog open={open} data-cy='dialog'>
      {!open && <Trigger asChild>{props.children}</Trigger>}
      <Content
        data-cy='dialog-content'
        css={
          css
            ? css
            : {
                lineHeight: '19.6px',
                width: '280px',
                padding: '$x4 0 $x1 0',
              }
        }
      >
        <Start css={{ paddingTop: icon ? 0 : '$x10', paddingBottom: '$x2' }}>
          {icon && (
            <Media
              type='icon'
              iconSrc={icon || 'Check'}
              colorConcept={iconColorConcept || 'success'}
            />
          )}
        </Start>
        <Middle css={{ padding: '$x2 $x4 $x1 $x4' }}>
          <Title css={{ lineHeight: '25.2px' }} data-cy='dialog-title' align='center'>
            {title}
          </Title>
          {description && (
            <Description
              data-cy='dialog-desc'
              align={alignDescription || 'center'}
              css={{
                whiteSpace: 'pre-line',
                fontSize: '14px',
                lineHeight: '20px',
              }}
            >
              {description}
            </Description>
          )}
          {customContentComponent && (
            <CustomContentWrapperStyled>{customContentComponent}</CustomContentWrapperStyled>
          )}
        </Middle>

        {!hideAllButtons && (
          <End>
            <ButtonGroup
              orientation={buttonOrientation || 'vertical'}
              buttonWidth='block'
              size={buttonSize || 's'}
            >
              <Button
                data-cy='dialog-confirmButton'
                onClick={onOk}
                loading={isLoading}
                disabled={isLoading}
                colorConcept={okButtonConcept || 'brand'}
              >
                {okButton || t('yes')}
              </Button>
              {!hideCancelButton && (
                <Button
                  data-cy='dialog-closeButton'
                  variant='ghost'
                  disabled={isLoading}
                  onClick={() => onCancel && onCancel()}
                >
                  {cancelButton || t('cancel')}
                </Button>
              )}
            </ButtonGroup>
          </End>
        )}
      </Content>
    </AhuaDialog>
  );
}
