import { Caption, Card, styled, Title } from '@yaradigitallabs/ahua-react';

export const DetailsContainer = styled('div', {
  padding: '$x10 $x14 0px',
  position: 'relative',
});

export const CardHeadWrapper = styled('div', {
  '&.card-header > div': {
    justifyContent: 'space-between',

    '& > div:first-child': {
      maxWidth: 'calc(100% - 100px)',
    },
  },
  'h1, p': {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
});

export const MMMCardHeadWrapper = styled('div', {
  width: 'auto',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: 'var(--space-x2) var(--space-x4)',
  '& > div': {
    padding: '0 !important',
  },
  height: 'var(--sizes-x14)',
  '& p': {
    fontSize: 'var(--fontSizes-scale2) !important',
    lineHeight: 'var(--lineHeights-scale5)',
  },
});

export const MMMCardHeaderTitle = styled(Title, {
  fontSize: 'var(--fontSizes-scale4) !important',
});

export const CardHeadTitleWrapper = styled('div', {
  maxWidth: 'calc(100% - 110px)',
  flex: '2 0 auto',
  width: 'fit-content',
  'h1, p': {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
});

export const PlanCardTitleCaption = styled(Caption, {
  color: 'var(--colors-neutral-contrast)',
  display: 'block !important',
});

export const StyledCardTail = styled(Card.Tail, {
  '&&': {
    padding: '$x3 $x4',
  },
  '& button': {
    height: '$x10',
    minHeight: '$x10',
    fontSize: '$scale2',
    width: '100%',
    span: {
      overflow: 'unset !important',
    },
  },
});
