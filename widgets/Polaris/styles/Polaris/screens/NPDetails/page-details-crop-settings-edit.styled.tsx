import { Card, styled } from '@yaradigitallabs/ahua-react';

export const EditButtonWrapper = styled(Card.Action, {
  '&&': {
    fontSize: '$scale3',
    width: '90px',
    '&:not(:disabled)': {
      color: '$blue60',
    },
  },

  '& svg': {
    width: '$x4',
    height: '$x4',
    minWidth: '$x4',
    minHeight: '$x4',
    marginRight: '$x1',
    strokeWidth: '10%',
  },

  '& span': {
    height: '18px',
  },
});

export const StyledCropSettingsCardBody = styled(Card.Body, {
  height: '87px',
  paddingLeft: '0 !important',
  justifyContent: 'space-between',

  '&& > *': {
    paddingTop: '$x5',
    paddingBottom: '$x5',
    paddingLeft: '0',
    paddingRight: '0',
  },
  '&& > *:first-child': {
    paddingLeft: '0',
  },
  '&& > *:last-child': {
    paddingRight: '0',
  },
  '& > div label:first-child': {
    fontSize: '$scale0',
  },
});

export const StyledCropSettingsFertigationCardBody = styled(Card.Body, {
  height: '87px',
  paddingLeft: '0 !important',
});

export const StyledCropSettingsCardContent = styled(Card.Content, {
  height: '87px',
  paddingLeft: '0 !important',
  display: 'flex',
  justifyContent: 'space-between',
  width: '100%',

  '&& > *': {
    paddingTop: '$x5',
    paddingBottom: '$x5',
    paddingLeft: '0',
    paddingRight: '0',
  },
  '&& > *:first-child': {
    paddingLeft: '0',
  },
  '&& > *:last-child': {
    paddingRight: '0',
  },
  '& > div label:first-child': {
    fontSize: '$scale0',
  },

  variants: {
    state: {
      contentFromStart: {
        justifyContent: 'start',
        '& > div': {
          width: '25% !important',
        },
      },
    },
  },
});

export const StyledCropSettingsCardTail = styled(Card.Tail, {
  '&&': {
    paddingTop: '$x3',
    paddingBottom: '10px',
    paddingLeft: '$x4',
    paddingRight: '$x4',
    gap: '6px',
  },
  '& > label': {
    lineHeight: '$scale4',
  },
});
