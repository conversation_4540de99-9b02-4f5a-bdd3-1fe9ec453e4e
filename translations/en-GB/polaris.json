{"polaris": {"common": {"snackBarError": {"closeButton": "Close"}, "saveChanges": "Save changes", "changesSaved": "Changes saved", "saveButton": "Save", "addButton": "Add", "crop": "Crop", "lastModified": "Last updated", "actions": "Actions", "save": "Save", "yes": "Yes", "no": "No", "none": "None", "cancel": "Cancel", "yesDelete": "Yes, delete", "snackbarDeleteMessage": "{{name}} deleted", "edit": "Edit {{name}}", "searchbarLabel": "Search", "table": {"actions": {"title": "Actions", "edit": "Edit", "delete": "Delete"}}, "ok": "OK", "min": "Min", "max": "Max", "isRequired": "{{fieldName}} is required", "levelAdded": "{{paramLevel}} level added", "splitAdded": "Growth phase {{growthPhaseNo}} is added successfully", "splitRemoved": "Split for growth phase {{growthPhaseNo}} deleted", "changeSaved": "Change saved", "clipboardCopyMessage": "Copied to clipboard", "emptySearchState": {"text": "No results matching", "button": "Back to list"}, "cropNutritionPlanData": "Crop nutrition plan data", "parameterLevel": {"VERY_LOW": "Very low", "LOW": "Low", "MEDIUM": "Medium", "HIGH": "High", "VERY_HIGH": "Very high"}, "statusEnabled": "Enabled", "statusDisabled": "Disabled"}, "sharedText": {"notValidatedYet": "Not validated yet", "lastUpdateValidationStatus": "Last updated by {{modifiedBy}} on {{modifiedDate}}", "validatedStatus": "Validated by {{modifiedBy}} on {{modifiedDate}}", "invalidatedStatus": "Invalidated by {{modifiedBy}} on {{modifiedDate}}", "failedStatus": "Validation attempt unsuccessful by {{modifiedBy}} on {{modifiedDate}}"}, "error": {"wentWrongTitle": "Something went wrong!", "mustBeNumber": "Must be a number", "isRequired": "{{name}} is required", "minGreaterThanMax": "Must be less than max value", "maxLessThanMin": "Must be more than min value", "withinMinMaxRange": "Must be within min-max range"}, "homepage": {"loadingText": "Loading...", "title": "Welcome to Polaris", "text": "Configure a new or existing feature", "countryDropdown": {"placeholder": "Country", "ariaLabel": "Country dropdown"}, "regionDropdown": {"placeholder": "Region", "ariaLabel": "Region dropdown"}, "cropDropdown": {"helperText": "You can select a new or existing crop.", "placeholder": "Crop", "ariaLabel": "Crop dropdown"}, "featureDropdown": {"placeholder": "Feature", "ariaLabel": "Feature dropdown"}}, "topbar": {"logoutDialog": {"title": "Logout confirmation", "description": "Are you sure you want to log out?", "buttonOk": "Yes, log out", "buttonCancel": "Cancel"}, "logoutButton": "Log out", "cropFeatures": {"title": "Crops and features"}, "overview": {"title": "Overview"}, "productsAvailable": {"title": "Products Available"}, "countrySettings": {"title": "Country Settings"}, "home": {"title": "<PERSON><PERSON>"}, "globalSettings": {"title": "Global Settings"}, "cnpDetails": {"title": "{{selectedCropName}} {{selectedCropDescriptionName}}: Crop Nutrition Plan", "soilBasedNutritionPlan": {"title": "Soil-based Nutrition Plan"}, "leafBasedNutritionPlan": {"title": "Leaf-based Nutrition Plan"}, "cerealsMmm": {"title": "Cereals MMM"}, "fertigationPlan": {"title": "Fertigation Plan"}, "soilBased-nutrition-plan": {"title": "<PERSON><PERSON>"}}, "cmmmDetails": {"title": "{{selectedCropName}} {{selectedCropDescriptionName}}: Cereals Master Mind Map (MMM)"}, "fpDetails": {"title": "{{selectedCropName}} {{selectedCropDescriptionName}}: Fertigation Plan"}}, "sideMenu": {"coachmarkMessage": "Select a location to access its crops, features, products, and partners.", "location": "Location", "overview": "Overview", "cropFeatures": "Crops & features", "productsAvailable": "Products available", "countrySettings": "Country settings", "globalSettings": "Global settings", "loadingText": "Loading..."}, "locationPopup": {"selectLocation": "Select location:", "countryPlaceholder": "Country", "countryLabel": "Country", "regionPlaceholder": "Region", "regionLabel": "Region", "applyButton": "Apply"}, "deleteAnalysisMethodPopup": {"title": "Delete methods", "placeholder": "Analysis methods", "dropdownHelperText": "Leave at least one analysis method for the parameter.", "dropdownDeleteErrorText": "At least one analysis method must be selected to proceed.", "save": "Save changes", "deleteAlertDialog": {"title": "Cannot delete method", "description": "A parameter must have at least one analysis method.", "ok": "OK"}, "snackbarMessageSingular": "{{count}} method deleted", "snackbarMessagePlural": "{{count}} methods deleted", "comboBoxAriaLabel": "Select Analysis methods combobox"}, "cnpDetails": {"title": "You are configuring {{selectedFeatureName}} for {{selectedCropSubclassName}} {{selectedCropDescriptionName}} in {{selectedRegionName}}, {{selectedCountryName}}", "cropSettings": {"title": "Crop settings", "growthScale": "Growth scale", "yieldUnit": "Yield unit", "recommendationUnit": "Recommendation unit", "partnersTitle": "Partners for this crop", "editButton": "Edit", "partnersCaption": "To add partners, go to the list of crops in Overview, or Crops & features", "selectedCropName": "Edit {{cropName}} settings", "location": "{{selectedLocation}}", "growthScaleLabel": "Growth scale", "growthScaleError": "Growth scale cannot be changed because it is already in use."}, "soilNutritionPlan": {"title": "Soil-based Nutrition Plan", "content": {"title": "Considers crop needs and soil analysis", "description": "You can configure parameters, soil analysis, demand calculations, and product recommendations."}, "partners": {"title": "CNP Partners", "description": "To add partners, configure and go to Product recommendations."}, "buttons": {"test": "Go to test harness", "configure": "Configure", "validate": "Validate"}}, "leafNutritionPlan": {"title": "Leaf-based Nutrition Plan", "subtitle": "--", "content": {"helperText": "Currently unavailable in your selected country"}, "buttons": {"test": "Go to test harness", "configure": "Configure", "validate": "Validate"}, "switchLabel": "Validate", "tooltipMessage": "Once validated, this configuration will be available to farming solutions.", "failedValidationPopup": {"title": "Can't validate the plan", "content": "There are errors in the configuration. \nPlease review and correct the highlighted fields before validating the plan.", "confirmButton": "Ok"}}, "planConfiguration": {"tabs": {"parameters": "Parameters", "soilAnalysis": "Soil analysis", "leafAnalysis": "Leaf analysis", "demandCalculations": "Demand calculations", "productRecommendations": "Product recommendations"}, "calculationParameters": {"yield": "Yield", "preCropTypeId": "Pre-crop", "preCropYield": "Pre-crop yield", "preCropResidueManagementId": "Pre-crop residue management", "postCropTypeId": "Post-crop type", "postCropYield": "Post-crop yield", "soilTypeId": "Soil type", "postCropResidueManagementId": "Post-crop residue management", "cropResidueManagementId": "Crop residue management", "previouslyAppliedProducts": "Previously applied products", "parameters": "Parameter", "showInAtfarm": "Show in Atfarm?", "actions": "Action", "tooltipText": "Default value will be used in case the user from the farming facing solution does not provide one", "defaultValue": "Default value", "headerTitle": "Calculation parameters", "headerSubtitle": "Only used in Atfarm"}, "prePostCropParams": {"title": "Pre-crops and post-crops", "subTitle": "The cropping sequence helps better understand soil conditions.", "emptyScreenText": "No pre-crop or post-crop added yet", "preCrop": "Pre-crop", "postCrop": "Post-crop", "mapsInAhdb": "Maps in AHDB?", "mapsInAhdbMessage": "For the UK and Ireland, we match crops to their AHDB equivalents so an external partner can create the nutrition plans.", "dialog": {"title": "Delete {{cropName}}?", "description": "This change is permanent and affects the Crop Nutrition Plan."}, "addPrePostTitle": "{{actionName}} pre- or post-crop", "isPrePostOrBoth": "Is this a pre-crop, post-crop, or both?", "parametersAdded": "Parameter added"}, "soilAnalysis": {"title": "Soil Analysis", "addTitle": " analysis methods: {{count}}", "dropdownItems": {"addTitle": "Add analysis method", "deleteTitle": "Delete methods", "deleteSubtitle": "At least one analysis method is required."}, "addTableButton": "Add another", "analysisBase": "Analysis base unit", "defaultValue": "Normal range expression", "analysisValueOptional": "Analysis value expression (optional)", "baseValueExpression": "Base value expression (optional)", "secondaryParameterExpression": "Secondary parameter expression", "elementForm": "Element form", "nutritionParamTitle": "Nutrition parameter levels", "switchLabel": "Consider secondary parameters", "table": {"header": {"default": "Set as default", "parameterLevel": "Parameter level", "secondaryParamLevel": "Secondary parameter level", "validityExp": "Validity expression", "action": "Action"}}, "dialog": {"title": "Delete {{cropName}}?", "description": "It will also be permanently deleted from Demand calculations. Other parameter levels will not be affected.", "info": {"title": "Cannot delete level", "description": "You cannot delete the default. \nTo delete this, set a different default."}, "deleteMessage": "Level deleted", "updateMessage": "Level changed", "addAnotherLevel": "{{actionName}} another level", "parameterLevel": "Parameter level", "validityExpression": "Validity expression", "setAsDefault": "Set as default? (optional)", "yesSetAsDefault": "Yes, set as default", "paramLevel": "Parameter level"}, "defaultActiveMsg": "This adjusts your selected parameter's levels based on the effects of secondary parameters in the soil."}, "productRecommendations": {"emptyState": {"title": "Recommend products for key growth stages", "message": "Split product applications across the growing season\n to meet the crop's nutritional demands.", "createFirstSplitButton": "Create first split"}}}, "soilValidation": {"tooltipMessage": "You must save a test harness before you validate the latest configuration.", "switchLabel": "Validate", "snackbarMessage": "Changes saved", "validationPopup": {"title": "Invalidate Crop Nutrition Plan?", "description": "This change is permanent and cannot be undone.", "confirm": "Yes, invalidate", "cancel": "Cancel"}, "failedValidationPopup": {"title": "Can't validate the plan", "content": "There are errors in the configuration. \nPlease review and correct the highlighted fields before validating the plan.", "confirmButton": "Ok"}}, "configuration": {"additionalParameters": {"title": "Additional Parameters", "subtitle": "Location-specific parameters make the plan more accurate.", "table": {"header": {"parameter": "Parameter", "ordinal": "Ordinal", "valueType": "Value type", "actions": "Actions"}, "actions": {"title": "Actions", "edit": "Edit", "delete": "Delete", "editSubParameter": "Edit value", "deleteSubParameter": "Delete parameter"}}, "emptyStateText": "No additional parameters added yet.", "dialog": {"description": "This change is permanent and affects the Crop Nutrition Plan.", "title": "Delete {{parameterName}}?"}, "additionalParametersPopup": {"title": "Add additional parameter", "checkboxDescription": "Select checkbox to use this parameter for calculations:", "checkboxLabel": "Yes, use for calculation", "parameterName": "Parameter", "parameterOrdinal": "Ordinal", "valueType": "Value type", "defaultValue": "Default value", "select": {"number": "Number", "numberWithUnit": "Number with unit", "list": "List", "date": "Date", "boolean": "Boolean", "text": "Text", "List": "List", "Number": "Number", "Date": "Date", "Boolean": "Boolean", "Number with unit": "Number with unit", "Text": "Text"}, "loadingText": "Loading...", "parameterOrdinalDuplicate": "Entity with same Ordinal already exists", "parameterNameDuplicate": "Entity with same Parameter already exists", "calendarLabel": "Default value", "booleanSelect": {"true": "True", "false": "False"}, "listType": {"emptyScreenText": "Create a list of parameters from Add to list.", "title": "List", "addToListButton": "Add to list", "addListItem": {"title": "Add parameter to list", "subParameterName": "Parameter", "continue": "Continue", "value": "Value", "parameterRequired": "Parameter is required", "valueRequired": "Value is required", "duplicateParameterName": "Parameter already in list", "editTitle": "Edit parameter value", "editConfirm": "Save change"}, "table": {"header": {"parameter": "Parameter", "value": "Value", "translationKey": "Translation key", "actions": "Actions"}}}, "unitTags": "Unit tags", "baseUnit": "Base unit", "baseUnitHelperText": "Linked to your selected unit tags", "editTitle": "Edit additional parameter", "saveAhdb": "Save AHDB parameters", "saveOther": "Save other parameters", "ahdbTitle": "Select AHDB parameters  you’d like to add:", "ahdbTooltipInfo": "For the UK and Ireland, AHDB parameters are required. Click Add.", "ahdbParameter": "AHDB parameters", "otherParameter": "Other parameters"}, "parameterAddedMessage": "Parameter added", "parameterUpdatedMessage": "Changes saved", "paramsAdded": "Parameters added"}}, "parameters": {"parameterEditPopup": {"title": "Edit {{calcParameterName}}", "description": "Show in Atfarm?", "defaultValue": "Default value", "yieldUnit": "Yield unit", "snackbarMessage": "Change saved", "placeholder": "Default value", "parameterName": "Parameter", "parameterType": "Value type", "helperText": "To set a default value, first add a {{cropType}}."}}, "soilAnalysis": {"nutrientList": {"title": "Nutrition parameters", "nutrientPopup": {"title": "Add nutrition parameters", "saveButton": "Save", "snackbarMessage": "{{selectedNutrientsLength}} nutrition parameters added", "copyNutrientTitle": "Copy {{nutrient}}'s analysis methods to:", "copyNutrientsTitle": "Copy all to another crop location", "countryDropdown": {"ariaLabel": "Country dropdown", "placeholder": "Country"}, "regionDropdown": {"ariaLabel": "Region dropdown", "placeholder": "Region"}, "cropDropdown": {"ariaLabel": "Crop dropdown", "placeholder": "Crop"}, "location": "{{selectedLocation}}", "helperTextSingle": "Overwrite existing analysis methods when copying? (cannot be undone)", "helperTextMultiple": "Overwrite existing soil parameters when copying? (cannot be undone)", "no": "No", "yes": "Yes, overwrite", "copyNutrientMessage": "Copied to {{selectedCropName}} {{selectedCropDescriptionName}} in {{selectedRegionName}}, {{selectedCountryName}}", "copyNutrientError": "Could not copy {{selectedNutrientName}}. Please try again.", "copyNutrientsError": "Could not copy. Please try again."}, "copy": "Copy all", "emptyStateText": "Click + to add nutrition parameters for Soil analysis and Demand calculations."}}}, "cmmmDetails": {"title": "Configure {{selectedFeatureName}} for {{selectedCropSubclassName}} {{selectedCropDescriptionName}} in {{selectedRegionName}}, {{selectedCountryName}}", "cerealsMMM": {"title": "Cereals MMM", "buttons": {"test": "Go to test harness", "configure": "Configure", "configureTooltipMessage": "Once validated, the configuration cannot be changed. Please invalidate first.", "testTooltipMessage": "Test harness will be available soon. For now, directly validate the configuration."}, "content": {"title": "Considers crop needs and soil analysis", "description": "You can configure soil analysis, demand calculations, organic fertilisers, N splitting schedule, and product recommendations."}, "validationSwitch": {"tooltipMessage": "Once validated, this configuration will be available to farming solutions.", "switchLabel": "Validate", "snackbarMessage": "Change saved", "validationPopup": {"title": "Invalidate this plan?", "description": "Once invalidated, this configuration can no longer be used by farming solutions.", "confirm": "Yes, invalidate", "cancel": "Cancel"}, "failedValidationPopup": {"title": "Can't validate the plan", "content": "There are errors in the configuration. \nPlease review and correct the highlighted fields before validating the plan.", "confirmButton": "Ok"}}}, "cropSettings": {"growthScale": "Growth scale", "editButton": "Edit", "recommendationUnit": "Recommendation unit", "defaultYield": "Default yield ({{- yieldUnit}})", "yieldUnit": "Yield unit", "partnersTitle": "Partners for this crop", "title": "Crop settings", "location": "{{selectedLocation}}", "selectedCropName": "Edit {{cropName}} settings: Units", "growthScaleHelperText": "Growth scale cannot be changed because it is already in use.", "growthScaleLabel": "Growth scale", "defaultYieldLabel": "Default yield ({{- yieldUnit}})", "partnersCaption": "To add partners, go to the list of crops in Overview, or Crops & features", "editTooltipMessage": "To edit the units, first invalidate the plan. After making changes, update the necessary configuration values."}, "planConfiguration": {"tabs": {"soilAnalysis": "Soil analysis", "demandCalculations": "Demand calculations", "organicFertilisers": "Organic fertilisers", "nSplittingSchedule": "N splitting schedule", "productRecommendations": "Product recommendations"}, "organicFertilisers": {"productsAvailable": {"headerTitle": "Products available", "headerSubtitle": "List of organic fertilisers in the region", "noteText": "Note: To add, edit or remove organic fertilisers for your region, go to Products in Polaris' old version. \nTo make products available for Cereals MMM, add the feature tag Cereals MMM.", "table": {"nutrientContentCell": "Nutrient content (%)", "col1": "Product family", "col2": "Product name", "col3": "Density \n(w/w)", "col4": "N", "col5": "P", "col6": "K", "col7": "S", "col8": "Mg"}}, "soilTypes": {"headerTitle": "Soil texture class", "headerSubtitle": "Soil texture class configuration for Cereals MMM", "description": "Configure which soil texture classes can be shown to farming-solution users.", "emptyStateText": "No soil texture class configurations for country and region.", "table": {"actionContentCell": "Show to users", "col1": "Set as default", "col2": "Soil texture class", "col3": "Action"}}, "marketRegulations": {"headerTitle": "Market regulations", "headerSubtitle": "Application limits from local governing bodies and regulatory agencies", "applicationLimitsTitle": "Organic fertiliser {{ name }} application limits", "inputs": {"label": "Maximum {{ name }} amount per application ({{-unit}})"}}, "pAvailabilityCorrection": {"headerTitle": "P availability correction", "headerSubtitle": "P availability values in Products will be overwritten", "emptyStateText": "No P availability correction configurations for country and region", "table": {"title": "Corrections per parameter level", "tooltip": "Levels are defined in Soil analysis.", "col1": "Parameter level", "col2": "P availability (%)"}}, "nAvailabilityCorrection": {"headerTitle": "N availability correction", "headerSubtitle": "Adjustments to N availability values in Products, based on influencing factors", "emptyStateText": "No N availability correction configurations for country and region", "table": {"title": "Corrections per type of factor", "techniqueCell": "Technique", "soilCell": "Soil", "dryMatterCell": "Dry mattercontent (%)", "col1": "Factor", "col2": "Type", "col3": "N availability correction (%)"}}}, "nSplittingSchedule": {"subTitle": "Split fertiliser N applications across the growing season", "validation": {"min": "Value should be higher or equal to {{ value }}", "max": "Value must not exceed {{ value }}.", "discontinuousRanges": "Discontinuous ranges. Relook at values.", "nonConsecutiveRangeValues": "Split rules should be consecutive", "totalMax": "Total exceeds {{ value }}%. Check splits.", "totalMin": "Total is less than {{ value }}%. Check splits."}, "headers": {"growthStage": {"numberHead": "Growth stage no.", "nameHead": "Growth stage", "imageHead": "Growth stage image"}, "rule": {"nameHead": "Splitting rules", "subNameHead": "When fertiliser N (kg/ha) demand is"}, "numberOfSplits": "No. of\nsplits", "totalPercentage": "Total (%)", "split": "Split {{ number }} (%)"}}}, "soilAnalysis": {"soilParamsList": {"title": "Soil parameters", "description": "Parameter levels defined here are used in fertiliser calculations.", "addTitle": " analysis methods: {{count}}", "addAnalysisMethod": {"title": "Add analysis method", "autofillDetails": {"title": "Autofill details", "from": "Autofill details from:", "existingAnalysisMethod": "Existing analysis method", "loadingAnalysisMethod": "Loading analysis method...", "to": "To:", "newAnalysisMethod": "New analysis method", "isRequired": "{{<PERSON><PERSON>eth<PERSON>}} is required"}, "manuallyFillDetails": {"title": "Manually fill details", "analysisMethodSelectLabel": "Analysis method", "analysisUnitSelectLabel": "Analysis unit", "secondaryParametersRadioSelectLabel": {"no": "No", "yes": "Yes, consider"}, "secondaryParametersRadioSelectGroup": "Secondary parameters radio select group", "secondaryParametersRadioSelectText": "Consider secondary parameters?", "isRequired": "{{selectItem}} is required"}, "saveButton": "Save", "analysisMethodCreated": "Changes saved", "analysisMethodCreationError": "Could not save changes"}, "dropdownItems": {"addTitle": "Add analysis method", "deleteTitle": "Delete methods", "deleteSubtitle": "At least one analysis method is required."}, "emptyStateText": "No nutrition parameters for Soil analysis and Demand calculations added yet."}, "deleteAnalysisMethodPopup": {"title": "Delete methods", "placeholder": "Analysis methods", "placeholderWithSelectedValue": "Select method to delete", "dropdownHelperText": "Leave at least one analysis method for the nutrition parameter.", "dropdownDeleteErrorText": "At least one analysis method must be selected to proceed.", "deleteAlertDialog": {"title": "Cannot delete method", "description": "A nutrition parameter must have at least one analysis method."}, "snackbarMessageSingular": "{{count}} method deleted", "snackbarMessagePlural": "{{count}} methods deleted", "comboBoxAriaLabel": "Select Analysis methods combobox"}, "addTableButton": "Add another", "analysisBase": "Soil analysis unit", "defaultValue": "Normal range expression", "analysisValueOptional": "Analysis value expression (optional)", "baseValueExpression": "Base value expression (optional)", "secondaryParameterExpression": "Secondary parameter expression", "elementForm": "Element form", "nutritionParamTitle": "Soil parameter levels", "nutritionParamSubTitle": "Set ranges for each parameter level.", "switchLabel": "Consider secondary parameters", "defaultActiveMsg": "This adjusts your selected parameter’s levels based on the effects of secondary parameters in the soil.", "switchTooltipMsg": "This setting is not yet available. For now, directly configure your selected parameter's levels.", "parameterLevelsTable": {"header": {"default": "Set as default", "parameterLevel": "Parameter level", "greaterThan": "Greater than or equal to (>=)", "lessThan": "Less than (<)", "action": "Action"}, "addTableButton": "Add another", "dialog": {"title": "Delete {{level}}?", "description": "It will also be permanently deleted from Demand calculations. Other parameter levels will not be affected.", "info": {"title": "Cannot delete level", "description": "You cannot delete the default. \nTo delete this, set a different default."}, "deleteMessageLevel": "Level deleted", "updateMessageLevel": "Level changed", "updateMessageValue": "Value changed", "addAnotherLevel": "{{actionName}} another level", "parameterLevel": "Parameter level", "greaterOrEqual": "Greater than or equal to (>=)", "lowerThan": "Less than (<)", "setAsDefault": "Set as default? (optional)", "yesSetAsDefault": "Yes, set as default", "selectError": "Parameter level is required", "paramValues": {"title": "Update values", "description": {"nextUpperLimit": "The upper limit for {{level}} must be lower than the next upper limit in the {{analysisType}} nutrient classification.", "previousUpperLimit": "The upper limit for {{level}} must be higher than the previous upper limit in the {{analysisType}} nutrient classification."}}}, "addLevelTableButton": "Add level", "nutrientClassificationRadioButtonLabel": "Select {{nutrientClassification}}"}, "soilAnalysisMethods": {"defaultTitle": "<PERSON><PERSON><PERSON>", "copyNutrientTitle": "Copy {{nutrient}}'s analysis methods to:", "copyNutrientsTitle": "Copy all to another crop location", "countryDropdown": {"ariaLabel": "Country dropdown", "placeholder": "Country"}, "regionDropdown": {"ariaLabel": "Region dropdown", "placeholder": "Region"}, "cropDropdown": {"ariaLabel": "Crop dropdown", "placeholder": "Crop"}, "location": "{{selectedLocation}}", "helperTextSingle": "Overwrite existing analysis methods when copying? (cannot be undone)", "helperTextMultiple": "Overwrite existing soil parameters when copying? (cannot be undone)", "yes": "Yes, overwrite", "copyNutrientMessage": "Copied to {{selectedCropName}} {{selectedCropDescriptionName}} in {{selectedRegionName}}, {{selectedCountryName}}", "copyNutrientError": "Could not copy {{selectedNutrientName}}. Please try again.", "copyNutrientsError": "Could not copy. Please try again."}, "copy": "Copy all", "emptyStateText": "Click + to add nutrition parameters for Soil analysis and Demand calculations."}, "demandCalculations": {"nutrientList": {"title": "Parameters", "emptyStateText": "No nutrition parameters"}, "nutrientForm": {"areaLabel": "Select nutrient form", "description": "Calculations are done for this nutrient form.", "title": "Nutrient form"}, "mainEquation": {"title": "Main equation", "description": "The algorithm uses this equation to calculate fertiliser {{nutrient}} demand. Some terms are optional.", "equationN": "Fertiliser N demand = Crop N demand / N use efficiency \n                     – (Soil org. matter N + Pre-crop N + Soil mineral N)", "equationP": "Fertiliser P demand = (Crop P demand / P use efficiency) x Soil correction factor\n                      + Soil fixed quantity correction", "equationK": "Fertiliser K demand = (Crop K demand / K use efficiency) x Soil correction factor\n                      + Soil fixed quantity correction", "equationMg": "Fertiliser Mg demand = (Crop Mg demand / Mg use efficiency) x Soil correction factor\n                       + Soil fixed quantity correction", "equationS": "Fertiliser S demand = Crop S demand / S use efficiency - Soil Mineral S"}, "cropNutrientDemand": {"header": {"title": "Crop {{nutrient}} demand", "subtitleN": "Depends on the target yield and {{nutrient}} uptake", "subtitleDefault": "Depends on the target yield and {{nutrient}} uptake or removal"}, "inputs": {"label": {"nutrientUptake": "{{nutrient}} uptake ({{-unit}})", "nutrientRemoval": "{{nutrient}} removal ({{-unit}})"}, "subtitle": "Enter required fields for the equation:", "tooltip": "Uptake or removal is considered based on whether the whole crop is harvested or only the grain.", "helperText": "Default value is 0."}, "equation": {"title": "Equations", "description": "Note: Target yield is provided by the farming-solution user.", "bodyN": "Crop {{nutrient}} demand = Target yield x <bold>{{nutrient}} uptake</bold>", "bodyDefault": "Crop {{nutrient}} demand = Target yield x <bold>{{nutrient}} uptake or removal</bold>"}}, "nutrientUseEfficiency": {"headerTitle": "{{nutrient}} use efficiency", "headerSubtitle": "How effectively the crop absorbs and uses available {{nutrient}}", "enterFields": "Enter the required field:", "inputLabelNUE": "{{nutrient}} use efficiency ({{unit}})"}, "soilOrganicMatterN": {"title": "Soil organic matter (SOM) N (optional)", "subtitle": "Fertiliser amount adjustments based on SOM analysis", "checkboxLabel": "Enable for use in fertiliser calculation", "somExpressionTitle": "To estimate SOM N, enter values in this expression:", "ifSomText": "If soil organic matter (SOM) > ", "inputs": {"labelSom": "SOM ({{-unit}})", "labelDefaultSom": "Default SOM ({{-unit}})"}, "thenSomText": ", then deduct", "forSomText": "for every", "defaultSomSubtitle": "Set a default SOM content:", "statusEnabled": "Enabled", "statusDisabled": "Disabled"}, "soilMineral": {"title": "Soil mineral {{nutrient}} (optional)", "subtitle": "Fertiliser amount adjustments based on soil analysis", "checkboxLabel": "Enable for use in fertiliser calculation", "inputSoilMineralTitle": "Set a default soil mineral {{nutrient}} content:", "inputSoilMineralLabel": "Default soil mineral {{nutrient}} ({{-unit}})", "isEditableLabel": "Editable by farming-solution users", "statusEnabled": "Enabled", "statusDisabled": "Disabled", "defaultSoilMineralTitle": "Enter the required field:", "defaultSoilMineralLabel": "Soil mineral {{nutrient}} ({{-unit}})"}, "soilCorrectionFactor": {"title": "Soil correction factor (optional)", "subtitle": "Fertiliser amount adjustments based on soil analysis", "checkboxLabel": "Enable for use in fertiliser calculation", "selectMethodTitle": "Select a method to adjust the fertiliser demand:", "statusEnabled": "Enabled", "statusDisabled": "Disabled", "multiplierCorrection": "Multiplier correction", "fixedCorrection": "Fixed quantity correction", "tableTitle": "Corrections per parameter level", "tableSubtitleMultiplier": "Increase or decrease fertiliser amount by a multiplication factor, based on soil analysis levels.", "tableSubtitleFixed": "Increase or decrease fertiliser amount by a fixed quantity, based on soil analysis levels.", "parameterLevel": "Parameter level", "strategy": "", "soilCorrectionParamLevelTable": {"header": {"parameterLevel": "Parameter level", "fertilisationStrategy": "Fertilisation strategy", "correctionTypeMultiplier": "Multiplier", "correctionTypeFixed": "Fixed quantity correction ({{-unit}})", "multiplier": "Multiplier"}}}, "preCrop": {"title": "Pre-crop N (optional)", "subtitle": "Fertiliser amount adjustments based on the previous crop", "checkboxLabel": "Enable for use in fertiliser calculation", "statusEnabled": "Enabled", "statusDisabled": "Disabled", "selectMethodTitle": "Select a method to adjust the fertiliser demand:", "preCropResidual": "Residual N", "preCropYield": "Residual N and yield", "tableTitleResidual": "Residual N per type of pre-crop", "tableTitle": "Residual N and default yield per type of pre-crop", "addPreCropButton": "Add pre-crop", "successDeleteMessage": "1 crop deleted", "addPreCropDialog": {"title": "Add pre-crop", "noPreCropsPlaceholder": "No crops available", "selectPlaceholder": "Crop", "residualPlaceholder": "Residual N ({{-unit}})", "yieldPlaceholder": "Default pre-crop yield ({{-unit}})", "errorMessage": "Crop is required"}, "preCropTable": {"deleteDialog": {"title": "Delete {{name}}?", "description": "This change is permanent and affects the nutrition plan."}, "infoDialog": {"title": "Cannot delete pre-crop", "description": "You cannot delete the default pre-crop.\n To delete this, set a different default pre-crop."}, "header": {"preCrop": "Pre-crop", "residual": "Residual N ({{-unit}})", "yield": "Default pre-crop yield ({{-unit}})", "action": "Action"}, "emptyState": "No pre-crop addedd yet."}, "setDefaultTitle": "Set a default pre-crop:", "setDefaultLabel": "Default pre-crop"}}, "productRecommendations": {"title": "Recommend products according to the splitting schedule", "soilApp": "Soil application", "foliarApp": "Foliar application", "lastUpdated": "Last updated by {{modifiedBy}} on {{date}}", "productRecommendationsTable": {"grStageName": "Growth stage", "grStageImg": "Growth stage image", "grStageNo": "Growth stage no.", "maximumAppRate": "Max application rate (kg/ha or l/ha)", "productType": "Product type", "rateRule": "Rate rule", "minimumAppRate": "Min application rate (kg/ha or l/ha)", "recommendedProducts": "Recommended products", "fixedApplicationRate": "Fixed application rate (kg/ha or l/ha)", "rateDefNutrients": "Rate-defining nutrient", "enterValueLabel": "Enter value", "solid": "Solid", "liquid": "Liquid", "foliar": "Foliar", "selectProductTypeLabel": "Select product type", "reminder": "Remainder {{rateRule}}", "n_splitting": "{{rateRule}} splitting rule", "addProducts": "Add products", "addProductsPopup": {"headerTitle": "Add products", "stage": "stage", "table": {"col1": "", "col2": "Product family", "col3": "Product name", "col4": "N (%)", "col5": "P (%)", "col6": "K (%)", "col7": "S (%)", "col8": "Mg (%)"}, "emptyState": "No products added yet at country level for Cereals MMM."}}}}, "fpDetails": {"title": "Configure {{selectedFeatureName}} for {{selectedCropSubclassName}} {{selectedCropDescriptionName}} in {{selectedRegionName}}, {{selectedCountryName}}", "fertigationPlan": {"title": "Fertigation Plan", "buttons": {"test": "Go to test harness", "configure": "Configure", "testTooltipMessage": "Test harness will be available soon. For now, directly validate the configuration.", "editTooltipMessage": "To edit the units, first invalidate the plan. After making changes, update the necessary configuration values."}, "content": {"title": "Crop nutrition through fertigation", "description": "You can configure analyses, demand calculations, splitting schedules and product recommendations."}, "validationSwitch": {"tooltipMessage": "Once validated, this configuration will be available to farming solutions.", "switchLabel": "Validate", "snackbarMessage": "Change saved", "validationPopup": {"title": "Invalidate this plan?", "description": "Once invalidated, this configuration can no longer be used by farming solutions.", "confirm": "Yes, invalidate", "cancel": "Cancel"}, "failedValidationPopup": {"title": "Can't validate the plan", "content": "There are errors in the configuration. \nPlease review and correct the highlighted fields before validating the plan.", "confirmButton": "Ok"}}}, "cropSettings": {"editButton": "Edit", "yieldUnit": "Yield unit", "defaultTargetYieldUnit": "Default target yield", "defaultTargetYieldUnitOpt": "Default target yield (optional)", "recommendationSolidsUnitPerArea": "Recommended solids unit per area", "recommendationLiquidsUnitPerArea": "Recommended liquids unit per area", "nutrientDemandUnit": "Nutrient demand unit", "recommendationSolidsUnitPerPlant": "Recommended solids unit per plant", "recommendationLiquidsUnitPerPlant": "Recommended liquids unit per plant", "totalIrrigationWaterUnit": "Irrigation water unit", "plantDensityUnit": "Plant density unit", "nutrientRemovalUnit": "Nutrient removal unit", "partnersTitle": "Partners for this crop", "settingsTitle": "Settings", "partnersCaption": "To add partners, go to the list of crops in Overview, or Crops & features", "location": "{{selectedLocation}}", "selectedCropName": "Edit {{cropName}} settings: Units", "yieldUnitHelperText": "Yield unit contributes to the other units, so any changes will revert the selected values.", "yieldUnitErrorText": "Yield unit not available for this crop. Contact a global digital agronomist.", "recommendedSolidPerAreaUnitErrorText": "No units available for this yield unit. Select another yield unit or contact an global digital agronomist.", "defaultTargetYieldHelperText": "Recommended to add country default. Editable by farming-solution users.", "nutrientRemovalUnitHelperText": "This is based on yield and recommended solids unit per area.", "nutrientDemandUnitHelperText": "This is based on yield and recommended solids unit per area.", "saveChangesButtonDisableTooltip": "Fill in all required fields. If you need any help contact a global digital agronomist.", "confirmationDialogTitle": "Unit Change Impact", "confirmationDialogContent": "Changing the units has affected some configuration values. Review the plan to ensure accurate calculations."}, "planConfiguration": {"tabs": {"soilAnalysis": "Soil analysis", "leafAnalysis": "Leaf analysis", "waterAnalysis": "Water analysis", "demandCalculations": "Demand calculations", "splittingSchedule": "Splitting schedule", "productRecommendations": "Product recommendations"}, "pages": {"splittingSchedule": {"calendarHelperText": "Beginning of the first growth phase", "table": {"headers": {"growthPhaseNo": "Growth phase no.", "growthPhaseName": "Growth phase name", "growthPhaseDuration": "Duration (days)", "growthPhaseTotalDays": "days", "growthPhaseImage": "Growth phase image", "growthPhaseNutrient": "Nutrient", "growthPhaseNutrientTotalPercent": "Total (%)", "growthPhaseNutrientSplitPercent": "Split (%)", "n": "N", "p": "P", "k": "K", "cu": "<PERSON><PERSON>", "ca": "Ca", "mn": "Mn", "fe": "Fe", "mg": "Mg", "mo": "Mo", "zn": "Zn", "s": "S", "b": "B", "addImageButton": "Add image", "showMoreButton": "Show more", "showLessButton": "Show less", "dialog": {"title": "Delete split for growth phase {{growthPhaseNo}}?", "description": "It will be permanently deleted from the splitting schedule."}, "exceedsThresholdWarningMessage": "{{property}} cannot exceed {{threshold}} in total", "totalNutrientsTooltipMessage": "Must equal 100"}, "addImageDialog": {"title": "Add growth phase image", "noImageName": "No image", "saveBtn": "Save"}, "title": "Split fertiliser nutrient application across the growing season", "addSplitButton": "Add split"}}}}, "soilAnalysis": {"soilParamsList": {"title": "Soil parameters", "description": "Parameter levels defined here are used in fertiliser calculations.", "addTitle": " soil analysis methods: {{count}}", "addAnalysisMethod": {"title": "Add analysis method", "autofillDetails": {"title": "Autofill details", "from": "Autofill details from:", "existingAnalysisMethod": "Existing analysis method", "loadingAnalysisMethod": "Loading analysis method...", "to": "To:", "newAnalysisMethod": "New analysis method", "isRequired": "{{<PERSON><PERSON>eth<PERSON>}} is required"}, "manuallyFillDetails": {"title": "Manually fill details"}, "saveButton": "Save", "analysisMethodCreated": "Changes saved", "analysisMethodCreationError": "Could not save changes"}, "dropdownItems": {"addTitle": "Add analysis method", "deleteTitle": "Delete methods", "deleteSubtitle": "At least one analysis method is required."}, "emptyStateText": "No nutrition parameters for Soil analysis and Demand calculations added yet."}, "analysisBase": "Soil analysis unit", "nutritionParamTitle": "Soil parameter levels", "nutritionParamSubTitle": "Set ranges for each parameter level.", "parameterLevelsTable": {"dialog": {"title": "Delete {{level}}?", "description": "It will also be permanently deleted from Demand calculations. Other parameter levels will not be affected.", "info": {"description": "You cannot delete the default. \nTo delete this, set a different default.", "title": "Cannot delete level"}, "deleteMessageLevel": "Level deleted", "updateMessageLevel": "Level changed", "updateMessageValue": "Value changed", "addAnotherLevel": "{{actionName}} another level", "parameterLevel": "Parameter level", "greaterOrEqual": "Greater than or equal to (>=)", "lowerThan": "Less than (<)", "setAsDefault": "Set as default? (optional)", "yesSetAsDefault": "Yes, set as default", "selectError": "Parameter level is required", "paramValues": {"title": "Update values", "description": {"nextUpperLimit": "The upper limit for {{level}} must be lower than the next upper limit in the {{analysisType}} nutrient classification.", "previousUpperLimit": "The upper limit for {{level}} must be higher than the previous upper limit in the {{analysisType}} nutrient classification."}}}, "header": {"default": "Set as default", "parameterLevel": "Parameter level", "greaterThan": "Greater than or equal to (>=)", "lessThan": "Less than (<)", "action": "Action"}, "addLevelTableButton": "Add level", "nutrientClassificationRadioButtonLabel": "Select {{nutrientClassification}}"}, "switchTooltipMsg": "This setting is not yet available. For now, directly configure your selected parameter's levels.", "switchLabel": "Consider secondary parameters", "defaultActiveMsg": "This adjusts your selected parameter’s levels based on the effects of secondary parameters in the soil."}, "waterAnalysis": {"levelChangedSuccessfully": "Level changed", "levelDeletedSuccessfully": "Level deleted", "levelAddedSuccessfully": "{{ level }} level added", "nutrients": {"title": "Water parameters", "emptyStateText": "No nutrition parameters for Water analysis added yet."}, "errors": {"incorrectMessageLength": "Must not exceed 120 characters"}, "analysisBase": "Water analysis unit", "nutritionParamTitle": "Water parameter levels", "parameterLevelsTable": {"header": {"default": "Set as default", "parameterLevel": "Parameter level", "greaterThan": "Greater than or equal to (>=)", "lessThan": "Less than (<)", "action": "Action", "warning": "Warning (optional)"}, "dialog": {"description": "It will also be permanently deleted from Demand calculations. Other parameter levels will not be affected.", "info": {"description": "You cannot delete the default. \nTo delete this, set a different default.", "title": "Cannot delete level"}, "title": "Delete {{level}}?", "deleteMessageLevel": "Level deleted", "updateMessageLevel": "Level changed", "updateMessageValue": "Value changed", "addAnotherLevel": "{{actionName}} another level", "parameterLevel": "Parameter level", "greaterOrEqual": "Greater than or equal to (>=)", "lowerThan": "Less than (<)", "setAsDefault": "Set as default? (optional)", "yesSetAsDefault": "Yes, set as default", "selectError": "Parameter level is required", "paramValues": {"title": "Update values", "description": {"nextUpperLimit": "The upper limit for {{level}} must be lower than the next upper limit in the {{analysisType}} nutrient classification.", "previousUpperLimit": "The upper limit for {{level}} must be higher than the previous upper limit in the {{analysisType}} nutrient classification."}}}, "addLevelTableButton": "Add level", "nutrientClassificationRadioButtonLabel": "Select {{nutrientClassification}}"}, "nutritionParamSubTitle": "Set ranges for each parameter level. Warnings are for farming-solution users."}, "leafAnalysis": {"leafParamsList": {"title": "Leaf parameters", "emptyStateText": "No nutrition parameters for Leaf analysis and Demand calculations added yet."}, "analysisBase": "Leaf analysis unit", "nutritionParamTitle": "Leaf parameter levels", "nutritionParamSubTitle": "Set ranges for each parameter level.", "parameterLevelsTable": {"dialog": {"title": "Delete {{level}}?", "description": "It will also be permanently deleted from Demand calculations. Other parameter levels will not be affected.", "info": {"description": "You cannot delete the default. \nTo delete this, set a different default.", "title": "Cannot delete level"}, "deleteMessageLevel": "Level deleted", "updateMessageLevel": "Level changed", "updateMessageValue": "Value changed", "addAnotherLevel": "{{actionName}} another level", "parameterLevel": "Parameter level", "greaterOrEqual": "Greater than or equal to (>=)", "lowerThan": "Less than (<)", "setAsDefault": "Set as default? (optional)", "yesSetAsDefault": "Yes, set as default", "selectError": "Parameter level is required", "paramValues": {"title": "Update values", "description": {"nextUpperLimit": "The upper limit for {{level}} must be lower than the next upper limit in the {{analysisType}} nutrient classification.", "previousUpperLimit": "The upper limit for {{level}} must be higher than the previous upper limit in the {{analysisType}} nutrient classification."}}}, "header": {"default": "Set as default", "parameterLevel": "Parameter level", "greaterThan": "Greater than or equal to (>=)", "lessThan": "Less than (<)", "action": "Action"}, "addLevelTableButton": "Add level", "nutrientClassificationRadioButtonLabel": "Select {{nutrientClassification}}"}}, "demandCalculations": {"title": "Demand calculations", "mainEquation": {"title": "Main equation", "description": "The algorithm uses this equation to calculate fertiliser nutrient demand. Some inputs are optional.", "equation": "Fertiliser nutrient demand = ((Crop nutrient demand / Nutrient use efficiency)\n                             - Irrigation water nutrient) x Soil correction factor"}, "nutrientForm": {"title": "Nutrient form", "areaLabel": "Select Nutrient form", "description": "Calculations are done for this nutrient form.", "tooltipText": "For changing the nutrient form, you need to invalidate the fertigation plan."}, "nutrientList": {"title": "Nutrient", "emptyStateText": "No nutrition parameters for Soil analysis and Demand calculations added yet."}, "nutrientUseEfficiency": {"headerTitle": "Nutrient use efficiency (NUE)", "headerSubtitle": "Depends on the type of fertigation system", "equationText": "Equation", "formulaNue": "Nutrient use efficiency = ", "formulaValues": "Maximum NUE x Fertigation system NUE", "enterFields": "Enter required fields for the equation:", "maximumNue": "Maximum NUE", "fertSystem": "Fertigation system NUE", "nue": "NUE", "drip": "<PERSON><PERSON>", "microSprinkler": "Micro-sprinkler", "sprinkler": "Sprinkler", "pivot": "Pivot", "dryApplication": "Dry application"}, "irrigationWaterNutrient": {"headerTitle": "Irrigation water nutrient (optional)", "headerSubtitle": "Fertiliser amount adjustments based on water analysis", "isEnabledText": "Enable for use in fertiliser calculation", "equationText": "Equation", "enterFields": "Enter required fields for the equation:", "boldText": "Irrigation system efficiency", "formulaIWN": "Irrigation water nutrient = [Total irrigation water x (Nutrient from water analysis \n                            x Nutrient form conversion factor) / 1000] \n                            x (", "tableHeadText": "Irrigation system efficiency per fertigation system ", "drip": "<PERSON><PERSON>", "microSprinkler": "Micro-sprinkler", "sprinkler": "Sprinkler", "pivot": "Pivot"}, "soilCorrectionFactor": {"paramTableSubTitle": "Increase or decrease fertiliser amount by a multiplication factor, based on soil analysis levels.", "checkboxLabel": "Enable for use in fertiliser calculation", "soilCorrectionParamLevelTable": {"header": {"parameterLevel": "Parameter level", "correctionTypeMultiplier": "Multiplier", "multiplier": "Multiplier"}}, "title": "Soil correction factor (optional)", "subtitle": "Fertiliser amount adjustments based on soil analysis", "statusDisabled": "Disabled", "statusEnabled": "Enabled", "tableTitle": "Corrections per parameter level", "tableSubtitleFixed": "Increase or decrease fertiliser amount by a multiplication factor, based on soil analysis levels."}, "cropNutrientDemand": {"header": {"title": "Crop nutrient demand", "subtitle": "For both vegetative and harvested parts of the plant"}, "inputs": {"label": {"nutrientRemoval": "Nutrient removal ({{-unit}})", "nutrientUptake": "Vegetative demand ({{-unit}})"}, "subtitle": "Enter required fields for the equation:", "helperText": "Default value is 0."}, "equation": {"title": "Equations", "description": "Note: Target yield is provided by the farming-solution user.", "body": "Crop nutrient demand = <bold>Vegetative demand</bold> + Reproductive demand,\nwhere Reproductive demand = Target yield x <bold>Nutrient removal</bold>"}}}, "productRecommendations": {"title": "Recommend products according to the splitting schedule", "soilApp": "Soil application", "foliarApp": "Foliar application", "productRecommendationsTable": {"grPhaseNo": "Growth phase no.", "grPhaseNoTooltip": "Last <NAME_EMAIL> on 12 Sep 2023", "grPhaseName": "Growth phase name", "growthPhase": "Growth phase", "grPhaseImg": "Growth phase image", "recommendedProducts": "Recommended products", "appMethod": "Application method", "appCondition": "Application condition", "rateRule": "Rate rule", "fixedAppRate": "Fixed application\nrate ({{massUnit}} or {{volumeUnit}})", "fixedAppRateDescription": "Fixed application rate", "fixedAppRateTooltip": "This can only be applied when there is one recommended product. Select the fixed rate rule.", "dynamicMethod": "Dynamic application rate method", "rateDefNutrients": "Rate-defining nutrients", "rateDefNutrientsTooltip": "These nutrients directly affect the application rate. You can only add nutrients that have validity expressions.", "maxAppRate": "Max application rate\n({{massUnit}} or {{volumeUnit}})", "minAppRate": "Min application rate\n({{massUnit}} or {{volumeUnit}})", "appOfProduct": "Application of product/\nplant ({{massUnit}} or {{volumeUnit}})", "nutrientSplitsOption": "Nutrient splits", "appOfProductPlantOption": "Application of product/plant", "grPhasePrefix": "Growth phase", "addProducts": "Add products", "selectAppMethodLabel": "Select method", "noConditionRequired": "No condition required", "selectRateRuleLabel": "Select rate rule", "fixedRateSelect": "Fixed application rate", "dynamicRateSelect": "Dynamic application rate", "enterValueLabel": "Enter value", "selectRateMethodLabel": "Select rate method", "selectNutrientsLabel": "Select nutrients", "baseDressing": "Base dressing", "sideDressing": "Side dressing", "liquid": "Liquid", "drench": "<PERSON><PERSON><PERSON>", "fertigation": "Fertigation", "duplicate": "Duplicate", "delete": "Delete", "addProductsPopup": {"headerTitle": "Add products", "headerSubTitle": "You can select multiple products only for a dynamic rate based on nutrient splits.", "growthPhase": "Growth phase {{growthPhaseNo}}", "table": {"col1": "", "col2": "Product family", "col3": "Product name", "col4": "Partners"}}, "resetProductDialog": {"title": "Continue with this selection?", "description": "Your previous product selection will be cleared and you will need to reselect a single product.", "yes": "Yes, continue", "cancel": "Cancel"}, "applicationConditionBuilder": {"shouldBeApplied": {"title": "Should the products be applied under any specific condition?", "yes": "Yes, add condition", "no": "No condition required"}, "title": "Application condition", "conditionBuilder": {"title": "Add application condition", "parameter": {"label": "Parameter", "ariaLabel": "Application condition parameter select", "orgM": "Organic matter", "waterPh": "Water pH", "soilPh": "Soil pH"}, "value": {"ariaLabel": "Application condition value", "label": "Value", "name": "Value"}, "operator": {"ariaLabel": "Application condition operator select", "greaterThan": ">", "lowerThan": "<", "greaterOrEqual": ">=", "lowerOrEqual": "<="}, "expand": "Expand", "shorten": "<PERSON>en", "logicalOperator": {"ariaLabel": "Application condition logical operator", "and": "And", "or": "Or"}}, "saveBtn": "Save changes"}}}}, "demandCalculations": {"emptyState": {"title": "Add nutrition parameters", "message": "You are yet to add nutrition parameters in Soil analysis.\n Once added, you can update their demand details here.", "copy": "Copy all", "nutritionParameters": "Nutrition parameters", "actionButtonText": "Go to Soil analysis"}}, "globalSettings": {"navigationCaption": "Manage the availability of features and configurations for all countries.", "sideMenu": {"crops": "Crops", "units": "Units", "soilAndLeaf": "Soil and leaf", "features": "Features", "partners": "Partners", "countriesAndRegions": "Countries and regions"}, "common": {"checkLink": "Checking for linked data...", "confirmDeleteDescription": "It will be permanently deleted from Polaris and unavailable to countries.", "confirmDelete": "Delete {{resourceName}}?", "cannotDelete": "Cannot delete \n {{resourceName}}", "cannotDeleteDescription": "It is in use in one or more locations.", "viewLinkedLocations": "View linked locations", "viewErrorLocations": "View error locations"}, "features": {"header": "Manage features", "addFeature": "Add feature", "totalFeatures": "Features", "switchLabel": "Show more details", "table": {"headers": {"featureCode": "Feature Code", "featureID": "Feature ID", "feature": "Feature", "featureName": "Feature name", "displayName": "Display name", "group": "Feature group", "lastUpdated": "Last updated", "action": "Action"}, "actions": {"edit": "Edit feature details", "delete": "Delete from Polaris"}}, "addFeatureModal": {"title": "Create feature", "inputIdHelperText": "Generate a version 4 UUID from uuidgenerator.net.", "displayNameHint": "To refer to the partner within Polaris", "snackBarSuccessMessage": "{{ displayName }} created", "errors": {"uuid4": "Must be a version 4 UUID", "alreadyExistsId": "Feature ID already exists. Regenerate UUID."}, "featureName": "polaris.globalSettings.features.addFeatureModal.featureName", "featureId": "polaris.globalSettings.features.addFeatureModal.featureId", "displayName": "polaris.globalSettings.features.addFeatureModal.displayName"}, "editFeature": {"successMessage": "Changes saved to feature"}}, "partners": {"header": "Manage partners", "partners": "Partners", "switchLabel": "Show more details", "successMsg": "Changes saved to partner", "editPartner": "Edit partner", "displayNameHint": "To refer to the partner within Polaris ", "headers": {"partnerCode": "Partner code", "partnerId": "Partner ID", "partner": "Partner", "partnerName": "Partner name", "displayName": "Display name", "partnerType": "Partner type", "countries": "Countries", "features": "Features", "lastUpdated": "Last updated", "actions": "Actions"}, "content": {"internal": "Internal", "external": "External"}, "actions": {"edit": "Edit partner details", "delete": "Delete from Polaris"}, "addPartner": {"title": "Add Partner", "inputIdHelperText": "Generate a version 4 UUID from uuidgenerator.net.", "inputDisplayNameHelperText": "To refer to the partner within Polaris", "snackBarSuccessMessage": "{{ displayName }} added", "errors": {"uuid4": "Must be a version 4 UUID", "alreadyExists": "Partner ID already exists. Regenerate UUID."}}}}}, "expressionBuilder": {"title": "Expression", "editButton": "Edit expression", "editMode": {"title": "Expression builder"}, "subtitle": "Last updated by {{modifiedBy}} on {{modifiedDate}}"}, "demandCalculations": {"emptyState": {"title": "Add nutrition parameters", "message": "You are yet to add nutrition parameters in Soil analysis.\n Once added, you can update their demand details here.", "copy": "Copy all", "nutritionParameters": "Nutrition parameters", "actionButtonText": "Go to Soil analysis"}}, "error": {"wentWrongTitle": "Something went wrong!"}, "Loamy clay": "Loamy clay", "Loamy sand": "Loamy sand", "Peat soil": "Peat soil", "Sandy": "<PERSON>", "Sandy loam": "Sandy loam", "Silty clay loam": "Silty clay loam", "Not applicable": "Not applicable", "Clay": "<PERSON>", "clay loams": "clay loams", "Loam": "Loam", "Sandy clay": "Sandy clay", "Silt": "Silt", "Silt loam": "Silt loam", "Sandy clay loam": "Sandy clay loam", "Silt Clay": "Silt <PERSON>", "Silty clay": "Silty clay", "Trailing shoe/hose": "Trailing shoe/hose", "Splash plate": "Splash plate", "Injection": "Injection"}