import {
  BaseUnit,
  CropSettings,
  GrowthScale,
  YieldUnit,
  ConfigurationType,
  ValidationStatus,
} from '@common/types';

export const yieldSolidUnitsResponse: YieldUnit[] = [
  {
    id: '37e43c19-e008-4291-9580-71ce094680c4',
    name: 'caqq/ha',
    tags: 'YieldUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const allUnitsResponse: BaseUnit[] = [
  {
    id: '37e43c19-e008-4291-9580-71ce094680c4',
    name: 'caqq/ha',
    tags: 'YieldUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const growthScalesResponse: { data: GrowthScale[] } = {
  data: [
    {
      id: '74255580-9feb-4fc2-a5ee-7635a4161bbf',
      name: 'BBCH - Cereals',
      abbreviatedName: 'BBCH - Cereals',
      translationKey: 'growthscale.bbch_cereals',
      mediaUri: [],
      modifiedBy: '<EMAIL>',
      created: new Date('2019-01-08T17:00:26.990Z'),
      modified: new Date('2021-03-30T13:17:24.763Z'),
      deleted: null,
    },
  ],
};

export const fertigationUnitSettingsResponse: CropSettings = {
  id: 'ff5d1c74-1df3-4fdb-ac04-36b741946914',
  countryId: '3e4964c8-22fd-4ea5-a209-65809fba3c34',
  cropRegionId: '8709a4a2-093f-475b-a897-fb2c304b2ebf',
  configuration: {
    data: {
      yieldUnitId: null,
      defaultTargetYield: 1,
      nutrientRemovalUnitId: null,
      nutrientDemandUnitId: null,
      recommendedSolidPerAreaUnitId: null,
      recommendedLiquidPerAreaUnitId: null,
      recommendedSolidPerPlantUnitId: null,
      recommendedLiquidPerPlantUnitId: null,
      totalIrrigationWaterUnitId: null,
    },
  },
  created: new Date('2025-02-24T12:00:54.868Z'),
  modified: new Date('2025-02-24T12:00:54.868Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
};

export const fertigationMMMValidationRespone = {
  id: 'ff5d1c74-1df3-4fdb-ac04-36b741946914',
  countryId: '3e4964c8-22fd-4ea5-a209-65809fba3c34',
  cropRegionId: '8709a4a2-093f-475b-a897-fb2c304b2ebf',
  validationStatus: ValidationStatus.VALIDATED,
  configurationType: ConfigurationType.Fertigation,
  created: new Date('2025-02-24T12:00:54.868Z'),
  modified: new Date('2025-02-24T12:00:54.868Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
};

export const FertigationYieldUnitResponse: BaseUnit[] = [
  {
    id: '394e8163-636e-48dd-8ded-a9fd6436d335',
    name: 't/ha',
    tags: 'FertigationYieldUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationRecommendedSolidsPerAreaUnitResponse: BaseUnit[] = [
  {
    id: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    name: 'kg/ha',
    tags: 'FertigationMinSolProdPerAreaUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationRecommendedLiquidsPerAreaUnitResponse: BaseUnit[] = [
  {
    id: '75121b51-a54d-4140-be59-01a74b638fcb',
    name: 'l/ha',
    tags: 'FertigationMinLiqProdPerAreaUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationNutrientRemovalUnitResponse: BaseUnit[] = [
  {
    id: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
    name: 'kg/t',
    tags: 'NutrientRemovalUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationNutrientDemandUnitResponse: BaseUnit[] = [
  {
    id: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    name: 'kg/ha',
    tags: 'FertigationNutrientDemandUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationRecommendedSolidsPerPlantUnitResponse: BaseUnit[] = [
  {
    id: '9fda0dbc-4a17-490f-be0b-afec56f55eb0',
    name: 'g/plant',
    tags: 'FertigationMinSolProdPerPlantUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationRecommendedLiquidsPerPlantUnitResponse: BaseUnit[] = [
  {
    id: '60e45b10-5c9f-48c8-b429-66c0f87a3b63',
    name: 'ml/plant',
    tags: 'FertigationMinLiqProdPerPlantUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationTotalIrrigationWaterUnitResponse: BaseUnit[] = [
  {
    id: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    name: 'kg/ha',
    tags: 'FertigationTotalIrrigationWaterUnit',
    translationKey: null,
    parentId: null,
    precision: null,
    created: '2024-01-29T13:44:53.200Z',
    modified: '2024-01-29T13:44:53.200Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
  },
];

export const FertigationPlantDensityUnitResponse: BaseUnit[] = [
  {
    translationKey: 'unit.plants_per_ha',
    parentId: null,
    created: '2021-02-22T17:06:28.588Z',
    modified: '2025-05-15T13:52:12.796Z',
    modifiedBy: '<EMAIL>',
    deleted: null,
    id: '135afc3b-3730-459a-a318-98b5265f67c5',
    name: 'plants/ha',
    tags: 'CftUnit,DensityUnit,FertigationPlantDensityUnit',
    precision: null,
  },
];

export const fertigationUnitSettingsResponseValid: CropSettings = {
  ...fertigationUnitSettingsResponse,
  configuration: {
    data: {
      yieldUnitId: FertigationYieldUnitResponse[0].id,
      defaultTargetYield: 1,
      nutrientRemovalUnitId: FertigationNutrientRemovalUnitResponse[0].id,
      recommendedSolidPerAreaUnitId: FertigationRecommendedSolidsPerAreaUnitResponse[0].id,
      recommendedLiquidPerAreaUnitId: FertigationRecommendedLiquidsPerAreaUnitResponse[0].id,
      recommendedSolidPerPlantUnitId: FertigationRecommendedSolidsPerPlantUnitResponse[0].id,
      recommendedLiquidPerPlantUnitId: FertigationRecommendedLiquidsPerPlantUnitResponse[0].id,
      nutrientDemandUnitId: FertigationNutrientDemandUnitResponse[0].id,
      totalIrrigationWaterUnitId: FertigationTotalIrrigationWaterUnitResponse[0].id,
      plantDensityUnitId: FertigationPlantDensityUnitResponse[0].id,
    },
  },
};
