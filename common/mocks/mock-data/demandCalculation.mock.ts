import { ParameterLevelEnum } from '@common/constants';
import {
  FertigationUseEfficiency,
  DemandCalculation,
  FertigationDemandCalculationModule,
  FertigationIrrigationWaterNutrient,
  CerealsUseEfficiency,
  CerealsMultipleCorrectionItem,
  CerealsFixedQuantityCorrectionItem,
  ConfigurationType,
  DemandCalculationModule,
  CerealsDemandCalculationModule,
} from '@common/types';

const mockUseEfficiency: FertigationUseEfficiency = {
  maximumNUE: 0,
  dripNUE: 100,
  microSprinklerNUE: 100,
  sprinklerNUE: 100,
  pivotNUE: 100,
  dryApplicationNUE: 100,
  unitIdNUE: '64d915ec-e4ed-437f-aad3-f9ac2ca92373',
};

const mockIrrigationWaterNutrient: FertigationIrrigationWaterNutrient = {
  dripIWN: 100,
  microSprinklerIWN: 100,
  sprinklerIWN: 100,
  pivotIWN: 100,
  unitIdIWN: '64d915ec-e4ed-437f-aad3-f9ac2ca92373',
};

export const mockUseEfficiencyModule: DemandCalculationModule = {
  id: 'c1df9084-5f43-4e60-a80b-916f8fcac1d4',
  demandCalculationId: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  name: FertigationDemandCalculationModule.USE_EFFICIENCY,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: mockUseEfficiency,
  },
  created: new Date('2024-10-31T07:49:54.719Z'),
  modified: new Date('2024-10-31T07:49:54.719Z'),
  modifiedBy: '<EMAIL>',
};

export const mockIrrigationWaterNutrientModule: DemandCalculationModule = {
  id: 'c1df9084-5f43-4e60-a80b-916f8fcac1d4',
  demandCalculationId: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  name: FertigationDemandCalculationModule.IRRIGATION_WATER_NUTRIENT,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: mockIrrigationWaterNutrient,
  },
  created: new Date('2024-10-31T07:49:54.719Z'),
  modified: new Date('2024-10-31T07:49:54.719Z'),
  modifiedBy: '<EMAIL>',
};

export const mockNutrientDemandModule: DemandCalculationModule = {
  id: '77cc2c1e-46da-4a4e-938e-7db8f1236d5d',
  demandCalculationId: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  name: FertigationDemandCalculationModule.CROP_DEMAND,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: {
      uptakeValue: 0.222,
      removalValue: 0,
      uptakeUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
      removalUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    },
  },
  created: new Date('2024-11-05T14:40:11.783Z'),
  modified: new Date('2024-11-08T11:12:24.832Z'),
  modifiedBy: '<EMAIL>',
};

export const mockDemandCalculationFertigation: DemandCalculation = {
  created: new Date('2023-01-01T00:00:00.000Z'),
  modified: new Date('2023-01-01T00:00:00.000Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
  id: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  countryId: '3e4964c8-22fd-4ea5-a209-65809fba3c34',
  cropRegionId: '2a7d2c6f-5e2a-4afb-b672-f2ff5fe779d9',
  nutrientId: '7a1d7c09-13fa-4ea0-b72b-8290663c31d5',
  elementFormId: 'ae2132a8-f1e6-4170-877c-ab8ccc2c6656',
  configurationType: ConfigurationType.Fertigation,
  demandCalculationModules: [mockUseEfficiencyModule, mockNutrientDemandModule],
};

export const soilCorrectionFactorMockData: DemandCalculationModule = {
  id: 'c1df9084-5f43-4e60-a80b-916f8fcac1d4',
  demandCalculationId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  isMandatory: true,
  isEnabled: false,
  name: FertigationDemandCalculationModule.SOIL_CORRECTION_FACTOR,
  configuration: {
    data: {
      multiplierCorrection: [
        {
          id: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c611',
          parameterLevel: ParameterLevelEnum.VERY_LOW,
          extractorId: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c933',
          multiplierValue: 0,
        },
        {
          id: 'c4c10644-5dc1-4177-b2bb-037e883431da',
          parameterLevel: ParameterLevelEnum.LOW,
          extractorId: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c933',
          multiplierValue: 0,
        },
        {
          id: '297d7039-e7b9-44e0-88b3-269aba7cfd1b',
          parameterLevel: ParameterLevelEnum.MEDIUM,
          extractorId: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c933',
          multiplierValue: 0,
        },
        {
          id: '1936e875-0f7b-4c2f-ac11-e7f9b467c5f2',
          parameterLevel: ParameterLevelEnum.HIGH,
          extractorId: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c933',
          multiplierValue: 0,
        },
        {
          id: '1254e544-e1ec-4245-80b6-919079df84a9',
          parameterLevel: ParameterLevelEnum.VERY_HIGH,
          extractorId: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c933',
          multiplierValue: 0,
        },
      ],
    },
  },
};

const mockCerealsUseEfficiency: CerealsUseEfficiency = {
  useEfficiency: 0,
  useEfficiencyUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664', //kg/t is default
};

export const mockCerealsSoilMineralModule: DemandCalculationModule = {
  id: '77cc2c1e-46da-4a4e-938e-7db8f1236d6d',
  demandCalculationId: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  name: CerealsDemandCalculationModule.SoilMineral,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: {
      defaultSoilMineral: 1,
      soilMineralUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
      isEditable: false,
    },
  },
  created: new Date('2024-11-05T14:40:11.783Z'),
  modified: new Date('2024-11-08T11:12:24.832Z'),
  modifiedBy: '<EMAIL>',
};

export const mockCerealsUseEfficiencyModule: DemandCalculationModule = {
  id: 'c1df9084-5f43-4e60-a80b-916f8fcac1d4',
  demandCalculationId: '5ee231b1-f617-4ddc-a9d6-5b636b8adec3',
  name: CerealsDemandCalculationModule.UseEfficiency,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: mockCerealsUseEfficiency,
  },
  created: new Date('2024-10-31T07:49:54.719Z'),
  modified: new Date('2024-10-31T07:49:54.719Z'),
  modifiedBy: '<EMAIL>',
};

export const mockCerealsNutrientDemandModule: DemandCalculationModule = {
  id: '77cc2c1e-46da-4a4e-938e-7db8f1236d5d',
  demandCalculationId: '5ee231b1-f617-4ddc-a9d6-5b636b8adec3',
  name: CerealsDemandCalculationModule.CropDemand,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: {
      uptakeValue: 0.222,
      removalValue: 0,
      uptakeUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
      removalUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
    },
  },
  created: new Date('2024-11-05T14:40:11.783Z'),
  modified: new Date('2024-11-08T11:12:24.832Z'),
  modifiedBy: '<EMAIL>',
};

export const mockCerealsSoilOrganicMatterModule: DemandCalculationModule = {
  id: '77cc2c1e-46da-4a4e-938e-7db8f1236d7d',
  demandCalculationId: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  name: CerealsDemandCalculationModule.SoilOrganicMatter,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: {
      somThreshold: 1,
      somThresholdUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
      deduct: 1,
      deductUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
      somEvery: 1,
      somEveryUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
      somDefault: 1,
      somDefaultUnitId: '2429df29-4f5e-4cdb-b02a-25c7e3693664',
    },
  },
  created: new Date('2024-11-05T14:40:11.783Z'),
  modified: new Date('2024-11-08T11:12:24.832Z'),
  modifiedBy: '<EMAIL>',
};

export const multiplierCorrection: CerealsMultipleCorrectionItem[] = [
  {
    id: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c611',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'VERY_LOW',
    multiplierValue: 2.222,
    fertilisationStrategy: 'Correction',
  },
  {
    id: 'c4c10644-5dc1-4177-b2bb-037e883431da',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'LOW',
    multiplierValue: 1,
    fertilisationStrategy: 'Correction',
  },
  {
    id: '297d7039-e7b9-44e0-88b3-269aba7cfd1b',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'MEDIUM',
    multiplierValue: 0,
    fertilisationStrategy: 'Maintenance',
  },
  {
    id: '1936e875-0f7b-4c2f-ac11-e7f9b467c5f2',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'HIGH',
    multiplierValue: 0,
    fertilisationStrategy: 'Replacement',
  },
  {
    id: '1254e544-e1ec-4245-80b6-919079df84a9',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'VERY_HIGH',
    multiplierValue: 0,
    fertilisationStrategy: 'No application',
  },
];

export const fixedQuantityCorrection: CerealsFixedQuantityCorrectionItem[] = [
  {
    id: 'd89f193e-c64c-4ec8-bc5c-3db9ecc0c611',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'VERY_LOW',
    fertilisationStrategy: 'Correction',
    quantityCorrectionValue: 0,
    quantityCorrectionUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  },
  {
    id: 'c4c10644-5dc1-4177-b2bb-037e883431da',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'LOW',
    fertilisationStrategy: 'Correction',
    quantityCorrectionValue: 0,
    quantityCorrectionUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  },
  {
    id: '297d7039-e7b9-44e0-88b3-269aba7cfd1b',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'MEDIUM',
    fertilisationStrategy: 'Maintenance',
    quantityCorrectionValue: 0,
    quantityCorrectionUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  },
  {
    id: '1936e875-0f7b-4c2f-ac11-e7f9b467c5f2',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'HIGH',
    fertilisationStrategy: 'Replacement',
    quantityCorrectionValue: 0,
    quantityCorrectionUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  },
  {
    id: '1254e544-e1ec-4245-80b6-919079df84a9',
    extractorId: '8d81c154-c686-4d00-89c3-ab21b2851c78',
    parameterLevel: 'VERY_HIGH',
    fertilisationStrategy: 'No application',
    quantityCorrectionValue: 0,
    quantityCorrectionUnitId: '53f8ea06-3244-4a0f-ae83-6914bac5f235',
  },
];

export const mockCerealsSoilCorrectionFactorModule: DemandCalculationModule = {
  id: '77cc2c1e-46da-4a4e-938e-7db8f1236d7d',
  demandCalculationId: '5eeee1b1-f617-4ddc-a9d6-5b636b8adec5',
  name: CerealsDemandCalculationModule.SoilCorrectionFactor,
  isMandatory: true,
  isEnabled: false,
  configuration: {
    data: {
      isFixed: false,
      multiplierCorrection: multiplierCorrection,
      fixedQuantityCorrection: fixedQuantityCorrection,
    },
  },
  created: new Date('2024-11-05T14:40:11.783Z'),
  modified: new Date('2024-11-08T11:12:24.832Z'),
  modifiedBy: '<EMAIL>',
};

export const mockCerealsPreCropResiduals = [
  {
    id: 'c687c9d6-3ac3-4994-bb9a-1b39db44a3de',
    cropId: 'e09eb7bb-f0ec-4f4b-aa06-e935c3f659c2',
    residualValue: 0,
    residualUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
  },
];

export const mockCerealsPreCropYields = [
  {
    id: '91cfc670-3f48-49d1-85c4-9ac7c7a5a9bc',
    cropId: 'e09eb7bb-f0ec-4f4b-aa06-e935c3f659c2',
    residualValue: 0,
    residualUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
    preCropYield: 0,
    preCropYieldUnitId: '394e8163-636e-48dd-8ded-a9fd6436d335',
  },
];

export const mockCerealsPreCrop = {
  isResidual: true,
  residuals: mockCerealsPreCropResiduals,
  residualsWithYield: mockCerealsPreCropYields,
  residualDefaultPreCropId: '394e8163-636e-48dd-8ded-a9fd6436d335',
  residualYieldDefaultPreCropId: '394e8163-636e-48dd-8ded-a9fd6436d335',
};

export const mockPreCropModule: DemandCalculationModule = {
  id: 'ce2c97af-17e4-4d85-8248-d7ba8b45527f',
  demandCalculationId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  isMandatory: true,
  isEnabled: false,
  name: CerealsDemandCalculationModule.PreCrop,
  configuration: {
    data: mockCerealsPreCrop,
  },
};

export const mockNDemandCalculationCereals: DemandCalculation = {
  created: new Date('2023-01-01T00:00:00.000Z'),
  modified: new Date('2023-01-01T00:00:00.000Z'),
  modifiedBy: '<EMAIL>',
  deleted: null,
  id: '5ee231b1-f617-4ddc-a9d6-5b636b8adec3',
  countryId: 'a8ccbf57-6056-44ec-9561-e77bd7f77abc',
  cropRegionId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  nutrientId: '880abcda-5ee5-4068-879c-94489be314d5',
  elementFormId: 'fe8dd085-1034-40b8-8891-af688b1a94fa',
  configurationType: ConfigurationType.Cereal,
  demandCalculationModules: [
    mockCerealsUseEfficiencyModule,
    mockCerealsNutrientDemandModule,
    mockCerealsSoilMineralModule,
    mockCerealsSoilOrganicMatterModule,
    mockCerealsSoilCorrectionFactorModule,
    mockPreCropModule,
  ],
};
