export type GrowthScale = {
  id: string;
  name: string;
  abbreviatedName: string;
  translationKey: string;
  mediaUri: MediaUri[];
  modifiedBy: string;
  created: Date;
  modified: Date;
  deleted: Date | null;
};

export type GrowthScaleStage = {
  id: string;
  growthScaleId: string;
  name: string;
  abbreviatedName: string;
  translationKey: string;
  mediaUri: MediaUri[] | null;
  ordinal: number;
  baseOrdinal: number | null;
  created: Date;
  modified: Date;
  modifiedBy: string;
  deleted: Date | null;
};

export type DefaultNoImageStage = {
  id: string;
  name: string;
  mediaUri: MediaUri[] | null;
};

export type GrowthScaleStageResponse = {
  entities: GrowthScaleStage[];
  hasNextPage: boolean;
};

export type MediaUri = {
  key: string;
  value: string;
};
