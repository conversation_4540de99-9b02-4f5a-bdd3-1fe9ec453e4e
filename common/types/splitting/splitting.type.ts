import { MediaUri } from '../growthScale';

export type Splitting = {
  id: string;
  countryId: string;
  cropRegionId: string;
  configuration: FertigationConfiguration;
  created: Date;
  modified: Date;
  modifiedBy: string;
  deleted: Date | null;
};

export type FertigationConfiguration = {
  data: FertigationConfigurationSingleSplit[];
  startDate: Date;
};

export type FertigationConfigurationSingleSplit = {
  splitId?: string;
  growthPhaseNo: number;
  name: string;
  daysDuration: number;
  mediaUri: MediaUri[] | null;
  nutrientsSplit: FertigationConfigurationNutrientsSplitDetails;
};

export type FertigationConfigurationNutrientsSplitDetails = {
  n: NutrientsSplitAttributes;
  p: NutrientsSplitAttributes;
  k: NutrientsSplitAttributes;
  ca: NutrientsSplitAttributes;
  mg: NutrientsSplitAttributes;
  s: NutrientsSplitAttributes;
  b: NutrientsSplitAttributes;
  cu: NutrientsSplitAttributes;
  fe: NutrientsSplitAttributes;
  mn: NutrientsSplitAttributes;
  zn: NutrientsSplitAttributes;
  mo: NutrientsSplitAttributes;
};

export type NutrientsSplitAttributes = {
  splitPercent: number | string;
  nutrientId: string;
  nutrientFormId: string;
};

export type FertigationSplittingResponse = {
  entities: Splitting[];
  hasNextPage: boolean;
};
