import { ParameterLevel } from '../analysisMethod';
import { MultipleCorrectionItem } from './demandCalculation';

export interface CerealsCropDemand {
  uptakeValue: number;
  uptakeUnitId: string;
  removalValue?: number;
  removalUnitId?: string;
}

export interface CerealsUseEfficiency {
  useEfficiency: number;
  useEfficiencyUnitId: string;
}

export interface CerealsSoilOrganicMatter {
  somThreshold: number;
  somThresholdUnitId: string;
  deduct: number;
  deductUnitId: string;
  somEvery: number;
  somEveryUnitId: string;
  somDefault: number;
  somDefaultUnitId: string;
}
export interface CerealsSoilMineral {
  defaultSoilMineral: number;
  soilMineralUnitId: string;
  isEditable: boolean;
}

export interface CerealsMultipleCorrectionItem extends MultipleCorrectionItem {
  fertilisationStrategy?: string;
}

export interface CerealsFixedQuantityCorrectionItem {
  id: string;
  parameterLevel: ParameterLevel;
  fertilisationStrategy: string;
  extractorId: string;
  quantityCorrectionValue: number;
  quantityCorrectionUnitId: string;
}

export interface CerealsSoilCorrectionFactor {
  isFixed: boolean;
  fixedQuantityCorrection: CerealsFixedQuantityCorrectionItem[];
  multiplierCorrection: CerealsMultipleCorrectionItem[];
}

export interface CerealsPreCropResidual {
  id: string;
  cropId: string;
  residualValue: number;
  residualUnitId: string;
}

export interface CerealsPreCropYield {
  id: string;
  cropId: string;
  residualValue: number;
  residualUnitId: string;
  preCropYield: number;
  preCropYieldUnitId: string;
}

export interface CerealsPreCrop {
  isResidual: boolean;
  residuals: CerealsPreCropResidual[];
  residualsWithYield: CerealsPreCropYield[];
  residualDefaultPreCropId: string;
  residualYieldDefaultPreCropId: string;
}

export interface CerealsDemandCalculationConfiguration {
  data:
    | CerealsCropDemand
    | CerealsUseEfficiency
    | CerealsSoilOrganicMatter
    | CerealsSoilMineral
    | CerealsSoilCorrectionFactor
    | CerealsPreCrop;
}

export enum CerealsDemandCalculationModule {
  CropDemand = 'CROP_DEMAND',
  UseEfficiency = 'USE_EFFICIENCY',
  SoilOrganicMatter = 'SOIL_ORGANIC_MATTER',
  SoilMineral = 'SOIL_MINERAL',
  SoilCorrectionFactor = 'SOIL_CORRECTION_FACTOR',
  PreCrop = 'PRE_CROP',
}

export type CerealsModuleNameToAccordionState = {
  [key in CerealsDemandCalculationModule]?: boolean;
};
