import { SoilCorrectionFactor } from './demandCalculation';

export interface FertigationCropDemand {
  uptakeValue: number;
  uptakeUnitId: string;
  removalValue: number;
  removalUnitId: string;
}

export interface FertigationUseEfficiency {
  maximumNUE: number;
  dripNUE: number;
  microSprinklerNUE: number;
  sprinklerNUE: number;
  pivotNUE: number;
  dryApplicationNUE: number;
  unitIdNUE: string;
}

// eslint-disable-next-line
export interface FertigationSoilCorrectionFactor extends SoilCorrectionFactor {}

export interface FertigationIrrigationWaterNutrient {
  dripIWN: number;
  microSprinklerIWN: number;
  sprinklerIWN: number;
  pivotIWN: number;
  unitIdIWN?: string;
}

export interface FertigationDemandCalculationConfiguration {
  data:
    | FertigationCropDemand
    | FertigationUseEfficiency
    | FertigationSoilCorrectionFactor
    | FertigationIrrigationWaterNutrient;
}

export enum FertigationDemandCalculationModule {
  CROP_DEMAND = 'CROP_DEMAND',
  USE_EFFICIENCY = 'USE_EFFICIENCY',
  SOIL_CORRECTION_FACTOR = 'SOIL_CORRECTION_FACTOR',
  IRRIGATION_WATER_NUTRIENT = 'IRRIGATION_WATER_NUTRIENT',
}

export interface UseEfficiencyModuleUpdate {
  isEnabled?: boolean;
  configuration?: {
    data?: {
      maximumNUE?: number;
      dripNUE?: number;
      microSprinklerNUE?: number;
      sprinklerNUE?: number;
      pivotNUE?: number;
      dryApplicationNUE?: number;
      unitIdNUE?: string;
    };
  };
}

export interface IrrigationWaterNutrientModuleUpdate {
  isEnabled?: boolean;
  configuration?: {
    data?: {
      dripIWN?: number;
      microSprinklerIWN?: number;
      sprinklerIWN?: number;
      pivotIWN?: number;
      unitIdIWN?: string;
    };
  };
}

export type FertigationModuleNameToAccordionState = {
  [key in FertigationDemandCalculationModule]: boolean;
};

export interface FertigationNutrientKeyAndFormData {
  nutrientKey: string;
  nutrientFormId: string;
  nutrientFormName: string;
  selected: boolean;
}
export interface FertigationNutrientWithNutrientForms {
  [key: string]: FertigationNutrientKeyAndFormData[];
}
