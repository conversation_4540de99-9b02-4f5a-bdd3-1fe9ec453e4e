import {
  CerealsDemandCalculationModule,
  CerealsDemandCalculationConfiguration,
  FertigationDemandCalculationModule,
  FertigationDemandCalculationConfiguration,
  ConfigurationType,
  SharedGenericResponseInfo,
  FertigationModuleNameToAccordionState,
  CerealsModuleNameToAccordionState,
} from '..';
import { ParameterLevel } from '../analysisMethod';

export interface CreateUpdateDemandCalculation {
  countryId: string;
  cropRegionId: string;
  nutrientId: string;
  elementFormId: string;
  demandCalculationModules: DemandCalculationModule[] | [];
  configurationType: ConfigurationType;
}

export interface DemandCalculation
  extends CreateUpdateDemandCalculation,
    SharedGenericResponseInfo {
  id: string;
}

export interface CreateUpdateDemandCalculationModule {
  demandCalculationId: string;
  isMandatory: boolean;
  isEnabled: boolean;
  name: CerealsDemandCalculationModule | FertigationDemandCalculationModule;
  configuration: CerealsDemandCalculationConfiguration | FertigationDemandCalculationConfiguration;
}

export interface DemandCalculationModule
  extends CreateUpdateDemandCalculationModule,
    SharedGenericResponseInfo {
  id: string;
}

export interface ModuleNameToAccordionNutrientState {
  nutrientId: string;
  accordionState: FertigationModuleNameToAccordionState | CerealsModuleNameToAccordionState;
}

export interface SoilCorrectionFactor {
  multiplierCorrection: MultipleCorrectionItem[];
}

export interface MultipleCorrectionItem {
  id: string;
  parameterLevel: ParameterLevel;
  extractorId: string;
  multiplierValue: number | string;
}
