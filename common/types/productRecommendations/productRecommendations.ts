import { ConfigurationType, SharedGenericResponseInfo } from '..';

// Fertigation
export enum ProductRecommendationRateRules {
  FIXED = 'FIXED',
  DYNAMIC = 'DYNAMIC',
}

export enum DynamicPRecommendationApplicationRate {
  NUTRIENT_SPLITS = 'NUTRIENT_SPLITS',
  PRODUCT_PLANT = 'PRODUCT_PLANT',
}

export enum ProductRecommendationApplicationTypes {
  SOIL = 'soilApplications',
  FOLIAR = 'foliarApplications',
}

export enum ProductRecommendationFoliarApplicationMethods {
  FOLIAR = 'FOLIAR',
}

export enum ProductRecommendationSoilApplicationMethods {
  BASE_DRESSING = 'BASE_DRESSING',
  SIDE_DRESSING = 'SIDE_DRESSING',
  LIQUID = 'LIQUID',
  DRENCH = 'DRENCH',
  FERTIGATION = 'FERTIGATION',
}

export interface DynamicApplicationRate {
  minRate: number | null;
  maxRate: number | null;
  rateUnits: string[] | [];
  dynamicApplicationRateMethod: DynamicPRecommendationApplicationRate | null;
  rateDefiningNutrients: string[] | [];
  productPlant: number | null;
  productPlantUnits: string[] | [];
}

export interface FixedApplicationRate {
  applicationValue: number | null;
  applicationValueUnitsIds: string[] | [];
}

export interface PhaseImage {
  key: string;
  value: string;
}

export interface FertigationPRecommendationGrowthPhaseUpdateResponse {
  recommendedProducts: string[];
  applicationMethod:
    | ProductRecommendationFoliarApplicationMethods
    | ProductRecommendationSoilApplicationMethods
    | null;
  applicationCondition: string;
  rateRule: ProductRecommendationRateRules;
  fixedApplicationRate?: FixedApplicationRate | null;
  dynamicApplicationRate?: DynamicApplicationRate | null;
  splitId: string;
  id: string;
}

export interface FertigationPRecommendationGrowthPhaseUpdate {
  phaseDetals: Omit<FertigationPRecommendationGrowthPhaseUpdateResponse, 'splitId' | 'id'>;
}

export interface FertigationPRecommendationGrowthPhaseEssentials
  extends Omit<FertigationPRecommendationGrowthPhaseUpdateResponse, 'splitId' | 'id'> {
  phaseName: string;
  phaseImage?: PhaseImage[];
  phaseNumber: number;
  ordinal: number;
}

export interface FertigationPRecommendationGrowthPhase
  extends FertigationPRecommendationGrowthPhaseEssentials {
  id: string;
}

export interface FertigationPRecommendationConfigData {
  soilApplications?: FertigationPRecommendationGrowthPhase[] | [];
  foliarApplications?: FertigationPRecommendationGrowthPhase[] | [];
}

export interface FertigationProductRecommendationsConfiguration {
  data: FertigationPRecommendationConfigData;
}

export interface CreateUpdateProductRecommendations {
  countryId: string;
  cropRegionId: string;
  configuration: FertigationProductRecommendationsConfiguration;
}

export interface ProductRecommendations
  extends CreateUpdateProductRecommendations,
    SharedGenericResponseInfo {
  id: string;
}

export interface ProductRecommendationDynamicUnitNames {
  recommendedSolidUnitPerArea: string | undefined;
  recommendedLiquidsUnitPerArea: string | undefined;
  recommendedSolidsUnitPerPlant: string | undefined;
  recommendedLiquidsUnitPerPlant: string | undefined;
}

export enum InputFieldType {
  FixedApplicationRate = 'FixedApplicationRate',
  MaxAppRate = 'MaxAppRate',
  MinAppRate = 'MinAppRate',
  AppOfProductPlant = 'AppOfProductPlant',
}

export const INPUT_FIELD_PATHS: Record<InputFieldType, string[]> = {
  [InputFieldType.FixedApplicationRate]: ['fixedApplicationRate', 'applicationValue'],
  [InputFieldType.MaxAppRate]: ['dynamicApplicationRate', 'maxRate'],
  [InputFieldType.MinAppRate]: ['dynamicApplicationRate', 'minRate'],
  [InputFieldType.AppOfProductPlant]: ['dynamicApplicationRate', 'productPlant'],
};

// Cereals MMM
export enum CerealsSoilApplicationRateRules {
  Reminder = 'REMINDER',
  NSplitting = 'N_SPLITTING',
}
export interface CerealsApplicationInitial {
  id: string;
  splitId: string | null;
  ordinal: number;
  productTypeId: string;
  growthStageId: string | null;
  stageNumber?: number | null;
}
export interface CerealsSoilApplicationPRecommendation extends CerealsApplicationInitial {
  recommendedProductIds: string[];
  rateDefiningNutrientId: string;
  minRate: number | null;
  minRateUnitId?: string | null;
  maxRate: number | null;
  maxRateUnitId?: string | null;
  ruleName: CerealsSoilApplicationRateRules;
  elementFormId: string | null;
}

export interface CerealsFoliarApplicationPRecommendation extends CerealsApplicationInitial {
  recommendedProductId?: string | null;
  fixedRate: number | null;
  fixedRateUnitId?: string | null;
}

export interface UpdateCerealsSoilPRecommendation {
  productTypeId: string;
  recommendedProductIds: string[];
  rateDefiningNutrientId: string;
  minRate?: number | null;
  minRateUnitId?: string | null;
  maxRate?: number | null;
  maxRateUnitId?: string | null;
  ruleName: CerealsSoilApplicationRateRules;
  elementFormId?: string | null;
}

export interface UpdateCerealsFoliarPRecommendation {
  productTypeId: string;
  recommendedProductId?: string;
  fixedRate?: number | null;
  fixedRateUnitId?: string | null;
}
export interface CerealsPRecommendationConfigData {
  soilApplications: CerealsSoilApplicationPRecommendation[];
  foliarApplications: CerealsFoliarApplicationPRecommendation[];
}

export type CerealsApplicationPRecommendation =
  | CerealsSoilApplicationPRecommendation
  | CerealsFoliarApplicationPRecommendation;

export interface CerealsPRecommendationConfig {
  data: CerealsPRecommendationConfigData;
}

export interface CerealsPRecommendationGrowthStageEssentials
  extends Omit<CerealsApplicationInitial, 'splitId' | 'id'> {
  stageName: string;
  stageImage?: PhaseImage[];
  stageNumber: number;
  ordinal: number;
}
export interface CerealsPRecommendationGrowthStage
  extends CerealsPRecommendationGrowthStageEssentials {
  id: string;
  splitId: string;
}

export interface CerealsCreateUpdateProductRecommendations {
  countryId: string;
  cropRegionId: string;
  configuration: CerealsPRecommendationConfig;
  configurationType: ConfigurationType.Cereal;
}

export interface CerealsProductRecommendations
  extends CerealsCreateUpdateProductRecommendations,
    SharedGenericResponseInfo {
  id: string;
}

export interface CerealsDisplaySoilApplication {
  id: string;
  stageNo?: number | null;
  stageName: string;
  stageImageUrl?: string;
  productType: string | null;
  selectedProducts: string[] | [];
  rateDefNutrientName?: string;
  maxAppRate: number | null;
  minAppRate: number | null;
  ruleName: string;
  rateRule?: string;
  updateInfo: string;
}

export interface CerealsDisplayFoliarApplication {
  id: string;
  stageNo: number | null;
  stageName: string;
  stageImageUrl: string;
  productType: string | null;
  selectedProducts: string[] | [];
  fixedAppRate: number | null;
  updateInfo: string;
}

export type CerealsDisplayAppData = CerealsDisplaySoilApplication | CerealsDisplayFoliarApplication;

export enum CerealsInputFieldType {
  MaxAppRate = 'maxAppRate',
  MinAppRate = 'minAppRate',
  FixedAppRate = 'fixedAppRate',
}

export const DEFAULT_FOLIAR_PRODUCT_TYPE_ID = '9e630e40-ffcc-4fb0-9d56-f4239d6939d7';

export const DEFAULT_SOLID_PRODUCT_TYPE_ID = 'fc8c2645-898c-4e38-b077-79de5196a061';
export const DEFAULT_LIQUID_PRODUCT_TYPE_ID = '0d7e10d4-480c-4525-be58-96fcb0302970';

export const DEFAULT_RATE_UNIT_ID = '53f8ea06-3244-4a0f-ae83-6914bac5f235';
